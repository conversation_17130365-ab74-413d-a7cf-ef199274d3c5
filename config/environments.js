/**
 * 🌍 FashionLab Environment Configuration
 * 
 * This is the SINGLE SOURCE OF TRUTH for all environment settings.
 * If you need to change URLs, project IDs, or add new environments, do it here.
 */

export const environments = {
  development: {
    name: "🛠️  Local Development",
    description: "Your local development environment running on your computer",
    url: "http://localhost:8080",
    branch: null,
    supabase: {
      projectId: null, // Uses local Docker container
      url: "http://127.0.0.1:54321",
      anonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0",
      serviceRoleKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
    },
    vercel: {
      required: false // No Vercel project for local development
    }
  },
  
  staging: {
    name: "🧪 Staging Environment",
    description: "Testing environment - safe place to try new features",
    url: "https://staging.fashionlab.tech",
    branch: "main",
    supabase: {
      projectId: "qnfmiotatmkoumlymynq",
      url: "https://qnfmiotatmkoumlymynq.supabase.co",
      // These will be loaded from environment variables
      anonKey: null, // VITE_SUPABASE_ANON_KEY_STAGING
      serviceRoleKey: null // SUPABASE_SERVICE_ROLE_KEY_STAGING
    },
    vercel: {
      required: true,
      projectName: "FashionLab Staging",
      // These will be loaded from environment variables
      projectId: null, // VERCEL_STAGING_PROJECT_ID
      orgId: null // VERCEL_ORG_ID
    }
  },
  
  production: {
    name: "🚀 Production Environment",
    description: "Live environment - what your users see",
    url: "https://app.fashionlab.tech",
    branch: "production",
    supabase: {
      projectId: "cpelxqvcjnbpnphttzsn",
      url: "https://cpelxqvcjnbpnphttzsn.supabase.co",
      // These will be loaded from environment variables
      anonKey: null, // VITE_SUPABASE_ANON_KEY_PRODUCTION
      serviceRoleKey: null // SUPABASE_SERVICE_ROLE_KEY_PRODUCTION
    },
    vercel: {
      required: true,
      projectName: "FashionLab Production",
      // These will be loaded from environment variables
      projectId: null, // VERCEL_PRODUCTION_PROJECT_ID
      orgId: null // VERCEL_ORG_ID
    }
  }
};

/**
 * 📋 Required Environment Variables
 * 
 * This defines what environment variables are needed for each environment.
 * The setup checker will use this to verify everything is configured correctly.
 */
export const requiredEnvVars = {
  development: [
    "VITE_ENVIRONMENT",
    "VITE_SUPABASE_URL", 
    "VITE_SUPABASE_ANON_KEY"
  ],
  
  staging: [
    "VITE_ENVIRONMENT",
    "VITE_SUPABASE_URL",
    "VITE_SUPABASE_ANON_KEY",
    "SUPABASE_ACCESS_TOKEN" // For CLI operations
  ],
  
  production: [
    "VITE_ENVIRONMENT",
    "VITE_SUPABASE_URL", 
    "VITE_SUPABASE_ANON_KEY",
    "SUPABASE_ACCESS_TOKEN" // For CLI operations
  ]
};

/**
 * 🔧 Helper Functions
 */

/**
 * Get environment configuration by name
 * @param {string} envName - Environment name (development, staging, production)
 * @returns {object} Environment configuration
 */
export function getEnvironment(envName) {
  const env = environments[envName];
  if (!env) {
    throw new Error(`Unknown environment: ${envName}`);
  }
  return env;
}

/**
 * Get current environment based on VITE_ENVIRONMENT variable
 * @returns {object} Current environment configuration
 */
export function getCurrentEnvironment() {
  const envName = process.env.VITE_ENVIRONMENT || 'development';
  return getEnvironment(envName);
}

/**
 * Check if an environment is valid
 * @param {string} envName - Environment name
 * @returns {boolean} True if valid
 */
export function isValidEnvironment(envName) {
  return envName in environments;
}

/**
 * Get all environment names
 * @returns {string[]} Array of environment names
 */
export function getAllEnvironmentNames() {
  return Object.keys(environments);
}