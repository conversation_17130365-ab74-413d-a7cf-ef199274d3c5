# 🌍 FashionLab Environment Variables Template
# 
# This file shows you all the environment variables used in the FashionLab project.
# Copy this file to create your actual environment files:
#
# For local development:    cp .env.example .env.local
# For staging testing:      cp .env.example .env.staging  
# For production testing:   cp .env.example .env.production
#
# 🚨 NEVER commit actual environment files with real values to Git!

# ==========================================
# 🎯 ENVIRONMENT IDENTIFICATION
# ==========================================
# Tell the app which environment it's running in
# Valid values: development, staging, production
VITE_ENVIRONMENT=development

# ==========================================
# 🚀 SUPABASE CONFIGURATION
# ==========================================
# Your Supabase project URL and keys
# These are different for each environment

# For local development (using Supabase local):
VITE_SUPABASE_URL=http://127.0.0.1:54321
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# For staging environment:
# VITE_SUPABASE_URL=https://qnfmiotatmkoumlymynq.supabase.co
# VITE_SUPABASE_ANON_KEY=your_staging_anon_key_here

# For production environment:
# VITE_SUPABASE_URL=https://cpelxqvcjnbpnphttzsn.supabase.co
# VITE_SUPABASE_ANON_KEY=your_production_anon_key_here

# ==========================================
# 🔑 SUPABASE CLI ACCESS
# ==========================================
# This token is needed to deploy migrations and manage Supabase projects
# Get it from: https://app.supabase.com/account/tokens
# SUPABASE_ACCESS_TOKEN=your_personal_access_token_here

# ==========================================
# 🗄️ DATABASE PASSWORDS (FOR MIGRATIONS)
# ==========================================
# These are only needed for direct database access and migrations
# Get these from Supabase Dashboard > Settings > Database
# STAGING_DB_PASSWORD=your_staging_db_password_here
# PRODUCTION_DB_PASSWORD=your_production_db_password_here

# ==========================================
# 📧 EMAIL SERVICE (RESEND)
# ==========================================
# For sending emails (invitations, notifications, etc.)
# Get your API key from: https://resend.com/api-keys
# VITE_RESEND_API_KEY=re_your_resend_api_key_here

# ==========================================
# 🎨 FASHION LAB API
# ==========================================
# For AI image generation
# FASHION_LAB_API_URL=https://your-api-endpoint
# FASHION_LAB_API_KEY=your-api-key
# FASHIONLAB_JWT_SECRET=your-jwt-secret-for-old-api

# ==========================================
# 🤖 OPENAI API
# ==========================================
# For garment analysis and description generation
# Get your API key from: https://platform.openai.com/api-keys
# VITE_OPENAI_API_KEY=sk-your-openai-api-key-here

# ==========================================
# 🔧 DEVELOPMENT TOOLS
# ==========================================
# Useful for debugging and development

# Enable debug logging
# VITE_DEBUG=true

# Enable verbose console logging
# VITE_VERBOSE_LOGGING=true

# ==========================================
# 📊 VERCEL CONFIGURATION (FOR DEPLOYMENTS)
# ==========================================
# These are used if you need to deploy manually or run deployment scripts
# Usually these are set in Vercel dashboard, not in local files

# Your Vercel authentication token
# VERCEL_TOKEN=your_vercel_token_here

# Your Vercel organization ID
# VERCEL_ORG_ID=your_org_id_here

# Vercel project IDs for each environment
# VERCEL_STAGING_PROJECT_ID=your_staging_project_id
# VERCEL_PRODUCTION_PROJECT_ID=your_production_project_id

# ==========================================
# 🎨 UI/UX CONFIGURATION
# ==========================================
# Feature flags and UI behavior

# Enable/disable certain features
# VITE_FEATURE_ASSET_COMPARE=true
# VITE_FEATURE_BULK_UPLOAD=true

# Analytics and tracking
# VITE_ANALYTICS_ID=your_analytics_id

# ==========================================
# 🔒 SECURITY SETTINGS
# ==========================================
# Security-related configuration

# Session timeout (in minutes)
# VITE_SESSION_TIMEOUT=60

# Enable security logging
# VITE_ENABLE_SECURITY_LOGGING=true

# ==========================================
# 📝 NOTES FOR EACH ENVIRONMENT
# ==========================================

# 🛠️  DEVELOPMENT (Local)
# - Uses local Supabase instance (Docker)
# - No real email sending
# - Safe to experiment with any settings
# - Always set VITE_ENVIRONMENT=development

# 🧪 STAGING 
# - Uses remote Supabase staging project
# - May send real emails (be careful!)
# - Should mirror production settings as closely as possible
# - Set VITE_ENVIRONMENT=staging

# 🚀 PRODUCTION
# - Uses remote Supabase production project  
# - Sends real emails to users
# - All settings affect live users
# - Set VITE_ENVIRONMENT=production

# ==========================================
# 🆘 GETTING HELP
# ==========================================
# If you're unsure about any of these settings:
#
# 1. Check the documentation: docs/DEPLOYMENT-SIMPLE.md
# 2. Run the setup checker: npm run dev:check
# 3. Check environment status: npm run status
# 4. Ask the team in Linear or Slack
#
# Remember: When in doubt, use the development environment first!