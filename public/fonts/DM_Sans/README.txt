DM Sans Variable Font
=====================

This download contains DM Sans as both variable fonts and static fonts.

DM Sans is a variable font with these axes:
  opsz
  wght

This means all the styles are contained in these files:
  DMSans-VariableFont_opsz,wght.ttf
  DMSans-Italic-VariableFont_opsz,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for DM Sans:
  static/DMSans-Thin.ttf
  static/DMSans-ExtraLight.ttf
  static/DMSans-Light.ttf
  static/DMSans-Regular.ttf
  static/DMSans-Medium.ttf
  static/DMSans-SemiBold.ttf
  static/DMSans-Bold.ttf
  static/DMSans-ExtraBold.ttf
  static/DMSans-Black.ttf
  static/DMSans_18pt-Thin.ttf
  static/DMSans_18pt-ExtraLight.ttf
  static/DMSans_18pt-Light.ttf
  static/DMSans_18pt-Regular.ttf
  static/DMSans_18pt-Medium.ttf
  static/DMSans_18pt-SemiBold.ttf
  static/DMSans_18pt-Bold.ttf
  static/DMSans_18pt-ExtraBold.ttf
  static/DMSans_18pt-Black.ttf
  static/DMSans_24pt-Thin.ttf
  static/DMSans_24pt-ExtraLight.ttf
  static/DMSans_24pt-Light.ttf
  static/DMSans_24pt-Regular.ttf
  static/DMSans_24pt-Medium.ttf
  static/DMSans_24pt-SemiBold.ttf
  static/DMSans_24pt-Bold.ttf
  static/DMSans_24pt-ExtraBold.ttf
  static/DMSans_24pt-Black.ttf
  static/DMSans_36pt-Thin.ttf
  static/DMSans_36pt-ExtraLight.ttf
  static/DMSans_36pt-Light.ttf
  static/DMSans_36pt-Regular.ttf
  static/DMSans_36pt-Medium.ttf
  static/DMSans_36pt-SemiBold.ttf
  static/DMSans_36pt-Bold.ttf
  static/DMSans_36pt-ExtraBold.ttf
  static/DMSans_36pt-Black.ttf
  static/DMSans-ThinItalic.ttf
  static/DMSans-ExtraLightItalic.ttf
  static/DMSans-LightItalic.ttf
  static/DMSans-Italic.ttf
  static/DMSans-MediumItalic.ttf
  static/DMSans-SemiBoldItalic.ttf
  static/DMSans-BoldItalic.ttf
  static/DMSans-ExtraBoldItalic.ttf
  static/DMSans-BlackItalic.ttf
  static/DMSans_18pt-ThinItalic.ttf
  static/DMSans_18pt-ExtraLightItalic.ttf
  static/DMSans_18pt-LightItalic.ttf
  static/DMSans_18pt-Italic.ttf
  static/DMSans_18pt-MediumItalic.ttf
  static/DMSans_18pt-SemiBoldItalic.ttf
  static/DMSans_18pt-BoldItalic.ttf
  static/DMSans_18pt-ExtraBoldItalic.ttf
  static/DMSans_18pt-BlackItalic.ttf
  static/DMSans_24pt-ThinItalic.ttf
  static/DMSans_24pt-ExtraLightItalic.ttf
  static/DMSans_24pt-LightItalic.ttf
  static/DMSans_24pt-Italic.ttf
  static/DMSans_24pt-MediumItalic.ttf
  static/DMSans_24pt-SemiBoldItalic.ttf
  static/DMSans_24pt-BoldItalic.ttf
  static/DMSans_24pt-ExtraBoldItalic.ttf
  static/DMSans_24pt-BlackItalic.ttf
  static/DMSans_36pt-ThinItalic.ttf
  static/DMSans_36pt-ExtraLightItalic.ttf
  static/DMSans_36pt-LightItalic.ttf
  static/DMSans_36pt-Italic.ttf
  static/DMSans_36pt-MediumItalic.ttf
  static/DMSans_36pt-SemiBoldItalic.ttf
  static/DMSans_36pt-BoldItalic.ttf
  static/DMSans_36pt-ExtraBoldItalic.ttf
  static/DMSans_36pt-BlackItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
