# FashionLab

FashionLab is an advanced AI platform designed specifically for major fashion enterprises like Fila, H&M, and other industry leaders. The platform enables fashion companies to generate photorealistic product imagery without traditional photoshoots by leveraging custom-trained LoRA (Low-Rank Adaptation) models.
The platform serves as a collaborative workspace where clients and FashionLab team members can work together to generate, review, and refine AI-generated fashion imagery. This collaborative approach ensures that all generated content maintains brand consistency while leveraging the power of AI.

## Core Capabilities

AI-Powered Fashion Imagery: Generate studio-quality product images without models, photographers, or physical studios
- Custom LoRA Model Training: Upload reference product images to train specialized AI models that understand your specific products and brand aesthetics
- Size-Inclusive Visualization: Display products in all sizes on appropriate body types to increase inclusivity while reducing return rates
- Creative Campaign Generation: Create complete marketing campaigns and creative assets at a fraction of traditional costs
- Brand Consistency: Maintain your specific brand guidelines and visual identity across all generated content
- Collaborative Workspace: Dedicated platform for clients and FashionLab team members to collaborate on image generation projects
- Future API Integration: Planned development of a FashionLab API to enable direct integration with client systems

## Benefits

- Cost Efficiency: Eliminate expensive photoshoots while increasing output volume
- Sustainability: Reduce return rates (typically 20%+) and associated carbon footprint through better size visualization
- Faster Time-to-Market: Generate product imagery rapidly without logistical constraints
- Diversity & Inclusion: Easily show products on diverse models across all sizes
- On-Brand Results: Decades of experience in fashion, advertising, photography, and e-commerce ensure quality output

## How It Works

- Initial consultation to understand your brand guidelines and visual requirements
- Upload reference images of your products (or use existing pack shots)
- Our proprietary AI technology trains custom LoRA models on your specific products
- Collaborate through our platform to generate and refine product visualizations
- Generate new product visualizations in various contexts and on diverse models
- Professional retouching and quality control ensures on-brand results
- Deliver final assets ready for e-commerce and marketing use

In the future, we'll be implementing a FashionLab API that will allow direct integration with your systems, enabling automated calls to our AI generation tools and seamless integration of the generated content back into your workflow.


## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **State Management**: React Query, Context API
- **Build Tool**: Vite

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- Docker and Docker Compose (for local Supabase)
- Supabase CLI

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/fashionlab.git
   cd fashionlab
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn
   ```

3. Set up local Supabase:
   ```bash
   # Install Supabase CLI if you haven't already
   npm install -g supabase
   
   # Initialize local Supabase
   supabase init

   # Link to project
   supabase link --project-ref <your-project-ref>
   ```

   You can find your project-ref in your Supabase dashboard URL:
   https://app.supabase.com/project/**<project-ref>**

   ```bash
   # Pull the latest schema and config from Supabase (includes public, auth, and storage schemas)
   supabase db pull

   # Start local Supabase (Postgres, Auth, Storage, etc.)
   supabase start
   ```   

   Once the services are running:
   ```bash
   # If needed, reset the local database to the latest migration state
   supabase db reset
   ```

4. Create a `.env` file in the root directory with the following content:
   ```
   VITE_SUPABASE_URL=http://127.0.0.1:54321
   VITE_SUPABASE_ANON_KEY=<your-anon-key>
   VITE_SUPABASE_SERVICE_ROLE_KEY=<your-service-role-key>
   ```
   Replace `<your-anon-key>` and `<your-service-role-key>` with the keys from the Supabase CLI output.

5. Set up the database schema:
   ```bash
   node src/setup-database.js
   ```

6. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

7. Open your browser and navigate to `http://localhost:8080`

## Documentation

Detailed documentation is available in the `docs` directory:

- [Documentation Guide](docs/README.md) - Overview of our documentation structure
- [Project Structure](docs/project-structure.md) - Comprehensive overview of codebase organization
- [Feature Documentation](docs/features/) - Feature-specific documentation

Each feature has its own documentation set:
- Product Requirements Document (PRD)
- Architecture Document
- Technical Specifications
- Project Structure Document
- Task Tracking
- Status Updates

To create documentation for a new feature, use the helper script:
```bash
./scripts/create-feature-docs.sh feature-name
```

This will:
1. Create a new directory for your feature in `docs/features/`
2. Copy all the necessary templates
3. Rename them appropriately
4. Replace placeholders with your feature name

## Testing

### Unit Tests (Vitest)
```bash
npm test                # Run unit tests
npm run test:ui         # Run with UI
npm run test:coverage   # Generate coverage report
npm run test:all        # Run all unit tests
```

Tests cover:
- Authentication and authorization
- Multi-tenant data isolation
- Asset upload and management
- Campaign/collection workflows
- Role-based access control

## Project Structure

```
fashionlab/
├── docs/                  # Documentation
│   ├── features/          # Feature-specific documentation
│   ├── templates/         # Documentation templates
│   └── README.md          # Documentation guide
├── public/                # Static assets
├── src/
│   ├── components/        # React components
│   │   ├── clients/       # Client-related components
│   │   ├── collections/   # Collection-related components
│   │   ├── layout/        # Layout components
│   │   ├── products/      # Product-related components
│   │   └── ui/            # UI components (shadcn/ui)
│   ├── contexts/          # React contexts
│   ├── hooks/             # Custom hooks
│   ├── lib/               # Utility functions
│   ├── pages/             # Page components
│   ├── types/             # TypeScript type definitions
│   ├── App.tsx            # Main App component
│   └── main.tsx           # Entry point
├── scripts/               # Utility scripts
├── .env                   # Environment variables
├── index.html             # HTML template
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
└── vite.config.ts         # Vite configuration
```

For a comprehensive overview of the project structure, see the [Project Structure](docs/project-structure.md) document.

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Supabase](https://supabase.com/) for the backend infrastructure
- [shadcn/ui](https://ui.shadcn.com/) for the UI components
- [React Query](https://tanstack.com/query/latest) for data fetching