#!/usr/bin/env node

/**
 * JWT Token Generator for Fashion Lab API Testing
 * 
 * This script generates a JWT token that can be used to test the Fashion Lab API
 * in Postman or other tools.
 */

const crypto = require('crypto');

// Configuration
const JWT_SECRET = process.env.FASHIONLAB_JWT_SECRET || 'your-secret-key-here';
const EXPIRATION_HOURS = 1;

// Base64URL encoding helper
function base64url(str) {
  return Buffer.from(str)
    .toString('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');
}

// Create JWT manually
function createJWT(payload, secret, expiresIn = 3600) {
  // Header
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  // Payload with timestamps
  const now = Math.floor(Date.now() / 1000);
  const fullPayload = {
    ...payload,
    iat: now,
    exp: now + expiresIn
  };

  // Encode header and payload
  const encodedHeader = base64url(JSON.stringify(header));
  const encodedPayload = base64url(JSON.stringify(fullPayload));

  // Create signature
  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64')
    .replace(/=/g, '')
    .replace(/\+/g, '-')
    .replace(/\//g, '_');

  // Combine all parts
  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// Generate the token
const token = createJWT({}, JWT_SECRET, EXPIRATION_HOURS * 3600);

console.log('🔐 JWT Token Generated Successfully!\n');
console.log('Token:', token);
console.log('\nToken Details:');
console.log('- Algorithm: HS256');
console.log('- Expiration: ' + EXPIRATION_HOURS + ' hour(s)');
console.log('- Secret: ' + (JWT_SECRET === 'your-secret-key-here' ? 'DEFAULT (change this!)' : 'Custom secret set'));

console.log('\n📋 How to use this token:\n');
console.log('1. Copy the token above');
console.log('2. In Postman, add to Headers:');
console.log('   Key: Authorization');
console.log('   Value: Bearer ' + token);

console.log('\n🧪 Test with curl:');
console.log(`curl -X POST https://fashionlab.notfirst.rodeo/generate-image-v2 \\
  -H "Authorization: Bearer ${token}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "model=@model.jpg" \\
  -F "garment=@garment.jpg" \\
  -F "pose=@pose.jpg" \\
  -F "background=@background.jpg" \\
  -F "prompt=your prompt here"`);

console.log('\n⚠️  Note: If you need a specific JWT secret, set the FASHIONLAB_JWT_SECRET environment variable');
console.log('Example: FASHIONLAB_JWT_SECRET=mysecret node generate-jwt-token.js\n');