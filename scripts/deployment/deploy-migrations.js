#!/usr/bin/env node

/**
 * 🚀 FashionLab Database Migration Deployment
 * 
 * This script helps you deploy database migrations to staging or production.
 * It will guide you through the process step by step and keep you informed
 * of exactly what's happening at each stage.
 * 
 * Usage: 
 *   npm run migrate:staging    (Deploy to staging)
 *   npm run migrate:production (Deploy to production)
 */

import { execSync, spawn } from 'child_process';
import { readFileSync } from 'fs';
import readline from 'readline';
import { environments } from '../../config/environments.js';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m'
};

// Helper functions for colored output
const success = (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`);
const error = (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`);
const warning = (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`);
const info = (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`);
const step = (msg) => console.log(`${colors.cyan}👉 ${msg}${colors.reset}`);
const header = (msg) => console.log(`\n${colors.bold}${colors.magenta}${msg}${colors.reset}\n`);

// Get the environment from command line arguments
const targetEnvironment = process.argv[2];

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Ask user a question and wait for response
 */
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

/**
 * Show deployment header with environment info
 */
function showHeader() {
  const env = environments[targetEnvironment];
  
  console.log(`
${colors.bold}${colors.cyan}🚀 FashionLab Database Migration Deployment${colors.reset}

${colors.bold}Target Environment:${colors.reset} ${env.name}
${colors.bold}URL:${colors.reset} ${env.url}
${colors.bold}Supabase Project:${colors.reset} ${env.supabase.projectId}

This script will help you deploy database migrations safely.
I'll guide you through each step and explain what's happening.
`);
}

/**
 * Validate that we can run this deployment
 */
async function validateEnvironment() {
  header("🔍 Step 1: Validating Environment");
  
  // Check if environment is valid
  if (!environments[targetEnvironment]) {
    error(`Invalid environment: ${targetEnvironment}`);
    error("Valid environments are: development, staging, production");
    process.exit(1);
  }
  
  success(`Environment '${targetEnvironment}' is valid`);
  
  // Check if Supabase CLI is available
  try {
    execSync('supabase --version', { stdio: 'pipe' });
    success("Supabase CLI is available");
  } catch (e) {
    error("Supabase CLI is not installed or not in PATH");
    error("Please install it with: npm install -g @supabase/cli");
    process.exit(1);
  }
  
  // Check for SUPABASE_ACCESS_TOKEN
  if (!process.env.SUPABASE_ACCESS_TOKEN) {
    error("SUPABASE_ACCESS_TOKEN environment variable is not set");
    error("You need this token to deploy migrations");
    info("Get your token from: https://app.supabase.com/account/tokens");
    info("Then set it with: export SUPABASE_ACCESS_TOKEN=your_token");
    process.exit(1);
  }
  
  success("Supabase access token is available");
}

/**
 * Show current project status
 */
async function showCurrentStatus() {
  header("📊 Step 2: Current Project Status");
  
  const env = environments[targetEnvironment];
  
  step("Checking which Supabase project is currently linked...");
  
  try {
    const output = execSync('supabase status', { encoding: 'utf8' });
    
    if (output.includes(env.supabase.projectId)) {
      success(`Already linked to ${targetEnvironment} project (${env.supabase.projectId})`);
    } else {
      warning(`Currently linked to a different project`);
      step(`Will link to ${targetEnvironment} project: ${env.supabase.projectId}`);
    }
  } catch (e) {
    info("No project currently linked (this is okay)");
  }
}

/**
 * Link to the correct Supabase project
 */
async function linkToProject() {
  header("🔗 Step 3: Connecting to Supabase Project");
  
  const env = environments[targetEnvironment];
  
  step(`Linking to ${env.name} project...`);
  info(`Project ID: ${env.supabase.projectId}`);
  
  try {
    // Link to the project
    execSync(`supabase link --project-ref ${env.supabase.projectId}`, { 
      stdio: 'inherit' // This will show any prompts to the user
    });
    
    success(`Successfully linked to ${env.name} project`);
  } catch (e) {
    error("Failed to link to Supabase project");
    error("This might be due to:");
    error("• Invalid project ID");
    error("• Invalid access token");
    error("• Network connectivity issues");
    process.exit(1);
  }
}

/**
 * Show pending migrations
 */
async function showPendingMigrations() {
  header("📝 Step 4: Checking Migrations");
  
  step("Looking for migrations that need to be deployed...");
  
  try {
    const output = execSync('supabase migration list', { encoding: 'utf8' });
    
    console.log(output);
    
    // Check if there are any migrations
    if (output.includes('No migrations found')) {
      warning("No migrations found in the project");
      
      const proceed = await askQuestion("Do you want to continue anyway? (y/N): ");
      if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
        info("Deployment cancelled");
        process.exit(0);
      }
    } else {
      success("Found migrations to potentially deploy");
    }
    
  } catch (e) {
    error("Could not list migrations");
    error("Error: " + e.message);
    process.exit(1);
  }
}

/**
 * Get final confirmation before deployment
 */
async function getFinalConfirmation() {
  header("⚠️  Step 5: Final Confirmation");
  
  const env = environments[targetEnvironment];
  
  console.log(`${colors.bold}You are about to deploy database migrations to:${colors.reset}`);
  console.log(`${colors.yellow}${colors.bold}${env.name}${colors.reset}`);
  console.log(`${colors.yellow}${colors.bold}${env.url}${colors.reset}`);
  console.log("");
  
  if (targetEnvironment === 'production') {
    warning("🚨 THIS IS PRODUCTION! 🚨");
    warning("This will affect the live application that users are accessing!");
    console.log("");
    
    const confirmText = "DEPLOY TO PRODUCTION";
    const confirmation = await askQuestion(`Type "${confirmText}" (exactly) to confirm: `);
    
    if (confirmation !== confirmText) {
      error("Confirmation text did not match exactly");
      info("Deployment cancelled for safety");
      process.exit(0);
    }
  } else {
    const confirmation = await askQuestion("Type 'DEPLOY' to confirm: ");
    
    if (confirmation !== 'DEPLOY') {
      info("Deployment cancelled");
      process.exit(0);
    }
  }
  
  success("Confirmation received. Proceeding with deployment...");
}

/**
 * Deploy the migrations
 */
async function deployMigrations() {
  header("🚀 Step 6: Deploying Migrations");
  
  step("Starting migration deployment...");
  info("This may take a few moments depending on the complexity of your migrations");
  
  try {
    // Run the deployment with live output
    execSync('supabase db push', { stdio: 'inherit' });
    
    success("🎉 Migrations deployed successfully!");
    
  } catch (e) {
    error("Migration deployment failed!");
    error("Please check the output above for specific error details");
    
    console.log(`\n${colors.yellow}Common issues and solutions:${colors.reset}`);
    console.log("• Migration syntax errors: Check your SQL syntax");
    console.log("• Foreign key constraints: Ensure referenced tables exist");
    console.log("• Permission issues: Verify your access token is valid");
    console.log("• Network issues: Check your internet connection");
    
    process.exit(1);
  }
}

/**
 * Verify deployment success
 */
async function verifyDeployment() {
  header("✅ Step 7: Verifying Deployment");
  
  step("Checking migration status after deployment...");
  
  try {
    const output = execSync('supabase migration list', { encoding: 'utf8' });
    console.log(output);
    
    success("Migration status retrieved successfully");
    
  } catch (e) {
    warning("Could not verify migration status");
    warning("Your migrations may have deployed successfully anyway");
  }
  
  const env = environments[targetEnvironment];
  
  step("Next steps:");
  info(`• Test your application at: ${env.url}`);
  if (targetEnvironment === 'staging') {
    info("• If everything looks good, you can deploy to production later");
  }
  info("• Monitor for any issues in the next few minutes");
  
  if (targetEnvironment === 'production') {
    warning("🔍 Keep an eye on production for the next 30 minutes");
    warning("Watch for any error reports or unusual behavior");
  }
}

/**
 * Show final summary
 */
function showSummary() {
  const env = environments[targetEnvironment];
  
  header("📋 Deployment Complete");
  
  console.log(`${colors.green}${colors.bold}✅ Successfully deployed migrations to ${env.name}${colors.reset}\n`);
  
  console.log(`${colors.bold}What happened:${colors.reset}`);
  console.log(`• Connected to ${env.supabase.projectId}`);
  console.log(`• Deployed database migrations`);
  console.log(`• Verified deployment status`);
  console.log("");
  
  console.log(`${colors.bold}What to do next:${colors.reset}`);
  console.log(`• Visit ${env.url} to test the application`);
  console.log(`• Check for any console errors or issues`);
  if (targetEnvironment === 'staging') {
    console.log(`• When ready, deploy to production with: npm run migrate:production`);
  }
  console.log("");
  
  if (targetEnvironment === 'production') {
    console.log(`${colors.yellow}${colors.bold}⚠️  Production Monitoring Reminder:${colors.reset}`);
    console.log(`• Monitor the application for the next 30 minutes`);
    console.log(`• Be ready to address any issues that arise`);
    console.log(`• Keep this terminal open to see any final messages`);
  }
}

/**
 * Main deployment function
 */
async function main() {
  try {
    showHeader();
    await validateEnvironment();
    await showCurrentStatus();
    await linkToProject();
    await showPendingMigrations();
    await getFinalConfirmation();
    await deployMigrations();
    await verifyDeployment();
    showSummary();
    
  } catch (error) {
    console.error(`\n${colors.red}❌ Unexpected error during deployment:${colors.reset}`);
    console.error(error.message);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Validate arguments
if (!targetEnvironment) {
  console.error(`${colors.red}❌ Please specify an environment: staging or production${colors.reset}`);
  console.error(`Usage: node deploy-migrations.js <environment>`);
  console.error(`Example: node deploy-migrations.js staging`);
  process.exit(1);
}

// Run the main function
main();