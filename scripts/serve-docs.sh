#!/bin/bash

# Documentation Server
# Serves documentation with hot reload for local viewing

set -e

DOCS_DIR="docs"
PORT=${1:-8080}

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}📚 FashionLab Documentation Server${NC}"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
  echo "❌ Python 3 is required but not installed."
  echo "Please install Python 3 and try again."
  exit 1
fi

# Generate index first
echo "📇 Generating documentation index..."
if [ -f "scripts/generate-docs-index.js" ]; then
  node scripts/generate-docs-index.js
fi

# Create a simple index.html that redirects to docs
cat > index.html << EOF
<!DOCTYPE html>
<html>
<head>
  <title>FashionLab Documentation</title>
  <meta charset="utf-8">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      line-height: 1.6;
    }
    h1 { color: #333; }
    a { color: #0066cc; text-decoration: none; }
    a:hover { text-decoration: underline; }
    .links { 
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin-top: 2rem;
    }
    .link-card {
      border: 1px solid #ddd;
      padding: 1rem;
      border-radius: 8px;
      transition: box-shadow 0.2s;
    }
    .link-card:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
  </style>
</head>
<body>
  <h1>🎨 FashionLab Documentation</h1>
  <p>AI-powered fashion imagery platform documentation.</p>
  
  <div class="links">
    <a href="/docs/00-quick-start/README.md" class="link-card">
      <h3>🚀 Quick Start</h3>
      <p>Get up and running in 5 minutes</p>
    </a>
    
    <a href="/docs/01-overview/README.md" class="link-card">
      <h3>📖 Overview</h3>
      <p>Understand the platform architecture</p>
    </a>
    
    <a href="/docs/04-api/README.md" class="link-card">
      <h3>🔌 API Reference</h3>
      <p>Complete API documentation</p>
    </a>
    
    <a href="/docs/07-security/README.md" class="link-card">
      <h3>🔒 Security</h3>
      <p>Security best practices and guidelines</p>
    </a>
    
    <a href="/docs/INDEX.md" class="link-card">
      <h3>📚 Full Index</h3>
      <p>Browse all documentation</p>
    </a>
    
    <a href="/docs/00-quick-start/troubleshooting.md" class="link-card">
      <h3>🔧 Troubleshooting</h3>
      <p>Common issues and solutions</p>
    </a>
  </div>
  
  <hr style="margin-top: 3rem;">
  <p style="text-align: center; color: #666;">
    Serving on <a href="http://localhost:${PORT}">http://localhost:${PORT}</a>
  </p>
</body>
</html>
EOF

# Start server
echo -e "${GREEN}✅ Starting documentation server...${NC}"
echo ""
echo -e "📄 Documentation: ${BLUE}http://localhost:${PORT}${NC}"
echo -e "📁 Serving from: ${BLUE}$(pwd)${NC}"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Use Python's built-in server with better MIME types
python3 -m http.server $PORT --bind 127.0.0.1 2>/dev/null || python3 -m SimpleHTTPServer $PORT