#!/usr/bin/env node

/**
 * Documentation Index Generator
 * Scans all markdown files and generates a hierarchical index
 */

import { readdir, readFile, writeFile } from 'fs/promises';
import { join, relative, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const DOCS_DIR = join(__dirname, '..', 'docs');
const INDEX_FILE = join(DOCS_DIR, 'INDEX.md');

// Directories to ignore
const IGNORE_DIRS = ['node_modules', '.git', 'dist', 'build'];

// Extract title from markdown file
async function extractTitle(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    const titleMatch = content.match(/^#\s+(.+)$/m);
    return titleMatch ? titleMatch[1] : 'Untitled';
  } catch (error) {
    return 'Untitled';
  }
}

// Recursively scan directory for markdown files
async function scanDirectory(dir, baseDir = dir) {
  const entries = await readdir(dir, { withFileTypes: true });
  const files = [];

  for (const entry of entries) {
    const fullPath = join(dir, entry.name);
    
    if (entry.isDirectory()) {
      if (!IGNORE_DIRS.includes(entry.name)) {
        const subFiles = await scanDirectory(fullPath, baseDir);
        files.push(...subFiles);
      }
    } else if (entry.isFile() && entry.name.endsWith('.md') && entry.name !== 'INDEX.md') {
      const relativePath = relative(baseDir, fullPath);
      const title = await extractTitle(fullPath);
      files.push({
        path: relativePath,
        title,
        depth: relativePath.split('/').length - 1
      });
    }
  }

  return files;
}

// Group files by directory
function groupByDirectory(files) {
  const grouped = {};
  
  for (const file of files) {
    const dir = dirname(file.path);
    if (!grouped[dir]) {
      grouped[dir] = [];
    }
    grouped[dir].push(file);
  }
  
  return grouped;
}

// Generate markdown index
function generateIndex(files) {
  const grouped = groupByDirectory(files);
  const directories = Object.keys(grouped).sort();
  
  let content = '# Documentation Index\n\n';
  content += 'Auto-generated index of all documentation files.\n\n';
  content += `*Last updated: ${new Date().toISOString()}*\n\n`;
  
  // Add quick links
  content += '## Quick Links\n\n';
  content += '- [Quick Start](./00-quick-start/README.md)\n';
  content += '- [API Reference](./04-api/README.md)\n';
  content += '- [Security Guide](./07-security/README.md)\n';
  content += '- [Troubleshooting](./00-quick-start/troubleshooting.md)\n\n';
  
  content += '## All Documentation\n\n';
  
  for (const dir of directories) {
    const level = dir.split('/').filter(Boolean).length;
    const indent = '  '.repeat(level);
    
    // Directory header
    if (dir !== '.') {
      const dirName = dir.split('/').pop();
      content += `${indent}### ${formatDirName(dirName)}\n\n`;
    }
    
    // Files in directory
    const dirFiles = grouped[dir].sort((a, b) => {
      // README first, then alphabetical
      if (a.path.includes('README.md')) return -1;
      if (b.path.includes('README.md')) return 1;
      return a.title.localeCompare(b.title);
    });
    
    for (const file of dirFiles) {
      const fileIndent = dir === '.' ? '' : indent;
      content += `${fileIndent}- [${file.title}](./${file.path})\n`;
    }
    
    content += '\n';
  }
  
  // Add statistics
  content += '## Statistics\n\n';
  content += `- Total documentation files: ${files.length}\n`;
  content += `- Directories: ${directories.length}\n`;
  content += `- Last updated: ${new Date().toLocaleString()}\n`;
  
  return content;
}

// Format directory name for display
function formatDirName(name) {
  // Handle numbered directories
  if (/^\d{2}-/.test(name)) {
    name = name.substring(3);
  }
  
  // Convert kebab-case to Title Case
  return name
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Main function
async function main() {
  try {
    console.log('📚 Generating documentation index...');
    
    // Scan for all markdown files
    const files = await scanDirectory(DOCS_DIR, DOCS_DIR);
    console.log(`Found ${files.length} documentation files`);
    
    // Generate index content
    const indexContent = generateIndex(files);
    
    // Write index file
    await writeFile(INDEX_FILE, indexContent);
    console.log(`✅ Index generated at: ${INDEX_FILE}`);
    
    // Summary
    const grouped = groupByDirectory(files);
    console.log(`\n📊 Summary:`);
    console.log(`   - Total files: ${files.length}`);
    console.log(`   - Directories: ${Object.keys(grouped).length}`);
    
  } catch (error) {
    console.error('❌ Error generating index:', error);
    process.exit(1);
  }
}

// Run if called directly
if (process.argv[1] === __filename) {
  main();
}