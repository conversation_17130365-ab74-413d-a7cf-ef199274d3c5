-- Fix any storage paths that have duplicate 'model-library/' prefix
UPDATE public.model_images
SET storage_path = SUBSTRING(storage_path FROM 15)  -- Remove first 14 chars ('model-library/')
WHERE storage_path LIKE 'model-library/model-library/%';

-- Show the results
SELECT 
  ml.code as model_code,
  mi.angle_type,
  mi.storage_path
FROM public.model_images mi
JOIN public.model_library ml ON mi.model_id = ml.id
WHERE mi.storage_path IS NOT NULL
ORDER BY ml.code, mi.angle_type;