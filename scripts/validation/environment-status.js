#!/usr/bin/env node

/**
 * 📊 FashionLab Environment Status Dashboard
 * 
 * This script checks the health and status of all your environments.
 * It gives you a quick overview of what's working and what needs attention.
 * 
 * Usage: npm run status
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { environments } from '../../config/environments.js';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m'
};

// Helper functions for colored output
const success = (msg) => `${colors.green}✅ ${msg}${colors.reset}`;
const error = (msg) => `${colors.red}❌ ${msg}${colors.reset}`;
const warning = (msg) => `${colors.yellow}⚠️  ${msg}${colors.reset}`;
const info = (msg) => `${colors.blue}ℹ️  ${msg}${colors.reset}`;
const header = (msg) => `${colors.bold}${colors.cyan}${msg}${colors.reset}`;

/**
 * Show welcome header
 */
function showWelcome() {
  console.log(`
${colors.bold}${colors.cyan}📊 FashionLab Environment Status Dashboard${colors.reset}

Checking the health of all your environments...
This may take a moment as I test each one.

`);
}

/**
 * Check if a URL is accessible and healthy
 */
async function checkUrlHealth(url) {
  try {
    const healthUrl = `${url}/health.json`;
    
    // Use fetch if available (Node.js 18+) or fallback to curl
    let response;
    try {
      response = await fetch(healthUrl, { 
        method: 'GET',
        timeout: 10000  // 10 second timeout
      });
      
      if (response.ok) {
        const data = await response.json();
        return {
          accessible: true,
          healthy: data.status === 'healthy',
          data: data
        };
      } else {
        return {
          accessible: true,
          healthy: false,
          error: `HTTP ${response.status}`
        };
      }
    } catch (fetchError) {
      // Fallback to curl
      try {
        const curlOutput = execSync(`curl -s -m 10 "${healthUrl}"`, { encoding: 'utf8' });
        const data = JSON.parse(curlOutput);
        return {
          accessible: true,
          healthy: data.status === 'healthy',
          data: data
        };
      } catch (curlError) {
        return {
          accessible: false,
          healthy: false,
          error: 'Not accessible'
        };
      }
    }
  } catch (e) {
    return {
      accessible: false,
      healthy: false,
      error: e.message
    };
  }
}

/**
 * Check local Supabase status
 */
function checkLocalSupabase() {
  try {
    const output = execSync('supabase status', { encoding: 'utf8', stdio: 'pipe' });
    
    // Check for either the "running" message OR the presence of API URL (which indicates it's running)
    if (output.includes('supabase local development setup is running') || output.includes('API URL:')) {
      return {
        running: true,
        services: extractServiceUrls(output)
      };
    } else {
      return {
        running: false,
        error: 'Not running'
      };
    }
  } catch (e) {
    return {
      running: false,
      error: 'Not available'
    };
  }
}

/**
 * Extract service URLs from Supabase status output
 */
function extractServiceUrls(output) {
  const services = {};
  const lines = output.split('\n');
  
  lines.forEach(line => {
    if (line.includes('API URL:')) {
      services.api = line.split('API URL:')[1].trim();
    }
    if (line.includes('Studio URL:')) {
      services.studio = line.split('Studio URL:')[1].trim();
    }
    if (line.includes('DB URL:')) {
      services.db = line.split('DB URL:')[1].trim();
    }
  });
  
  return services;
}

/**
 * Check current Git status
 */
function checkGitStatus() {
  try {
    const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    
    // Check for uncommitted changes
    const statusOutput = execSync('git status --porcelain', { encoding: 'utf8' });
    const hasUncommittedChanges = statusOutput.trim().length > 0;
    
    // Check if branch is ahead/behind
    try {
      const aheadBehind = execSync(`git rev-list --left-right --count origin/${branch}...HEAD`, { 
        encoding: 'utf8', 
        stdio: 'pipe' 
      }).trim();
      const [behind, ahead] = aheadBehind.split('\t').map(n => parseInt(n));
      
      return {
        currentBranch: branch,
        hasUncommittedChanges,
        commitsAhead: ahead,
        commitsBehind: behind
      };
    } catch (e) {
      return {
        currentBranch: branch,
        hasUncommittedChanges,
        commitsAhead: 0,
        commitsBehind: 0,
        remoteStatus: 'unknown'
      };
    }
  } catch (e) {
    return {
      error: 'Not a git repository or git not available'
    };
  }
}

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  const checks = {
    development: false,
    staging: false,
    production: false,
    supabaseToken: !!process.env.SUPABASE_ACCESS_TOKEN
  };
  
  // Check if we can determine environment
  const currentEnv = process.env.VITE_ENVIRONMENT;
  if (currentEnv && currentEnv in environments) {
    checks[currentEnv] = true;
  }
  
  return checks;
}

/**
 * Format environment status
 */
function formatEnvironmentStatus(envName, envConfig, healthCheck) {
  const lines = [];
  
  lines.push(header(`${envConfig.name}`));
  lines.push(`  URL: ${envConfig.url}`);
  
  if (envName === 'development') {
    // Special handling for local development
    const supabaseStatus = checkLocalSupabase();
    
    if (supabaseStatus.running) {
      lines.push(`  ${success('Frontend: Ready for development')}`);
      lines.push(`  ${success('Supabase: Running locally')}`);
      if (supabaseStatus.services.studio) {
        lines.push(`  ${info('Studio: ' + supabaseStatus.services.studio)}`);
      }
    } else {
      lines.push(`  ${warning('Frontend: Ready for development')}`);
      lines.push(`  ${error('Supabase: ' + supabaseStatus.error)}`);
      lines.push(`  ${info('Start with: npm run supabase:start')}`);
    }
  } else {
    // Remote environments
    if (healthCheck.accessible && healthCheck.healthy) {
      lines.push(`  ${success('Status: Healthy')}`);
      if (healthCheck.data.environment) {
        lines.push(`  ${info('Environment: ' + healthCheck.data.environment)}`);
      }
      if (healthCheck.data.version) {
        lines.push(`  ${info('Version: ' + healthCheck.data.version)}`);
      }
    } else if (healthCheck.accessible && !healthCheck.healthy) {
      lines.push(`  ${warning('Status: Accessible but not healthy')}`);
      if (healthCheck.error) {
        lines.push(`  ${error('Error: ' + healthCheck.error)}`);
      }
    } else {
      lines.push(`  ${error('Status: Not accessible')}`);
      if (healthCheck.error) {
        lines.push(`  ${error('Error: ' + healthCheck.error)}`);
      }
    }
    
    lines.push(`  ${info('Supabase Project: ' + envConfig.supabase.projectId)}`);
  }
  
  return lines.join('\n') + '\n';
}

/**
 * Show Git status summary
 */
function showGitStatus(gitStatus) {
  console.log(header('🔄 Git Status'));
  
  if (gitStatus.error) {
    console.log(`  ${error(gitStatus.error)}`);
    return;
  }
  
  console.log(`  Current branch: ${gitStatus.currentBranch}`);
  
  if (gitStatus.hasUncommittedChanges) {
    console.log(`  ${warning('You have uncommitted changes')}`);
  } else {
    console.log(`  ${success('Working directory is clean')}`);
  }
  
  if (gitStatus.commitsAhead > 0) {
    console.log(`  ${info('Commits ahead of remote: ' + gitStatus.commitsAhead)}`);
  }
  
  if (gitStatus.commitsBehind > 0) {
    console.log(`  ${warning('Commits behind remote: ' + gitStatus.commitsBehind)}`);
  }
  
  if (gitStatus.commitsAhead === 0 && gitStatus.commitsBehind === 0) {
    console.log(`  ${success('Up to date with remote')}`);
  }
  
  console.log('');
}

/**
 * Show environment variables status
 */
function showEnvironmentVariablesStatus(envVars) {
  console.log(header('🔧 Environment Configuration'));
  
  if (envVars.supabaseToken) {
    console.log(`  ${success('Supabase access token is set')}`);
  } else {
    console.log(`  ${warning('Supabase access token not found')}`);
    console.log(`  ${info('Set with: export SUPABASE_ACCESS_TOKEN=your_token')}`);
  }
  
  const currentEnv = process.env.VITE_ENVIRONMENT || 'not set';
  if (currentEnv !== 'not set') {
    console.log(`  ${success(`Current environment: ${currentEnv}`)}`);
  } else {
    console.log(`  ${warning('Current environment: not set')}`);
  }
  
  console.log('');
}

/**
 * Show summary and recommendations
 */
function showSummary(results) {
  console.log(header('📋 Summary & Recommendations'));
  
  const { envHealth, gitStatus, envVars } = results;
  
  // Count healthy environments
  const healthyCount = Object.values(envHealth).filter(h => 
    h.accessible && h.healthy
  ).length;
  
  const totalRemoteEnvs = Object.keys(environments).length - 1; // Exclude development
  
  console.log(`  Remote environments healthy: ${healthyCount}/${totalRemoteEnvs}`);
  
  if (envHealth.development && envHealth.development.running) {
    console.log(`  ${success('Local development environment is ready')}`);
  } else {
    console.log(`  ${warning('Local development environment needs setup')}`);
    console.log(`    Run: npm run dev:check`);
  }
  
  if (gitStatus && !gitStatus.error) {
    if (gitStatus.hasUncommittedChanges) {
      console.log(`  ${info('You have uncommitted changes - consider committing them')}`);
    }
    
    if (gitStatus.commitsAhead > 0) {
      console.log(`  ${info('Push your commits to share them with the team')}`);
    }
  }
  
  if (!envVars.supabaseToken) {
    console.log(`  ${warning('Set up Supabase access token for deployments')}`);
  }
  
  console.log('');
  console.log(`  ${info('Run individual checks:')}`);
  console.log(`    npm run dev:check  - Check local development setup`);
  console.log(`    npm run status     - Run this dashboard again`);
  console.log('');
}

/**
 * Load environment variables from .env.local
 */
function loadEnvFile() {
  try {
    const envPath = '.env.local';
    if (existsSync(envPath)) {
      const envContent = readFileSync(envPath, 'utf8');
      envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0 && !process.env[key.trim()]) {
            process.env[key.trim()] = valueParts.join('=').trim();
          }
        }
      });
    }
  } catch (e) {
    // Ignore errors loading env file
  }
}

/**
 * Main function
 */
async function main() {
  // Load environment variables first
  loadEnvFile();
  
  showWelcome();
  
  const results = {
    envHealth: {},
    gitStatus: null,
    envVars: null
  };
  
  // Check each environment
  for (const [envName, envConfig] of Object.entries(environments)) {
    console.log(`Checking ${envConfig.name}...`);
    
    if (envName === 'development') {
      const supabaseStatus = checkLocalSupabase();
      results.envHealth[envName] = supabaseStatus;
    } else {
      const healthCheck = await checkUrlHealth(envConfig.url);
      results.envHealth[envName] = healthCheck;
    }
  }
  
  console.log(''); // Add some space
  
  // Check Git status
  results.gitStatus = checkGitStatus();
  
  // Check environment variables
  results.envVars = checkEnvironmentVariables();
  
  // Display all results
  console.log(header('🌍 Environment Status Report'));
  console.log('');
  
  for (const [envName, envConfig] of Object.entries(environments)) {
    const healthCheck = results.envHealth[envName];
    console.log(formatEnvironmentStatus(envName, envConfig, healthCheck));
  }
  
  showGitStatus(results.gitStatus);
  showEnvironmentVariablesStatus(results.envVars);
  showSummary(results);
}

// Run the main function
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});