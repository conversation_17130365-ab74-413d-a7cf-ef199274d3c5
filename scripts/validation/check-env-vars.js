#!/usr/bin/env node

/**
 * 🔧 FashionLab Environment Variables Checker
 * 
 * This script helps you manage and validate environment variables across
 * all the different systems: local files, Vercel, Supabase, etc.
 * 
 * It will tell you:
 * - What variables are missing
 * - What variables might be wrong
 * - Where to find/set each variable
 * - How to fix any issues
 * 
 * Usage: npm run env:check
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { environments, requiredEnvVars } from '../../config/environments.js';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m'
};

// Helper functions for colored output
const success = (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`);
const error = (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`);
const warning = (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`);
const info = (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`);
const step = (msg) => console.log(`${colors.cyan}👉 ${msg}${colors.reset}`);
const header = (msg) => console.log(`\n${colors.bold}${colors.magenta}${msg}${colors.reset}\n`);

let hasErrors = false;
let hasWarnings = false;

/**
 * Load environment variables from a file
 */
function loadEnvFile(filename) {
  if (!existsSync(filename)) {
    return null;
  }
  
  try {
    const content = readFileSync(filename, 'utf8');
    const variables = {};
    
    content.split('\n').forEach(line => {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#') && trimmed.includes('=')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          variables[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    return variables;
  } catch (e) {
    return null;
  }
}

/**
 * Show welcome header
 */
function showWelcome() {
  console.log(`
${colors.bold}${colors.cyan}🔧 FashionLab Environment Variables Checker${colors.reset}

This tool will help you understand and fix your environment variable setup.
I'll check all your environment files and guide you through any issues.

`);
}

/**
 * Check environment files
 */
function checkEnvironmentFiles() {
  header("📄 Environment Files Status");
  
  const files = [
    { name: '.env.local', description: 'Local development variables', required: true },
    { name: '.env.example', description: 'Template file (reference)', required: false },
    { name: '.env.staging', description: 'Staging test variables', required: false },
    { name: '.env.production', description: 'Production test variables', required: false }
  ];
  
  const foundFiles = {};
  
  files.forEach(file => {
    if (existsSync(file.name)) {
      success(`${file.name} exists (${file.description})`);
      foundFiles[file.name] = loadEnvFile(file.name);
    } else {
      if (file.required) {
        error(`${file.name} is missing (${file.description})`);
        hasErrors = true;
        info(`Create it with: cp .env.example ${file.name}`);
      } else {
        info(`${file.name} not found (${file.description}) - This is optional`);
      }
    }
  });
  
  return foundFiles;
}

/**
 * Validate environment variables for current environment
 */
function validateCurrentEnvironment(envFiles) {
  header("🎯 Current Environment Validation");
  
  // Start with system environment variables
  let currentVars = { ...process.env };
  
  // Overlay with .env.local if it exists
  if (envFiles['.env.local']) {
    currentVars = { ...currentVars, ...envFiles['.env.local'] };
  }
  
  const currentEnv = currentVars.VITE_ENVIRONMENT || 'development';
  
  info(`Current environment: ${currentEnv}`);
  
  if (!environments[currentEnv]) {
    error(`Invalid environment: ${currentEnv}`);
    error(`Valid options: ${Object.keys(environments).join(', ')}`);
    hasErrors = true;
    return;
  }
  
  success(`Environment '${currentEnv}' is valid`);
  
  // Check required variables for this environment
  const required = requiredEnvVars[currentEnv] || [];
  
  let allPresent = true;
  
  required.forEach(varName => {
    if (currentVars[varName]) {
      success(`${varName} is set`);
      
      // Additional validation for specific variables
      if (varName === 'VITE_SUPABASE_URL') {
        const url = currentVars[varName];
        if (currentEnv === 'development') {
          if (!url.includes('127.0.0.1') && !url.includes('localhost')) {
            warning(`For development, VITE_SUPABASE_URL should point to local Supabase`);
            warning(`Expected: http://127.0.0.1:54321`);
            warning(`Current: ${url}`);
            hasWarnings = true;
          }
        } else {
          const expectedProjectId = environments[currentEnv].supabase.projectId;
          if (!url.includes(expectedProjectId)) {
            error(`VITE_SUPABASE_URL doesn't match expected project for ${currentEnv}`);
            error(`Expected to contain: ${expectedProjectId}`);
            error(`Current: ${url}`);
            hasErrors = true;
          }
        }
      }
      
    } else {
      error(`${varName} is missing`);
      allPresent = false;
      hasErrors = true;
      
      // Provide specific help for each variable
      provideVariableHelp(varName, currentEnv);
    }
  });
  
  return allPresent;
}

/**
 * Provide specific help for missing variables
 */
function provideVariableHelp(varName, currentEnv) {
  switch (varName) {
    case 'VITE_ENVIRONMENT':
      info(`Set to: ${currentEnv}`);
      break;
      
    case 'VITE_SUPABASE_URL':
      if (currentEnv === 'development') {
        info('For local development: http://127.0.0.1:54321');
        info('Make sure Supabase is running: npm run supabase:start');
      } else {
        const env = environments[currentEnv];
        info(`For ${currentEnv}: ${env.supabase.url}`);
        info('Get this from your Supabase project dashboard > Settings > API');
      }
      break;
      
    case 'VITE_SUPABASE_ANON_KEY':
      if (currentEnv === 'development') {
        info('For local development, use the default local key (see .env.example)');
      } else {
        info(`Get this from your Supabase project dashboard > Settings > API`);
        info('Look for the "anon public" key');
      }
      break;
      
    case 'SUPABASE_ACCESS_TOKEN':
      info('Get your personal access token from: https://app.supabase.com/account/tokens');
      info('This is needed for CLI operations like deploying migrations');
      break;
      
    default:
      info(`Check .env.example for guidance on ${varName}`);
  }
}

/**
 * Check for environment variable conflicts
 */
function checkForConflicts(envFiles) {
  header("⚠️  Environment Variable Conflicts");
  
  const conflicts = [];
  
  // Check if system environment variables might override file variables
  Object.keys(envFiles).forEach(filename => {
    const fileVars = envFiles[filename];
    if (!fileVars) return;
    
    Object.keys(fileVars).forEach(varName => {
      if (process.env[varName] && process.env[varName] !== fileVars[varName]) {
        conflicts.push({
          variable: varName,
          file: filename,
          fileValue: fileVars[varName],
          systemValue: process.env[varName]
        });
      }
    });
  });
  
  if (conflicts.length === 0) {
    success("No environment variable conflicts detected");
  } else {
    conflicts.forEach(conflict => {
      warning(`${conflict.variable} has different values:`);
      console.log(`  File (${conflict.file}): ${conflict.fileValue}`);
      console.log(`  System environment: ${conflict.systemValue}`);
      console.log(`  The system environment value will be used.`);
    });
    hasWarnings = true;
  }
}

/**
 * Show environment variable locations guide
 */
function showLocationGuide() {
  header("📍 Where Environment Variables Are Used");
  
  console.log(`${colors.bold}Local Development:${colors.reset}`);
  console.log(`  • .env.local file (create from .env.example)`);
  console.log(`  • System environment variables`);
  console.log(``);
  
  console.log(`${colors.bold}Vercel (Frontend Hosting):${colors.reset}`);
  console.log(`  • Staging: Vercel project settings for staging`);
  console.log(`  • Production: Vercel project settings for production`);
  console.log(`  • Access via: Vercel Dashboard > Project > Settings > Environment Variables`);
  console.log(``);
  
  console.log(`${colors.bold}Supabase (Database):${colors.reset}`);
  console.log(`  • Configuration is mostly automatic`);
  console.log(`  • CLI access token: Set locally for deployment scripts`);
  console.log(`  • Access via: https://app.supabase.com/account/tokens`);
  console.log(``);
  
  console.log(`${colors.bold}GitHub (if using Actions):${colors.reset}`);
  console.log(`  • Repository Settings > Secrets and variables > Actions`);
  console.log(`  • Note: We're not using GitHub Actions anymore`);
}

/**
 * Show recommendations
 */
function showRecommendations() {
  header("💡 Recommendations");
  
  console.log(`${colors.bold}For better environment management:${colors.reset}`);
  console.log(``);
  
  console.log(`1. ${colors.cyan}Use .env.local for development${colors.reset}`);
  console.log(`   • Copy .env.example to .env.local`);
  console.log(`   • Customize for your local setup`);
  console.log(`   • Never commit this file to Git`);
  console.log(``);
  
  console.log(`2. ${colors.cyan}Set up Vercel environment variables${colors.reset}`);
  console.log(`   • Go to each Vercel project settings`);
  console.log(`   • Add environment-specific variables`);
  console.log(`   • Test deployments to verify they work`);
  console.log(``);
  
  console.log(`3. ${colors.cyan}Keep a personal backup${colors.reset}`);
  console.log(`   • Document your environment variable values`);
  console.log(`   • Store securely (password manager, etc.)`);
  console.log(`   • This helps if you need to recreate your setup`);
  console.log(``);
  
  console.log(`4. ${colors.cyan}Regular validation${colors.reset}`);
  console.log(`   • Run this check before starting work: npm run env:check`);
  console.log(`   • Run environment status: npm run status`);
  console.log(`   • Check local setup: npm run dev:check`);
}

/**
 * Show summary
 */
function showSummary() {
  header("📊 Summary");
  
  if (!hasErrors && !hasWarnings) {
    console.log(`${colors.green}${colors.bold}🎉 All environment variables look good!${colors.reset}`);
    console.log("");
    success("Your environment is properly configured");
    info("You can start developing with confidence");
  } else if (hasErrors) {
    console.log(`${colors.red}${colors.bold}🚨 Environment issues found that need fixing${colors.reset}`);
    console.log("");
    error("Please fix the errors above before continuing");
    info("Check the .env.example file for reference");
    info("Run this check again after making changes");
  } else if (hasWarnings) {
    console.log(`${colors.yellow}${colors.bold}⚠️  Environment warnings detected${colors.reset}`);
    console.log("");
    warning("Your setup should work, but consider addressing the warnings");
    info("These might cause issues later or in different environments");
  }
  
  console.log("");
  info("Helpful commands:");
  console.log("  npm run env:check     - Run this check again");
  console.log("  npm run dev:check     - Full development environment check");
  console.log("  npm run status        - Overall environment status");
  console.log("");
}

/**
 * Main function
 */
async function main() {
  showWelcome();
  
  // Check environment files
  const envFiles = checkEnvironmentFiles();
  
  // Validate current environment setup
  validateCurrentEnvironment(envFiles);
  
  // Check for conflicts
  checkForConflicts(envFiles);
  
  // Show helpful guides
  showLocationGuide();
  showRecommendations();
  
  // Show summary
  showSummary();
  
  // Exit with appropriate code
  if (hasErrors) {
    process.exit(1);
  } else {
    process.exit(0);
  }
}

// Run the main function
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});