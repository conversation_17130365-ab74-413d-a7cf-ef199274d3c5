#!/bin/bash

# Documentation Link Checker
# Finds broken internal links and missing README files

set -e

DOCS_DIR="docs"
ERRORS=0
WARNINGS=0

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo "🔍 Checking documentation..."
echo ""

# Check for missing README files in directories
echo "📁 Checking for missing README files..."
for dir in $(find $DOCS_DIR -type d -not -path "*/\.*"); do
  if [ ! -f "$dir/README.md" ]; then
    echo -e "${YELLOW}Warning: Missing README.md in $dir${NC}"
    WARNINGS=$((WARNINGS + 1))
  fi
done

# Check for broken internal links
echo ""
echo "🔗 Checking for broken internal links..."

# Find all markdown files
for file in $(find $DOCS_DIR -name "*.md" -type f); do
  # Extract all markdown links
  links=$(grep -Eo '\[([^]]+)\]\(([^)]+)\)' "$file" | grep -Eo '\]\([^)]+\)' | sed 's/](\(.*\))/\1/')
  
  for link in $links; do
    # Skip external links
    if [[ $link == http* ]] || [[ $link == mailto:* ]]; then
      continue
    fi
    
    # Skip anchors
    if [[ $link == \#* ]]; then
      continue
    fi
    
    # Remove anchor from link
    link_path=$(echo $link | cut -d'#' -f1)
    
    # Convert relative path to absolute
    dir_path=$(dirname "$file")
    target_path="$dir_path/$link_path"
    
    # Normalize path
    target_path=$(cd "$dir_path" 2>/dev/null && cd "$(dirname "$link_path")" 2>/dev/null && pwd)/$(basename "$link_path") || echo ""
    
    # Check if file exists
    if [ ! -f "$target_path" ] && [ ! -d "$target_path" ]; then
      echo -e "${RED}Error: Broken link in $file${NC}"
      echo "  Link: $link"
      echo "  Expected: $target_path"
      ERRORS=$((ERRORS + 1))
    fi
  done
done

# Check for outdated content markers
echo ""
echo "📅 Checking for outdated content markers..."
for file in $(find $DOCS_DIR -name "*.md" -type f); do
  if grep -q "TODO\|FIXME\|OUTDATED\|DEPRECATED" "$file"; then
    echo -e "${YELLOW}Warning: Found markers in $file${NC}"
    grep -n "TODO\|FIXME\|OUTDATED\|DEPRECATED" "$file" | while read -r line; do
      echo "  Line $line"
    done
    WARNINGS=$((WARNINGS + 1))
  fi
done

# Check for consistent formatting
echo ""
echo "📝 Checking formatting consistency..."

# Check for files without proper headers
for file in $(find $DOCS_DIR -name "*.md" -type f); do
  first_line=$(head -n 1 "$file" 2>/dev/null)
  if [[ ! $first_line =~ ^#[[:space:]] ]]; then
    echo -e "${YELLOW}Warning: $file doesn't start with a header${NC}"
    WARNINGS=$((WARNINGS + 1))
  fi
done

# Check for very long lines
for file in $(find $DOCS_DIR -name "*.md" -type f); do
  long_lines=$(awk 'length > 120 && !/^[[:space:]]*```/ && !/\|.*\|/' "$file" | wc -l)
  if [ $long_lines -gt 0 ]; then
    echo -e "${YELLOW}Warning: $file has $long_lines lines over 120 characters${NC}"
    WARNINGS=$((WARNINGS + 1))
  fi
done

# Summary
echo ""
echo "📊 Summary:"
echo "  Total files checked: $(find $DOCS_DIR -name "*.md" -type f | wc -l)"
echo -e "  Errors: ${RED}$ERRORS${NC}"
echo -e "  Warnings: ${YELLOW}$WARNINGS${NC}"

if [ $ERRORS -gt 0 ]; then
  echo ""
  echo -e "${RED}❌ Documentation check failed with $ERRORS errors${NC}"
  exit 1
elif [ $WARNINGS -gt 0 ]; then
  echo ""
  echo -e "${YELLOW}⚠️  Documentation check passed with $WARNINGS warnings${NC}"
  exit 0
else
  echo ""
  echo -e "${GREEN}✅ Documentation check passed!${NC}"
  exit 0
fi