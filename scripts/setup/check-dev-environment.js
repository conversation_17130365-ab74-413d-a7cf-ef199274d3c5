#!/usr/bin/env node

/**
 * 🔍 FashionLab Development Environment Checker
 * 
 * This script checks if your local development environment is properly set up.
 * Run this whenever you start working to make sure everything is ready to go!
 * 
 * Usage: npm run dev:check
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';
import { resolve } from 'path';
import { environments, requiredEnvVars } from '../../config/environments.js';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Helper functions for colored output
const success = (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`);
const error = (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`);
const warning = (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`);
const info = (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`);
const header = (msg) => console.log(`\n${colors.bold}${colors.cyan}${msg}${colors.reset}\n`);

let hasErrors = false;
let hasWarnings = false;

function markError() {
  hasErrors = true;
}

function markWarning() {
  hasWarnings = true;
}

/**
 * Show a nice welcome header
 */
function showWelcome() {
  console.log(`
${colors.bold}${colors.cyan}🌟 FashionLab Development Environment Checker${colors.reset}

This tool will verify that your local development setup is ready for coding.
If anything is wrong, I'll tell you exactly how to fix it!

`);
}

/**
 * Check which Git branch you're currently on
 */
function checkCurrentBranch() {
  header("📍 Current Git Branch");
  
  try {
    const branch = execSync('git branch --show-current', { encoding: 'utf8' }).trim();
    
    if (branch === 'main') {
      info(`You're on the '${branch}' branch (staging deployment branch)`);
    } else if (branch === 'production') {
      warning(`You're on the '${branch}' branch (production deployment branch)`);
      warning("Consider switching to a feature branch for development");
      markWarning();
    } else {
      success(`You're on the '${branch}' branch (feature branch) 👍`);
    }
    
    // Check if branch is up to date
    try {
      execSync('git fetch --dry-run', { stdio: 'pipe' });
      success("Your branch is up to date with remote");
    } catch (e) {
      warning("Unable to check if branch is up to date. Check your internet connection.");
      markWarning();
    }
    
  } catch (e) {
    error("Unable to determine current Git branch");
    error("Make sure you're in a Git repository");
    markError();
  }
}

/**
 * Check if required files exist
 */
function checkRequiredFiles() {
  header("📄 Required Files");
  
  const requiredFiles = [
    '.env.local',
    'package.json',
    'supabase/config.toml'
  ];
  
  requiredFiles.forEach(file => {
    if (existsSync(file)) {
      success(`${file} exists`);
    } else {
      error(`${file} is missing`);
      
      if (file === '.env.local') {
        info("Create a .env.local file with your environment variables");
        info("See .env.example for reference");
      }
      markError();
    }
  });
}

/**
 * Check environment variables
 */
function checkEnvironmentVariables() {
  header("🔧 Environment Variables");
  
  // Load environment variables from .env.local if it exists
  if (existsSync('.env.local')) {
    try {
      const envContent = readFileSync('.env.local', 'utf8');
      const envVars = {};
      
      envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
          const [key, ...valueParts] = trimmed.split('=');
          if (key && valueParts.length > 0) {
            envVars[key] = valueParts.join('=');
          }
        }
      });
      
      // Merge with process.env for checking
      Object.assign(process.env, envVars);
      
      success("Loaded variables from .env.local");
    } catch (e) {
      warning("Could not read .env.local file");
      markWarning();
    }
  }
  
  const required = requiredEnvVars.development;
  let allPresent = true;
  
  required.forEach(envVar => {
    if (process.env[envVar]) {
      success(`${envVar} is set`);
    } else {
      error(`${envVar} is missing`);
      allPresent = false;
      markError();
    }
  });
  
  if (allPresent) {
    const currentEnv = process.env.VITE_ENVIRONMENT || 'development';
    info(`Environment is set to: ${currentEnv}`);
    
    if (currentEnv !== 'development') {
      warning("VITE_ENVIRONMENT should be 'development' for local work");
      markWarning();
    }
  }
}

/**
 * Check Supabase status
 */
function checkSupabaseStatus() {
  header("🚀 Supabase Local Instance");
  
  try {
    // Check if Supabase CLI is installed
    execSync('supabase --version', { stdio: 'pipe' });
    success("Supabase CLI is installed");
    
    // Check if Supabase is running
    try {
      const output = execSync('supabase status', { encoding: 'utf8' });
      
      if (output.includes('supabase local development setup is running') || output.includes('API URL:')) {
        success("Supabase is running locally 🎉");
        
        // Show key services status
        const lines = output.split('\n');
        lines.forEach(line => {
          if (line.includes('API URL:')) {
            info(`  ${line.trim()}`);
          } else if (line.includes('Studio URL:')) {
            info(`  ${line.trim()}`);
          } else if (line.includes('DB URL:')) {
            info(`  ${line.trim()}`);
          }
        });
      } else {
        warning("Supabase status unclear. Output:");
        console.log(output);
        markWarning();
      }
      
    } catch (e) {
      error("Supabase is not running locally");
      info("Start it with: npm run supabase:start");
      info("Or: supabase start");
      markError();
    }
    
  } catch (e) {
    error("Supabase CLI is not installed");
    info("Install it with: npm install -g @supabase/cli");
    markError();
  }
}

/**
 * Check Node.js and npm versions
 */
function checkNodeVersion() {
  header("📦 Node.js & npm");
  
  try {
    const nodeVersion = process.version;
    success(`Node.js version: ${nodeVersion}`);
    
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      warning("Node.js version is older than recommended (18+)");
      markWarning();
    }
    
    const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
    success(`npm version: ${npmVersion}`);
    
  } catch (e) {
    error("Unable to check Node.js/npm versions");
    markError();
  }
}

/**
 * Check if project dependencies are installed
 */
function checkDependencies() {
  header("📚 Project Dependencies");
  
  if (existsSync('node_modules')) {
    success("node_modules directory exists");
    
    try {
      execSync('npm list --depth=0', { stdio: 'pipe' });
      success("All dependencies appear to be installed");
    } catch (e) {
      warning("Some dependencies might be missing or outdated");
      info("Run: npm install");
      markWarning();
    }
  } else {
    error("node_modules directory is missing");
    info("Run: npm install");
    markError();
  }
}

/**
 * Show final summary
 */
function showSummary() {
  header("📊 Summary");
  
  if (!hasErrors && !hasWarnings) {
    console.log(`${colors.green}${colors.bold}🎉 Everything looks great! You're ready to start developing.${colors.reset}`);
    console.log("");
    info("To start the development server: npm run dev");
    info("To open Supabase Studio: supabase status (look for Studio URL)");
  } else if (hasErrors) {
    console.log(`${colors.red}${colors.bold}🚨 There are some issues that need to be fixed before you can start developing.${colors.reset}`);
    console.log("");
    error("Please fix the errors above and run this check again.");
  } else if (hasWarnings) {
    console.log(`${colors.yellow}${colors.bold}⚠️  There are some warnings, but you can probably start developing.${colors.reset}`);
    console.log("");
    warning("Consider addressing the warnings above for the best development experience.");
  }
  
  console.log("");
  info("Run this check anytime with: npm run dev:check");
  console.log("");
}

/**
 * Main function - runs all checks
 */
async function main() {
  showWelcome();
  
  // Run all checks
  checkCurrentBranch();
  checkRequiredFiles();
  checkEnvironmentVariables();
  checkNodeVersion();
  checkDependencies();
  checkSupabaseStatus();
  
  // Show final summary
  showSummary();
  
  // Exit with appropriate code
  if (hasErrors) {
    process.exit(1);
  } else {
    process.exit(0);
  }
}

// Run the main function
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});