-- Script to ensure all models have image records for all angles
-- This creates missing model_images records for existing models

DO $$
DECLARE
  model_record RECORD;
  angle TEXT;
  angles TEXT[] := ARRAY[
    'face',
    'half-body-front',
    'half-body-back',
    'half-body-34-left',
    'half-body-34-right',
    'full-body-front',
    'full-body-back',
    'full-body-side-left',
    'full-body-side-right',
    'full-body-34-left',
    'full-body-34-right'
  ];
BEGIN
  -- Loop through all models
  FOR model_record IN SELECT id, code FROM public.model_library
  LOOP
    -- Loop through all angles
    FOREACH angle IN ARRAY angles
    LOOP
      -- Insert image record if it doesn't exist
      INSERT INTO public.model_images (
        model_id,
        angle_type,
        storage_path
      )
      SELECT 
        model_record.id,
        angle,
        NULL -- No storage path yet, will be updated when image is uploaded
      WHERE NOT EXISTS (
        SELECT 1 
        FROM public.model_images 
        WHERE model_id = model_record.id 
        AND angle_type = angle
      );
    END LOOP;
  END LOOP;
  
  -- Report how many records were created
  RAISE NOTICE 'Ensured all models have image records for all angles';
END $$;

-- Verify the results
SELECT 
  ml.code as model_code,
  ml.name as model_name,
  COUNT(mi.id) as image_records
FROM public.model_library ml
LEFT JOIN public.model_images mi ON ml.id = mi.model_id
GROUP BY ml.id, ml.code, ml.name
ORDER BY ml.display_order;