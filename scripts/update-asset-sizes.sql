-- Update all assets with random sizes from the fixed set: XS, S, M, L, XL
-- This script assigns sizes randomly to all assets in the database

DO $$
DECLARE
    sizes text[] := ARRAY['XS', 'S', 'M', 'L', 'XL'];
    asset_record RECORD;
    random_size text;
    current_metadata jsonb;
BEGIN
    -- Loop through all assets
    FOR asset_record IN SELECT id, metadata FROM assets
    LOOP
        -- Select a random size
        random_size := sizes[1 + floor(random() * array_length(sizes, 1))];
        
        -- Get current metadata or create empty object if null
        current_metadata := COALESCE(asset_record.metadata, '{}'::jsonb);
        
        -- Add or update the size field
        current_metadata := jsonb_set(current_metadata, '{size}', to_jsonb(random_size), true);
        
        -- Update the asset
        UPDATE assets 
        SET metadata = current_metadata,
            updated_at = NOW()
        WHERE id = asset_record.id;
    END LOOP;
    
    RAISE NOTICE 'Updated all assets with random sizes';
END $$;

-- Verify the distribution
SELECT 
    metadata->>'size' as size,
    COUNT(*) as count
FROM assets
WHERE metadata->>'size' IS NOT NULL
GROUP BY metadata->>'size'
ORDER BY 
    CASE metadata->>'size'
        WHEN 'XS' THEN 1
        WHEN 'S' THEN 2
        WHEN 'M' THEN 3
        WHEN 'L' THEN 4
        WHEN 'XL' THEN 5
    END;