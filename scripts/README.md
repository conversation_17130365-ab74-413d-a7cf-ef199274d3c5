# 🛠️ FashionLab Scripts Directory

This directory contains helpful scripts that make working with FashionLab easier and safer. All scripts are designed to be **human-friendly** with clear explanations of what's happening.

## 📁 Directory Structure

```
scripts/
├── setup/           # Scripts for setting up your development environment
├── deployment/      # Scripts for deploying to staging and production  
├── validation/      # Scripts for checking status and validating setup
└── README.md       # This file
```

> **Note:** Claude commands have moved to `/claude/commands/`. Use `/project:daily:start` instead of npm scripts.

## 🚀 Quick Start

**Most common scripts you'll use:**

```bash
# Check if development environment is ready
npm run dev:check

# Check environment variables setup
npm run env:check

# See status of all environments
npm run status

# Deploy database changes to staging
npm run migrate:staging

# Deploy database changes to production  
npm run migrate:production
```

## 📋 Script Categories

### 🔧 Setup Scripts (`setup/`)

These help you get your development environment ready:

| Script | NPM Command | Purpose |
|--------|-------------|---------|
| `check-dev-environment.js` | `npm run dev:check` | Comprehensive check of your local development setup |

**What it checks:**
- Git branch status
- Required files (`.env.local`, etc.)
- Environment variables
- Node.js/npm versions
- Project dependencies
- Supabase local instance

### 🚀 Deployment Scripts (`deployment/`)

These help you safely deploy changes:

| Script | NPM Command | Purpose |
|--------|-------------|---------|
| `deploy-migrations.js` | `npm run migrate:staging`<br>`npm run migrate:production` | Human-friendly database migration deployment |

**What it does:**
- Guides you through each step
- Validates environment before deployment
- Shows pending migrations
- Requires explicit confirmation (especially for production)
- Provides clear error messages and next steps

### ✅ Validation Scripts (`validation/`)

These help you understand what's happening:

| Script | NPM Command | Purpose |
|--------|-------------|---------|
| `environment-status.js` | `npm run status` | Shows status of all environments (dev, staging, production) |
| `check-env-vars.js` | `npm run env:check` | Validates environment variables across all systems |

## 🎯 Design Principles

All scripts in this directory follow these principles:

### 1. **Human-Friendly Communication**
- Use simple, clear language
- Avoid technical jargon
- Explain what's happening at each step
- Use emojis and colors for visual clarity

### 2. **Safety First**
- Always validate before making changes
- Require explicit confirmation for risky operations
- Provide clear warnings for production deployments
- Show exactly what will happen before doing it

### 3. **Helpful Error Messages**
- Explain what went wrong in plain English
- Provide specific steps to fix the problem
- Show where to get help or more information
- Never leave users wondering what to do next

### 4. **Progressive Disclosure**
- Show summary first, details on request
- Don't overwhelm with too much information
- Focus on what the user needs to know right now
- Provide "next steps" guidance

## 🔧 Script Implementation Details

### Environment Configuration

All scripts use the central configuration in `config/environments.js`:

```javascript
import { environments } from '../../config/environments.js';

// Get environment configuration
const env = environments.staging;
console.log(env.name); // "🧪 Staging Environment"
console.log(env.supabase.projectId); // "qnfmiotatmkoumlymynq"
```

### Error Handling

All scripts handle errors gracefully:

```javascript
try {
  // Attempt operation
} catch (error) {
  error("❌ Something went wrong");
  info("💡 Here's how to fix it...");
  process.exit(1);
}
```

### User Interaction

Scripts that require user input use readline:

```javascript
const answer = await askQuestion("Continue? (y/N): ");
if (answer.toLowerCase() !== 'y') {
  info("Operation cancelled");
  process.exit(0);
}
```

## 🎨 Output Formatting

All scripts use consistent formatting:

- ✅ **Success messages** (green)
- ❌ **Error messages** (red)  
- ⚠️ **Warning messages** (yellow)
- ℹ️ **Information messages** (blue)
- 👉 **Action steps** (cyan)
- 🔍 **Headers/sections** (bold cyan)

## 🚀 Adding New Scripts

When adding new scripts to this directory:

### 1. Follow the File Structure
```
scripts/
├── category/
│   ├── your-script.js
│   └── README.md (if category needs explanation)
```

### 2. Use the Template Structure
```javascript
#!/usr/bin/env node

/**
 * 🎯 Script Title
 * 
 * Description of what this script does in simple terms.
 * Explain when and why someone would use it.
 * 
 * Usage: npm run your-command
 */

import { /* dependencies */ } from '...';

// Colors and helper functions
const colors = { /* ... */ };
const success = (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`);
// ... other helpers

/**
 * Show welcome message
 */
function showWelcome() {
  console.log(`
${colors.bold}${colors.cyan}🎯 Your Script Name${colors.reset}

Brief explanation of what this script will do.
`);
}

/**
 * Main function
 */
async function main() {
  showWelcome();
  
  // Your script logic here
  
  // Always end with a clear summary
  showSummary();
}

// Run the script
main().catch(error => {
  console.error(`${colors.red}❌ Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
```

### 3. Add NPM Script
Add to `package.json`:
```json
{
  "scripts": {
    "your-command": "node scripts/category/your-script.js"
  }
}
```

### 4. Update Documentation
- Add to this README
- Update `docs/QUICK-REFERENCE.md` if it's a commonly used script
- Update `docs/GETTING-STARTED-GUIDE.md` if it's part of the main workflow

## 🆘 Troubleshooting Scripts

### "Permission denied" errors
```bash
chmod +x scripts/category/your-script.js
```

### "Module not found" errors
Make sure you're using the correct relative imports:
```javascript
import { environments } from '../../config/environments.js';
```

### Scripts not showing colors
Make sure your terminal supports ANSI colors. Most modern terminals do.

## 📚 Related Documentation

- **[Getting Started Guide](../docs/GETTING-STARTED-GUIDE.md)** - Complete setup and usage guide
- **[Quick Reference](../docs/QUICK-REFERENCE.md)** - Common commands cheat sheet
- **[Environment Management](../config/environments.js)** - Central configuration
- **[Package.json Scripts](../package.json)** - All available npm commands

---

**💡 Remember:** These scripts are designed to help you work more confidently and safely. If you're ever unsure what a script does, just run it - they'll explain everything before making any changes!