#!/usr/bin/env python3

"""
JWT Token Generator for Fashion Lab API Testing

This script generates a JWT token that can be used to test the Fashion Lab API
in Postman or other tools.
"""

import json
import base64
import hmac
import hashlib
import time
import os
import sys

def base64url_encode(data):
    """Encode data to base64url format"""
    if isinstance(data, str):
        data = data.encode('utf-8')
    encoded = base64.urlsafe_b64encode(data).decode('utf-8')
    return encoded.rstrip('=')

def create_jwt(payload, secret, expires_in=3600):
    """Create a JWT token"""
    # Header
    header = {
        "alg": "HS256",
        "typ": "JWT"
    }
    
    # Add timestamps to payload
    now = int(time.time())
    full_payload = {
        **payload,
        "iat": now,
        "exp": now + expires_in
    }
    
    # Encode header and payload
    encoded_header = base64url_encode(json.dumps(header))
    encoded_payload = base64url_encode(json.dumps(full_payload))
    
    # Create signature
    message = f"{encoded_header}.{encoded_payload}"
    signature = hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).digest()
    encoded_signature = base64url_encode(signature)
    
    # Combine all parts
    return f"{encoded_header}.{encoded_payload}.{encoded_signature}"

def main():
    # Configuration
    jwt_secret = os.environ.get('FASHIONLAB_JWT_SECRET', 'your-secret-key-here')
    expiration_hours = 1
    
    # Generate token
    token = create_jwt({}, jwt_secret, expiration_hours * 3600)
    
    print("🔐 JWT Token Generated Successfully!\n")
    print(f"Token: {token}")
    print("\nToken Details:")
    print("- Algorithm: HS256")
    print(f"- Expiration: {expiration_hours} hour(s)")
    print(f"- Secret: {'DEFAULT (change this!)' if jwt_secret == 'your-secret-key-here' else 'Custom secret set'}")
    
    print("\n📋 How to use this token:\n")
    print("1. Copy the token above")
    print("2. In Postman, add to Headers:")
    print("   Key: Authorization")
    print(f"   Value: Bearer {token}")
    
    print("\n🧪 Test with curl:")
    print(f"""curl -X POST https://fashionlab.notfirst.rodeo/generate-image-v2 \\
  -H "Authorization: Bearer {token}" \\
  -H "Content-Type: multipart/form-data" \\
  -F "model=@model.jpg" \\
  -F "garment=@garment.jpg" \\
  -F "pose=@pose.jpg" \\
  -F "background=@background.jpg" \\
  -F "prompt=your prompt here" """)
    
    print("\n⚠️  Note: If you need a specific JWT secret, set the FASHIONLAB_JWT_SECRET environment variable")
    print("Example: FASHIONLAB_JWT_SECRET=mysecret python3 generate-jwt-token.py\n")

if __name__ == "__main__":
    main()