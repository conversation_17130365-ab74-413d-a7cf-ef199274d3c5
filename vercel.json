{"rewrites": [{"source": "/(.*)", "destination": "/"}], "headers": [{"source": "/(.*)\\.svg", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}, {"key": "Content-Type", "value": "image/svg+xml"}]}, {"source": "/fonts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "buildCommand": "npm run build", "outputDirectory": "dist"}