{"compilerOptions": {"target": "es2020", "module": "esnext", "lib": ["dom", "es2020", "esnext.asynciterable"], "jsx": "react-jsx", "skipLibCheck": true, "sourceMap": true, "outDir": "./dist", "moduleResolution": "node", "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "baseUrl": "."}, "exclude": ["node_modules"], "include": ["./src/**/*.ts", "./src/**/*.tsx"]}