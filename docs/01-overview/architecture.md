# Architecture Overview

## Overview

FashionLab follows a modern, scalable architecture designed for multi-tenant SaaS operations with AI integration.

## Prerequisites

- Understanding of React and TypeScript
- Familiarity with Supabase platform
- Knowledge of REST APIs

## System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        B[Browser/PWA]
        M[Mobile Web]
    end
    
    subgraph "CDN/Edge"
        V[Vercel Edge]
        CF[Cloudflare]
    end
    
    subgraph "Application Layer"
        R[React SPA]
        N[Next.js API Routes]
    end
    
    subgraph "Backend Services"
        S[Supabase Platform]
        E[Edge Functions]
        A[Auth Service]
        D[(PostgreSQL)]
        ST[Storage]
    end
    
    subgraph "External Services"
        FL[Fashion Lab API]
        EM[Email Service]
        AI[AI Models]
    end
    
    B --> V
    M --> V
    V --> R
    R --> S
    S --> E
    S --> A
    S --> D
    S --> ST
    E --> FL
    E --> EM
    FL --> AI
```

## Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 5
- **Styling**: Tailwind CSS + shadcn/ui
- **State Management**: React Context + Zustand
- **Routing**: React Router v6
- **Forms**: React Hook Form + Zod

### Backend
- **Platform**: Supabase
- **Database**: PostgreSQL 15
- **Auth**: Supabase Auth (JWT)
- **Storage**: Supabase Storage (S3 compatible)
- **Functions**: Deno-based Edge Functions
- **Realtime**: WebSocket subscriptions

### Infrastructure
- **Hosting**: Vercel (Frontend)
- **Database**: Supabase Cloud
- **CDN**: Vercel Edge Network
- **Monitoring**: Vercel Analytics

## Core Components

### 1. Authentication Layer

```typescript
// Centralized auth management
SupabaseContext
  ├── Session management
  ├── Token refresh
  ├── Role verification
  └── Organization context
```

### 2. Data Layer

```typescript
// Row-Level Security (RLS)
Database
  ├── Multi-tenant isolation
  ├── Role-based access
  ├── Audit trails
  └── Soft deletes
```

### 3. API Layer

```typescript
// Edge Functions
/functions/v1/
  ├── generate-images      // AI generation
  ├── fashion-lab-proxy    // External API
  ├── send-email          // Notifications
  └── queue-status        // Job polling
```

### 4. Storage Layer

```
/storage/
  ├── assets/            // Original images
  ├── thumbnails/        // 200x200 previews
  ├── compressed/        // WebP versions
  ├── ai-generated/      // AI outputs
  └── profiles/          // User avatars
```

## Data Flow

### Image Generation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant E as Edge Function
    participant AI as Fashion Lab
    participant S as Storage
    participant DB as Database
    
    U->>F: Upload images & prompt
    F->>E: Call generate-images
    E->>AI: Submit generation job
    AI-->>E: Return queue_id
    E-->>F: Return queue_id
    
    loop Poll for completion
        F->>E: Check queue status
        E->>AI: Get job status
        AI-->>E: Status + URLs
    end
    
    E->>S: Download & store images
    E->>DB: Create asset records
    E-->>F: Return asset data
    F->>U: Display images
```

### Multi-Tenant Data Access

```mermaid
graph LR
    U[User] --> A[Auth]
    A --> O[Organization]
    O --> R[Role Check]
    R --> D[Data Access]
    D --> RLS[RLS Policies]
    RLS --> DB[(Database)]
```

## Security Architecture

### Defense in Depth

1. **Network Layer**
   - HTTPS everywhere
   - CORS policies
   - Rate limiting

2. **Application Layer**
   - Input validation
   - Output encoding
   - CSRF protection

3. **Data Layer**
   - Row-level security
   - Encrypted at rest
   - Audit logging

4. **Access Control**
   ```
   Platform Admin
     └── Organization Admin
           └── Organization Member
                 └── External Contractor
   ```

## Scalability Considerations

### Horizontal Scaling
- Stateless frontend (Vercel)
- Database connection pooling
- Edge function auto-scaling
- CDN asset delivery

### Performance Optimization
- Lazy loading components
- Image optimization pipeline
- Database query optimization
- Caching strategies

### Monitoring Points
- API response times
- Database query performance
- Storage usage trends
- Error rates by service

## Deployment Architecture

### Environments

```
Production (main branch)
  ├── app.fashionlab.ai
  ├── cpelxqvcjnbpnphttzsn.supabase.co
  └── Edge functions (prod)

Staging (staging branch)
  ├── staging.fashionlab.ai
  ├── qnfmiotatmkoumlymynq.supabase.co
  └── Edge functions (staging)

Development (local)
  ├── localhost:5173
  ├── localhost:54321
  └── Edge functions (local)
```

### CI/CD Pipeline

```mermaid
graph LR
    C[Commit] --> G[GitHub]
    G --> V[Vercel Build]
    V --> T[Tests]
    T --> S[Staging Deploy]
    S --> A[Approval]
    A --> P[Production Deploy]
```

## Key Design Decisions

### 1. Supabase Platform
- **Why**: Integrated auth, database, storage
- **Trade-off**: Vendor lock-in vs development speed
- **Benefit**: Reduced complexity, built-in security

### 2. Edge Functions
- **Why**: Close to data, auto-scaling
- **Trade-off**: Deno runtime vs Node.js
- **Benefit**: Better performance, lower latency

### 3. Multi-Tenant via RLS
- **Why**: PostgreSQL native security
- **Trade-off**: Complexity vs isolation
- **Benefit**: Strong security, efficient queries

### 4. React SPA
- **Why**: Rich interactions, familiar stack
- **Trade-off**: Initial load vs interactivity
- **Benefit**: Better UX, easier development

## Future Architecture

### Planned Enhancements
1. **Microservices**: Extract heavy processing
2. **Event-Driven**: Add message queues
3. **GraphQL**: Unified API layer
4. **Edge Computing**: Process images at edge

### Scaling Strategy
1. **Database**: Read replicas, partitioning
2. **Storage**: Multi-region replication
3. **Compute**: Serverless functions
4. **Caching**: Redis layer

## Related Documentation

- [Tech Stack Details](./tech-stack.md)
- [Database Schema](../05-database/schema.md)
- [API Design](../04-api/README.md)
- [Security Model](../07-security/README.md)