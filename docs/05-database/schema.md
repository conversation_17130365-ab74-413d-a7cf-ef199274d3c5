# Database Schema

## Overview

FashionLab uses PostgreSQL with Supabase, implementing a multi-tenant architecture with row-level security (RLS) for data isolation.

## Core Tables

### organizations
Represents brands/companies using the platform.

```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  settings JSONB DEFAULT '{}',
  storage_quota_gb INTEGER DEFAULT 5,
  monthly_generation_quota INTEGER DEFAULT 100,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### users
Extended user information beyond Supabase auth.

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  full_name TEXT,
  avatar_url TEXT,
  role user_role NOT NULL DEFAULT 'free_user',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### organization_users
Links users to organizations with roles.

```sql
CREATE TABLE organization_users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role organization_role NOT NULL DEFAULT 'member',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(organization_id, user_id)
);
```

### collections
Campaigns containing products and assets.

```sql
CREATE TABLE collections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  brief TEXT,
  visibility visibility_level DEFAULT 'organization',
  tags TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### products
Products within collections.

```sql
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  collection_id UUID NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  sku TEXT,
  description TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### assets
Images and generated content.

```sql
CREATE TABLE assets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  collection_id UUID REFERENCES collections(id) ON DELETE SET NULL,
  product_id UUID REFERENCES products(id) ON DELETE SET NULL,
  storage_path TEXT NOT NULL,
  thumbnail_path TEXT,
  compressed_path TEXT,
  workflow_stage workflow_stage NOT NULL DEFAULT 'raw',
  tags TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### model_library
AI models for generation.

```sql
CREATE TABLE model_library (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  model_id TEXT NOT NULL,
  name TEXT NOT NULL,
  short_name TEXT,
  description TEXT,
  prompt_text TEXT,
  lora TEXT,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Enums

### User Roles
```sql
CREATE TYPE user_role AS ENUM (
  'platform_admin',
  'organization_admin',
  'organization_member',
  'contractor',
  'free_user'
);
```

### Organization Roles
```sql
CREATE TYPE organization_role AS ENUM (
  'admin',
  'member',
  'external'
);
```

### Workflow Stages
```sql
CREATE TYPE workflow_stage AS ENUM (
  'raw',
  'upscale',
  'retouch',
  'final'
);
```

### Visibility Levels
```sql
CREATE TYPE visibility_level AS ENUM (
  'private',
  'organization',
  'public'
);
```

## Row-Level Security

### Organization Isolation
```sql
-- Users can only see assets from their organizations
CREATE POLICY "Users can view own organization assets" ON assets
  FOR SELECT USING (
    organization_id IN (
      SELECT organization_id FROM organization_users
      WHERE user_id = auth.uid()
    )
  );
```

### Role-Based Access
```sql
-- Only admins can update organization settings
CREATE POLICY "Admins can update organization" ON organizations
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM organization_users
      WHERE organization_id = organizations.id
        AND user_id = auth.uid()
        AND role = 'admin'
    )
  );
```

## Indexes

### Performance Optimization
```sql
-- Frequently queried combinations
CREATE INDEX idx_assets_collection_product ON assets(collection_id, product_id);
CREATE INDEX idx_assets_organization_stage ON assets(organization_id, workflow_stage);
CREATE INDEX idx_assets_tags ON assets USING GIN (tags);

-- JSON queries
CREATE INDEX idx_assets_metadata ON assets USING GIN (metadata);
CREATE INDEX idx_organizations_settings ON organizations USING GIN (settings);
```

## Functions

### Updated Timestamp Trigger
```sql
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables
CREATE TRIGGER update_assets_updated_at
  BEFORE UPDATE ON assets
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();
```

### Organization Storage Usage
```sql
CREATE OR REPLACE FUNCTION get_organization_storage_usage(org_id UUID)
RETURNS BIGINT AS $$
  SELECT COALESCE(SUM(size), 0)
  FROM storage.objects
  WHERE bucket_id = 'assets'
    AND name LIKE org_id::TEXT || '/%'
$$ LANGUAGE sql SECURITY DEFINER;
```

## Views

### Asset Summary View
```sql
CREATE VIEW v_asset_summary AS
SELECT 
  a.id,
  a.storage_path,
  a.workflow_stage,
  c.name as collection_name,
  p.name as product_name,
  o.name as organization_name
FROM assets a
  LEFT JOIN collections c ON a.collection_id = c.id
  LEFT JOIN products p ON a.product_id = p.id
  LEFT JOIN organizations o ON a.organization_id = o.id;
```

## Migration Strategy

### Version Control
All migrations are tracked in:
- `supabase/migrations/` directory
- Sequential timestamp prefixes
- Descriptive names

### Rollback Plan
Each migration includes:
- Forward migration (up)
- Rollback script (down)
- Data preservation strategy

## Best Practices

### Naming Conventions
- Tables: plural, snake_case
- Columns: snake_case
- Indexes: idx_table_columns
- Constraints: table_column_fkey

### Data Types
- UUIDs for primary keys
- TIMESTAMPTZ for timestamps
- JSONB for flexible data
- Arrays for tags/lists

### Performance
- Index foreign keys
- Index frequently filtered columns
- Use partial indexes where appropriate
- Regular VACUUM and ANALYZE

## Related Documentation

- [API Reference](../04-api/README.md)
- [RLS Policies](./rls-policies.md)
- [Migration Guide](./migrations.md)
- [Performance Tuning](./performance.md)