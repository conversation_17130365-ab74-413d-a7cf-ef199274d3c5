# Database Documentation

## Overview

FashionLab uses PostgreSQL via Supabase with row-level security (RLS) for multi-tenant data isolation. This documentation covers schema design, migrations, and best practices.

## Prerequisites

- Understanding of PostgreSQL
- Familiarity with RLS concepts
- Knowledge of SQL

## Database Architecture

```mermaid
erDiagram
    organizations ||--o{ organization_members : has
    organizations ||--o{ collections : owns
    organizations ||--o{ products : owns
    organizations ||--o{ assets : owns
    users ||--o{ organization_members : belongs_to
    collections ||--o{ assets : contains
    products ||--o{ assets : displays
    assets ||--o{ asset_tags : has
    tags ||--o{ asset_tags : used_by
    assets ||--o{ comments : receives
    users ||--o{ comments : writes
```

## Core Tables

### Organizations
Central tenant table for multi-tenant isolation:
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  email TEXT,
  logo_url TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Users
Extended from Supabase auth.users:
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  role user_role NOT NULL DEFAULT 'org_member',
  last_seen_at TIMESTAMPTZ,
  email_confirmed_at TIMESTAMPTZ
);
```

### Collections
Groups of related fashion products:
```sql
CREATE TABLE collections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  name TEXT NOT NULL,
  description TEXT,
  brief_url TEXT,
  cover_image_url TEXT,
  status collection_status DEFAULT 'draft',
  settings JSONB DEFAULT '{}',
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Assets
Core image storage and workflow tracking:
```sql
CREATE TABLE assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id),
  collection_id UUID REFERENCES collections(id),
  product_id UUID REFERENCES products(id),
  storage_path TEXT NOT NULL,
  thumbnail_path TEXT,
  compressed_path TEXT,
  workflow_stage workflow_stage NOT NULL,
  workflow_substage TEXT,
  metadata JSONB DEFAULT '{}',
  size_bytes BIGINT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Enums and Types

### User Roles
```sql
CREATE TYPE user_role AS ENUM (
  'platform_admin',
  'org_admin', 
  'org_member',
  'freelancer'
);
```

### Workflow Stages
```sql
CREATE TYPE workflow_stage AS ENUM (
  'raw',          -- Raw AI generated
  'upscale',      -- Enhanced resolution
  'retouch',      -- Manual edits
  'refined',      -- Color/style refined
  'final'         -- Production ready
);
```

### Collection Status
```sql
CREATE TYPE collection_status AS ENUM (
  'draft',
  'active',
  'archived'
);
```

## Row-Level Security

### Organization Isolation
All tables implement organization-based isolation:

```sql
-- Users can only see their organization's data
CREATE POLICY "Organization members access own data" ON assets
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );
```

### Role-Based Access
Different permissions by role:

```sql
-- Only admins can delete
CREATE POLICY "Only admins can delete" ON collections
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM organization_members
      WHERE user_id = auth.uid()
      AND organization_id = collections.organization_id
      AND role IN ('org_admin', 'platform_admin')
    )
  );
```

## Indexes

### Performance Indexes
```sql
-- Frequently queried columns
CREATE INDEX idx_assets_organization ON assets(organization_id);
CREATE INDEX idx_assets_collection ON assets(collection_id);
CREATE INDEX idx_assets_workflow ON assets(workflow_stage);
CREATE INDEX idx_assets_created ON assets(created_at DESC);

-- Composite indexes for common queries
CREATE INDEX idx_assets_org_collection 
  ON assets(organization_id, collection_id);
```

### Unique Constraints
```sql
-- Prevent duplicate slugs
CREATE UNIQUE INDEX idx_organizations_slug ON organizations(slug);

-- One membership per user per org
CREATE UNIQUE INDEX idx_unique_membership 
  ON organization_members(user_id, organization_id);
```

## Migration Best Practices

### Creating Migrations
```bash
# Generate migration file
supabase migration new descriptive_name

# Apply locally
supabase db push

# Apply to staging/production
supabase db push --project-ref PROJECT_ID
```

### Migration Template
```sql
-- Migration: YYYYMMDDHHMMSS_descriptive_name.sql
-- Description: What this migration does

BEGIN;

-- Add your changes here
ALTER TABLE assets ADD COLUMN new_field TEXT;

-- Add RLS policies
CREATE POLICY "New policy" ON assets
  FOR SELECT USING (auth.uid() = created_by);

-- Add indexes
CREATE INDEX idx_assets_new_field ON assets(new_field);

COMMIT;
```

### Safe Migration Patterns

1. **Add columns with defaults**
   ```sql
   ALTER TABLE assets 
   ADD COLUMN view_count INTEGER DEFAULT 0;
   ```

2. **Rename safely**
   ```sql
   -- Step 1: Add new column
   ALTER TABLE assets ADD COLUMN asset_type TEXT;
   
   -- Step 2: Copy data
   UPDATE assets SET asset_type = type;
   
   -- Step 3: Drop old column (later migration)
   ALTER TABLE assets DROP COLUMN type;
   ```

3. **Add constraints carefully**
   ```sql
   -- Check constraint is valid first
   ALTER TABLE assets 
   ADD CONSTRAINT check_positive_size 
   CHECK (size_bytes >= 0) NOT VALID;
   
   -- Validate in separate transaction
   ALTER TABLE assets 
   VALIDATE CONSTRAINT check_positive_size;
   ```

## Query Patterns

### Common Queries

```sql
-- Get organization's assets with tags
SELECT 
  a.*,
  array_agg(t.name) as tags
FROM assets a
LEFT JOIN asset_tags at ON a.id = at.asset_id
LEFT JOIN tags t ON at.tag_id = t.id
WHERE a.organization_id = $1
GROUP BY a.id
ORDER BY a.created_at DESC;

-- Collection summary
SELECT 
  c.*,
  COUNT(DISTINCT a.id) as asset_count,
  COUNT(DISTINCT a.product_id) as product_count
FROM collections c
LEFT JOIN assets a ON c.id = a.collection_id
WHERE c.organization_id = $1
GROUP BY c.id;
```

### Performance Tips

1. **Use indexes**
   ```sql
   -- Good: Uses index
   SELECT * FROM assets 
   WHERE organization_id = $1 
   AND workflow_stage = 'final';
   
   -- Bad: No index on metadata
   SELECT * FROM assets 
   WHERE metadata->>'color' = 'red';
   ```

2. **Avoid N+1 queries**
   ```sql
   -- Good: Single query
   SELECT c.*, array_agg(a.*) as assets
   FROM collections c
   LEFT JOIN assets a ON c.id = a.collection_id
   GROUP BY c.id;
   
   -- Bad: N+1 pattern
   -- Don't fetch collections then loop for assets
   ```

## Backup and Recovery

### Automated Backups
- Supabase provides daily backups
- Point-in-time recovery available
- Test restore procedures regularly

### Manual Backup
```bash
# Export schema and data
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Export schema only
pg_dump --schema-only $DATABASE_URL > schema.sql

# Export specific tables
pg_dump -t assets -t collections $DATABASE_URL > partial.sql
```

## Monitoring

### Key Metrics
- Query performance (pg_stat_statements)
- Table sizes and growth
- Index usage statistics
- Connection pool utilization

### Health Checks
```sql
-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check slow queries
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

## Related Documentation

- [Schema Reference](./schema.md)
- [Migration Guide](./migrations.md)
- [RLS Policies](./rls-policies.md)
- [Query Optimization](./optimization.md)