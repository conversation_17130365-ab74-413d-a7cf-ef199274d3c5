# Deployment Documentation

## Overview

FashionLab uses a continuous deployment pipeline with automated builds and environment-based deployments. This guide covers deployment processes, environments, and best practices.

## Prerequisites

- GitHub repository access
- Vercel team access
- Supabase project access
- Understanding of Git workflows

## Deployment Architecture

```mermaid
graph LR
    subgraph "Development"
        L[Local Dev]
        F[Feature Branch]
    end
    
    subgraph "Staging"
        S[Staging Branch]
        SV[Staging Vercel]
        SS[Staging Supabase]
    end
    
    subgraph "Production"
        P[Production Branch]
        PV[Production Vercel]
        PS[Production Supabase]
    end
    
    L --> F
    F --> S
    S --> SV
    S --> SS
    S --> P
    P --> PV
    P --> PS
```

## Environments

### Development (Local)
- **URL**: http://localhost:5173
- **Database**: Local Supabase or staging
- **Purpose**: Active development
- **Branch**: Feature branches

### Staging
- **URL**: https://staging.fashionlab.ai
- **Database**: qnfmiotatmkoumlymynq
- **Purpose**: Testing and QA
- **Branch**: `main`
- **Auto-deploy**: Yes

### Production
- **URL**: https://app.fashionlab.ai
- **Database**: cpelxqvcjnbpnphttzsn
- **Purpose**: Live application
- **Branch**: `production`
- **Auto-deploy**: After PR merge

## Deployment Workflows

### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes
# ... code changes ...

# Commit and push
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Create PR
gh pr create --base main
```

### 2. Deploy to Staging

```bash
# Merge to main (auto-deploys)
gh pr merge --merge

# Monitor deployment
# Check Vercel dashboard for build status
# URL: https://vercel.com/your-team/fashionlab
```

### 3. Deploy to Production

```bash
# Create production PR
gh pr create \
  --base production \
  --head main \
  --title "Deploy: v1.2.0 to production" \
  --body "## Changes
  - Feature X
  - Bug fix Y
  
  ## Testing
  - [ ] Tested on staging
  - [ ] No critical issues"

# After approval, merge
gh pr merge --merge
```

## Pre-Deployment Checklist

### Code Quality
- [ ] All tests passing
- [ ] No TypeScript errors
- [ ] ESLint warnings resolved
- [ ] Build completes successfully

```bash
# Run all checks
npm run lint
npm run typecheck
npm run test
npm run build
```

### Security
- [ ] No exposed secrets
- [ ] Dependencies updated
- [ ] Security scan passed

```bash
# Check for secrets
git secrets --scan

# Audit dependencies
npm audit

# Update if needed
npm audit fix
```

### Database
- [ ] Migrations tested locally
- [ ] Rollback plan prepared
- [ ] Data backup completed

## Environment Configuration

### Vercel Environment Variables

Required variables for each environment:

```env
# Supabase
VITE_SUPABASE_URL=https://PROJECT.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Fashion Lab API
VITE_FASHION_LAB_API_KEY=your-api-key
VITE_FASHION_LAB_API_URL=https://fashionlab.notfirst.rodeo

# Environment
VITE_APP_ENV=staging|production

# Optional
VITE_SENTRY_DSN=your-sentry-dsn
VITE_ANALYTICS_ID=your-analytics-id
```

### Setting Variables

1. **Via Vercel Dashboard**
   - Go to Project Settings
   - Navigate to Environment Variables
   - Add variables for each environment

2. **Via CLI**
   ```bash
   vercel env add VITE_SUPABASE_URL production
   ```

## Database Migrations

### Apply Migrations

```bash
# Staging
supabase db push --project-ref qnfmiotatmkoumlymynq

# Production (requires extra caution)
supabase db push --project-ref cpelxqvcjnbpnphttzsn
```

### Migration Strategy

1. **Test locally first**
   ```bash
   supabase db reset
   supabase db push
   ```

2. **Apply to staging**
   - Test thoroughly
   - Monitor for issues

3. **Schedule production migration**
   - Low-traffic period
   - Have rollback ready

## Build Configuration

### Vercel Configuration

```json
// vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### Build Optimization

```javascript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'react-vendor': ['react', 'react-dom'],
          'supabase': ['@supabase/supabase-js'],
          'ui': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  }
});
```

## Monitoring Deployments

### Build Logs

```bash
# Via CLI
vercel logs

# Filter by deployment
vercel logs --scope your-team --limit 100
```

### Runtime Logs

```bash
# Check function logs
vercel logs --source lambda

# Check static logs  
vercel logs --source static
```

### Health Checks

1. **Automated Checks**
   ```javascript
   // api/health.js
   export default function handler(req, res) {
     res.status(200).json({
       status: 'healthy',
       timestamp: new Date().toISOString(),
       version: process.env.VERCEL_GIT_COMMIT_SHA
     });
   }
   ```

2. **Manual Verification**
   - Load application
   - Test authentication
   - Check core features
   - Monitor error logs

## Rollback Procedures

### Quick Rollback (Vercel)

1. Go to Vercel Dashboard
2. Navigate to Deployments
3. Find previous stable deployment
4. Click "..." → "Promote to Production"

### Git-based Rollback

```bash
# Revert last commit
git revert HEAD
git push origin production

# Or reset to specific commit
git reset --hard COMMIT_SHA
git push origin production --force
```

### Database Rollback

```bash
# If migration caused issues
# 1. Identify problem migration
supabase migration list --project-ref PROJECT_ID

# 2. Create rollback migration
supabase migration new rollback_MIGRATION_NAME

# 3. Apply rollback
supabase db push --project-ref PROJECT_ID
```

## Post-Deployment

### Verification Steps

1. **Smoke Tests**
   - [ ] Application loads
   - [ ] Login works
   - [ ] Core features functional
   - [ ] No console errors

2. **Performance Check**
   - [ ] Page load times normal
   - [ ] API response times OK
   - [ ] No memory leaks

3. **Monitoring**
   - [ ] Error rates normal
   - [ ] No spike in 4xx/5xx
   - [ ] Database queries performant

### Communication

```markdown
# Deployment Notification Template

🚀 **Deployment Complete**

**Version**: v1.2.0
**Environment**: Production
**Time**: 2024-01-15 14:30 UTC

**Changes**:
- ✨ New feature: Bulk export
- 🐛 Fixed: Upload progress indicator
- 🔧 Improved: Query performance

**Status**: ✅ All systems operational

**Next Steps**:
- Monitor for 30 minutes
- Check user feedback
- Update documentation
```

## Troubleshooting

### Build Failures

```bash
# Clear cache and rebuild
vercel --force

# Check build command locally
npm run build

# Review error logs
vercel logs --since 1h
```

### Environment Issues

```bash
# Verify environment variables
vercel env ls

# Check specific variable
vercel env pull
```

### Performance Issues

1. Check bundle size
2. Review database queries
3. Monitor API response times
4. Check CDN cache hit rates

## Related Documentation

- [CI/CD Pipeline](./ci-cd.md)
- [Environment Setup](./environments.md)
- [Monitoring](../08-operations/monitoring.md)
- [Rollback Procedures](./rollback.md)