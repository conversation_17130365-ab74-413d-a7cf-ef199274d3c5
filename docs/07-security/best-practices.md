# Security Best Practices

## Overview

This guide provides security best practices for developing and maintaining the FashionLab platform. Follow these guidelines to ensure robust security.

## Prerequisites

- Basic security knowledge
- Understanding of web vulnerabilities
- Familiarity with FashionLab architecture

## Development Security

### 1. Secure Coding Standards

#### Input Validation
```typescript
// ❌ Bad: No validation
const processInput = (userInput: string) => {
  return db.query(`SELECT * FROM users WHERE name = '${userInput}'`);
};

// ✅ Good: Validated and parameterized
import { z } from 'zod';

const userSchema = z.object({
  name: z.string().min(1).max(100).regex(/^[a-zA-Z0-9\s]+$/),
  email: z.string().email(),
  role: z.enum(['member', 'admin'])
});

const processInput = async (userInput: unknown) => {
  const validated = userSchema.parse(userInput);
  return await supabase
    .from('users')
    .select('*')
    .eq('name', validated.name);
};
```

#### Output Encoding
```typescript
// ❌ Bad: Direct HTML injection
const displayComment = (comment: string) => {
  return `<div>${comment}</div>`;
};

// ✅ Good: Proper escaping
import DOMPurify from 'isomorphic-dompurify';

const displayComment = (comment: string) => {
  const sanitized = DOMPurify.sanitize(comment, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
    ALLOWED_ATTR: []
  });
  return <div dangerouslySetInnerHTML={{ __html: sanitized }} />;
};
```

### 2. Authentication Security

#### Password Requirements
```typescript
const passwordPolicy = {
  minLength: 12,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommon: true,
  preventReuse: 5
};

export function validatePassword(password: string): string[] {
  const errors: string[] = [];
  
  if (password.length < passwordPolicy.minLength) {
    errors.push(`Password must be at least ${passwordPolicy.minLength} characters`);
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain uppercase letters');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain lowercase letters');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain numbers');
  }
  
  if (!/[!@#$%^&*]/.test(password)) {
    errors.push('Password must contain special characters');
  }
  
  return errors;
}
```

#### Session Management
```typescript
// Secure session configuration
export const sessionConfig = {
  // Short-lived sessions
  expiresIn: '1h',
  
  // Refresh token rotation
  refreshTokenRotation: true,
  reuseInterval: 10,
  
  // Cookie settings
  cookie: {
    secure: true,
    httpOnly: true,
    sameSite: 'strict',
    domain: '.fashionlab.ai',
    path: '/'
  }
};

// Session activity monitoring
export async function trackSession(userId: string, event: string) {
  await supabase.from('security_activity').insert({
    user_id: userId,
    event_type: event,
    ip_address: getClientIP(),
    user_agent: getUserAgent(),
    timestamp: new Date().toISOString()
  });
}
```

### 3. API Security

#### Request Validation
```typescript
// API endpoint security wrapper
export function secureEndpoint(
  handler: (req: Request) => Promise<Response>
) {
  return async (req: Request) => {
    try {
      // 1. Validate authentication
      const user = await validateAuth(req);
      if (!user) {
        return new Response('Unauthorized', { status: 401 });
      }
      
      // 2. Check rate limits
      await enforceRateLimit(user.id, req.url);
      
      // 3. Validate CSRF token for mutations
      if (['POST', 'PUT', 'DELETE'].includes(req.method)) {
        await validateCSRF(req);
      }
      
      // 4. Log request
      await logAPIRequest(user.id, req);
      
      // 5. Process request
      const response = await handler(req);
      
      // 6. Add security headers
      return addSecurityHeaders(response);
      
    } catch (error) {
      await logError(error);
      return new Response('Internal Error', { status: 500 });
    }
  };
}
```

#### API Key Management
```typescript
// Secure API key storage and rotation
export class APIKeyManager {
  async generateKey(userId: string, scope: string[]) {
    const key = crypto.randomBytes(32).toString('hex');
    const hash = await bcrypt.hash(key, 10);
    
    await supabase.from('api_keys').insert({
      user_id: userId,
      key_hash: hash,
      scope: scope,
      expires_at: addDays(new Date(), 90),
      last_used: null
    });
    
    return key; // Return only once, never stored
  }
  
  async validateKey(key: string) {
    const keys = await supabase
      .from('api_keys')
      .select('*')
      .gt('expires_at', new Date().toISOString());
    
    for (const record of keys) {
      if (await bcrypt.compare(key, record.key_hash)) {
        await this.updateLastUsed(record.id);
        return record;
      }
    }
    
    return null;
  }
}
```

### 4. Data Protection

#### Encryption at Rest
```typescript
// Field-level encryption for sensitive data
import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';

export class FieldEncryption {
  private algorithm = 'aes-256-gcm';
  private key: Buffer;
  
  constructor(masterKey: string) {
    this.key = Buffer.from(masterKey, 'hex');
  }
  
  encrypt(text: string): string {
    const iv = randomBytes(16);
    const cipher = createCipheriv(this.algorithm, this.key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return iv.toString('hex') + ':' + 
           authTag.toString('hex') + ':' + 
           encrypted;
  }
  
  decrypt(encryptedData: string): string {
    const parts = encryptedData.split(':');
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];
    
    const decipher = createDecipheriv(this.algorithm, this.key, iv);
    decipher.setAuthTag(authTag);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
```

#### Data Masking
```typescript
// PII data masking for logs and non-privileged views
export function maskPII(data: any): any {
  const masked = { ...data };
  
  // Email masking
  if (masked.email) {
    const [local, domain] = masked.email.split('@');
    masked.email = local.substring(0, 2) + '***@' + domain;
  }
  
  // Phone masking
  if (masked.phone) {
    masked.phone = masked.phone.substring(0, 3) + '-***-' + 
                   masked.phone.substring(masked.phone.length - 2);
  }
  
  // Name masking
  if (masked.full_name) {
    const names = masked.full_name.split(' ');
    masked.full_name = names.map((n: string) => 
      n.charAt(0) + '*'.repeat(n.length - 1)
    ).join(' ');
  }
  
  return masked;
}
```

### 5. Infrastructure Security

#### Environment Variables
```typescript
// Secure environment variable handling
export function getEnvVar(name: string, required = true): string {
  const value = process.env[name];
  
  if (!value && required) {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  
  // Validate format based on variable name
  if (name.includes('_URL') && value) {
    try {
      new URL(value);
    } catch {
      throw new Error(`Invalid URL format for ${name}`);
    }
  }
  
  if (name.includes('_KEY') && value && value.length < 32) {
    throw new Error(`${name} appears to be too short for a secure key`);
  }
  
  return value || '';
}

// Usage
const apiKey = getEnvVar('FASHION_LAB_API_KEY');
const dbUrl = getEnvVar('DATABASE_URL');
```

#### Secure Headers
```typescript
// Comprehensive security headers
export function getSecurityHeaders(): HeadersInit {
  return {
    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',
    
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',
    
    // Enable XSS filter
    'X-XSS-Protection': '1; mode=block',
    
    // Control referrer
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https: blob:",
      "font-src 'self' data:",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
      "media-src 'self'",
      "object-src 'none'",
      "frame-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests"
    ].join('; '),
    
    // HSTS
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    
    // Feature Policy
    'Permissions-Policy': [
      'accelerometer=()',
      'camera=()',
      'geolocation=()',
      'gyroscope=()',
      'magnetometer=()',
      'microphone=()',
      'payment=()',
      'usb=()'
    ].join(', ')
  };
}
```

## Security Checklist

### Pre-Deployment

- [ ] All dependencies updated
- [ ] Security scan passed
- [ ] Environment variables secured
- [ ] API keys rotated
- [ ] Logs sanitized
- [ ] Error messages generic
- [ ] Rate limiting configured
- [ ] CORS properly set

### Regular Maintenance

- [ ] Weekly dependency updates
- [ ] Monthly security scans
- [ ] Quarterly penetration tests
- [ ] Annual security audit
- [ ] Incident response drills
- [ ] Security training
- [ ] Access reviews
- [ ] Key rotation

## Common Vulnerabilities

### SQL Injection Prevention
```typescript
// ❌ Never do this
const query = `SELECT * FROM users WHERE id = ${userId}`;

// ✅ Always use parameterized queries
const { data } = await supabase
  .from('users')
  .select('*')
  .eq('id', userId);
```

### XSS Prevention
```typescript
// ❌ Dangerous
<div dangerouslySetInnerHTML={{ __html: userContent }} />

// ✅ Safe
<div>{userContent}</div>
// React automatically escapes content
```

### CSRF Prevention
```typescript
// Generate CSRF token
const generateCSRFToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

// Validate CSRF token
const validateCSRFToken = (token: string, sessionToken: string) => {
  return crypto.timingSafeEqual(
    Buffer.from(token),
    Buffer.from(sessionToken)
  );
};
```

## Incident Response

### Security Incident Checklist

1. **Immediate Actions**
   - [ ] Isolate affected systems
   - [ ] Preserve evidence
   - [ ] Notify security team
   - [ ] Begin investigation

2. **Investigation**
   - [ ] Identify scope
   - [ ] Determine root cause
   - [ ] Assess damage
   - [ ] Document findings

3. **Remediation**
   - [ ] Patch vulnerability
   - [ ] Reset credentials
   - [ ] Update security controls
   - [ ] Verify fix

4. **Recovery**
   - [ ] Restore services
   - [ ] Monitor closely
   - [ ] Communicate status
   - [ ] Update documentation

5. **Post-Incident**
   - [ ] Conduct review
   - [ ] Update procedures
   - [ ] Security training
   - [ ] Improve monitoring

## Related Documentation

- [Security Overview](./README.md)
- [Threat Model](./threat-model.md)
- [Assessment Report](./assessment-report.md)
- [Authentication](../04-api/authentication.md)