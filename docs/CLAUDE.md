# Documentation Guide

This file provides guidance for creating and maintaining documentation in the FashionLab project.

## Documentation Philosophy

- **Document decisions, not just implementations**
- **Keep documentation close to code**
- **Update docs as part of feature work**
- **Remove outdated documentation**

## Documentation Structure

```
docs/
├── architecture/        # System design and architecture decisions
├── deployment/         # Deployment processes and configurations
├── development/        # Development guides and patterns
├── features/           # Feature-specific documentation
├── technical-review-*/ # Weekly technical reviews and retrospectives
└── CLAUDE.md          # This file
```

## What to Document

### Always Document
1. **Architecture Decisions** - Why we chose X over Y
2. **Complex Business Logic** - The "why" behind the code
3. **API Contracts** - Endpoints, payloads, responses
4. **Setup Instructions** - Getting started guides
5. **Deployment Processes** - How to ship code
6. **Security Considerations** - Authentication, authorization, data protection

### Don't Document
1. **Self-evident code** - Good code is self-documenting
2. **Implementation details** - These change frequently
3. **Outdated features** - Remove docs for removed features
4. **Business negotiations** - Keep technical docs technical

## Documentation Standards

### File Naming
- Use lowercase with hyphens: `feature-name.md`
- Date-based folders for reviews: `technical-review-2025-05-23/`
- Descriptive names over abbreviations

### Markdown Conventions
```markdown
# Main Title (H1 - one per document)

## Section Title (H2)

### Subsection (H3)

**Bold** for emphasis
*Italic* for first-time terms
`code` for inline code
```

### Code Examples
Always provide working examples:
```typescript
// ✅ Good: Complete, runnable example
async function fetchAssets(collectionId: string): Promise<Asset[]> {
  const { data, error } = await supabase
    .from('assets')
    .select('*')
    .eq('collection_id', collectionId);
    
  if (error) throw error;
  return data;
}

// ❌ Bad: Incomplete pseudocode
function getAssets() {
  // fetch assets somehow
}
```

### Diagrams
Use ASCII diagrams for simple relationships:
```
Frontend (React)
    ↓
API (Supabase)
    ↓
Database (PostgreSQL)
```

For complex diagrams, use Mermaid:
```mermaid
graph TD
    A[User] -->|Upload| B[Asset]
    B --> C{Process}
    C -->|Compress| D[Compressed]
    C -->|Thumbnail| E[Thumbnail]
```

## Maintenance

### When to Update
- **During PR Review** - Outdated docs = PR rejection
- **After Major Changes** - Architecture, APIs, processes
- **Weekly Reviews** - Technical review documents

### When to Remove
- Feature is deprecated/removed
- Information is outdated
- Duplicate documentation exists
- Business-only content (move to appropriate location)

## Documentation Templates

### Feature Documentation
```markdown
# Feature Name

## Overview
Brief description of what this feature does and why it exists.

## Technical Design
How it's implemented, key components, data flow.

## Usage
How to use the feature, API examples, UI flows.

## Testing
How to test the feature, key test cases.

## Known Issues
Current limitations or bugs.

## Future Improvements
Planned enhancements.
```

### Architecture Decision Record (ADR)
```markdown
# ADR-001: Title

## Status
Accepted/Rejected/Deprecated

## Context
What problem are we solving?

## Decision
What we decided to do.

## Consequences
What happens as a result (good and bad).

## Alternatives Considered
What else we looked at and why we didn't choose it.
```

## Quick Reference

### Good Documentation
- Answers "why" not just "what"
- Includes examples
- Stays up to date
- Easy to find
- Concise but complete

### Bad Documentation
- Outdated information
- No examples
- Too verbose
- Hidden in random places
- Duplicates code comments

## Related Documentation
- [Technical Review Template](./technical-review-*/README.md)
- [Coding Standards](./technical-review-*/30-coding-standards.md)
- Root [CLAUDE.md](../CLAUDE.md) for project overview