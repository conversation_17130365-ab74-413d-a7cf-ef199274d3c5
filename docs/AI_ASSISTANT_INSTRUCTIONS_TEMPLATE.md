# AI Assistant Instructions Template

## 📋 Copy-Paste Template for New Linear Issues

```markdown
I'm working on a new Linear issue and need your help. Here's the context:

**Linear Issue**: [Linear-XXX] - [Issue Title]
**Issue URL**: [Linear Issue URL]
**Priority**: [High/Medium/Low] - [Reason for priority]

## Problem Description
[Describe what needs to be fixed or built]

## Acceptance Criteria
- [ ] [Criteria 1]
- [ ] [Criteria 2]
- [ ] [Criteria 3]

## Technical Context
- **Affected Areas**: [List relevant parts of codebase]
- **Technologies**: [React, Supabase, etc.]
- **User Roles**: [If permissions involved]
- **Browsers/Devices**: [If specific compatibility needed]

## Additional Context
- **Screenshots/Videos**: [Describe or mention if attached to Linear]
- **Related Issues**: [Any dependencies or related work]
- **Constraints**: [Any technical or business constraints]

## Expected Approach
[If you have preferences for how to tackle this]

Please help me:
1. Break this down into manageable tasks
2. Gather the necessary context from the codebase
3. Implement the solution following our coding standards
4. Ensure proper testing coverage

Let's start by understanding the current codebase structure related to this issue.
```

## 🎯 Specific Issue Type Templates

### Bug Fix Template
```markdown
**Bug Report**: [Linear-XXX] - [Bug Title]

## Current Behavior
[What's happening now that's wrong]

## Expected Behavior
[What should happen instead]

## Steps to Reproduce
1. [Step 1]
2. [Step 2]
3. [Bug occurs]

## Environment
- **Browser**: [Chrome, Safari, etc.]
- **Device**: [Desktop, Mobile, etc.]
- **User Role**: [If relevant]

## Error Details
- **Console Errors**: [Any JavaScript errors]
- **Network Errors**: [Any API failures]
- **Screenshots**: [Describe visual issues]

Please help me:
1. Reproduce this bug locally
2. Identify the root cause
3. Implement a fix that doesn't break related functionality
4. Add appropriate error handling/validation
```

### Feature Request Template
```markdown
**Feature Request**: [Linear-XXX] - [Feature Title]

## Feature Description
[What new functionality needs to be built]

## User Story
As a [user type], I want [functionality] so that [benefit].

## Acceptance Criteria
- [ ] [Functional requirement 1]
- [ ] [Functional requirement 2]
- [ ] [UI/UX requirement]
- [ ] [Performance requirement]
- [ ] [Error handling requirement]

## Design Requirements
- **UI Components**: [New components needed]
- **User Flow**: [How users will interact]
- **Responsive**: [Mobile/desktop considerations]
- **Accessibility**: [A11y requirements]

## Technical Requirements
- **API Changes**: [Backend changes needed]
- **Database**: [Schema changes needed]
- **Integrations**: [Third-party services]
- **Performance**: [Speed/efficiency requirements]

Please help me:
1. Plan the technical architecture
2. Break down into development phases
3. Identify any potential challenges
4. Implement following our design system
```

### Technical Debt Template
```markdown
**Technical Debt**: [Linear-XXX] - [Debt Title]

## Current State
[Describe the current problematic implementation]

## Problems with Current Approach
- [Problem 1]
- [Problem 2]
- [Problem 3]

## Proposed Solution
[Describe the better approach]

## Benefits of Refactoring
- [Benefit 1]
- [Benefit 2]
- [Benefit 3]

## Migration Considerations
- **Backward Compatibility**: [What needs to stay compatible]
- **Data Migration**: [Any data changes needed]
- **Rollback Plan**: [How to revert if needed]
- **Testing Strategy**: [How to ensure nothing breaks]

Please help me:
1. Analyze the current implementation
2. Plan a safe migration strategy
3. Implement the improvements incrementally
4. Ensure comprehensive testing
```

## 🔧 AI Assistant Workflow Expectations

### Information Gathering Phase
```markdown
Before making any changes, please:
1. Use codebase-retrieval to understand the current implementation
2. Identify all related files and components
3. Check for existing patterns we should follow
4. Look for similar implementations in the codebase
5. Understand the data flow and dependencies
```

### Planning Phase
```markdown
For complex issues, please:
1. Create a task breakdown using the task management tools
2. Identify potential risks or challenges
3. Suggest the order of implementation
4. Highlight any decisions that need my input
5. Estimate the complexity of each task
```

### Implementation Phase
```markdown
During implementation:
1. Always use str-replace-editor for file modifications
2. Follow existing code patterns and conventions
3. Add appropriate comments for complex logic
4. Consider error handling and edge cases
5. Update task progress as you complete work
6. Ask for clarification if requirements are unclear
```

### Testing Phase
```markdown
For testing guidance:
1. Suggest specific test scenarios
2. Identify areas that need regression testing
3. Recommend edge cases to check
4. Provide testing instructions for staging
5. Highlight any manual testing steps needed
```

## 📝 Communication Preferences

### Progress Updates
```markdown
Please provide regular updates including:
- Current task being worked on
- Any blockers or questions
- Estimated time to completion
- Any discoveries that change the scope
```

### Decision Points
```markdown
When you encounter decisions, please:
- Present the options clearly
- Explain pros/cons of each approach
- Recommend your preferred solution
- Explain the reasoning behind your recommendation
```

### Completion Summary
```markdown
When work is complete, please provide:
- Summary of changes made
- Files modified
- Testing recommendations
- Any follow-up work needed
- Commit message suggestion
```

---

## 🚀 Quick Start Commands

### For Bug Fixes
```
I have a bug report: [Linear-XXX]. The issue is [brief description]. Please help me investigate the current implementation and identify the root cause.
```

### For Features
```
I need to implement a new feature: [Linear-XXX]. The requirement is [brief description]. Please help me plan the technical approach and break it down into tasks.
```

### For Refactoring
```
I need to refactor [component/system]: [Linear-XXX]. The current implementation has [problems]. Please help me plan a safe migration strategy.
```

---

*Use these templates to ensure consistent, thorough communication with the AI assistant for every Linear issue.*
