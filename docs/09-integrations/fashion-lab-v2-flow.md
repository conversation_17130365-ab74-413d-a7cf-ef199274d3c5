# Fashion Lab V2 API Flow Documentation

## Overview

The Fashion Lab V2 API provides AI-powered image generation capabilities using a face image and three reference images. This document details the complete flow from frontend request to final asset storage.

## Prerequisites

- Understanding of JWT authentication
- Familiarity with Edge Functions
- Knowledge of async/promise patterns
- Access to Fashion Lab API credentials

## Architecture Overview

```mermaid
sequenceDiagram
    participant F as Frontend
    participant E1 as Edge Function<br/>(generate-images)
    participant FL as Fashion Lab API
    participant E2 as Edge Function<br/>(queue-status)
    participant S as Supabase Storage
    participant DB as Database

    F->>E1: POST /generate-images<br/>(with base64 images)
    Note over E1: Creates Fashion Lab JWT
    E1->>FL: POST /generate-image-v2<br/>(multipart/form-data)
    FL-->>E1: { queue_id }
    E1-->>F: { queue_id }
    
    loop Poll for completion
        F->>E2: POST /queue-status
        Note over E2: Creates Fashion Lab JWT
        E2->>FL: GET /queue/{queue_id}
        FL-->>E2: Status + Image URLs
        
        alt Status: Completed
            E2->>FL: Download images
            E2->>S: Store in ai-generated bucket
            E2->>DB: Create ai_generated_images records
            E2-->>F: { status: completed, images: [...] }
        else Status: Processing
            E2-->>F: { status: processing, progress: 50 }
        end
    end
    
    Note over F: User selects images
    F->>DB: Mark images as selected
    F->>S: Process through compression pipeline
    F->>S: Store in assets bucket
    F->>DB: Create asset records
```

## Detailed Flow

### 1. Image Generation Request

The frontend initiates generation by calling the edge function:

```typescript
// Frontend call
const result = await FashionLabImageService.generateImages({
  prompt: "elegant summer dress on model",
  faceImage: "data:image/jpeg;base64,...",  // Base64 encoded
  image2: "data:image/jpeg;base64,...",      // Reference image 1
  image3: "data:image/jpeg;base64,...",      // Reference image 2  
  image4: "data:image/jpeg;base64,...",      // Reference image 3
  collectionId: "uuid-here",
  seed1: 12345,  // Optional: for reproducible results
  seed2: 67890,
  seed3: 11111,
  seed4: 22222
});
```

### 2. JWT Token Generation

The edge function creates a JWT specifically for Fashion Lab API:

```typescript
// In generate-images edge function
const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET');
const secret = new TextEncoder().encode(jwtSecret);

const fashionLabToken = await new jose.SignJWT({
  iss: 'fashionlab-app',
  scope: 'image:generate image:refine',
})
  .setProtectedHeader({ alg: 'HS256' })
  .setIssuedAt()
  .setExpirationTime('1h')
  .sign(secret);
```

**Important**: This is NOT the user's Supabase JWT. It's a separate token created specifically for Fashion Lab API authentication.

### 3. Fashion Lab API Request

The edge function prepares and sends the request:

```typescript
// Convert base64 to blobs
const formData = new FormData();
formData.append('face', faceBlob, 'face.jpg');
formData.append('image_2', image2Blob, 'image_2.jpg');
formData.append('image_3', image3Blob, 'image_3.jpg');
formData.append('image_4', image4Blob, 'image_4.jpg');
formData.append('prompt', prompt);

// Include seed values if provided
if (seed1) formData.append('seed1', seed1.toString());

// Send to Fashion Lab
const response = await fetch('https://fashionlab.notfirst.rodeo/generate-image-v2', {
  method: 'POST',
  headers: {
    'Authorization': `jwt ${fashionLabToken}`,  // Note: "jwt" prefix, NOT "Bearer"
  },
  body: formData,
});
```

### 4. Queue Status Polling

The frontend polls for completion:

```typescript
// Frontend polling loop
const status = await FashionLabImageService.waitForCompletion(
  queueId,
  collectionId,
  {
    maxAttempts: 60,      // 5 minutes max
    pollInterval: 5000,   // Check every 5 seconds
    onProgress: (progress) => {
      console.log(`Generation progress: ${progress}%`);
    }
  }
);
```

### 5. Image Storage Process

When generation completes, the queue-status function:

1. **Downloads images** from Fashion Lab using the JWT
2. **Stores in Supabase** `ai-generated` bucket
3. **Creates database records** in `ai_generated_images` table
4. **Returns public URLs** to the frontend

```typescript
// Storage path structure
const storagePath = `${organizationId}/${collectionId}/generated/${timestamp}_${queueId}_${index}.png`;
```

### 6. Image Selection & Asset Creation

When users select images:

```typescript
// Mark as selected and process
await FashionLabImageService.selectGeneratedImages(
  selectedImageIds,
  collectionId
);
```

This triggers:
1. Download from `ai-generated` bucket
2. Process through compression pipeline (original, compressed, thumbnail)
3. Store in `assets` bucket
4. Create proper asset records with workflow stage

## Authentication Details

### Two Types of JWT Tokens

1. **Supabase User JWT**
   - Used for platform authentication
   - Passed in requests to edge functions
   - Format: `Authorization: Bearer ${token}`
   - Contains user session information

2. **Fashion Lab API JWT**
   - Created on-demand by edge functions
   - Used only for Fashion Lab API calls
   - Format: `Authorization: jwt ${token}` ⚠️ **Critical: Use "jwt" prefix**
   - Contains API scopes and issuer

### JWT Format Comparison

```http
# ✅ CORRECT - Fashion Lab API
Authorization: jwt eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ❌ WRONG - This will fail with Fashion Lab API
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ✅ CORRECT - Supabase/Platform calls
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Configuration

### Environment Variables

```env
# Edge Functions require these
FASHIONLAB_JWT_SECRET=your-jwt-secret      # For signing JWTs
FASHIONLAB_API_TOKEN=your-api-token        # Master API token (legacy)
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-key
```

### Image Requirements

- **Format**: JPEG, PNG, or WebP
- **Size Limit**: ~1MB per image (Fashion Lab constraint)
- **Dimensions**: No strict limit, but larger images may timeout
- **Base64 Encoding**: Include MIME type prefix

Example base64 format:
```
data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...
```

## Troubleshooting

### Common Issues

#### 1. JWT Authentication Errors

**Symptom**: 401 Unauthorized from Fashion Lab API

**Causes & Solutions**:
- **Wrong JWT prefix**: Ensure using `jwt` not `Bearer`
- **Expired JWT**: JWTs expire after 1 hour
- **Invalid secret**: Check `FASHIONLAB_JWT_SECRET` is correct
- **Missing scopes**: JWT must include `image:generate` scope

#### 2. Image Size Errors

**Symptom**: Request fails or times out

**Solutions**:
```typescript
// Check image sizes before sending
const maxSize = 1024 * 1024; // 1MB
if (imageBlob.size > maxSize) {
  // Compress or resize image
}
```

#### 3. Timeout Issues

**Symptom**: 504 Gateway Timeout

**Solutions**:
- Edge function has 25-second timeout
- Returns mock queue_id on timeout
- Continue polling normally

#### 4. Storage Failures

**Symptom**: Images generate but don't appear in Storage

**Check**:
- `ai-generated` bucket exists and has proper permissions
- Organization and collection IDs are valid
- User has proper access rights
- Storage quota not exceeded

### Debug Tips

1. **Enable verbose logging**:
```typescript
console.log('Fashion Lab queue status:', JSON.stringify(result, null, 2));
```

2. **Check JWT contents**:
```typescript
const decoded = jose.decodeJwt(fashionLabToken);
console.log('JWT claims:', decoded);
```

3. **Verify image data**:
```typescript
console.log(`Image size: ${imageBlob.size} bytes`);
console.log(`Image type: ${imageBlob.type}`);
```

## Security Considerations

1. **JWT Secrets**: Never expose `FASHIONLAB_JWT_SECRET` in client code
2. **Access Control**: Always verify user has access to collection
3. **Rate Limiting**: Implement client-side throttling
4. **Image Validation**: Validate image types and sizes
5. **Storage Paths**: Use organization/collection hierarchy for isolation

## Best Practices

1. **Error Handling**
   - Implement retry logic with exponential backoff
   - Provide user feedback during long operations
   - Handle partial failures gracefully

2. **Performance**
   - Compress images before uploading
   - Cache generated results
   - Use progress indicators

3. **User Experience**
   - Show generation progress
   - Allow cancellation of long-running operations
   - Provide clear error messages

## API Response Examples

### Successful Generation Start
```json
{
  "queue_id": "q_abc123def456",
  "status": "processing",
  "images": [],
  "error": null
}
```

### Completed Generation
```json
{
  "queue_id": "q_abc123def456",
  "status": "completed",
  "progress": 100,
  "images": [
    "https://xxx.supabase.co/storage/v1/object/public/ai-generated/...",
    "https://xxx.supabase.co/storage/v1/object/public/ai-generated/..."
  ],
  "seed1": 12345,
  "seed2": 67890,
  "seed3": 11111,
  "seed4": 22222,
  "stored": true,
  "metadata": {
    "executionTime": 45.2,
    "model": "fashion-lab-v2"
  }
}
```

### Failed Generation
```json
{
  "queue_id": "q_abc123def456",
  "status": "failed",
  "progress": 0,
  "error": "Invalid prompt: contains prohibited content",
  "images": []
}
```

## Development Tips

### Local Testing

1. **Mock Mode**: Edge functions return mock data when JWT secret not configured
2. **Test Queue IDs**: Use `mock-*` or `timeout-*` prefixes for testing
3. **Placeholder Images**: Mock mode returns placeholder.co images

### Monitoring

- Check edge function logs: `supabase functions logs generate-images`
- Monitor storage usage in Supabase dashboard
- Track API rate limits and usage

## Related Documentation

- [Fashion Lab API Integration](./fashion-lab-api.md) - General API overview
- [Image Generation Service](../../src/services/fashionLabImageService.ts) - Frontend service
- [Edge Functions](../../supabase/functions/) - Backend implementation
- [Database Schema](../05-database/schema.md) - Related tables