# Fashion Lab API Integration

## Overview

Fashion Lab API provides AI-powered fashion image generation capabilities. This comprehensive guide covers the complete integration with FashionLab platform, including authentication, API endpoints, image generation workflow, and troubleshooting.

## Prerequisites

- Fashion Lab API access (contact <<EMAIL>>)
- JWT secret for signing tokens
- Supabase project with edge functions enabled
- Storage buckets configured

## Architecture Overview

```
Frontend (React App)
    ↓ Supabase JWT
Edge Functions
    ├── generate-images      → Creates Fashion Lab JWT → Fashion Lab API
    ├── queue-status        → Polls with JWT → Downloads images → Storage
    ├── fashion-lab-proxy   → General API proxy
    └── image-proxy        → Image download proxy
```

## ⚠️ Critical Authentication Details

### Two JWT Token Systems

The platform uses two completely different JWT tokens:

1. **Supabase User JWT** 
   - For platform authentication (frontend ↔ edge functions)
   - Format: `Authorization: Bearer ${token}`
   - Contains user session data
   - Used by frontend to call edge functions
   
2. **Fashion Lab API JWT**
   - For Fashion Lab API authentication only
   - Format: `Authorization: jwt ${token}` ⚠️ **Note: "jwt" prefix, NOT "Bearer"**
   - Created on-demand by edge functions
   - Contains API scopes and issuer
   - Expires after 1 hour
   - Frontend never sees these tokens

### Authentication Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant EdgeFunction
    participant FashionLabAPI
    
    User->>Frontend: Login
    Frontend->>Frontend: Get Supabase JWT
    Frontend->>EdgeFunction: Request with Bearer token
    EdgeFunction->>EdgeFunction: Create Fashion Lab JWT
    EdgeFunction->>FashionLabAPI: Request with jwt token
    FashionLabAPI-->>EdgeFunction: Response
    EdgeFunction-->>Frontend: Processed response
```

## Quick Start

### 1. Set Environment Variables

```bash
# In Supabase secrets
supabase secrets set FASHIONLAB_JWT_SECRET=2ms4LQBtkbvJ8RwFmBht
```

### 2. Test Authentication

```javascript
// Test Fashion Lab API authentication
const response = await fetch('https://fashionlab.notfirst.rodeo/api/health', {
  headers: {
    'Authorization': 'jwt 2ms4LQBtkbvJ8RwFmBht'
  }
});

console.log('Status:', response.status); // Should be 200
```

### 3. Generate Images

```javascript
import { FashionLabImageService } from '@/services/fashionLabImageService';

const { queue_id } = await FashionLabImageService.generateImages({
  prompt: "elegant summer dress on model",
  faceImage: base64FaceImage,
  image2: garmentImage,
  image3: poseImage,
  image4: backgroundImage,
  collectionId: collectionId
});

// Poll for completion
const result = await FashionLabImageService.waitForCompletion(
  queue_id,
  collectionId,
  true // Auto-store images
);
```

## API Endpoints

### Base URL

```
https://fashionlab.notfirst.rodeo
```

### Available Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/generate-image` | POST | Text-based generation (v1) |
| `/generate-image-v2` | POST | Image-based generation (v2) |
| `/queue/{queue_id}` | GET | Check generation status |
| `/image/{image_id}` | GET | Download generated image |

## Complete Image Generation Workflow

### 1. Frontend Initiates Generation

```typescript
// User uploads images and enters prompt
const result = await FashionLabImageService.generateImages({
  prompt: "Fashion model wearing elegant dress",
  faceImage: base64FaceImage,    // Model's face
  image2: base64GarmentImage,     // Garment/clothing
  image3: base64PoseImage,        // Pose reference
  image4: base64BackgroundImage,  // Background
  collectionId: 'collection-uuid'
});
```

### 2. Edge Function Processing

The `generate-images` edge function:

1. **Validates Authentication**
   ```typescript
   const { data: { user } } = await supabase.auth.getUser(token);
   ```

2. **Creates Fashion Lab JWT**
   ```typescript
   const fashionLabToken = await new jose.SignJWT({
     iss: 'fashionlab-app',
     scope: 'image:generate image:refine',
   })
     .setProtectedHeader({ alg: 'HS256' })
     .setIssuedAt()
     .setExpirationTime('1h')
     .sign(secret);
   ```

3. **Sends to Fashion Lab API**
   ```typescript
   const response = await fetch('https://fashionlab.notfirst.rodeo/generate-image-v2', {
     method: 'POST',
     headers: {
       'Authorization': `jwt ${fashionLabToken}` // ⚠️ jwt prefix!
     },
     body: formData // multipart with images
   });
   ```

### 3. Status Polling & Image Storage

The `queue-status` edge function handles completion:

```mermaid
sequenceDiagram
    participant UI as Frontend
    participant EF as Edge Function
    participant FL as Fashion Lab API
    participant S as Supabase Storage
    participant DB as Database

    UI->>EF: Check status (queue_id)
    EF->>EF: Create new JWT
    EF->>FL: GET /queue/{queue_id}
    FL-->>EF: Status + Image URLs
    
    alt Status: Completed
        loop For each image
            EF->>FL: Download image (jwt auth)
            FL-->>EF: Image data
            EF->>S: Upload to ai-generated bucket
            EF->>DB: Create ai_generated_images record
        end
        EF-->>UI: { status: completed, images: [...] }
    else Status: Processing
        EF-->>UI: { status: processing, progress: 50 }
    end
```

### 4. Image Selection & Asset Creation

When users select generated images:

1. Images are marked as selected in `ai_generated_images` table
2. Selected images are processed through compression pipeline
3. Final assets are created in `assets` table with proper workflow stage

## Request Examples

### V2 Generation (Image-based)

```javascript
// Note: This is what happens inside the edge function
// Frontend never directly calls Fashion Lab API

const formData = new FormData();
formData.append('prompt', 'Fashion model wearing elegant dress');
formData.append('face', faceBlob, 'face.png');
formData.append('image_2', garmentBlob, 'garment.png');
formData.append('image_3', poseBlob, 'pose.png');
formData.append('image_4', backgroundBlob, 'background.png');

// Edge function creates JWT
const fashionLabToken = await createFashionLabJWT();

const response = await fetch('https://fashionlab.notfirst.rodeo/generate-image-v2', {
  method: 'POST',
  headers: {
    'Authorization': `jwt ${fashionLabToken}` // Dynamic JWT, not hardcoded
  },
  body: formData
});

const { queue_id } = await response.json();
```

### Check Queue Status

```javascript
// Inside edge function
const fashionLabToken = await createFashionLabJWT();

const response = await fetch(
  `https://fashionlab.notfirst.rodeo/queue/${queue_id}`,
  {
    headers: {
      'Authorization': `jwt ${fashionLabToken}`
    }
  }
);

const result = await response.json();
// {
//   status: "completed",
//   data: {
//     images: ["image_url_1", "image_url_2"]
//   }
// }
```

### Download Generated Image

```javascript
// Inside edge function - downloading image
const fashionLabToken = await createFashionLabJWT();

const response = await fetch(imageUrl, {
  headers: {
    'Authorization': `jwt ${fashionLabToken}`
  }
});

const blob = await response.blob();
```

## Edge Function Implementation

### generate-images Function

```typescript
// /supabase/functions/generate-images/index.ts
serve(async (req) => {
  const { prompt, face_image, image_2, image_3, image_4 } = await req.json();
  
  // Create JWT token for Fashion Lab API
  const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET');
  const secret = new TextEncoder().encode(jwtSecret);
  
  const fashionLabToken = await new jose.SignJWT({
    iss: 'fashionlab-app',
    scope: 'image:generate image:refine',
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('1h')
    .sign(secret);
  
  const formData = new FormData();
  formData.append('prompt', prompt);
  
  // Convert base64 to blob and append
  formData.append('face', base64ToBlob(face_image), 'face.png');
  formData.append('image_2', base64ToBlob(image_2), 'image_2.png');
  formData.append('image_3', base64ToBlob(image_3), 'image_3.png');
  formData.append('image_4', base64ToBlob(image_4), 'image_4.png');
  
  const response = await fetch('https://fashionlab.notfirst.rodeo/generate-image-v2', {
    method: 'POST',
    headers: {
      'Authorization': `jwt ${fashionLabToken}` // Use jwt prefix
    },
    body: formData
  });
  
  return new Response(JSON.stringify(await response.json()));
});
```

### queue-status Function

```typescript
// /supabase/functions/queue-status/index.ts
serve(async (req) => {
  const { queue_id, collection_id, store_images } = await req.json();
  
  // Create new JWT token for this request
  const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET');
  const secret = new TextEncoder().encode(jwtSecret);
  
  const fashionLabToken = await new jose.SignJWT({
    iss: 'fashionlab-app',
    scope: 'image:generate image:refine',
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('1h')
    .sign(secret);
  
  // Check status
  const statusResponse = await fetch(
    `https://fashionlab.notfirst.rodeo/queue/${queue_id}`,
    {
      headers: {
        'Authorization': `jwt ${fashionLabToken}`
      }
    }
  );
  
  const result = await statusResponse.json();
  
  if (result.status === 'completed' && store_images) {
    // Download and store images
    for (const imageUrl of result.data.images) {
      const imageResponse = await fetch(imageUrl, {
        headers: {
          'Authorization': `jwt ${fashionLabToken}`
        }
      });
      
      const blob = await imageResponse.blob();
      
      // Upload to Supabase storage
      const { data: uploadData } = await supabaseAdmin.storage
        .from('ai-generated')
        .upload(storagePath, blob);
      
      // Create database record
      await supabaseAdmin.from('ai_generated_images').insert({
        queue_id,
        collection_id,
        image_url: publicUrl,
        storage_path: storagePath
      });
    }
  }
  
  return new Response(JSON.stringify(result));
});
```

## Frontend Integration

### Using the React Hook

```typescript
import { useFashionLabImages } from '@/hooks/useFashionLabImages';

function ImageGenerator() {
  const {
    generateImages,
    isGenerating,
    progress,
    generatedImages,
    error
  } = useFashionLabImages({
    collectionId: 'collection-uuid'
  });

  const handleGenerate = async () => {
    await generateImages({
      prompt: "Fashion model in summer dress",
      faceImage: base64Face,
      image2: base64Garment,
      image3: base64Pose,
      image4: base64Background
    });
  };

  return (
    <div>
      <button onClick={handleGenerate} disabled={isGenerating}>
        {isGenerating ? `Generating... ${progress}%` : 'Generate'}
      </button>
      
      {generatedImages.map(image => (
        <img key={image.id} src={image.image_url} alt="Generated" />
      ))}
      
      {error && <div>Error: {error.message}</div>}
    </div>
  );
}
```

## Troubleshooting Guide

### 🚨 Most Common Issue: Authentication Format

**Symptoms:**
- Generation shows "All photos uploaded successfully" but no images appear
- Empty results when querying `ai_generated_images` table
- 401 errors in edge function logs

**Root Cause:** Using `Bearer` instead of `jwt` prefix for Fashion Lab API

**Solution:**
```typescript
// ❌ WRONG - Causes 401 errors
headers: { 'Authorization': `Bearer ${fashionLabToken}` }

// ✅ CORRECT - Fashion Lab requires this format
headers: { 'Authorization': `jwt ${fashionLabToken}` }
```

**How to Verify Fix:**
```bash
# Check function logs
supabase functions logs queue-status --tail

# Look for success messages:
"Successfully downloaded image 1, size: 401484 bytes"
"Successfully uploaded image 1 to storage"
"Successfully created ai_generated_images record"
```

### Images Not Storing

**Symptom**: Images generate but don't save to Supabase

**Possible Causes**:
1. Wrong auth format when downloading images
2. Storage bucket permissions
3. Database RLS policies

**Debug Steps**:

```bash
# 1. Test image download
node test-image-download.js

# 2. Check storage bucket exists
supabase storage ls

# 3. Verify database records
node check-ai-images.js
```

### Queue Timeout

**Symptom**: Polling stops before images complete

**Solution**: Increase polling attempts

```javascript
// In waitForCompletion
const maxAttempts = 60; // Increase for longer generations
const pollInterval = 5000; // 5 seconds between polls
```

## Testing

### End-to-End Test

```bash
# Run comprehensive test
node test-complete-image-generation-flow.js

# Expected output:
# ✅ Signed in successfully
# ✅ Generation completed!
# ✅ Found 3 stored images
# ✅ Images accessible via public URL
# 🎉 Complete test PASSED!
```

### Component Tests

```bash
# Test Fashion Lab API directly (for debugging only)
# Note: In production, only edge functions should call Fashion Lab API
curl -H "Authorization: jwt YOUR_JWT_TOKEN" \
  https://fashionlab.notfirst.rodeo/api/health

# Test local functions
supabase functions serve generate-images
supabase functions serve queue-status
```

## Performance Tips

1. **Image Optimization**
   - Compress images before sending
   - Use appropriate formats (JPEG for photos)
   - Resize to reasonable dimensions

2. **Polling Strategy**
   - Start with 1-second intervals
   - Increase delay exponentially
   - Max out at 10-second intervals

3. **Error Recovery**
   - Retry failed downloads
   - Fall back to original URLs
   - Log all errors for debugging

## Security Best Practices

1. **Token Management**
   - Store JWT in Supabase secrets
   - Never expose in client code
   - Rotate tokens periodically

2. **Input Validation**
   - Validate image formats
   - Check file sizes (< 5MB)
   - Sanitize prompts

3. **Access Control**
   - Verify user authentication
   - Check organization membership
   - Apply RLS policies

## Common Errors

| Error | Cause | Solution |
|-------|-------|----------|
| 401 Unauthorized | Wrong auth format | Use `jwt` not `Bearer` |
| 413 Payload Too Large | Images too big | Compress images < 5MB |
| 429 Rate Limited | Too many requests | Implement backoff |
| 500 Server Error | API issue | Retry with backoff |

## Testing the Integration

### End-to-End Test Script

```javascript
// test-complete-flow.js
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testCompleteFlow() {
  // 1. Authenticate
  const { data: { session } } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'password'
  });

  // 2. Generate images
  const generateResponse = await fetch(`${SUPABASE_URL}/functions/v1/generate-images`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      prompt: 'Test generation',
      face_image: TEST_IMAGE_BASE64,
      collection_id: COLLECTION_ID
    })
  });

  const { queue_id } = await generateResponse.json();
  console.log('✅ Generation started:', queue_id);

  // 3. Poll for completion
  let completed = false;
  while (!completed) {
    const statusResponse = await fetch(`${SUPABASE_URL}/functions/v1/queue-status`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        queue_id,
        collection_id: COLLECTION_ID,
        store_images: true
      })
    });

    const status = await statusResponse.json();
    if (status.status === 'completed') {
      completed = true;
      console.log('✅ Images generated and stored:', status.images);
    }
    
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  // 4. Verify storage
  const { data: images } = await supabase
    .from('ai_generated_images')
    .select('*')
    .eq('queue_id', queue_id);

  console.log('✅ Found', images.length, 'stored images');
}
```

### Local Development Testing

See [Local Testing Guide](./local-testing-guide.md) for:
- Mock mode configuration
- Test data setup
- Debugging tips

## Deployment Checklist

- [ ] Set `FASHIONLAB_JWT_SECRET` in Supabase secrets
- [ ] Deploy all edge functions
- [ ] Create `ai-generated` storage bucket
- [ ] Apply database migrations
- [ ] Configure RLS policies
- [ ] Test authentication flow
- [ ] Verify image storage

## Related Documentation

- [V2 API Flow Details](./fashion-lab-v2-flow.md) - Technical deep dive
- [Local Testing Guide](./local-testing-guide.md) - Development setup
- [Edge Functions Reference](../04-api/endpoints/edge-functions.md)
- [Database Schema](../05-database/schema.md)