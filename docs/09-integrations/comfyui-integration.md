# ComfyUI Serverless Integration

## Overview

FashionLab integrates with a custom ComfyUI serverless endpoint on RunPod for advanced AI image generation workflows. This integration provides complex image processing capabilities including FLUX models, custom nodes, and style transfer.

## Architecture

```
FashionLab Platform
    ↓
ComfyUI Service Layer
    ↓
RunPod Serverless Endpoint
    ├── Custom Docker Image
    ├── ComfyUI Engine
    ├── Custom Models
    └── Custom Nodes
```

## Technical Details

### Endpoint Configuration

- **Production URL**: `https://api.runpod.ai/v2/s426ojl4svhd9e/run`
- **Docker Image**: `docker.io/fashion2025/my-comfyui-models:v3`
- **Architecture**: True serverless with auto-scaling
- **GPU Type**: Based on model requirements

### Available Features

1. **Custom Models**
   - `epicrealismXL_vxviLastfameRealism.safetensors`
   - `fluxToolsRedux_reduxDev.safetensors`
   - FLUX models for advanced generation

2. **Custom Nodes**
   - WAS Node Suite (automated installation)
   - PuLID Flux (manual installation)
   - Impact Suite (manual installation)
   - DWPose Estimator
   - Style transfer nodes

3. **Capabilities**
   - Multiple input image support
   - Advanced FLUX model processing
   - Face detection and enhancement
   - Style transfer and color matching
   - Complex workflow pipelines

## API Integration

### Request Format

```json
{
  "input": {
    "images": [
      {
        "name": "face.png",
        "image": "data:image/png;base64,..."
      },
      {
        "name": "garment.png",
        "image": "data:image/png;base64,..."
      },
      {
        "name": "pose.png",
        "image": "data:image/png;base64,..."
      },
      {
        "name": "background.webp",
        "image": "data:image/webp;base64,..."
      }
    ],
    "workflow": {
      // ComfyUI workflow JSON
    }
  }
}
```

### Response Format

```json
{
  "id": "sync-uuid-string",
  "status": "COMPLETED",
  "output": {
    "images": [
      {
        "filename": "ComfyUI_00001_.png",
        "type": "base64",
        "data": "iVBORw0KGgoAAAANSUhEUg..."
      }
    ]
  },
  "delayTime": 123,
  "executionTime": 4567
}
```

## Workflow Examples

### Fashion Model Generation

This workflow combines:
- Face image for model identity
- Garment image for clothing
- Pose reference for body position
- Background image for scene

Key nodes used:
- `FluxProKontextMulti_fal` - Main generation
- `ApplyPulidFlux` - Face consistency
- `FluxUnionControlNetApply` - Pose control
- `StyleModelApply` - Style transfer

### Processing Pipeline

1. **Input Preparation**
   - Load and validate images
   - Create context windows
   - Apply masks and preprocessing

2. **Generation**
   - FLUX model inference
   - ControlNet application
   - Style transfer
   - Face enhancement

3. **Post-Processing**
   - Color matching
   - Upscaling
   - Detail enhancement
   - Final composition

## Deployment Process

### Custom Docker Image

The deployment uses a modified ComfyUI base image:

```dockerfile
# Base from official RunPod worker
FROM runpod/worker-comfyui:latest

# Add custom nodes
RUN git clone https://github.com/WASasquatch/was-node-suite-comfyui.git custom_nodes/was-node-suite-comfyui
RUN pip install -r custom_nodes/was-node-suite-comfyui/requirements.txt

# Custom models added via volume mounts
```

### Environment Variables

```bash
RUNPOD_HANDLER_MODULE=handler
RUNPOD_HANDLER_FUNCTION=handler
REFRESH_WORKER=false
SERVE_API_LOCALLY=false
testing=false
```

## Performance Optimization

### Resource Management
- **Container Disk**: Sized for custom models
- **Memory**: Optimized for workflow complexity
- **GPU Allocation**: Dynamic based on demand

### Best Practices
1. **Batch Processing**: Group similar requests
2. **Caching**: Reuse loaded models
3. **Timeout Handling**: Set appropriate timeouts
4. **Error Recovery**: Implement retry logic

## Cost Management

### Serverless Benefits
- **Pay-per-use**: Only charged for processing time
- **Auto-scaling**: Containers start/stop on demand
- **Resource Sharing**: Efficient GPU utilization

### Optimization Tips
1. Minimize workflow complexity
2. Use appropriate model sizes
3. Batch similar operations
4. Monitor usage patterns

## Monitoring & Debugging

### RunPod Dashboard
- Job status tracking
- Resource utilization
- Error logs
- Performance metrics

### Debug Steps
1. Check endpoint logs in RunPod
2. Test with simpler workflows
3. Monitor resource usage
4. Validate input formats

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| Queue delays | High demand | Implement retry logic |
| Model loading errors | Missing files | Verify model paths |
| Memory errors | Large workflows | Optimize workflow |
| Timeout errors | Complex processing | Increase timeout |

## Security Considerations

1. **API Key Protection**: Secure RunPod API keys
2. **Input Validation**: Validate image formats/sizes
3. **Container Isolation**: Each job runs isolated
4. **Access Control**: Limit endpoint access

## Integration with FashionLab

### Service Layer

```typescript
class ComfyUIService {
  async generateWithWorkflow(
    images: ImageInput[],
    workflow: WorkflowConfig
  ): Promise<GeneratedImage[]> {
    const response = await fetch(RUNPOD_ENDPOINT, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RUNPOD_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        input: { images, workflow }
      })
    });
    
    return this.processResponse(response);
  }
}
```

### Use Cases

1. **Advanced Fashion Generation**
   - Model + garment + pose combination
   - Style transfer between images
   - Background replacement

2. **Image Enhancement**
   - Face detail improvement
   - Upscaling with AI models
   - Color correction

3. **Creative Workflows**
   - Multiple style variations
   - Pose modifications
   - Virtual try-on

## Future Enhancements

1. **Workflow Templates**: Pre-built workflows for common tasks
2. **Model Library**: Expanded custom model collection
3. **Real-time Preview**: Streaming generation progress
4. **Cost Optimization**: Intelligent routing based on complexity

## Related Documentation

- [Fashion Lab API](./fashion-lab-api.md) - Primary AI integration
- [RunPod Documentation](https://docs.runpod.io/)
- [ComfyUI Documentation](https://github.com/comfyanonymous/ComfyUI)