# Local Testing Guide for Fashion Lab V2 API

## Overview

This guide helps developers test the Fashion Lab V2 API integration locally without hitting production services.

## Prerequisites

- Local Supabase instance running
- Node.js installed for test scripts
- Environment variables configured

## Setting Up Mock Mode

### 1. Configure Edge Functions for Mock Mode

When `FASHIONLAB_JWT_SECRET` is not set, edge functions automatically return mock responses:

```bash
# Start Supabase without Fashion Lab credentials
supabase start

# Functions will detect missing JWT secret and use mock mode
```

### 2. Mock Response Format

The edge functions return predictable mock data:

```json
// generate-images response
{
  "queue_id": "mock-1706543210000",
  "status": "processing",
  "images": [],
  "error": null
}

// queue-status response (completed)
{
  "queue_id": "mock-1706543210000",
  "status": "completed",
  "progress": 100,
  "images": [
    "https://placehold.co/1024x1024/purple/white?text=Mock+Image+1",
    "https://placehold.co/1024x1024/blue/white?text=Mock+Image+2"
  ],
  "metadata": {
    "model_name": "Mock Model",
    "prompt_used": "Mock generation for testing",
    "note": "This is a mock response for testing purposes"
  }
}
```

## Test Scripts

### 1. Basic Generation Test

Create `test-generation.js`:

```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'http://localhost:54321',
  'your-anon-key'
);

async function testGeneration() {
  // Sign in
  const { data: { session } } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'testpassword'
  });

  // Prepare test images (small base64 samples)
  const testImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

  // Call generate-images
  const response = await fetch('http://localhost:54321/functions/v1/generate-images', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      prompt: 'Test generation',
      face_image: testImage,
      image_2: testImage,
      image_3: testImage,
      image_4: testImage,
      collection_id: 'test-collection-id'
    })
  });

  const result = await response.json();
  console.log('Generation started:', result);

  // Poll for completion
  let attempts = 0;
  while (attempts < 5) {
    const statusResponse = await fetch('http://localhost:54321/functions/v1/queue-status', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        queue_id: result.queue_id,
        collection_id: 'test-collection-id',
        store_images: false
      })
    });

    const status = await statusResponse.json();
    console.log(`Attempt ${attempts + 1}:`, status);

    if (status.status === 'completed') {
      console.log('✅ Generation completed!');
      console.log('Images:', status.images);
      break;
    }

    attempts++;
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

testGeneration().catch(console.error);
```

### 2. Test with Timeout Simulation

Use special queue ID prefixes to simulate different scenarios:

```javascript
// Simulate timeout
const timeoutTest = {
  queue_id: 'timeout-test-123'
};

// Simulate immediate completion
const mockTest = {
  queue_id: 'mock-test-456'
};
```

### 3. Edge Function Direct Testing

Test edge functions directly with curl:

```bash
# Test generate-images
curl -X POST http://localhost:54321/functions/v1/generate-images \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Test prompt",
    "face_image": "data:image/png;base64,iVBORw0KGgo...",
    "image_2": "data:image/png;base64,iVBORw0KGgo...",
    "image_3": "data:image/png;base64,iVBORw0KGgo...",
    "image_4": "data:image/png;base64,iVBORw0KGgo...",
    "collection_id": "test-collection"
  }'

# Test queue-status
curl -X POST http://localhost:54321/functions/v1/queue-status \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "queue_id": "mock-123456",
    "collection_id": "test-collection",
    "store_images": false
  }'
```

## Frontend Testing

### 1. Mock Service for Development

Create a mock service that mimics the real service:

```typescript
// src/services/mockFashionLabService.ts
export class MockFashionLabImageService {
  static async generateImages(options: any) {
    console.log('Mock: Generating images', options);
    return {
      queue_id: `mock-${Date.now()}`
    };
  }

  static async checkQueueStatus(queueId: string) {
    console.log('Mock: Checking status', queueId);
    
    // Simulate processing for 3 checks, then complete
    const mockNumber = parseInt(queueId.split('-')[1]);
    const checkCount = Date.now() - mockNumber;
    
    if (checkCount > 10000) { // 10 seconds
      return {
        queue_id: queueId,
        status: 'completed',
        progress: 100,
        images: [
          'https://placehold.co/1024x1024/purple/white?text=Mock+1',
          'https://placehold.co/1024x1024/blue/white?text=Mock+2'
        ]
      };
    } else {
      return {
        queue_id: queueId,
        status: 'processing',
        progress: Math.min(90, (checkCount / 10000) * 100)
      };
    }
  }

  static async waitForCompletion(queueId: string, collectionId: string, options: any = {}) {
    const maxAttempts = 5;
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const status = await this.checkQueueStatus(queueId);
      
      if (options.onProgress) {
        options.onProgress(status.progress);
      }
      
      if (status.status === 'completed') {
        return status;
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error('Mock timeout');
  }
}
```

### 2. Environment-based Service Selection

```typescript
// src/services/imageGeneration.ts
import { FashionLabImageService } from './fashionLabImageService';
import { MockFashionLabImageService } from './mockFashionLabService';

// Use mock service in development without API keys
export const ImageService = import.meta.env.VITE_USE_MOCK_API === 'true' 
  ? MockFashionLabImageService 
  : FashionLabImageService;
```

## Testing Strategies

### 1. Unit Tests

Test individual functions with mocked fetch:

```typescript
// fashionLabImageService.test.ts
import { vi, describe, it, expect } from 'vitest';

describe('FashionLabImageService', () => {
  it('should handle generation start', async () => {
    const mockFetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => ({ queue_id: 'test-123' })
    });
    
    global.fetch = mockFetch;
    
    const result = await FashionLabImageService.generateImages({
      prompt: 'test',
      faceImage: 'base64...',
      // ... other params
    });
    
    expect(result.queue_id).toBe('test-123');
    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining('/generate-images'),
      expect.objectContaining({
        method: 'POST'
      })
    );
  });
});
```

### 2. Integration Tests

Test the full flow with local Supabase:

```typescript
// integration/imageGeneration.test.ts
describe('Image Generation Integration', () => {
  it('should complete full generation flow', async () => {
    // Start with real local Supabase
    const { queue_id } = await ImageService.generateImages({
      // ... test data
    });
    
    expect(queue_id).toMatch(/^mock-/);
    
    const result = await ImageService.waitForCompletion(
      queue_id,
      'test-collection'
    );
    
    expect(result.status).toBe('completed');
    expect(result.images).toHaveLength(2);
  });
});
```

## Debugging Tips

### 1. Enable Verbose Logging

```typescript
// Add to edge functions
console.log('Request body:', JSON.stringify(body, null, 2));
console.log('Fashion Lab response:', JSON.stringify(result, null, 2));
```

### 2. Check Edge Function Logs

```bash
# Watch logs in real-time
supabase functions logs --tail

# Filter for specific function
supabase functions logs generate-images --tail
```

### 3. Common Issues

**Issue**: "No JWT token provided"
- **Solution**: Ensure you're passing the Supabase session token

**Issue**: Images not storing in mock mode
- **Solution**: Mock mode doesn't actually store images, just returns URLs

**Issue**: Timeout in tests
- **Solution**: Increase polling intervals or max attempts

## Production Testing

### 1. Staging Environment

Test against staging with real API:

```bash
# Set staging variables
export VITE_SUPABASE_URL=https://qnfmiotatmkoumlymynq.supabase.co
export VITE_SUPABASE_ANON_KEY=your-staging-anon-key

# Run tests
npm run test:integration
```

### 2. API Response Validation

Validate real API responses match expected format:

```javascript
function validateQueueResponse(response) {
  const required = ['queue_id', 'status'];
  const valid = required.every(field => field in response);
  
  if (!valid) {
    console.error('Invalid response:', response);
    throw new Error('API response validation failed');
  }
  
  return response;
}
```

## Related Documentation

- [V2 API Flow](./fashion-lab-v2-flow.md) - Complete flow documentation
- [Fashion Lab API](./fashion-lab-api.md) - API integration guide
- [Edge Functions](../../supabase/functions/) - Function implementations