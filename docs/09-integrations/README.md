# External Integrations

## Overview

FashionLab integrates with external services to provide AI-powered image generation and advanced processing capabilities. This section documents how these external services connect with the platform.

## Available Integrations

### 1. Fashion Lab API

Our primary AI image generation service providing fashion-specific model generation.

**Key Features:**
- V2 image-based generation (face + garment + pose + background)
- Queue-based asynchronous processing
- Automatic image storage in Supabase
- JWT-based authentication (special format)

**Documentation:**
- [Fashion Lab API Integration](./fashion-lab-api.md) - Complete integration guide
- [V2 API Flow Details](./fashion-lab-v2-flow.md) - Technical deep dive
- [Local Testing Guide](./local-testing-guide.md) - Development setup

### 2. ComfyUI Serverless

Advanced image processing using ComfyUI workflows on RunPod infrastructure.

**Key Features:**
- Custom FLUX models and nodes
- Complex workflow processing
- Style transfer and enhancement
- Serverless auto-scaling

**Documentation:**
- [ComfyUI Integration](./comfyui-integration.md) - RunPod endpoint setup

## Integration Architecture

```
FashionLab Platform
    ↓
Edge Functions (Authentication & Orchestration)
    ├── Fashion Lab API → AI Model Generation
    └── ComfyUI/RunPod → Advanced Processing
```

## Authentication Patterns

### Fashion Lab API
- Uses custom JWT format: `Authorization: jwt ${token}`
- Tokens created on-demand by edge functions
- Frontend never handles Fashion Lab tokens directly

### ComfyUI/RunPod
- API key authentication
- Managed at service layer

## Common Integration Patterns

### 1. Asynchronous Processing

All AI operations use queue-based processing:

```typescript
// 1. Submit job
const { queue_id } = await service.generateImages(params);

// 2. Poll for completion
const result = await service.waitForCompletion(queue_id);

// 3. Store results
await service.storeGeneratedImages(result.images);
```

### 2. Image Storage Flow

Generated images follow this path:
1. External service generates images
2. Edge function downloads images
3. Images stored in Supabase Storage
4. Database records created
5. Frontend accesses via public URLs

### 3. Error Handling

Consistent error handling across integrations:
- Retry with exponential backoff
- Fallback to original URLs on storage failure
- Comprehensive error logging
- User-friendly error messages

## Security Considerations

1. **API Credentials**: Stored in Supabase secrets, never exposed to frontend
2. **Token Management**: Edge functions handle all external API authentication
3. **Access Control**: RLS policies enforce data isolation
4. **Input Validation**: All inputs validated before sending to external services

## Performance Optimization

1. **Caching**: Results cached where appropriate
2. **Parallel Processing**: Multiple operations batched when possible
3. **CDN Delivery**: Generated content served via CDN
4. **Resource Management**: Automatic scaling based on demand

## Monitoring & Debugging

### Key Metrics
- Generation success rates
- Average processing time
- API error rates
- Storage success rates

### Debug Tools
- Edge function logs: `supabase functions logs [function-name]`
- External service dashboards
- Database query tools

## Adding New Integrations

When adding new external services:

1. **Create Edge Function**: Handle authentication and API calls
2. **Document API Format**: Especially authentication requirements
3. **Implement Storage**: Follow existing patterns for asset storage
4. **Add Error Handling**: Consistent with platform patterns
5. **Create Testing Guide**: Include mock mode for development

## Related Documentation

- [API Reference](../04-api/README.md) - Platform API documentation
- [Edge Functions](../04-api/endpoints/edge-functions.md) - Edge function reference
- [Database Schema](../05-database/schema.md) - Storage structure