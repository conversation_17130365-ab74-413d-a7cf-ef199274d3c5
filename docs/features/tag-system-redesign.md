# Tag System Redesign: Global vs Collection-Specific Tags

## Current State Analysis

### Problems with Current Implementation
1. **Tag Pollution**: All tags are global, leading to accumulation of test tags ("asdf", "123as", etc.)
2. **No Privacy**: Tags created by one organization are visible to all others
3. **No Organization**: Cannot have collection-specific categorization
4. **Scalability Issues**: As more organizations join, the global tag list becomes unwieldy

### Current Database Structure
- `tags` table has no `organization_id` or `collection_id` column
- All tags are shared globally across the entire platform
- Tags like "Front view", "Back view", "Details" are used by multiple organizations

## Proposed Solution: Hybrid Tag System

### Core Concept
Implement a two-tier tag system:
1. **Global Tags**: Platform-wide tags useful for all collections (e.g., view types, standard workflow stages)
2. **Collection-Specific Tags**: Tags that only exist within a specific collection's context

### Database Changes

#### 1. Schema Updates
```sql
-- Add collection_id to tags table
ALTER TABLE tags 
ADD COLUMN collection_id UUID REFERENCES collections(id) ON DELETE CASCADE;

-- Add index for performance
CREATE INDEX idx_tags_collection_id ON tags(collection_id);

-- Add composite unique constraint
CREATE UNIQUE INDEX idx_tags_unique_name_per_scope 
ON tags(name, COALESCE(collection_id, '00000000-0000-0000-0000-000000000000'::uuid));
```

#### 2. Tag Categories Update
- **Always Global**:
  - `workflow_stage`: Standard workflow stages
  - `view_type`: Front, back, side, detail views
- **Collection-Specific by Default**:
  - `custom`: User-created tags
  - `product_specific`: Product-related tags
- **New Category**:
  - `global_custom`: User-created tags promoted to global

#### 3. RLS Policy Updates
```sql
-- View policy: See global tags + tags from accessible collections
CREATE POLICY "Users can view relevant tags" ON tags
FOR SELECT USING (
  collection_id IS NULL -- Global tags
  OR
  collection_id IN (
    SELECT c.id FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- Create policy: Can create tags in accessible collections
CREATE POLICY "Users can create collection tags" ON tags
FOR INSERT WITH CHECK (
  collection_id IN (
    SELECT c.id FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
    AND (
      (SELECT role FROM users WHERE id = auth.uid()) 
      IN ('brand_admin', 'brand_member', 'platform_super', 'platform_admin')
    )
  )
);
```

### Frontend Implementation

#### 1. UI/UX Changes

**Tag Display**:
- Add visual indicators for tag scope:
  - 🌍 Globe icon for global tags
  - 📁 Folder icon for collection-specific tags
- Group tags by scope in dropdowns and filters
- Add tooltip explaining tag scope

**Tag Creation Flow**:
```typescript
interface TagCreationOptions {
  name: string;
  category: TagCategory;
  makeGlobal?: boolean; // Only for platform admins
  collectionId?: string;
}
```

**Filter Sidebar Updates**:
- Show only relevant tags (global + current collection)
- Add section headers: "Campaign Tags" and "Global Tags"
- Sort collection tags above global tags for relevance

#### 2. Component Updates

**AssetTagsManager.tsx**:
- Display tag scope indicator
- Prevent duplicate names within same scope
- Show collection name for collection-specific tags in search

**BulkTagManager.tsx**:
- Group available tags by scope
- Add "Create Global Tag" option for platform admins
- Show tag origin in multi-collection contexts

**FilterSidebar.tsx**:
- Separate sections for global vs collection tags
- Add tag count by scope
- Implement scope-aware search

### Data Migration Strategy

#### Phase 1: Categorize Existing Tags
```sql
-- Identify commonly used tags that should be global
WITH tag_usage AS (
  SELECT 
    t.id,
    t.name,
    COUNT(DISTINCT c.organization_id) as org_count,
    COUNT(DISTINCT at.asset_id) as usage_count
  FROM tags t
  LEFT JOIN asset_tags at ON t.id = at.tag_id
  LEFT JOIN assets a ON at.asset_id = a.id
  LEFT JOIN collections c ON a.collection_id = c.id
  GROUP BY t.id, t.name
)
SELECT * FROM tag_usage WHERE org_count > 1 ORDER BY usage_count DESC;
```

#### Phase 2: Clean Up Test Tags
1. Identify tags with low usage and test-like names
2. Assign to specific collections based on asset associations
3. Delete truly unused tags

#### Phase 3: Set Proper Categories
- View-related tags → `view_type` category
- Workflow tags → `workflow_stage` category
- Meaningful custom tags → Keep as `custom` with collection assignment

### Implementation Checklist

- [ ] Create database migration for schema changes
- [ ] Update TypeScript types in `database.types.ts`
- [ ] Modify `useTags` hooks to include collection filtering
- [ ] Update tag creation components with scope selection
- [ ] Add visual indicators for tag scope in UI
- [ ] Implement duplicate prevention within scope
- [ ] Create data migration scripts
- [ ] Update API endpoints to handle collection context
- [ ] Add tests for new tag scoping logic
- [ ] Update documentation

### Additional Considerations

#### Performance
- Index on `collection_id` for fast filtering
- Consider caching global tags separately
- Optimize queries to avoid N+1 problems

#### Permissions
- Who can create global tags? (Platform admins only?)
- Can collection admins promote tags to global?
- Should external users see global tags?

#### Future Enhancements
1. **Tag Templates**: Pre-defined tag sets for new collections
2. **Tag Hierarchies**: Parent-child relationships for tags
3. **Tag Aliases**: Multiple names for the same concept
4. **Tag Merging**: Tools to consolidate duplicate tags
5. **Organization-Level Tags**: Mid-tier between global and collection

#### Backward Compatibility
- Existing tag IDs remain unchanged
- Current tag associations preserved
- Gradual migration path for organizations

#### Search Implications
- Update search to consider tag scope
- Add "search in global tags" option
- Consider tag scope in relevance ranking

### Risks and Mitigation

1. **Migration Complexity**
   - Risk: Data migration could affect existing tags
   - Mitigation: Comprehensive backup and rollback plan

2. **User Confusion**
   - Risk: Users confused by two tag scopes
   - Mitigation: Clear UI indicators and tooltips

3. **Performance Impact**
   - Risk: Additional filtering could slow queries
   - Mitigation: Proper indexing and query optimization

### Success Metrics
- Reduction in total tag count
- Decrease in "junk" tags
- Improved tag search relevance
- User satisfaction with tag organization
- Performance metrics maintained

### Timeline Estimate
1. Database changes: 1 day
2. Backend updates: 2 days
3. Frontend updates: 3 days
4. Data migration: 1 day
5. Testing: 2 days
6. Documentation: 1 day

Total: ~10 days of development work

### Questions for Product Team
1. Should platform admins be the only ones who can create global tags?
2. Do we want organization-level tags as a future enhancement?
3. Should we implement tag usage analytics?
4. What's the process for promoting collection tags to global?
5. Should we limit the number of tags per collection?