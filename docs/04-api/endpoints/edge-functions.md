# Edge Functions API Reference

## Overview

FashionLab uses Supabase Edge Functions for serverless API endpoints. These functions handle image generation, queue management, and external API integrations.

## Base URL

```
Development: http://localhost:54321/functions/v1
Staging: https://qnfmiotatmkoumlymynq.supabase.co/functions/v1
Production: https://cpelxqvcjnbpnphttzsn.supabase.co/functions/v1
```

## Authentication

All edge functions require Supabase JWT authentication:

```http
Authorization: Bearer YOUR_SUPABASE_JWT_TOKEN
Content-Type: application/json
```

## Available Functions

### 1. Generate Images

Initiates AI image generation using Fashion Lab API.

**Endpoint**: `POST /functions/v1/generate-images`

**Request Body**:
```json
{
  "prompt": "Fashion model wearing elegant dress",
  "face_image": "data:image/jpeg;base64,...",
  "image_2": "data:image/jpeg;base64,...",
  "image_3": "data:image/jpeg;base64,...",
  "image_4": "data:image/jpeg;base64,...",
  "collection_id": "uuid",
  "seed1": 12345,
  "seed2": 67890,
  "seed3": 11111,
  "seed4": 22222
}
```

**Response**:
```json
{
  "queue_id": "q_abc123def456",
  "status": "processing",
  "images": [],
  "error": null
}
```

**Error Responses**:
- `401 Unauthorized` - Invalid or missing authentication token
- `400 Bad Request` - Missing required fields
- `500 Internal Server Error` - Fashion Lab API error

### 2. Queue Status

Checks generation status and optionally stores completed images.

**Endpoint**: `POST /functions/v1/queue-status`

**Request Body**:
```json
{
  "queue_id": "q_abc123def456",
  "collection_id": "uuid",
  "store_images": true,
  "prompt": "Original prompt used"
}
```

**Response (Processing)**:
```json
{
  "queue_id": "q_abc123def456",
  "status": "processing",
  "progress": 50,
  "images": [],
  "error": null
}
```

**Response (Completed)**:
```json
{
  "queue_id": "q_abc123def456",
  "status": "completed",
  "progress": 100,
  "images": [
    "https://xxx.supabase.co/storage/v1/object/public/ai-generated/...",
    "https://xxx.supabase.co/storage/v1/object/public/ai-generated/..."
  ],
  "stored": true,
  "metadata": {
    "executionTime": 45.2,
    "model": "fashion-lab-v2"
  }
}
```

### 3. Fashion Lab Proxy

Proxies requests to Fashion Lab API with authentication.

**Endpoint**: `POST /functions/v1/fashion-lab-proxy/*`

**Usage**: Replace Fashion Lab API URLs with proxy endpoint:
```
https://fashionlab.notfirst.rodeo/generate-image-v2
→ 
/functions/v1/fashion-lab-proxy/generate-image-v2
```

**Headers**: Same as direct API calls, but uses Supabase JWT

### 4. Fashion Lab Image Proxy

Downloads images from Fashion Lab with proper authentication.

**Endpoint**: `GET /functions/v1/fashion-lab-image-proxy`

**Query Parameters**:
- `url` - The Fashion Lab image URL to download

**Example**:
```
GET /functions/v1/fashion-lab-image-proxy?url=https://fashionlab.notfirst.rodeo/images/abc123.jpg
```

### 5. Send Email

Sends transactional emails using Resend.

**Endpoint**: `POST /functions/v1/send-email`

**Request Body**:
```json
{
  "to": ["<EMAIL>"],
  "subject": "Your images are ready",
  "html": "<p>Your generated images are now available.</p>",
  "from": "FashionLab <<EMAIL>>"
}
```

**Response**:
```json
{
  "success": true,
  "messageId": "msg_123abc"
}
```

## Error Handling

All edge functions return consistent error responses:

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Detailed error message",
    "details": {
      "field": "prompt",
      "reason": "Required field missing"
    }
  }
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Invalid or missing authentication |
| `FORBIDDEN` | Insufficient permissions |
| `INVALID_REQUEST` | Request validation failed |
| `EXTERNAL_API_ERROR` | Fashion Lab API error |
| `STORAGE_ERROR` | Failed to store images |
| `DATABASE_ERROR` | Database operation failed |

## Rate Limiting

Edge functions have the following limits:

- **Timeout**: 25 seconds per request
- **Payload Size**: 6MB for request body
- **Concurrent Requests**: 1000 per instance

## Development

### Local Testing

```bash
# Start local Supabase
supabase start

# Serve functions locally
supabase functions serve

# Test with curl
curl -X POST http://localhost:54321/functions/v1/generate-images \
  -H "Authorization: Bearer YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "test", "collection_id": "uuid"}'
```

### Deployment

```bash
# Deploy a specific function
supabase functions deploy generate-images

# Deploy all functions
supabase functions deploy

# Set secrets
supabase secrets set FASHIONLAB_JWT_SECRET=your-secret
```

### Environment Variables

Required environment variables for edge functions:

```env
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-key
FASHIONLAB_JWT_SECRET=fashion-lab-jwt-secret
RESEND_API_KEY=your-resend-key
```

## Monitoring

### View Logs

```bash
# Stream logs for a function
supabase functions logs generate-images --tail

# View recent logs
supabase functions logs queue-status --limit 100
```

### Metrics

Monitor function performance in Supabase Dashboard:
- Invocation count
- Error rate
- Average duration
- Memory usage

## Best Practices

1. **Error Handling**: Always wrap function logic in try-catch
2. **Validation**: Validate all inputs before processing
3. **Timeouts**: Implement request timeouts for external APIs
4. **Logging**: Log key events for debugging
5. **Security**: Never log sensitive data like tokens

## Related Documentation

- [Authentication Guide](../authentication.md)
- [REST API Reference](./rest-api.md)
- [Fashion Lab Integration](../../09-integrations/fashion-lab-api.md)