# REST API Reference

## Overview

FashionLab provides a RESTful API for managing assets, collections, products, and organizations. This API is powered by Supabase and PostgREST.

## Base URL

```
Development: http://localhost:54321/rest/v1
Staging: https://qnfmiotatmkoumlymynq.supabase.co/rest/v1
Production: https://cpelxqvcjnbpnphttzsn.supabase.co/rest/v1
```

## Authentication

All requests require a valid Supabase JWT token:

```http
Authorization: Bearer YOUR_JWT_TOKEN
apikey: YOUR_ANON_KEY
Content-Type: application/json
```

## Common Parameters

### Filtering

Use query parameters to filter results:

```
?column=eq.value     # Equals
?column=neq.value    # Not equals
?column=gt.value     # Greater than
?column=gte.value    # Greater than or equal
?column=lt.value     # Less than
?column=lte.value    # Less than or equal
?column=like.*value* # Pattern matching
?column=in.(1,2,3)   # In list
?column=is.null      # Is null
?column=is.not.null  # Is not null
```

### Sorting

```
?order=column.asc    # Ascending order
?order=column.desc   # Descending order
```

### Pagination

```
?limit=20           # Limit results
?offset=40          # Skip results
```

### Selecting Columns

```
?select=id,name,created_at              # Select specific columns
?select=*,collection(*)                 # Include relations
?select=*,collection!inner(*)           # Inner join
```

## Endpoints

### Assets

#### List Assets

```http
GET /rest/v1/assets
```

**Query Parameters**:
- `collection_id` - Filter by collection
- `workflow_stage` - Filter by stage (raw, selected, approved, final)
- `organization_id` - Filter by organization
- `limit` - Number of results (default: 50)
- `offset` - Pagination offset

**Example**:
```http
GET /rest/v1/assets?collection_id=eq.uuid&workflow_stage=eq.final&select=*,collection(name)
```

#### Get Asset

```http
GET /rest/v1/assets?id=eq.{asset_id}
```

#### Create Asset

```http
POST /rest/v1/assets
Content-Type: application/json

{
  "collection_id": "uuid",
  "product_id": "uuid",
  "workflow_stage": "raw",
  "storage_path": "org-uuid/assets/file.jpg",
  "tags": ["summer", "dress"],
  "metadata": {
    "angle": "front",
    "color": "blue"
  }
}
```

#### Update Asset

```http
PATCH /rest/v1/assets?id=eq.{asset_id}
Content-Type: application/json

{
  "workflow_stage": "approved",
  "tags": ["summer", "dress", "bestseller"]
}
```

#### Delete Asset

```http
DELETE /rest/v1/assets?id=eq.{asset_id}
```

### Collections

#### List Collections

```http
GET /rest/v1/collections
```

**Query Parameters**:
- `organization_id` - Filter by organization
- `is_archived` - Filter archived collections
- `search` - Search in name/description

**Example**:
```http
GET /rest/v1/collections?organization_id=eq.uuid&is_archived=eq.false&select=*,assets(count)
```

#### Get Collection

```http
GET /rest/v1/collections?id=eq.{collection_id}
```

#### Create Collection

```http
POST /rest/v1/collections
Content-Type: application/json

{
  "organization_id": "uuid",
  "name": "Summer 2024",
  "description": "Summer collection photoshoot",
  "metadata": {
    "season": "summer",
    "year": 2024
  }
}
```

#### Update Collection

```http
PATCH /rest/v1/collections?id=eq.{collection_id}
Content-Type: application/json

{
  "name": "Summer 2024 - Final",
  "is_archived": false
}
```

### Products

#### List Products

```http
GET /rest/v1/products
```

**Query Parameters**:
- `organization_id` - Filter by organization
- `sku` - Filter by SKU
- `is_active` - Filter active products

#### Get Product

```http
GET /rest/v1/products?id=eq.{product_id}
```

#### Create Product

```http
POST /rest/v1/products
Content-Type: application/json

{
  "organization_id": "uuid",
  "sku": "DRESS-001",
  "name": "Summer Dress",
  "description": "Light summer dress",
  "metadata": {
    "color": "blue",
    "size": ["S", "M", "L"],
    "material": "cotton"
  }
}
```

### Organizations

#### Get Organization

```http
GET /rest/v1/organizations?id=eq.{org_id}
```

#### List User's Organizations

```http
GET /rest/v1/organization_memberships?user_id=eq.{user_id}&select=*,organization(*)
```

#### Update Organization

```http
PATCH /rest/v1/organizations?id=eq.{org_id}
Content-Type: application/json

{
  "settings": {
    "default_workflow": ["raw", "selected", "approved", "final"],
    "storage_quota_gb": 100
  }
}
```

### AI Generated Images

#### List Generated Images

```http
GET /rest/v1/ai_generated_images?collection_id=eq.{collection_id}
```

#### Get Generated Image

```http
GET /rest/v1/ai_generated_images?id=eq.{image_id}
```

#### Mark Image as Selected

```http
PATCH /rest/v1/ai_generated_images?id=eq.{image_id}
Content-Type: application/json

{
  "selected": true
}
```

## Response Format

### Success Response

```json
[
  {
    "id": "uuid",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    // ... other fields
  }
]
```

### Error Response

```json
{
  "code": "PGRST116",
  "details": null,
  "hint": null,
  "message": "JWT expired"
}
```

## Batch Operations

### Insert Multiple Records

```http
POST /rest/v1/assets
Content-Type: application/json

[
  { "collection_id": "uuid1", "workflow_stage": "raw" },
  { "collection_id": "uuid2", "workflow_stage": "raw" }
]
```

### Update Multiple Records

```http
PATCH /rest/v1/assets?collection_id=eq.{collection_id}
Content-Type: application/json

{
  "workflow_stage": "approved"
}
```

### Delete Multiple Records

```http
DELETE /rest/v1/assets?collection_id=eq.{collection_id}&workflow_stage=eq.raw
```

## Advanced Queries

### Full-Text Search

```http
GET /rest/v1/products?or=(name.fts.dress,description.fts.dress)
```

### Complex Filters

```http
GET /rest/v1/assets?and=(workflow_stage.eq.final,created_at.gte.2024-01-01)&organization_id=eq.uuid
```

### Aggregations

```http
GET /rest/v1/assets?select=collection_id,count(*)&group_by=collection_id
```

## RLS (Row Level Security)

All endpoints automatically filter results based on the authenticated user's permissions:

- Users can only access data from their organizations
- Platform admins have access to all data
- Soft-deleted records are hidden by default

## Rate Limiting

- **Authenticated requests**: 1000/hour per user
- **Bulk operations**: 100/minute
- **Large queries**: Use pagination for results > 1000 rows

## Best Practices

1. **Use Select**: Only request fields you need
2. **Paginate**: Always paginate large result sets
3. **Filter Early**: Apply filters to reduce data transfer
4. **Use Transactions**: For related operations
5. **Handle Errors**: Implement proper error handling

## Related Documentation

- [Authentication Guide](../authentication.md)
- [Edge Functions](./edge-functions.md)
- [Database Schema](../../05-database/schema.md)