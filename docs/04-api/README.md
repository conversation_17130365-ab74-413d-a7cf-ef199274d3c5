# API Documentation

## Overview

FashionLab provides comprehensive APIs for asset management, AI operations, and platform functionality. This documentation covers the platform's REST APIs, Edge Functions, and authentication patterns.

## Quick Links

- [Authentication Guide](./authentication.md) - Supabase authentication setup
- [REST API Reference](./endpoints/rest-api.md) - Database operations via PostgREST
- [Edge Functions](./endpoints/edge-functions.md) - Serverless function endpoints

## API Architecture

### Base URLs

#### REST API (PostgREST)
- **Development**: `http://localhost:54321/rest/v1`
- **Staging**: `https://qnfmiotatmkoumlymynq.supabase.co/rest/v1`
- **Production**: `https://cpelxqvcjnbpnphttzsn.supabase.co/rest/v1`

#### Edge Functions
- **Development**: `http://localhost:54321/functions/v1`
- **Staging**: `https://qnfmiotatmkoumlymynq.supabase.co/functions/v1`
- **Production**: `https://cpelxqvcjnbpnphttzsn.supabase.co/functions/v1`

### API Types

1. **REST API** - Direct database operations via PostgREST
2. **Edge Functions** - Business logic and external integrations
3. **Storage API** - File upload and management

## Authentication

All API requests require authentication using Supabase JWT tokens.

### Request Headers

```http
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json
```

### Getting a Token

```javascript
// Login and get token
const { data: { session } } = await supabase.auth.signIn({
  email: '<EMAIL>',
  password: 'password'
});

const token = session.access_token;
```

## Core Endpoints

### Asset Management

```http
GET    /rest/v1/assets              # List assets
POST   /rest/v1/assets              # Create asset
GET    /rest/v1/assets/:id          # Get asset
PATCH  /rest/v1/assets/:id          # Update asset
DELETE /rest/v1/assets/:id          # Delete asset
```

### Collections

```http
GET    /rest/v1/collections         # List collections
POST   /rest/v1/collections         # Create collection
GET    /rest/v1/collections/:id     # Get collection
PATCH  /rest/v1/collections/:id     # Update collection
```

### Image Generation

```http
POST   /functions/v1/generate-images # Generate AI images
POST   /functions/v1/fashion-lab-proxy # Fashion Lab API proxy
```

## Request/Response Examples

### Create Asset

**Request:**
```http
POST /rest/v1/assets
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "collection_id": "uuid-here",
  "product_id": "uuid-here",
  "workflow_stage": "raw",
  "tags": ["summer", "dress"],
  "metadata": {
    "angle": "front",
    "generated_by": "fashion-lab"
  }
}
```

**Response:**
```json
{
  "id": "asset-uuid",
  "collection_id": "uuid-here",
  "product_id": "uuid-here",
  "workflow_stage": "raw",
  "storage_path": "org-uuid/assets/asset-uuid.jpg",
  "created_at": "2024-01-15T10:30:00Z",
  "tags": ["summer", "dress"],
  "metadata": {
    "angle": "front",
    "generated_by": "fashion-lab"
  }
}
```

### Generate Images

**Request:**
```http
POST /functions/v1/generate-images
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "prompt": "elegant summer dress on model",
  "model_id": "model-uuid",
  "angles": ["front", "back", "side"],
  "settings": {
    "quality": "high",
    "style": "editorial"
  }
}
```

**Response:**
```json
{
  "job_id": "job-uuid",
  "status": "processing",
  "images": [],
  "estimated_time": 120
}
```

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Validation failed",
    "details": {
      "field": "prompt",
      "reason": "Required field missing"
    }
  }
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `UNAUTHORIZED` | 401 | Missing or invalid token |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `INVALID_REQUEST` | 400 | Validation error |
| `RATE_LIMITED` | 429 | Too many requests |
| `SERVER_ERROR` | 500 | Internal error |

## Rate Limiting

- **Authenticated requests**: 1000/hour
- **Image generation**: 100/hour
- **Bulk operations**: 10/minute

Rate limit headers:
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642345678
```

## Pagination

List endpoints support pagination:

```http
GET /rest/v1/assets?limit=20&offset=0
```

Response includes pagination metadata:
```json
{
  "data": [...],
  "pagination": {
    "total": 150,
    "limit": 20,
    "offset": 0,
    "has_more": true
  }
}
```

## Filtering and Sorting

### Filtering
```http
GET /rest/v1/assets?workflow_stage=eq.final&organization_id=eq.org-uuid
```

### Sorting
```http
GET /rest/v1/assets?order=created_at.desc
```

### Complex Queries
```http
GET /rest/v1/assets?tags=cs.{summer,dress}&created_at=gte.2024-01-01
```

## Webhooks

Configure webhooks for async events:

```json
{
  "url": "https://your-app.com/webhooks/fashionlab",
  "events": ["image.generated", "asset.processed"],
  "secret": "webhook-secret"
}
```

## SDK Usage

### JavaScript/TypeScript

```typescript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// List assets
const { data, error } = await supabase
  .from('assets')
  .select('*')
  .eq('workflow_stage', 'final')
  .order('created_at', { ascending: false });
```

## Best Practices

1. **Always handle errors** - Check for error responses
2. **Use pagination** - Don't fetch all records at once
3. **Cache responses** - Reduce API calls
4. **Respect rate limits** - Implement exponential backoff
5. **Secure tokens** - Never expose tokens in client code

## Testing

### Test Environment
- Base URL: `https://staging.fashionlab.ai/api`
- Use test accounts and data
- Rate limits are relaxed

### Postman Collection
Import our [Postman collection](./fashionlab-api.postman.json) for testing.

## Migration Guide

For migrating from v1 to v2 API, see [Migration Guide](./migration-v1-to-v2.md).

## Related Documentation

- [Authentication Guide](./authentication.md)
- [REST API Reference](./endpoints/rest-api.md)
- [Edge Functions Reference](./endpoints/edge-functions.md)
- [External Integrations](../09-integrations/README.md)
- [Database Schema](../05-database/schema.md)