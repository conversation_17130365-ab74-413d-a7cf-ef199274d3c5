# Authentication Guide

## Overview

FashionLab uses Supabase Auth for user authentication and session management. This guide covers authentication flows, token management, and security best practices.

## Prerequisites

- Supabase project with Auth enabled
- Client application configured with Supabase keys

## Authentication Architecture

```mermaid
graph TD
    A[Client App] -->|1. Login| B[Supabase Auth]
    B -->|2. Session Token| A
    A -->|3. API Request| C[REST API/Edge Functions]
    C -->|4. Validate <PERSON>ken| D[Supabase Auth]
    D -->|5. User Context| C
    C -->|6. Response| A
```

## Authentication Flows

### 1. User Login

```javascript
// Sign in with email/password
const { data: { session }, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'secure-password'
});

// Access token for API calls
const accessToken = session.access_token;
```

### 2. Magic Link Authentication

```javascript
// Send magic link
const { error } = await supabase.auth.signInWithOtp({
  email: '<EMAIL>',
  options: {
    emailRedirectTo: 'https://app.fashionlab.ai/auth/callback'
  }
});
```

### 3. OAuth Providers

```javascript
// Sign in with Google
const { data, error } = await supabase.auth.signInWithOAuth({
  provider: 'google',
  options: {
    redirectTo: 'https://app.fashionlab.ai/auth/callback'
  }
});
```

## Token Management

### Supabase Session Token

Used for all Supabase operations:

```javascript
// Get current session
const { data: { session } } = await supabase.auth.getSession();

// Use in requests
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': `Bearer ${session.access_token}`
  }
});
```

### Token Refresh

Supabase automatically refreshes tokens before expiration:

```javascript
// Manual token refresh
const { data: { session }, error } = await supabase.auth.refreshSession();

if (error) {
  // Handle refresh failure
  console.error('Token refresh failed:', error);
  // Redirect to login
  window.location.href = '/login';
}

// Use refreshed token
const newToken = session.access_token;
```

## API Authentication

### Request Headers

All authenticated requests require:

```http
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json
```

### Token Validation

Tokens are validated at multiple levels:

1. **Edge Function**: Validates Supabase session
2. **RLS Policies**: Database-level access control
3. **Application Logic**: Additional permission checks

## Security Implementation

### Edge Function Authentication

```typescript
// Validate request authentication
const jwt = req.headers.get('Authorization')?.replace('Bearer ', '');

if (!jwt) {
  return new Response('Unauthorized', { status: 401 });
}

// Verify JWT
const { data: { user }, error } = await supabase.auth.getUser(jwt);

if (error || !user) {
  return new Response('Invalid token', { status: 401 });
}

// Check organization membership
const { data: membership } = await supabase
  .from('organization_members')
  .select('role')
  .eq('user_id', user.id)
  .eq('organization_id', organizationId)
  .single();

if (!membership) {
  return new Response('Access denied', { status: 403 });
}
```

### RLS Policies

Database-level security:

```sql
-- Users can only access their organization's data
CREATE POLICY "Users access own org data" ON assets
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid()
    )
  );
```

## Session Management

### Auto-refresh

```javascript
// Set up auto-refresh
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'TOKEN_REFRESHED') {
    console.log('Token refreshed successfully');
  }
});
```

### Manual Refresh

```javascript
// Manually refresh session
const { data: { session }, error } = await supabase.auth.refreshSession();

if (error) {
  // Handle refresh failure - redirect to login
  window.location.href = '/login';
}
```

### Logout

```javascript
// Sign out user
const { error } = await supabase.auth.signOut();

// Clear local data
localStorage.clear();
sessionStorage.clear();

// Redirect to login
window.location.href = '/login';
```

## Protected Routes

### React Router Protection

```typescript
// ProtectedRoute component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { session, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!session) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
}

// Usage
<Route path="/dashboard" element={
  <ProtectedRoute>
    <Dashboard />
  </ProtectedRoute>
} />
```

### API Route Protection

```typescript
// Middleware for API routes
export async function requireAuth(req: Request) {
  const token = req.headers.get('Authorization')?.replace('Bearer ', '');
  
  if (!token) {
    throw new Response('Unauthorized', { status: 401 });
  }
  
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Response('Invalid token', { status: 401 });
  }
  
  return user;
}
```

## Role-Based Access Control

### User Roles

| Role | Permissions |
|------|-------------|
| `platform_admin` | Full system access |
| `org_admin` | Manage organization |
| `org_member` | Access organization data |
| `freelancer` | Limited access |

### Permission Checks

```typescript
// Check user role
export async function hasRole(userId: string, role: string): Promise<boolean> {
  const { data } = await supabase
    .from('organization_members')
    .select('role')
    .eq('user_id', userId)
    .eq('role', role)
    .single();
    
  return !!data;
}

// Usage in component
const canEdit = await hasRole(user.id, 'org_admin');
```

## Common Authentication Patterns

### Auth Context Provider

```typescript
// contexts/AuthContext.tsx
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    // Listen for changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setSession(session);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ session, loading }}>
      {children}
    </AuthContext.Provider>
  );
}
```

### useAuth Hook

```typescript
// hooks/useAuth.ts
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  
  return context;
}
```

## Troubleshooting

### Common Issues

| Issue | Cause | Solution |
|-------|-------|----------|
| 401 Unauthorized | Expired token | Refresh session |
| 403 Forbidden | Missing permissions | Check user role |
| Token refresh fails | Network issue | Retry with backoff |
| Session lost | Cookie blocked | Check browser settings |

### Debug Authentication

```javascript
// Log authentication state
supabase.auth.onAuthStateChange((event, session) => {
  console.log('Auth event:', event);
  console.log('Session:', session);
});

// Check current session
const { data: { session }, error } = await supabase.auth.getSession();
console.log('Current session:', session);
console.log('Session valid until:', new Date(session.expires_at * 1000));
```

## Security Best Practices

1. **Token Storage**
   - Use httpOnly cookies for sensitive tokens
   - Never store tokens in localStorage for sensitive apps
   - Clear tokens on logout

2. **Token Validation**
   - Always validate tokens server-side
   - Check token expiration
   - Verify token signature

3. **Rate Limiting**
   - Implement login attempt limits
   - Use captcha for repeated failures
   - Monitor suspicious activity

4. **CORS Configuration**
   - Restrict allowed origins
   - Validate referrer headers
   - Use CSRF tokens

## Related Documentation

- [API Reference](./README.md)
- [REST API Endpoints](./endpoints/rest-api.md)
- [Edge Functions](./endpoints/edge-functions.md)
- [Security Best Practices](../07-security/best-practices.md)
- [User Management](../02-features/organizations/README.md)