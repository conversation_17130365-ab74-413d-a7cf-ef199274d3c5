# Troubleshooting Guide

## Overview

This section helps you diagnose and resolve common issues with FashionLab. Start with the quick fixes, then move to specific problem areas.

## Quick Fixes

### Try These First
1. **Clear browser cache** - Ctrl+Shift+R (Cmd+Shift+R on Mac)
2. **Check internet connection** - Ensure stable connectivity
3. **Update browser** - Use latest Chrome, Firefox, Safari, or Edge
4. **Disable extensions** - Ad blockers may interfere
5. **Check status page** - status.fashionlab.ai

## Common Issues

### Authentication Problems

#### Can't Log In
- **Wrong credentials** → Reset password
- **Account locked** → Contact support
- **2FA issues** → Use backup codes
- **Session expired** → Log in again

#### Permission Denied
- **Wrong organization** → Switch organization
- **Insufficient role** → Contact admin
- **Expired invitation** → Request new invite

### Upload Issues

#### Upload Fails
```
Error: Upload failed
```
**Solutions:**
- Check file size (max 10MB free, 50MB pro)
- Verify file format (JPG, PNG, WebP)
- Check storage quota
- Try smaller batches

#### Processing Stuck
- Wait 5 minutes (normal processing time)
- Refresh the page
- Check asset status
- Contact support if > 30 minutes

### Generation Problems

#### Generation Fails
```
Error: Generation failed
```
**Solutions:**
- Check generation quota
- Simplify prompt
- Try different model
- Check API status

#### Poor Quality Results
- Refine prompt clarity
- Use style references
- Try different angles
- Adjust quality settings

### Performance Issues

#### Slow Loading
- Check internet speed
- Clear browser cache
- Disable heavy extensions
- Try incognito mode

#### UI Not Responding
- Refresh page
- Check browser console (F12)
- Update browser
- Try different browser

## Platform-Specific Issues

### Supabase Connection
```
Error: Database connection failed
```
**IPv6 Workaround:**
Use pooler connection string:
```
postgresql://postgres.[project-id]:[password]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres
```

### Fashion Lab API
```
Error: 401 Unauthorized
```
**Important:** Use `jwt` prefix, not `Bearer`:
```javascript
headers: {
  'Authorization': 'jwt YOUR_TOKEN'  // ✓ Correct
  'Authorization': 'Bearer TOKEN'    // ✗ Wrong
}
```

## Browser Console Errors

### Check Console
1. Open Developer Tools (F12)
2. Go to Console tab
3. Look for red errors
4. Screenshot for support

### Common Errors
- **CORS errors** → Backend issue, contact support
- **404 Not Found** → Resource deleted or moved
- **500 Server Error** → Temporary issue, retry
- **NetworkError** → Check connection

## Debugging Steps

### 1. Isolate the Problem
- When did it start?
- What were you doing?
- Does it happen every time?
- Only your account?

### 2. Gather Information
- Browser and version
- Error messages
- Console logs
- Network requests

### 3. Basic Troubleshooting
- Log out and back in
- Try different browser
- Check with colleague
- Test on another network

### 4. Contact Support
Include:
- Problem description
- Steps to reproduce
- Screenshots/videos
- Browser console logs

## Error Reference

### HTTP Status Codes
- **400** - Bad request (check input)
- **401** - Not authenticated (log in)
- **403** - Not authorized (check permissions)
- **404** - Not found (resource deleted)
- **429** - Rate limited (slow down)
- **500** - Server error (contact support)

### Application Errors
- **QUOTA_EXCEEDED** - Upgrade plan or clean up
- **INVALID_FILE_TYPE** - Use JPG/PNG/WebP
- **FILE_TOO_LARGE** - Reduce file size
- **GENERATION_LIMIT** - Wait or upgrade

## Advanced Troubleshooting

### Network Issues
```bash
# Check API connectivity
curl https://api.fashionlab.ai/health

# Test Supabase connection
curl https://[project-id].supabase.co/rest/v1/
```

### Local Storage Issues
```javascript
// Clear local storage
localStorage.clear();
// Reload page
location.reload();
```

### Cache Issues
```javascript
// Force refresh
caches.keys().then(names => {
  names.forEach(name => caches.delete(name));
});
```

## Getting Help

### Self-Service
1. Check this guide
2. Search documentation
3. Check status page
4. Try quick fixes

### Contact Support
- **Email**: <EMAIL>
- **Response Time**: 
  - Free: 48 hours
  - Pro: 24 hours
  - Enterprise: 4 hours

### Information to Provide
- Account email
- Organization name
- Problem description
- Steps to reproduce
- Error messages
- Screenshots
- Browser/OS info

## Related Documentation

- [Quick Start](../00-quick-start/troubleshooting.md)
- [API Errors](../04-api/error-handling.md)
- [Common Issues](../08-operations/common-issues.md)
- [Support Guide](../10-reference/support.md)