# Linear Issue Quick Reference Checklist

## 🎯 Issue Intake (5 minutes)
- [ ] Read issue thoroughly
- [ ] Check attachments/screenshots
- [ ] Assess complexity (Simple/Medium/Complex)
- [ ] Move to "In Progress" in Linear
- [ ] Create branch with Linear's suggested name
- [ ] Brief AI assistant with context

## 🤖 AI Assistant Briefing Template
```
Context: [Linear-XXX] - [Brief Description]
Problem: [What needs to be fixed/built]
Acceptance Criteria: 
- [ ] [Criteria 1]
- [ ] [Criteria 2]
Technical Context: [Relevant areas of codebase]
Priority: [High/Medium/Low] - [Why]
```

## 💻 Development Phase
- [ ] AI gathers context with codebase-retrieval first
- [ ] Create task breakdown for complex issues
- [ ] Follow existing code patterns
- [ ] Use str-replace-editor (not file recreation)
- [ ] Add comments for complex logic
- [ ] Update task progress as you go

## 🧪 Local Testing (Required)
- [ ] Run application locally
- [ ] Test the specific issue scenario
- [ ] Test related functionality
- [ ] Check for console errors
- [ ] Verify responsive design (if UI changes)
- [ ] Test different user roles (if applicable)

## 📝 Commit & Push
### Commit Message Format:
```
[Linear-XXX] Brief description

- Specific change 1
- Specific change 2
- Fixes: [what was broken]
- Testing: [how tested]

Linear: [Linear Issue URL]
```

### Push Checklist:
- [ ] All tests pass locally
- [ ] No debug code or unnecessary files
- [ ] Commit message follows format
- [ ] Push to main/staging branch

## 🚀 Staging Deployment
- [ ] Verify deployment succeeded
- [ ] Smoke test on staging
- [ ] Update Linear with:
  - [ ] Commit hash
  - [ ] Staging URL
  - [ ] Testing instructions
  - [ ] Move to "Ready for Review"

## 📋 Staging Testing Instructions Template
```markdown
## Staging Testing - [Linear-XXX]

**Staging URL**: [Direct link]
**Test Account**: [If needed]

### Steps to Test:
1. [Step 1]
2. [Step 2]
3. [Expected result]

### Success Criteria:
- [ ] [Behavior 1]
- [ ] [Behavior 2]
- [ ] No console errors (F12 → Console)

### If Issues: Screenshot + browser info → Linear comment
```

## ✅ Issue Completion
- [ ] Staging testing passed
- [ ] All acceptance criteria met
- [ ] No regressions found
- [ ] Add final Linear comment with summary
- [ ] Move to "Done" in Linear
- [ ] Log time spent

## 🚨 When to Escalate
- More complex than estimated
- Requires architectural changes
- Security implications
- Breaking changes needed

**Escalation**: Update Linear → Add "needs-discussion" label → Tag stakeholders

---

## 📊 Quality Gates Quick Check
- [ ] **Code**: Follows patterns, proper error handling
- [ ] **Performance**: No unnecessary re-renders, efficient queries
- [ ] **Security**: Input validation, proper auth
- [ ] **UX**: Error states, loading states, responsive

## 🔄 Issue Types
- **Bug**: Reproduce → Root cause → Fix → Regression test
- **Feature**: Break down → UX consideration → Error states → Extensibility
- **Tech Debt**: Document current → Migration plan → Backward compatibility

---

*Keep this checklist handy for every Linear issue!*
