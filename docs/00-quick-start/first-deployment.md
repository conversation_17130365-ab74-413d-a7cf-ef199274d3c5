# First Deployment Guide

## Overview

Deploy your first version of FashionLab to staging environment. This guide walks through creating your first collection and deploying changes.

## Prerequisites

- Completed [Installation](./installation.md)
- Development server running
- Access to staging environment

## Step 1: Create Test Data

### 1.1 Create Your First User

```bash
# Option A: Use the UI
# 1. Navigate to http://localhost:5173
# 2. Click "Sign Up"
# 3. Enter your email and password
# 4. Check email for verification

# Option B: Use Supabase Dashboard
# 1. Open http://127.0.0.1:54323
# 2. Go to Authentication → Users
# 3. Click "Add User"
# 4. Set email and password
```

### 1.2 Create an Organization

```sql
-- In Supabase SQL Editor
INSERT INTO organizations (name, slug, email, settings)
VALUES (
  'Test Fashion Brand',
  'test-brand',
  '<EMAIL>',
  '{"primary_color": "#000000", "storage_quota_gb": 10}'
);

-- Add yourself as admin
INSERT INTO organization_members (organization_id, user_id, role)
SELECT 
  o.id,
  auth.uid(),
  'org_admin'
FROM organizations o
WHERE o.slug = 'test-brand';
```

### 1.3 Create Your First Collection

1. Log in to the application
2. Navigate to Collections
3. Click "Create Collection"
4. Fill in:
   - Name: "Summer 2024 Test"
   - Description: "Test collection for deployment"
   - Status: Active
5. Save collection

## Step 2: Test Core Features

### 2.1 Upload Assets

```typescript
// Test single upload
1. Go to your collection
2. Click "Upload Assets"
3. Select an image file
4. Set workflow stage to "Raw AI Images"
5. Upload

// Verify in database
SELECT * FROM assets 
WHERE collection_id = 'your-collection-id'
ORDER BY created_at DESC;
```

### 2.2 Test Image Generation

1. Navigate to collection
2. Click "Generate Images"
3. Upload reference images:
   - Face image
   - Garment image  
   - Pose reference
   - Background
4. Enter prompt: "Fashion model wearing summer dress"
5. Generate and wait for completion

### 2.3 Verify Storage

```bash
# Check storage usage
supabase storage ls assets

# Verify thumbnails generated
supabase storage ls thumbnails
```

## Step 3: Prepare for Deployment

### 3.1 Run Pre-deployment Checks

```bash
# Lint check
npm run lint

# Type check
npm run typecheck

# Build test
npm run build

# Run tests
npm run test
```

### 3.2 Update Version

```json
// package.json
{
  "version": "1.0.1"
}
```

### 3.3 Create Deployment Branch

```bash
# Create feature branch
git checkout -b deploy/first-deployment

# Commit your changes
git add .
git commit -m "feat: prepare first deployment

- Add test collection
- Verify core features
- Update version to 1.0.1"
```

## Step 4: Deploy to Staging

### 4.1 Push to GitHub

```bash
# Push branch
git push origin deploy/first-deployment

# Create pull request
gh pr create \
  --title "Deploy: First staging deployment" \
  --body "First deployment with test data and verified features" \
  --base main
```

### 4.2 Vercel Deployment

Vercel automatically deploys when PR is created:

1. Check PR for deployment preview URL
2. Wait for build to complete (2-3 minutes)
3. Click preview URL to test

### 4.3 Database Migrations

```bash
# If you have schema changes
supabase db diff --use-migra -f your_migration_name

# Apply to staging
supabase db push --project-ref qnfmiotatmkoumlymynq
```

## Step 5: Verify Staging Deployment

### 5.1 Smoke Test Checklist

- [ ] Application loads without errors
- [ ] Login works with test account
- [ ] Can view collections
- [ ] Can upload images
- [ ] Storage URLs work
- [ ] No console errors

### 5.2 Test Core Workflows

```typescript
// 1. Authentication Flow
- Sign in
- Sign out  
- Password reset

// 2. Asset Management
- Upload single asset
- View asset details
- Update asset metadata

// 3. Collection Features
- Create collection
- Edit collection
- View collection assets
```

### 5.3 Check Monitoring

```bash
# View deployment logs
vercel logs your-deployment-url

# Check Supabase logs
supabase functions logs --project-ref qnfmiotatmkoumlymynq

# Monitor errors
# Check browser console
# Check network tab for failed requests
```

## Step 6: Deployment Rollback (If Needed)

### Quick Rollback

```bash
# Revert in Vercel Dashboard
1. Go to Vercel Dashboard
2. Select project
3. Go to Deployments
4. Find last working deployment
5. Click "..." → "Promote to Production"
```

### Git Rollback

```bash
# Revert commits
git revert HEAD
git push origin main

# Or reset to previous commit
git reset --hard <previous-commit-hash>
git push origin main --force
```

## Production Deployment

### Prerequisites for Production

- [ ] All tests passing
- [ ] Security scan complete
- [ ] Performance acceptable
- [ ] Stakeholder approval
- [ ] Backup created

### Production Deployment Steps

```bash
# 1. Create production PR
gh pr create \
  --base production \
  --head main \
  --title "Deploy: v1.0.1 to production" \
  --body "Verified features ready for production"

# 2. Apply database migrations
supabase db push --project-ref cpelxqvcjnbpnphttzsn

# 3. Merge PR (triggers deployment)

# 4. Verify production
# - Check https://app.fashionlab.ai
# - Run smoke tests
# - Monitor for errors
```

## Post-Deployment

### 1. Update Linear

```bash
# Using Linear CLI
linear issue update ISSUE-ID --status "Deployed"

# Or use the web UI
# Move tickets to "Done" column
```

### 2. Notify Team

```markdown
# Slack message template
🚀 **Deployment Complete**

**Version**: 1.0.1
**Environment**: Staging
**Changes**:
- First deployment setup
- Core features verified
- Test data created

**Testing**: https://staging.fashionlab.ai
**PR**: #123
```

### 3. Document Issues

Create issues for any problems found:

```bash
gh issue create \
  --title "Bug: Issue description" \
  --body "Steps to reproduce..." \
  --label "bug,staging"
```

## Troubleshooting Deployment

### Build Failures

```bash
# Check build logs
vercel logs

# Common fixes:
# - Clear cache: vercel --force
# - Check environment variables
# - Verify dependencies
```

### Database Issues

```bash
# Check migration status
supabase migration list --project-ref qnfmiotatmkoumlymynq

# Repair migrations
supabase migration repair --status applied

# Reset if needed
supabase db reset --project-ref qnfmiotatmkoumlymynq
```

### Performance Issues

```bash
# Check bundle size
npm run analyze

# Optimize if needed:
# - Lazy load components
# - Optimize images
# - Remove unused dependencies
```

## Next Steps

Congratulations! You've completed your first deployment. Next:

1. **Monitor the deployment**: Watch for errors in the first 24 hours
2. **Gather feedback**: Share with team for testing
3. **Plan next features**: Check the roadmap
4. **Learn more**: 
   - [Deployment Process](../06-deployment/README.md)
   - [CI/CD Pipeline](../06-deployment/ci-cd.md)
   - [Monitoring](../08-operations/monitoring.md)

## Deployment Checklist Template

Copy this for future deployments:

```markdown
## Pre-Deployment
- [ ] Code review approved
- [ ] Tests passing
- [ ] Lint/typecheck clean
- [ ] Build successful
- [ ] Security scan passed
- [ ] Environment variables verified

## Deployment
- [ ] Branch created
- [ ] PR opened
- [ ] Preview tested
- [ ] Stakeholder approved
- [ ] Merged to main/production

## Post-Deployment
- [ ] Smoke tests passed
- [ ] Monitoring checked
- [ ] Team notified
- [ ] Issues documented
- [ ] Linear updated
```