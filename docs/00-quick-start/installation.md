# Installation Guide

## Overview

Complete installation guide for setting up FashionLab development environment. This guide covers all dependencies, configuration, and verification steps.

## Prerequisites

### System Requirements
- **OS**: macOS, Linux, or Windows with WSL2
- **Node.js**: v18.0.0 or higher
- **npm**: v9.0.0 or higher (or bun)
- **Git**: v2.30.0 or higher
- **Docker**: For local Supabase (optional)

### Required Accounts
- GitHub account with repository access
- Supabase account (free tier works)
- Fashion Lab API access (contact <EMAIL>)

## Step 1: Clone Repository

```bash
# Clone the repository
git clone https://github.com/your-org/fashionlab-v1.git
cd fashionlab-v1

# Verify you're on the correct branch
git branch
# Should show: * main
```

## Step 2: Install Dependencies

### Using npm
```bash
npm install
```

### Using bun (faster)
```bash
bun install
```

### Verify Installation
```bash
# Check installed packages
npm list --depth=0

# Verify no vulnerabilities
npm audit
```

## Step 3: Environment Setup

### Create Environment File
```bash
# Copy example environment file
cp .env.example .env.local
```

### Configure Environment Variables
Edit `.env.local` with your values:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Fashion Lab API (<NAME_EMAIL>)
VITE_FASHION_LAB_API_KEY=your-api-key-here
VITE_FASHION_LAB_API_URL=https://fashionlab.notfirst.rodeo

# Optional: Email Service
VITE_RESEND_API_KEY=your-resend-key-here

# Optional: Environment
VITE_APP_ENV=development
```

### Get Supabase Credentials

1. Log in to [Supabase Dashboard](https://app.supabase.com)
2. Select your project
3. Go to Settings → API
4. Copy:
   - Project URL → `VITE_SUPABASE_URL`
   - Anon/Public key → `VITE_SUPABASE_ANON_KEY`

## Step 4: Database Setup

### Option A: Use Existing Supabase Project

If using staging/production database:
```bash
# No additional setup needed
# Your .env.local should point to the project
```

### Option B: Local Supabase (Recommended for Development)

1. **Install Supabase CLI**
   ```bash
   # macOS
   brew install supabase/tap/supabase

   # npm
   npm install -g supabase

   # Linux/WSL
   wget -qO- https://github.com/supabase/cli/releases/download/v1.142.2/supabase_linux_amd64.tar.gz | tar xvz
   sudo mv supabase /usr/local/bin
   ```

2. **Start Local Supabase**
   ```bash
   # Initialize (first time only)
   supabase init

   # Start services
   supabase start
   ```

3. **Run Migrations**
   ```bash
   # Apply all migrations
   supabase db push

   # Verify migrations
   supabase migration list
   ```

4. **Update .env.local**
   ```env
   # Local Supabase
   VITE_SUPABASE_URL=http://127.0.0.1:54321
   VITE_SUPABASE_ANON_KEY=your-local-anon-key
   ```

## Step 5: Verify Setup

### 1. Check Environment
```bash
# Run setup verification script
npm run check:env

# Should output:
# ✓ Node.js version OK
# ✓ Dependencies installed
# ✓ Environment variables set
# ✓ Supabase connection OK
```

### 2. Test Database Connection
```bash
# Run database test
npm run test:db

# Should show:
# ✓ Connected to Supabase
# ✓ Tables accessible
# ✓ RLS policies active
```

### 3. Build Test
```bash
# Test build process
npm run build

# Should complete without errors
```

## Step 6: IDE Setup

### VS Code (Recommended)

1. **Install Extensions**
   ```bash
   # Install recommended extensions
   code --install-extension dbaeumer.vscode-eslint
   code --install-extension esbenp.prettier-vscode
   code --install-extension bradlc.vscode-tailwindcss
   code --install-extension ZixuanChen.vitest-explorer
   ```

2. **Configure Settings**
   Create `.vscode/settings.json`:
   ```json
   {
     "editor.formatOnSave": true,
     "editor.defaultFormatter": "esbenp.prettier-vscode",
     "editor.codeActionsOnSave": {
       "source.fixAll.eslint": true
     },
     "typescript.tsdk": "node_modules/typescript/lib"
   }
   ```

## Step 7: Run Development Server

```bash
# Start the development server
npm run dev

# Output:
# VITE v5.0.0  ready in 500 ms
# ➜  Local:   http://localhost:5173/
# ➜  Network: http://*************:5173/
```

### Verify Application
1. Open http://localhost:5173
2. You should see the login page
3. Check browser console for any errors

## Common Installation Issues

### Issue: Port 5173 Already in Use
```bash
# Kill process on port
lsof -ti:5173 | xargs kill -9

# Or use different port
npm run dev -- --port 3000
```

### Issue: Supabase Connection Failed
```bash
# Check Supabase is running
supabase status

# Restart if needed
supabase stop
supabase start

# Get fresh credentials
supabase status
```

### Issue: Missing Dependencies
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### Issue: TypeScript Errors
```bash
# Regenerate types
npm run types:generate

# Clear TypeScript cache
rm -rf node_modules/.cache
```

## Next Steps

✅ Installation complete! Now you can:

1. **Create a test account**: See [First Deployment](./first-deployment.md)
2. **Explore the codebase**: See [Development Guide](../03-development/README.md)
3. **Understand architecture**: See [Architecture](../01-overview/architecture.md)
4. **Start developing**: Make your first change!

## Additional Resources

### Development Tools
- [Supabase Studio](http://127.0.0.1:54323) - Local database UI
- [React DevTools](https://react.dev/learn/react-developer-tools)
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss)

### Documentation
- [Troubleshooting Guide](./troubleshooting.md)
- [Environment Configuration](../03-development/setup-local.md)
- [API Documentation](../04-api/README.md)

## Getting Help

If you encounter issues:

1. Check [Troubleshooting Guide](./troubleshooting.md)
2. Search existing [GitHub Issues](https://github.com/your-org/fashionlab-v1/issues)
3. Ask in the development Slack channel
4. Create a new issue with:
   - Error messages
   - Steps to reproduce
   - Environment details