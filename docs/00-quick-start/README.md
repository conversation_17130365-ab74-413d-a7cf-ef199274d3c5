# Quick Start Guide

## Overview

Get up and running with FashionLab in 5 minutes. This guide covers the essential steps to deploy your first AI-powered fashion imagery.

## Prerequisites

- Node.js 18+ and npm/bun
- Supabase account
- Fashion Lab API access (contact <EMAIL>)

## Installation Steps

### 1. Clone and Install

```bash
git clone https://github.com/your-org/fashionlab-v1.git
cd fashionlab-v1
npm install
```

### 2. Environment Setup

Create `.env.local` file:

```bash
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key

# Fashion Lab API
VITE_FASHION_LAB_API_KEY=your_api_key
```

### 3. Database Setup

```bash
npm run supabase:start
npm run db:migrate
```

### 4. Start Development

```bash
npm run dev
```

Your app is now running at `http://localhost:5173`

## Next Steps

1. [Complete Installation Guide](./installation.md) - Detailed setup instructions
2. [First Deployment](./first-deployment.md) - Deploy to staging
3. [Architecture Overview](/01-overview/architecture.md) - Understand the system

## Common Issues

### Port Already in Use
```bash
# Kill process on port 5173
lsof -ti:5173 | xargs kill -9
```

### Database Connection Failed
Check your Supabase project is running and `.env.local` has correct credentials.

### Missing API Key
Contact <EMAIL> for Fashion Lab API access.

## Getting Help

- [Troubleshooting Guide](./troubleshooting.md)
- [Developer Guide](/03-development/README.md)
- GitHub Issues: [Report a Bug](https://github.com/your-org/fashionlab-v1/issues)