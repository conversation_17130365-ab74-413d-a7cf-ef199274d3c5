# Troubleshooting Guide

## Overview

Common issues and solutions for FashionLab development and deployment. This guide helps you quickly resolve typical problems.

## Prerequisites

- Basic command line knowledge
- Access to error logs
- Understanding of the tech stack

## Development Issues

### Application Won't Start

#### Symptom
```bash
npm run dev
# Error: Cannot find module or other startup errors
```

#### Solutions

1. **Missing Dependencies**
   ```bash
   # Clean install
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :5173
   
   # Kill process
   kill -9 <PID>
   
   # Or use different port
   npm run dev -- --port 3000
   ```

3. **Environment Variables Missing**
   ```bash
   # Check if .env.local exists
   ls -la .env*
   
   # Copy from example
   cp .env.example .env.local
   
   # Edit with your values
   nano .env.local
   ```

### TypeScript Errors

#### Symptom
```
Type error: Cannot find module '@/components/...' or its corresponding type declarations
```

#### Solutions

1. **Regenerate Types**
   ```bash
   # Generate Supabase types
   npm run types:generate
   
   # Clear TypeScript cache
   rm -rf node_modules/.cache/typescript
   ```

2. **Check Path Aliases**
   ```json
   // tsconfig.json
   {
     "compilerOptions": {
       "paths": {
         "@/*": ["./src/*"]
       }
     }
   }
   ```

### Build Failures

#### Symptom
```bash
npm run build
# Error: Build failed
```

#### Solutions

1. **Memory Issues**
   ```bash
   # Increase Node memory
   export NODE_OPTIONS="--max-old-space-size=4096"
   npm run build
   ```

2. **Dependency Conflicts**
   ```bash
   # Check for conflicts
   npm ls
   
   # Force resolution
   npm install --force
   ```

## Database Issues

### Supabase Connection Failed

#### Symptom
```
Error: Failed to connect to Supabase
```

#### Solutions

1. **Check Credentials**
   ```bash
   # Verify environment variables
   echo $VITE_SUPABASE_URL
   echo $VITE_SUPABASE_ANON_KEY
   ```

2. **Local Supabase Not Running**
   ```bash
   # Check status
   supabase status
   
   # Start if needed
   supabase start
   
   # Get local credentials
   supabase status
   ```

3. **Network Issues**
   ```bash
   # Test connection
   curl https://your-project.supabase.co/rest/v1/
   
   # Check DNS
   nslookup your-project.supabase.co
   ```

### Migration Errors

#### Symptom
```
Error: Migration failed
```

#### Solutions

1. **Check Migration Status**
   ```bash
   # List migrations
   supabase migration list
   
   # Show specific migration
   supabase migration show 20240501000000
   ```

2. **Fix Failed Migration**
   ```bash
   # Repair migration
   supabase migration repair --status applied
   
   # Reset and reapply
   supabase db reset
   ```

### RLS Policy Issues

#### Symptom
```
Error: Permission denied for table
```

#### Solutions

1. **Check RLS Status**
   ```sql
   -- In Supabase SQL Editor
   SELECT 
     schemaname,
     tablename,
     rowsecurity 
   FROM pg_tables 
   WHERE schemaname = 'public';
   ```

2. **Debug Policies**
   ```sql
   -- Test as specific user
   SET LOCAL role TO 'authenticated';
   SET LOCAL request.jwt.claim.sub TO 'user-uuid';
   
   -- Try query
   SELECT * FROM your_table;
   ```

## Authentication Issues

### Login Failures

#### Symptom
```
Error: Invalid login credentials
```

#### Solutions

1. **Reset Password**
   ```typescript
   // Use forgot password flow
   const { error } = await supabase.auth.resetPasswordForEmail(email, {
     redirectTo: `${window.location.origin}/reset-password`,
   });
   ```

2. **Check User Status**
   ```sql
   -- In Supabase Dashboard
   SELECT * FROM auth.users 
   WHERE email = '<EMAIL>';
   ```

3. **Email Confirmation**
   ```sql
   -- Check if email confirmed
   SELECT email_confirmed_at 
   FROM auth.users 
   WHERE email = '<EMAIL>';
   ```

### Session Issues

#### Symptom
```
Error: Session expired or invalid
```

#### Solutions

1. **Clear Session**
   ```javascript
   // Clear all auth data
   await supabase.auth.signOut();
   localStorage.clear();
   sessionStorage.clear();
   ```

2. **Refresh Token**
   ```javascript
   // Manual refresh
   const { data, error } = await supabase.auth.refreshSession();
   ```

## API Issues

### Fashion Lab API Errors

#### Symptom
```
Error: 401 Unauthorized from Fashion Lab API
```

#### Solutions

1. **Check Authentication Format**
   ```javascript
   // ❌ Wrong
   headers: {
     'Authorization': `Bearer ${token}`
   }
   
   // ✅ Correct
   headers: {
     'Authorization': `jwt ${token}`
   }
   ```

2. **Verify API Key**
   ```bash
   # Test API key
   curl -H "Authorization: jwt YOUR_API_KEY" \
     https://fashionlab.notfirst.rodeo/api/health
   ```

### Edge Function Errors

#### Symptom
```
Error: Edge function invocation failed
```

#### Solutions

1. **Check Logs**
   ```bash
   # Local logs
   supabase functions logs function-name
   
   # Production logs
   supabase functions logs function-name --project-ref your-project-ref
   ```

2. **Test Locally**
   ```bash
   # Serve function locally
   supabase functions serve function-name
   
   # Test with curl
   curl -i --location --request POST \
     'http://127.0.0.1:54321/functions/v1/function-name' \
     --header 'Authorization: Bearer YOUR_ANON_KEY' \
     --header 'Content-Type: application/json' \
     --data '{"test": "data"}'
   ```

## Storage Issues

### Upload Failures

#### Symptom
```
Error: Failed to upload file
```

#### Solutions

1. **Check File Size**
   ```javascript
   // Verify size before upload
   const MAX_SIZE = 10 * 1024 * 1024; // 10MB
   if (file.size > MAX_SIZE) {
     throw new Error('File too large');
   }
   ```

2. **Verify Bucket Exists**
   ```bash
   # List buckets
   supabase storage ls
   
   # Create if missing
   supabase storage create-bucket assets --public
   ```

3. **Check Permissions**
   ```sql
   -- Check storage policies
   SELECT * FROM storage.buckets;
   ```

### Image Not Displaying

#### Symptom
Images return 404 or don't load

#### Solutions

1. **Check URL Format**
   ```javascript
   // Get public URL
   const { data } = supabase.storage
     .from('assets')
     .getPublicUrl(path);
   ```

2. **Verify File Exists**
   ```bash
   # List files in bucket
   supabase storage ls assets/path/to/file
   ```

## Performance Issues

### Slow Page Load

#### Solutions

1. **Check Bundle Size**
   ```bash
   # Analyze bundle
   npm run build
   npm run analyze
   ```

2. **Enable Caching**
   ```javascript
   // Add cache headers
   response.headers.set('Cache-Control', 'public, max-age=3600');
   ```

3. **Lazy Load Components**
   ```javascript
   // Dynamic imports
   const HeavyComponent = lazy(() => import('./HeavyComponent'));
   ```

### Database Query Slow

#### Solutions

1. **Add Indexes**
   ```sql
   -- Check missing indexes
   EXPLAIN ANALYZE 
   SELECT * FROM assets 
   WHERE collection_id = 'uuid';
   
   -- Add index
   CREATE INDEX idx_assets_collection 
   ON assets(collection_id);
   ```

2. **Optimize Queries**
   ```javascript
   // ❌ N+1 query
   const collections = await getCollections();
   for (const c of collections) {
     c.assets = await getAssets(c.id);
   }
   
   // ✅ Single query
   const { data } = await supabase
     .from('collections')
     .select('*, assets(*)');
   ```

## Deployment Issues

### Vercel Build Errors

#### Solutions

1. **Check Build Command**
   ```json
   // vercel.json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist"
   }
   ```

2. **Environment Variables**
   - Go to Vercel Dashboard
   - Settings → Environment Variables
   - Add all required vars

### Production Errors

#### Debug Steps

1. **Check Logs**
   ```bash
   # Vercel logs
   vercel logs https://your-app.vercel.app
   
   # Supabase logs
   supabase functions logs --project-ref prod-ref
   ```

2. **Monitor Network**
   - Open browser DevTools
   - Check Network tab
   - Look for failed requests

## Common Error Messages

### Quick Reference

| Error | Cause | Solution |
|-------|-------|----------|
| `ECONNREFUSED` | Service not running | Start local services |
| `MODULE_NOT_FOUND` | Missing dependency | Run `npm install` |
| `CORS error` | Cross-origin blocked | Check API CORS config |
| `401 Unauthorized` | Invalid credentials | Check auth token |
| `403 Forbidden` | No permission | Check user role |
| `404 Not Found` | Wrong URL/path | Verify endpoint |
| `500 Server Error` | Backend issue | Check server logs |

## Getting More Help

### 1. Enable Debug Mode

```javascript
// Add to .env.local
VITE_DEBUG=true

// In your code
if (import.meta.env.VITE_DEBUG) {
  console.log('Debug info:', data);
}
```

### 2. Collect Information

When reporting issues, include:
- Error message (full text)
- Browser console errors
- Network requests (HAR file)
- Steps to reproduce
- Environment details

### 3. Resources

- [GitHub Issues](https://github.com/your-org/fashionlab-v1/issues)
- [Supabase Discord](https://discord.supabase.com)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/supabase)
- Internal Slack: #fashionlab-dev

## Related Documentation

- [Installation Guide](./installation.md)
- [Development Guide](../03-development/README.md)
- [API Documentation](../04-api/README.md)
- [Deployment Guide](../06-deployment/README.md)