# Linear Issue Workflow Guidelines

## 🎯 Simple Workflow for Fashion Lab Issues

This is the streamlined process for handling Linear issues from the Fashion Lab team.

## 📋 Step 1: Analyze the Issue (Understanding Phase)

When you get a new Linear issue from the Fashion Lab team:

### 1.1 Read & Interpret
- [ ] **Read the issue carefully** - Fashion Lab feedback is often non-technical
- [ ] **Look for screenshots/videos** - Visual context is crucial
- [ ] **Identify the real problem** - What are they actually experiencing?
- [ ] **Ask clarifying questions** if the issue is unclear

### 1.2 AI Assistant Analysis
Brief the AI assistant with this template:

```
I have a new Linear issue from the Fashion Lab team that needs analysis:

**Linear Issue**: [Issue ID] - [Title]
**Original Description**: [Copy the exact description from Linear]
**Attachments**: [Describe any screenshots/videos]

The Fashion Lab team reported this issue, but it's not very technical. Help me:
1. Understand what they're actually experiencing
2. Investigate the codebase to find the root cause
3. Check the database structure if needed
4. Identify all related components and systems

Let's start by analyzing what this issue really means technically.
```

### 1.3 Deep Investigation
Work with AI assistant to:
- [ ] **Use codebase-retrieval** to understand current implementation
- [ ] **Check database schema** if data-related
- [ ] **Identify all affected components** and systems
- [ ] **Find similar patterns** in the codebase
- [ ] **Understand the user flow** that's broken

## 📝 Step 2: Create Technical Plan

Once you fully understand the issue:

### 2.1 Document the Analysis
Add this section to the Linear issue description (below the original):

```markdown
---

## Technical Analysis & Implementation Plan

### Problem Analysis
**Root Cause**: [What's actually causing the issue]
**Affected Systems**: [List components, database tables, APIs, etc.]
**User Impact**: [How this affects the user experience]

### Technical Details
**Current Implementation**: [How it works now]
**What's Broken**: [Specific technical problem]
**Database Impact**: [Any schema or data issues]

### Implementation Plan
**Approach**: [High-level solution strategy]
**Files to Modify**:
- [File 1] - [What needs to change]
- [File 2] - [What needs to change]

**Database Changes**: [If any schema/data changes needed]
**Testing Strategy**: [How to verify the fix]

### Acceptance Criteria (Technical)
- [ ] [Specific technical requirement 1]
- [ ] [Specific technical requirement 2]
- [ ] [No regressions in related functionality]

**Estimated Effort**: [Simple/Medium/Complex - X hours]
**Risk Level**: [Low/Medium/High]
```

### 2.2 Update Linear Issue (Using MCP Tools)
Use the Linear MCP tools for reliable issue updates:

```
# Update issue description with technical analysis
linear_updateIssue_mcp-linear:
- id: "ISSUE-ID" (e.g., "FAS-157")
- description: "[Original report + Technical analysis in Markdown]"

# Update issue status and assignment
linear_updateIssue_mcp-linear:
- id: "ISSUE-ID"
- stateId: "ready-for-development" (or appropriate state)
- assigneeId: "user-id" (get from linear_getViewer_mcp-linear)
```

- [ ] **Add the technical plan** to the issue description using `linear_updateIssue_mcp-linear`
- [ ] **Move to appropriate status** (e.g., "Ready for Development")
- [ ] **Assign to Asger** as owner
- [ ] **Add time estimate** if required
- [ ] **Tag any dependencies** or related issues

## 🚀 Step 3: Ready for Development

The issue is now ready to be picked up later with:
- ✅ Clear understanding of the problem
- ✅ Technical analysis complete
- ✅ Implementation plan documented
- ✅ Proper ownership and status

## 🔧 Step 4: When Ready to Implement

When you're ready to work on the issue:

### 4.1 AI Assistant Implementation Brief
```
I'm ready to implement this Linear issue: [Issue ID]

The technical analysis is already complete (see the issue description).

Please help me:
1. Review the implementation plan
2. Start with codebase-retrieval for the identified files
3. Implement the solution following our existing patterns
4. Ensure proper testing coverage

Let's begin implementation following the documented plan.
```

### 4.2 Development Process
- [ ] **Follow the documented plan** from the issue
- [ ] **Use task management** for complex implementations
- [ ] **Test locally** before pushing
- [ ] **Update Linear** with progress

### 4.3 Deployment & Testing
- [ ] **Commit with proper message**: `[Linear-XXX] Brief description`
- [ ] **Push to staging**
- [ ] **Test on staging** yourself first
- [ ] **Update Linear** with staging URL and testing instructions using `linear_createComment_mcp-linear`
- [ ] **Move to "Ready for Review"** using `linear_updateIssue_mcp-linear` for Fashion Lab team testing

## 🎯 Key Principles

### For Analysis Phase
- **Assume non-technical feedback** - Translate user problems to technical issues
- **Dig deep** - Use codebase-retrieval extensively to understand current state
- **Document everything** - Future you will thank you
- **Ask questions** - Better to clarify than assume

### For Implementation Phase
- **Follow the plan** - The analysis phase should guide implementation
- **Test thoroughly** - Fashion Lab issues often involve user workflows
- **Think about edge cases** - Users find creative ways to break things

## � Quick Reference Checklist

### New Issue Received
- [ ] Read and understand the user problem
- [ ] Brief AI assistant for technical analysis
- [ ] Investigate codebase and database
- [ ] Ask clarifying questions if needed
- [ ] Document technical plan in Linear
- [ ] Update status and assign to Asger

### Ready to Implement
- [ ] Review the documented plan
- [ ] Brief AI assistant for implementation
- [ ] Follow existing code patterns
- [ ] Test locally and on staging
- [ ] Update Linear with results using MCP tools

### Common AI Assistant Prompts

**For Analysis**:
```
Analyze this Fashion Lab issue: [description]. Help me understand the technical root cause and investigate the codebase.
```

**For Implementation**:
```
Implement the solution for Linear-XXX following the documented technical plan. Start by reviewing the current implementation.
```

**For Database Issues**:
```
This issue involves database problems. Help me check the schema, relationships, and data integrity for [specific area].
```

### Linear MCP Tools Reference

**Essential Linear MCP Commands**:
```
# Get current user info
linear_getViewer_mcp-linear

# Get issue details
linear_getIssueById_mcp-linear:
- id: "ISSUE-ID" (e.g., "FAS-157")

# Update issue description with technical analysis
linear_updateIssue_mcp-linear:
- id: "ISSUE-ID"
- description: "[Markdown formatted description]"

# Update issue status and assignment
linear_updateIssue_mcp-linear:
- id: "ISSUE-ID"
- stateId: "ready-for-development"
- assigneeId: "user-id"

# Add comments for additional context
linear_createComment_mcp-linear:
- issueId: "ISSUE-ID"
- body: "[Markdown comment]"

# Get teams and workflow states
linear_getTeams_mcp-linear
linear_getWorkflowStates_mcp-linear:
- teamId: "team-id"
```

**Markdown Formatting Tips for Linear**:
- Use proper Markdown syntax (headings, lists, code blocks, task lists)
- Linear supports full Markdown including tables and checkboxes
- No need to escape newlines - MCP tools handle formatting automatically
- Use `**bold**`, `*italic*`, `` `code` ``, and `- [ ]` task lists

---

## 🔧 Technical Notes

### Linear MCP vs API Tools
- **Use Linear MCP tools** (`linear_*_mcp-linear`) for all Linear operations
- **Avoid the generic `linear` tool** - it has formatting and authentication issues
- **MCP tools handle Markdown properly** - no need to escape newlines or quotes
- **Authentication**: MCP tools use proper Linear API authentication

### Troubleshooting
- If MCP tools show authentication errors, check Linear API token configuration
- For complex descriptions, break into multiple updates if needed
- Always test MCP tool authentication with `linear_getViewer_mcp-linear` first

---

*This workflow ensures every Fashion Lab issue is properly analyzed and documented before implementation, making it easy to pick up work later.*
