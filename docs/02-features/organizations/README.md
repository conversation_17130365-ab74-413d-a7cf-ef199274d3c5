# Organization Management

## Overview

Organizations (displayed as "Brands" in the UI) are the top-level entities in FashionLab, providing multi-tenant isolation, user management, and resource allocation.

## Key Concepts

### Organization Structure
```
Organization (Brand)
├── Users (with roles)
├── Collections (Campaigns)
├── Assets
├── Products
└── Settings
```

### Multi-Tenancy
- Complete data isolation
- Independent user management
- Separate billing
- Custom branding

## User Roles

### Platform Admin
- Manage all organizations
- System configuration
- Global monitoring
- Support access

### Organization Admin
- Full organization control
- User management
- Billing access
- Settings configuration

### Organization Member
- Create/edit content
- Manage assets
- Full platform access
- No admin functions

### External User
- Limited read access
- Asset downloads
- No editing rights
- Invite-based access

## Organization Setup

### 1. Creation
Organizations are created by platform admins:
```typescript
{
  "name": "Fashion Brand Inc",
  "domain": "fashionbrand.com",
  "settings": {
    "storage_quota_gb": 50,
    "monthly_generation_quota": 1000
  }
}
```

### 2. Branding
Customize organization appearance:
- Logo upload
- Brand colors
- Custom domain (Enterprise)
- Email templates

### 3. User Management
Add and manage team members:
- Send invitations
- Assign roles
- Set permissions
- Monitor activity

## Features

### Resource Management
- Storage quotas
- Generation limits
- User seats
- API rate limits

### Billing & Subscriptions
- Subscription tiers
- Usage tracking
- Invoice management
- Payment methods

### Security
- SSO integration (Enterprise)
- 2FA enforcement
- IP restrictions
- Audit logs

### Collaboration
- Shared workspaces
- Team notifications
- Activity feeds
- Comments

## Administration

### Settings Management
Configure organization preferences:
- Default workflow stages
- Naming conventions
- Tag libraries
- Export formats

### User Administration
Manage team access:
```typescript
// Invite user
POST /rest/v1/invitations
{
  "email": "<EMAIL>",
  "role": "member",
  "organization_id": "org-uuid"
}
```

### Usage Monitoring
Track resource utilization:
- Storage usage
- Generation counts
- API calls
- User activity

## Best Practices

### Organization Structure
1. **Clear Naming** - Use official brand names
2. **Role Assignment** - Follow least privilege
3. **Regular Audits** - Review user access
4. **Documentation** - Maintain setup guides

### User Management
1. **Onboarding** - Provide training materials
2. **Offboarding** - Remove access promptly
3. **Role Reviews** - Quarterly access audits
4. **Activity Monitoring** - Track usage patterns

### Security
1. **Enforce 2FA** - For all admin users
2. **Regular Reviews** - Check security settings
3. **Access Logs** - Monitor suspicious activity
4. **Data Backup** - Regular export schedules

## Integration

### API Access
Organizations can access data via API:
```typescript
// Get organization details
GET /rest/v1/organizations/:id

// List organization users
GET /rest/v1/organization_users?organization_id=eq.uuid
```

### Webhooks
Configure webhooks for events:
- User added/removed
- Quota exceeded
- Asset uploaded
- Campaign created

### External Systems
Integration options:
- SSO providers
- CDN services
- Analytics platforms
- CRM systems

## Troubleshooting

### Common Issues

1. **Access Denied**
   - Check role assignments
   - Verify organization membership
   - Review permissions

2. **Quota Exceeded**
   - Monitor usage dashboard
   - Upgrade plan
   - Clean up old assets

3. **Invitation Issues**
   - Check email delivery
   - Verify email domain
   - Resend invitation

## Related Documentation

- [User Roles & Permissions](../../07-security/access-control.md)
- [Collections Guide](../collections/README.md)
- [API Authentication](../../04-api/authentication.md)
- [Billing Guide](../../10-reference/billing.md)