# Collections Guide

## Overview

Collections (displayed as "Campaigns" in the UI) are the primary organizational unit for grouping related fashion assets, products, and creative content within FashionLab.

## Key Concepts

### Collection Structure
```
Collection (Campaign)
├── Products
│   ├── SKUs
│   └── Variants
├── Assets
│   ├── Product Images
│   ├── Lifestyle Shots
│   └── Marketing Materials
└── Metadata
    ├── Brief
    ├── Tags
    └── Settings
```

### Collection Types
1. **Seasonal Campaigns** - Spring/Summer, Fall/Winter
2. **Product Launches** - New product lines
3. **Marketing Campaigns** - Promotional content
4. **Lookbooks** - Style guides and catalogs

## Creating Collections

### 1. Basic Setup
```typescript
{
  "name": "Summer 2024 Collection",
  "brief": "Vibrant beachwear focusing on sustainability",
  "tags": ["summer", "2024", "beachwear"],
  "visibility": "organization",
  "target_date": "2024-06-01"
}
```

### 2. Add Products
- Import product catalog
- Create new products
- Define variants (sizes, colors)
- Set pricing info

### 3. Generate Assets
- Upload existing images
- Generate AI imagery
- Process and enhance
- Organize by product

## Collection Management

### Workflow Stages
Track asset progress through stages:
1. **Planning** - Brief and ideation
2. **Production** - Asset creation
3. **Review** - Quality control
4. **Approved** - Ready for use
5. **Published** - Live and distributed

### Collaboration Features
- Team assignments
- Task management
- Comments and feedback
- Approval workflows

### Asset Organization
- Automatic grouping by product
- Custom folder structure
- Tag-based filtering
- Smart collections

## Features

### Bulk Operations
- Import product CSV
- Batch asset upload
- Mass tagging
- Bulk status updates

### Templates
Save and reuse collection structures:
- Campaign briefs
- Product templates
- Workflow presets
- Tag libraries

### Distribution
Share collections internally and externally:
- Team access control
- Client preview links
- Download packages
- API access

### Analytics
Track collection performance:
- Asset usage stats
- Download metrics
- Team activity
- Timeline tracking

## Best Practices

### Planning
1. **Clear Briefs** - Define objectives and requirements
2. **Product Structure** - Organize SKUs logically
3. **Naming Conventions** - Consistent across assets
4. **Timeline Management** - Set realistic deadlines

### Execution
1. **Progressive Enhancement** - Start with basics
2. **Quality Control** - Review at each stage
3. **Version Control** - Track changes
4. **Regular Updates** - Keep team informed

### Organization
1. **Logical Grouping** - Products, styles, shots
2. **Comprehensive Tagging** - Season, style, color
3. **Clear Status** - Track progress visually
4. **Archive Completed** - Keep workspace clean

## Advanced Features

### Collection Templates
```typescript
// Create from template
POST /rest/v1/collections
{
  "template_id": "seasonal-campaign",
  "name": "Fall 2024",
  "customizations": {...}
}
```

### Automated Workflows
- Auto-tagging rules
- Status transitions
- Notification triggers
- Export schedules

### Integration Points
- Product Information Management (PIM)
- Digital Asset Management (DAM)
- Content Management Systems (CMS)
- E-commerce platforms

## API Usage

### List Collections
```typescript
GET /rest/v1/collections?organization_id=eq.uuid
```

### Create Collection
```typescript
POST /rest/v1/collections
{
  "name": "New Campaign",
  "organization_id": "uuid",
  "brief": "Campaign details"
}
```

### Add Products
```typescript
POST /rest/v1/products
{
  "collection_id": "uuid",
  "name": "Summer Dress",
  "sku": "SMR-DRS-001"
}
```

## Troubleshooting

### Common Issues

1. **Missing Assets**
   - Check filters
   - Verify upload status
   - Review permissions

2. **Workflow Blocks**
   - Check approval queue
   - Review stage requirements
   - Contact approvers

3. **Export Problems**
   - Verify selection
   - Check file sizes
   - Monitor progress

## Related Documentation

- [Asset Management](../assets/README.md)
- [Product Management](./product-management.md)
- [Workflow Stages](./workflow-stages.md)
- [API Reference](../../04-api/README.md)