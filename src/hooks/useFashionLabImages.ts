import { useState, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { FashionLabImageService } from '../services/fashionLabImageService';
import { toast } from 'react-hot-toast';

interface UseGenerateImagesOptions {
  collectionId: string;
  onSuccess?: (queueId: string) => void;
  onComplete?: (images: string[]) => void;
}

interface GenerateParams {
  prompt: string;
  faceImage: string;
  image2: string;
  image3: string;
  image4: string;
  metadata?: Record<string, unknown>;
  // Seed values for reproducible generation (optional)
  seed1?: number | null;
  seed2?: number | null;
  seed3?: number | null;
  seed4?: number | null;
  // Number of images to generate (will make multiple API calls)
  numImages?: number;
}

export function useFashionLabImages(options: UseGenerateImagesOptions) {
  const queryClient = useQueryClient();
  const [activeQueueIds, setActiveQueueIds] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [currentMetadata, setCurrentMetadata] = useState<Record<string, unknown> | null>(null);

  // Generate images mutation (V2 API)
  const generateMutation = useMutation({
    mutationFn: async (params: GenerateParams) => {
      setCurrentPrompt(params.prompt);
      setCurrentMetadata(params.metadata || null);

      const numImages = params.numImages || 1;
      const queueIds: string[] = [];
      const errors: string[] = [];

      console.log(`[FashionLab] Starting generation of ${numImages} images`);

      // Make multiple API calls for multiple images
      for (let i = 0; i < numImages; i++) {
        try {
          console.log(`[FashionLab] Generating image ${i + 1}/${numImages}`);
          
          // Add a small delay between API calls to avoid potential rate limiting
          if (i > 0) {
            await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
          }
          
          const result = await FashionLabImageService.generateImages({
            prompt: params.prompt,
            faceImage: params.faceImage,
            image2: params.image2,
            image3: params.image3,
            image4: params.image4,
            collectionId: options.collectionId,
            storeOnCompletion: true,
            metadata: {
              ...params.metadata,
              imageIndex: i + 1,
              totalImages: numImages,
            },
            // Only include seeds if they are explicitly set (not null or undefined)
            // This allows the API to generate random seeds when not provided
            ...(params.seed1 !== null && params.seed1 !== undefined && { seed1: params.seed1 }),
            ...(params.seed2 !== null && params.seed2 !== undefined && { seed2: params.seed2 }),
            ...(params.seed3 !== null && params.seed3 !== undefined && { seed3: params.seed3 }),
            ...(params.seed4 !== null && params.seed4 !== undefined && { seed4: params.seed4 }),
            // Always pass numImages as 1 since we're making individual calls
            numImages: 1,
          });

          if (result && result.queue_id) {
            queueIds.push(result.queue_id);
            console.log(`[FashionLab] Successfully queued image ${i + 1}/${numImages} with queue_id: ${result.queue_id}`);
          } else {
            console.error(`[FashionLab] Failed to get queue_id for image ${i + 1}/${numImages}`);
            errors.push(`Image ${i + 1}: No queue_id received`);
          }
        } catch (error) {
          console.error(`[FashionLab] Error generating image ${i + 1}/${numImages}:`, error);
          errors.push(`Image ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
          
          // Continue with remaining images instead of failing completely
          continue;
        }
      }

      // If we got some queue IDs but not all, still return what we have
      if (queueIds.length > 0) {
        if (errors.length > 0) {
          console.warn(`[FashionLab] Partial success: ${queueIds.length}/${numImages} images queued. Errors:`, errors);
          toast.error(`Generated ${queueIds.length} of ${numImages} images. Some failed.`);
        }
        return queueIds;
      }

      // If no queue IDs were generated, throw an error
      if (queueIds.length === 0) {
        throw new Error(`Failed to generate any images. Errors: ${errors.join('; ')}`);
      }

      return queueIds;
    },
    onSuccess: (queueIds) => {
      setActiveQueueIds(queueIds);
      setProgress(0);
      toast.success(`Image generation started (${queueIds.length} ${queueIds.length === 1 ? 'image' : 'images'})`);

      // For backward compatibility, call onSuccess with the first queue ID
      if (queueIds.length > 0) {
        options.onSuccess?.(queueIds[0]);
      }

      // Start polling for completion of all queue IDs
      queueIds.forEach(queueId => pollForCompletion(queueId));
    },
    onError: (error: Error) => {
      toast.error(`Failed to generate images: ${error.message}`);
    },
  });

  // Poll for completion
  const pollForCompletion = useCallback(async (queueId: string) => {
    try {
      const result = await FashionLabImageService.waitForCompletion(
        queueId,
        options.collectionId,
        {
          onProgress: (queueProgress) => {
            // Calculate overall progress when multiple queues are active
            if (activeQueueIds.length > 1) {
              // This is a simple average, could be improved with per-queue tracking
              setProgress(queueProgress);
            } else {
              setProgress(queueProgress);
            }
          },
          prompt: currentPrompt,
          metadata: currentMetadata || undefined,
        }
      );

      if (result.status === 'completed' && result.stored) {
        // Remove this queue ID from active list
        setActiveQueueIds(prev => prev.filter(id => id !== queueId));
        
        // Only show success and invalidate queries when all are complete
        if (activeQueueIds.length <= 1) { // This is the last one
          toast.success('All images generated and stored successfully');
          
          // Invalidate queries to refresh image lists
          queryClient.invalidateQueries({ queryKey: ['ai-generated-images', options.collectionId] });
          queryClient.invalidateQueries({ queryKey: ['generated-images', options.collectionId] });
          
          setProgress(0);
        }

        if (result.images) {
          options.onComplete?.(result.images);
        }
      } else if (result.status === 'failed') {
        toast.error(`Image generation failed for queue ${queueId}`);
        setActiveQueueIds(prev => prev.filter(id => id !== queueId));
        
        if (activeQueueIds.length <= 1) {
          setProgress(0);
        }
      }
    } catch (error) {
      toast.error(`Failed to complete image generation for queue ${queueId}`);
      setActiveQueueIds(prev => prev.filter(id => id !== queueId));
      
      if (activeQueueIds.length <= 1) {
        setProgress(0);
      }
    }
  }, [options, queryClient, currentPrompt, currentMetadata, activeQueueIds]);

  // Query for generated images
  const generatedImagesQuery = useQuery({
    queryKey: ['generated-images', options.collectionId],
    queryFn: () => FashionLabImageService.getGeneratedImages(options.collectionId),
    enabled: !!options.collectionId,
  });

  // Check status manually
  const checkStatus = useCallback(async (queueId: string) => {
    const result = await FashionLabImageService.checkQueueStatus(
      queueId,
      options.collectionId
    );
    return result;
  }, [options.collectionId]);

  return {
    generate: generateMutation.mutate,
    isGenerating: generateMutation.isPending || activeQueueIds.length > 0,
    progress,
    activeQueueIds,
    // For backward compatibility, return the first queue ID
    activeQueueId: activeQueueIds.length > 0 ? activeQueueIds[0] : null,
    generatedImages: generatedImagesQuery.data || [],
    isLoadingImages: generatedImagesQuery.isLoading,
    checkStatus,
    refetchImages: generatedImagesQuery.refetch,
  };
}