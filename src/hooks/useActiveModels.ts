import { useState, useEffect } from 'react';
import { supabase } from '../components/common/utils/supabase';
import { Model, ModelImage } from './useModelLibrary';

interface ModelWithImages extends Model {
  imageMapping: Record<string, string>;
}

export function useActiveModels() {
  const [models, setModels] = useState<ModelWithImages[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActiveModels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase
        .from('model_library')
        .select(`
          *,
          images:model_images(*),
          organization:organizations(name),
          collection:collections(name)
        `)
        .eq('is_active', true)
        .order('display_order', { ascending: true });

      if (error) throw error;

      // Transform data to include image mapping
      const modelsWithImages = (data || []).map(model => {
        const imageMapping: Record<string, string> = {};
        
        // Map our database angle types to the format expected by ImageGenerator components
        const angleTypeMapping: Record<string, string> = {
          'face': 'face',
          'half-body-front': 'Half-body Front',
          'half-body-back': 'Half-body Back',
          'half-body-34-left': 'Half-body 3/4 Left',
          'half-body-34-right': 'Half-body 3/4 Right',
          'full-body-front': 'Full-height Front',
          'full-body-back': 'Full-height Back',
          'full-body-side-left': 'Full-height Side Left',
          'full-body-side-right': 'Full-height Side Right',
          'full-body-34-left': 'Full-height 3/4 Left',
          'full-body-34-right': 'Full-height 3/4 Right',
          // Also map the reverse for compatibility
          'Half-body Front': 'half-body-front',
          'Half-body Back': 'half-body-back',
          'Half-body 3/4 Left': 'half-body-34-left',
          'Half-body 3/4 Right': 'half-body-34-right',
          'Full-height Front': 'full-body-front',
          'Full-height Back': 'full-body-back',
          'Full-height Side Left': 'full-body-side-left',
          'Full-height Side Right': 'full-body-side-right',
          'Full-height 3/4 Left': 'full-body-34-left',
          'Full-height 3/4 Right': 'full-body-34-right'
        };
        
        // Create mapping from angle_type to public URL
        model.images?.forEach((image: ModelImage) => {
          if (image.storage_path) {
            // Remove duplicate prefix if it exists
            const cleanPath = image.storage_path.replace(/^model-library\//, '');
            const { data } = supabase.storage.from('model-library').getPublicUrl(cleanPath);
            const mappedAngleType = angleTypeMapping[image.angle_type] || image.angle_type;
            imageMapping[mappedAngleType] = data?.publicUrl || '';
            
            // Also add the original angle type for compatibility
            imageMapping[image.angle_type] = data?.publicUrl || '';
          }
        });

        return {
          ...model,
          imageMapping,
          organization_name: model.organization?.name,
          collection_name: model.collection?.name,
          organization: undefined, // Remove nested object
          collection: undefined    // Remove nested object
        };
      });

      setModels(modelsWithImages);
    } catch (err) {
      console.error('Error fetching active models:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch models');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchActiveModels();
  }, []);

  // Create backward compatible format for ImageGeneratorDemo
  const modelImageMapping = models.reduce((acc, model) => {
    acc[model.code] = model.imageMapping;
    return acc;
  }, {} as Record<string, Record<string, string>>);

  // Create model blocks for the UI
  const modelBlocks = models.map(model => ({
    id: model.code,
    name: model.name,
    tag: model.code
  }));

  return {
    models,
    modelBlocks,
    modelImageMapping,
    isLoading,
    error,
    refetch: fetchActiveModels
  };
}