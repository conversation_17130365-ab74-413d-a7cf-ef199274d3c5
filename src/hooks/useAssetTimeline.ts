import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

/**
 * Hook to fetch and manage asset timeline data
 * This demonstrates how the timeline data flows from database to UI
 */

interface TimelineFilters {
  collectionId: string;
  productId?: string;
  size?: string;
  viewType?: string;
  tags?: string[];
}

interface AssetTimelineData {
  lineageId: string;
  stages: {
    stage: string;
    assets: {
      id: string;
      filePath: string;
      thumbnailPath: string;
      metadata: any;
      createdAt: string;
      createdBy: {
        id: string;
        name: string;
        avatarUrl?: string;
      };
      comments: {
        id: string;
        content: string;
        status: string;
        createdAt: string;
        author: {
          name: string;
          avatarUrl?: string;
        };
      }[];
      parentAssetId?: string;
      childAssets?: string[];
    }[];
  }[];
  variantGroup?: {
    productName?: string;
    size?: string;
    viewType?: string;
  };
  totalAssets: number;
  totalComments: number;
}

export function useAssetTimeline(filters: TimelineFilters) {
  return useQuery({
    queryKey: ['asset-timeline', filters],
    queryFn: async () => {
      // Step 1: Find all lineages matching the filters
      const lineageQuery = supabase
        .from('asset_lineage')
        .select(`
          lineage_id,
          asset:assets!inner(
            id,
            file_path,
            thumbnail_path,
            workflow_stage,
            metadata,
            created_at,
            created_by,
            variant_group:asset_variant_groups(
              size,
              view_type,
              product:products(
                name
              )
            )
          )
        `)
        .eq('asset.collection_id', filters.collectionId);

      // Apply optional filters
      if (filters.productId) {
        lineageQuery.eq('asset.variant_group.product_id', filters.productId);
      }
      if (filters.size) {
        lineageQuery.eq('asset.variant_group.size', filters.size);
      }
      if (filters.viewType) {
        lineageQuery.eq('asset.variant_group.view_type', filters.viewType);
      }

      const { data: lineageResults, error: lineageError } = await lineageQuery;
      
      if (lineageError) throw lineageError;

      // Step 2: Group by lineage and get full timeline data
      const lineageIds = [...new Set(lineageResults?.map(r => r.lineage_id) || [])];
      
      const timelines = await Promise.all(
        lineageIds.map(async (lineageId) => {
          // Get all assets in this lineage with comments
          const { data: timelineData, error: timelineError } = await supabase
            .from('asset_lineage')
            .select(`
              lineage_id,
              asset_id,
              parent_asset_id,
              metadata,
              asset:assets!inner(
                id,
                file_path,
                thumbnail_path,
                workflow_stage,
                metadata,
                created_at,
                created_by:users!assets_created_by_fkey(
                  id,
                  full_name,
                  avatar_url
                ),
                comments(
                  id,
                  content,
                  status,
                  created_at,
                  user:users!comments_user_id_fkey(
                    full_name,
                    avatar_url
                  )
                ),
                variant_group:asset_variant_groups(
                  size,
                  view_type,
                  product:products(
                    name
                  )
                )
              )
            `)
            .eq('lineage_id', lineageId)
            .order('asset.workflow_stage');

          if (timelineError) throw timelineError;

          // Group assets by workflow stage
          const stageGroups = timelineData?.reduce((acc, item) => {
            const stage = item.asset.workflow_stage;
            if (!acc[stage]) {
              acc[stage] = [];
            }
            acc[stage].push({
              id: item.asset.id,
              filePath: item.asset.file_path,
              thumbnailPath: item.asset.thumbnail_path,
              metadata: item.asset.metadata,
              createdAt: item.asset.created_at,
              createdBy: {
                id: item.asset.created_by.id,
                name: item.asset.created_by.full_name,
                avatarUrl: item.asset.created_by.avatar_url
              },
              comments: item.asset.comments.map((c: any) => ({
                id: c.id,
                content: c.content,
                status: c.status,
                createdAt: c.created_at,
                author: {
                  name: c.user.full_name,
                  avatarUrl: c.user.avatar_url
                }
              })),
              parentAssetId: item.parent_asset_id
            });
            return acc;
          }, {} as Record<string, any[]>);

          // Convert to array format
          const stages = Object.entries(stageGroups).map(([stage, assets]) => ({
            stage,
            assets
          }));

          // Get variant group info from first asset
          const firstAsset = timelineData?.[0]?.asset;
          const variantGroup = firstAsset?.variant_group ? {
            productName: firstAsset.variant_group.product?.name,
            size: firstAsset.variant_group.size,
            viewType: firstAsset.variant_group.view_type
          } : undefined;

          // Calculate totals
          const totalAssets = timelineData?.length || 0;
          const totalComments = timelineData?.reduce(
            (sum, item) => sum + (item.asset.comments?.length || 0), 
            0
          ) || 0;

          return {
            lineageId,
            stages,
            variantGroup,
            totalAssets,
            totalComments
          };
        })
      );

      return timelines;
    },
    staleTime: 30000, // Consider data stale after 30 seconds
  });
}

/**
 * Hook to create asset relationships when generating new assets
 */
export function useCreateAssetLineage() {
  return async (params: {
    parentAssetIds: string[];
    newAssetId: string;
    lineageId?: string; // Use existing or create new
    metadata?: Record<string, any>;
  }) => {
    const { parentAssetIds, newAssetId, metadata = {} } = params;
    
    // If no lineage ID provided, inherit from first parent
    let lineageId = params.lineageId;
    
    if (!lineageId && parentAssetIds.length > 0) {
      const { data: parentLineage } = await supabase
        .from('asset_lineage')
        .select('lineage_id')
        .eq('asset_id', parentAssetIds[0])
        .single();
      
      lineageId = parentLineage?.lineage_id;
    }
    
    // Create new lineage if needed
    if (!lineageId) {
      lineageId = crypto.randomUUID();
    }
    
    // Insert lineage record
    const lineageMetadata = {
      ...metadata,
      parent_assets: parentAssetIds.length > 1 ? parentAssetIds : undefined,
      generation_type: parentAssetIds.length > 1 ? 'multi_source' : 'single_source'
    };
    
    const { error } = await supabase
      .from('asset_lineage')
      .insert({
        lineage_id: lineageId,
        asset_id: newAssetId,
        parent_asset_id: parentAssetIds.length === 1 ? parentAssetIds[0] : null,
        metadata: lineageMetadata
      });
    
    if (error) throw error;
    
    return lineageId;
  };
}

/**
 * Hook to link existing assets into a timeline
 */
export function useLinkAssets() {
  return async (assetIds: string[], groupName?: string) => {
    const lineageId = crypto.randomUUID();
    
    // Create lineage records for all assets
    const lineageRecords = assetIds.map((assetId, index) => ({
      lineage_id: lineageId,
      asset_id: assetId,
      parent_asset_id: null, // These are root assets in the new lineage
      metadata: {
        linked_retroactively: true,
        original_position: index,
        group_name: groupName
      }
    }));
    
    const { error } = await supabase
      .from('asset_lineage')
      .insert(lineageRecords);
    
    if (error) throw error;
    
    return lineageId;
  };
}