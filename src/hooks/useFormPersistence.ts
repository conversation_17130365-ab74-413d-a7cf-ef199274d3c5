import { useEffect, useCallback } from 'react';

// Import the CollectionFormData type from our form hook
import type { CollectionFormData } from './useCollectionForm';

interface UseFormPersistenceOptions {
  storageKey?: string;
  stepKey?: string;
  completedStepsKey?: string;
  enabled?: boolean;
}

interface UseFormPersistenceReturn {
  persistFormData: (data: CollectionFormData) => void;
  persistStep: (step: number) => void;
  persistCompletedSteps: (steps: number[]) => void;
  loadFormData: () => CollectionFormData | null;
  loadStep: () => number | null;
  loadCompletedSteps: () => number[];
  clearStoredData: () => void;
  clearAll: () => void;
}

const DEFAULT_OPTIONS: Required<UseFormPersistenceOptions> = {
  storageKey: 'collectionFormData',
  stepKey: 'collectionCurrentStep',
  completedStepsKey: 'collectionCompletedSteps',
  enabled: true
};

// Utility to safely serialize form data (removes File objects)
const serializeFormData = (data: CollectionFormData): string => {
  const serializable = { ...data };
  
  // Remove File objects that can't be serialized
  if (serializable.coverImage) {
    serializable.coverImage = null;
  }
  
  if (serializable.briefFiles) {
    serializable.briefFiles = null;
  }
  
  // Reset file arrays in references
  if (serializable.references) {
    serializable.references = {
      existingShoot: [],
      productTraining: [],
      stylingElements: []
    };
  }
  
  // Reset file arrays in enhanced brief
  if (serializable.enhancedBrief) {
    const enhancedBrief = { ...serializable.enhancedBrief };
    
    // Reset all file arrays
    Object.keys(enhancedBrief).forEach(key => {
      const section = enhancedBrief[key as keyof typeof enhancedBrief];
      if (section && typeof section === 'object' && 'files' in section) {
        (section as any).files = [];
      }
    });
    
    // Reset specific file arrays
    if (enhancedBrief.modelProfile) {
      enhancedBrief.modelProfile.faceExamples = [];
      enhancedBrief.modelProfile.bodyExamples = [];
    }
    
    serializable.enhancedBrief = enhancedBrief;
  }
  
  return JSON.stringify(serializable);
};

// Utility to safely deserialize form data
const deserializeFormData = (data: string): CollectionFormData | null => {
  try {
    const parsed = JSON.parse(data);
    
    // Ensure all required nested objects exist
    if (!parsed.references) {
      parsed.references = {
        existingShoot: [],
        productTraining: [],
        stylingElements: []
      };
    }
    
    if (!parsed.imageOutput) {
      parsed.imageOutput = {
        types: {
          fullHeightFront: 0,
          fullHeightBack: 0,
          fullHeightSide: 0,
          halfBody: 0,
          portrait: 0
        },
        deadline: '',
        pixelSize: { width: 1200, height: 1600 },
        format: 'JPG',
        maxResolution: 10
      };
    }
    
    if (!parsed.enhancedBrief) {
      parsed.enhancedBrief = {
        targetGarmentImages: { files: [] },
        secondaryGarment: { files: [] },
        shoes: { files: [] },
        stylingDetails: { files: [], notes: '' },
        photoshootStyle: { files: [], backgroundNotes: '', lightingNotes: '' },
        modelProfile: { 
          faceExamples: [], 
          bodyExamples: [], 
          ageRange: '', 
          appearance: '' 
        },
        requiredAngles: {
          halfBodyFront: false,
          halfBodyBack: false,
          halfBodySide: false,
          selectedAngles: []
        },
        formatSelection: {
          portrait: false,
          landscape: false
        }
      };
    }
    
    // Ensure all enhanced brief sections exist
    const requiredSections = [
      'targetGarmentImages', 'secondaryGarment', 'shoes', 
      'stylingDetails', 'photoshootStyle', 'modelProfile',
      'requiredAngles', 'formatSelection'
    ];
    
    requiredSections.forEach(section => {
      if (!parsed.enhancedBrief[section]) {
        switch (section) {
          case 'targetGarmentImages':
          case 'secondaryGarment':
          case 'shoes':
            parsed.enhancedBrief[section] = { files: [] };
            break;
          case 'stylingDetails':
            parsed.enhancedBrief[section] = { files: [], notes: '' };
            break;
          case 'photoshootStyle':
            parsed.enhancedBrief[section] = { files: [], backgroundNotes: '', lightingNotes: '' };
            break;
          case 'modelProfile':
            parsed.enhancedBrief[section] = { 
              faceExamples: [], 
              bodyExamples: [], 
              ageRange: '', 
              appearance: '' 
            };
            break;
          case 'requiredAngles':
            parsed.enhancedBrief[section] = {
              halfBodyFront: false,
              halfBodyBack: false,
              halfBodySide: false,
              selectedAngles: []
            };
            break;
          case 'formatSelection':
            parsed.enhancedBrief[section] = {
              portrait: false,
              landscape: false
            };
            break;
        }
      }
    });
    
    return parsed;
  } catch (error) {
    console.error('Failed to deserialize form data:', error);
    return null;
  }
};

export const useFormPersistence = (
  formData: CollectionFormData,
  currentStep: number,
  completedSteps: number[],
  options: UseFormPersistenceOptions = {}
): UseFormPersistenceReturn => {
  const config = { ...DEFAULT_OPTIONS, ...options };

  // Persist form data to localStorage
  const persistFormData = useCallback((data: CollectionFormData) => {
    if (!config.enabled) return;
    
    try {
      const serialized = serializeFormData(data);
      localStorage.setItem(config.storageKey, serialized);
    } catch (error) {
      console.error('Failed to persist form data:', error);
    }
  }, [config.enabled, config.storageKey]);

  // Persist current step
  const persistStep = useCallback((step: number) => {
    if (!config.enabled) return;
    
    try {
      localStorage.setItem(config.stepKey, step.toString());
    } catch (error) {
      console.error('Failed to persist step:', error);
    }
  }, [config.enabled, config.stepKey]);

  // Persist completed steps
  const persistCompletedSteps = useCallback((steps: number[]) => {
    if (!config.enabled) return;
    
    try {
      localStorage.setItem(config.completedStepsKey, JSON.stringify(steps));
    } catch (error) {
      console.error('Failed to persist completed steps:', error);
    }
  }, [config.enabled, config.completedStepsKey]);

  // Load form data from localStorage
  const loadFormData = useCallback((): CollectionFormData | null => {
    if (!config.enabled) return null;
    
    try {
      const stored = localStorage.getItem(config.storageKey);
      return stored ? deserializeFormData(stored) : null;
    } catch (error) {
      console.error('Failed to load form data:', error);
      return null;
    }
  }, [config.enabled, config.storageKey]);

  // Load current step
  const loadStep = useCallback((): number | null => {
    if (!config.enabled) return null;
    
    try {
      const stored = localStorage.getItem(config.stepKey);
      return stored ? parseInt(stored, 10) : null;
    } catch (error) {
      console.error('Failed to load step:', error);
      return null;
    }
  }, [config.enabled, config.stepKey]);

  // Load completed steps
  const loadCompletedSteps = useCallback((): number[] => {
    if (!config.enabled) return [];
    
    try {
      const stored = localStorage.getItem(config.completedStepsKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load completed steps:', error);
      return [];
    }
  }, [config.enabled, config.completedStepsKey]);

  // Clear stored form data
  const clearStoredData = useCallback(() => {
    if (!config.enabled) return;
    
    try {
      localStorage.removeItem(config.storageKey);
    } catch (error) {
      console.error('Failed to clear stored data:', error);
    }
  }, [config.enabled, config.storageKey]);

  // Clear all stored data
  const clearAll = useCallback(() => {
    if (!config.enabled) return;
    
    try {
      localStorage.removeItem(config.storageKey);
      localStorage.removeItem(config.stepKey);
      localStorage.removeItem(config.completedStepsKey);
    } catch (error) {
      console.error('Failed to clear all stored data:', error);
    }
  }, [config.enabled, config.storageKey, config.stepKey, config.completedStepsKey]);

  // Auto-persist form data when it changes
  useEffect(() => {
    if (config.enabled) {
      persistFormData(formData);
    }
  }, [formData, persistFormData, config.enabled]);

  // Auto-persist step when it changes
  useEffect(() => {
    if (config.enabled) {
      persistStep(currentStep);
    }
  }, [currentStep, persistStep, config.enabled]);

  // Auto-persist completed steps when they change
  useEffect(() => {
    if (config.enabled) {
      persistCompletedSteps(completedSteps);
    }
  }, [completedSteps, persistCompletedSteps, config.enabled]);

  return {
    persistFormData,
    persistStep,
    persistCompletedSteps,
    loadFormData,
    loadStep,
    loadCompletedSteps,
    clearStoredData,
    clearAll
  };
};
