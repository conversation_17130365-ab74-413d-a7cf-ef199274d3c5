import { useQuery } from '@tanstack/react-query';
import { supabase } from '../components/common/utils/supabase';

export interface Organization {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Collection {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  status: 'active' | 'archived';
  created_at: string;
  updated_at: string;
  cover_image_url?: string;
  metadata?: Record<string, any>;
}

// Fetch all organizations the user has access to
export function useOrganizations() {
  return useQuery({
    queryKey: ['organizations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('name', { ascending: true });

      if (error) throw error;
      return data as Organization[];
    }
  });
}

// Fetch collections for a specific organization
export function useCollections(organizationId?: string) {
  return useQuery({
    queryKey: ['collections', organizationId],
    queryFn: async () => {
      if (!organizationId) return [];
      
      const { data, error } = await supabase
        .from('collections')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('status', 'active')
        .order('name', { ascending: true });

      if (error) throw error;
      return data as Collection[];
    },
    enabled: !!organizationId
  });
}

// Fetch all collections the user has access to
export function useAllCollections() {
  return useQuery({
    queryKey: ['collections', 'all'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('collections')
        .select(`
          *,
          organization:organizations(name)
        `)
        .eq('status', 'active')
        .order('name', { ascending: true });

      if (error) throw error;
      
      // Transform the data to include organization name
      const collections = data?.map(collection => ({
        ...collection,
        organization_name: collection.organization?.name,
        organization: undefined // Remove nested object
      })) || [];
      
      return collections as (Collection & { organization_name?: string })[];
    }
  });
}

// Get user's organization memberships
export function useUserOrganizations() {
  return useQuery({
    queryKey: ['user-organizations'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('organization_memberships')
        .select(`
          *,
          organization:organizations(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      return data?.map(membership => ({
        ...membership,
        organization: membership.organization as Organization
      })) || [];
    }
  });
}
