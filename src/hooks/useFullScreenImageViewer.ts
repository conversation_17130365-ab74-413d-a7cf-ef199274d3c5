import { useState, useCallback } from 'react';

interface ImageItem {
  id: string;
  url: string;
  title?: string;
  description?: string;
}

export function useFullScreenImageViewer(images: ImageItem[] = []) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const openViewer = useCallback((index: number = 0) => {
    setCurrentIndex(index);
    setIsOpen(true);
  }, []);

  const closeViewer = useCallback(() => {
    setIsOpen(false);
  }, []);

  const navigateToImage = useCallback((index: number) => {
    if (index >= 0 && index < images.length) {
      setCurrentIndex(index);
    }
  }, [images.length]);

  return {
    isOpen,
    currentIndex,
    openViewer,
    closeViewer,
    navigateToImage
  };
}