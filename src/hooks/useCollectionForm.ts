import { useState, useCallback } from 'react';

// Define types locally for now - these will be moved to a shared types file later
type CollectionType = 'product' | 'campaign' | '';
type ModelChoice = 'custom' | 'library' | '';

interface ImageOutputTypes {
  fullHeightFront: number;
  fullHeightBack: number;
  fullHeightSide: number;
  halfBody: number;
  portrait: number;
}

interface ImageOutputSettings {
  types: ImageOutputTypes;
  deadline: string;
  pixelSize: {
    width: number;
    height: number;
  };
  imageSize?: string;
  format: 'JPG' | 'TIFF' | 'PNG' | 'WebP' | 'Other';
  customFormat?: string;
  maxFileSize?: number;
  maxResolution: number;
}

interface ReferenceSection {
  existingShoot: File[];
  productTraining: File[];
  stylingElements: File[];
}

interface CampaignDetails {
  purpose: string;
  lookAndFeel: string;
  poses: string;
  targetGroup: string;
}

// Enhanced Brief Sections based on FAS-60 requirements
interface TargetGarmentImages {
  files: File[];
}

interface SecondaryGarment {
  files: File[];
  description?: string;
}

interface ShoesSection {
  files: File[];
}

interface StylingDetails {
  files: File[];
  notes: string;
  textDescriptions: string[];
}

interface PhotoshootStyle {
  files: File[];
  backgroundColor: string;
  dropShadow: string;
  lightDirection: string;
  backgroundNotes: string;
  lightingNotes: string;
}

interface ModelProfile {
  faceExamples: File[];
  bodyExamples: File[];
  ageRange: string;
  appearance: string;
}

interface RequiredAngles {
  halfBodyFront: boolean;
  halfBodyBack: boolean;
  halfBodySide: boolean;
  selectedAngles: string[];
  customAngles: string[];
}

interface FormatSelection {
  portrait: boolean;
  landscape: boolean;
}

// Campaign-specific sections
interface SettingReferences {
  files: File[];
  descriptions: string[];
}

interface PhotoshootInspiration {
  files: File[];
  backgroundSettings: string[];
  lightingDirections: string[];
}

interface EnhancedBriefSections {
  targetGarmentImages: TargetGarmentImages;
  secondaryGarment: SecondaryGarment;
  shoes: ShoesSection;
  stylingDetails: StylingDetails;
  photoshootStyle: PhotoshootStyle;
  modelProfile: ModelProfile;
  requiredAngles: RequiredAngles;
  formatSelection: FormatSelection;
  videoAngles?: string[];
  // Campaign-specific sections
  settingReferences?: SettingReferences;
  photoshootInspiration?: PhotoshootInspiration;
  videoUploads?: File[];
}

export interface CollectionFormData {
  name: string;
  description: string;
  coverImage: File | null;
  collectionType: CollectionType;
  mainProduct?: 'top' | 'bottoms' | 'shoes';
  secondaryProducts?: ('top' | 'bottoms' | 'shoes')[];
  modelChoice: ModelChoice;
  modelCount?: number;
  modelNames?: string[];
  productCount?: number;
  aiImagesPerProduct?: number;
  productsPerImage?: 1 | 2 | 3;
  totalAiImages?: number;
  campaignDetails?: CampaignDetails;
  briefFiles?: File[] | null;
  references: ReferenceSection;
  imageOutput: ImageOutputSettings;
  enhancedBrief?: EnhancedBriefSections;
}

// Export other types that might be needed
export type { CollectionType, ModelChoice, EnhancedBriefSections, ImageOutputSettings, ImageOutputTypes, CampaignDetails, ReferenceSection };

// Type-safe nested field update utility
type NestedKeyOf<T> = {
  [K in keyof T]: T[K] extends object 
    ? K | `${K & string}.${NestedKeyOf<T[K]> & string}`
    : K;
}[keyof T];

interface UseCollectionFormReturn {
  formData: CollectionFormData;
  updateFormData: (updates: Partial<CollectionFormData>) => void;
  updateNestedField: <T>(path: string, value: T) => void;
  updateEnhancedBrief: (
    section: keyof EnhancedBriefSections,
    field: string,
    value: any
  ) => void;
  updateImageOutput: (
    field: keyof ImageOutputSettings | 'types',
    value: any,
    subField?: keyof ImageOutputTypes | 'width' | 'height'
  ) => void;
  updateCampaignDetails: (field: keyof CampaignDetails, value: string) => void;
  updateReferences: (type: keyof ReferenceSection, files: File[]) => void;
  resetForm: () => void;
}

// Default form data factory
const createDefaultFormData = (): CollectionFormData => ({
  name: '',
  description: '',
  coverImage: null,
  collectionType: '',
  modelChoice: '',
  briefFiles: null,
  references: {
    existingShoot: [],
    productTraining: [],
    stylingElements: []
  },
  imageOutput: {
    types: {
      fullHeightFront: 0,
      fullHeightBack: 0,
      fullHeightSide: 0,
      halfBody: 0,
      portrait: 0
    },
    deadline: '',
    pixelSize: {
      width: 1200,
      height: 1600
    },
    format: 'JPG',
    maxResolution: 10
  },
  enhancedBrief: {
    targetGarmentImages: { files: [] },
    secondaryGarment: { files: [], description: '' },
    shoes: { files: [] },
    stylingDetails: { files: [], notes: '', textDescriptions: [] },
    photoshootStyle: {
      files: [],
      backgroundColor: '',
      dropShadow: '',
      lightDirection: '',
      backgroundNotes: '',
      lightingNotes: ''
    },
    modelProfile: {
      faceExamples: [],
      bodyExamples: [],
      ageRange: '',
      appearance: ''
    },
    requiredAngles: {
      halfBodyFront: false,
      halfBodyBack: false,
      halfBodySide: false,
      selectedAngles: [],
      customAngles: []
    },
    formatSelection: {
      portrait: false,
      landscape: false
    },
    settingReferences: { files: [], descriptions: [] },
    photoshootInspiration: { files: [], backgroundSettings: [], lightingDirections: [] },
    videoUploads: []
  }
});

export const useCollectionForm = (initialData?: CollectionFormData): UseCollectionFormReturn => {
  const [formData, setFormData] = useState<CollectionFormData>(() => {
    // Ensure we always have proper default values to avoid controlled/uncontrolled input warnings
    const defaultData = createDefaultFormData();
    if (!initialData) return defaultData;

    // Merge initial data with defaults to ensure all fields are defined
    return {
      ...defaultData,
      ...initialData,
      // Ensure string fields are never undefined
      name: initialData.name || '',
      description: initialData.description || '',
      collectionType: initialData.collectionType || '',
      modelChoice: initialData.modelChoice || ''
    };
  });

  // Basic form data update
  const updateFormData = useCallback((updates: Partial<CollectionFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  }, []);

  // Type-safe nested field update
  const updateNestedField = useCallback(<T>(path: string, value: T) => {
    setFormData(prev => {
      const keys = path.split('.');
      const newData = { ...prev };
      let current: any = newData;
      
      // Navigate to the parent of the target field
      for (let i = 0; i < keys.length - 1; i++) {
        if (current[keys[i]] === undefined) {
          current[keys[i]] = {};
        }
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }
      
      // Set the final value
      current[keys[keys.length - 1]] = value;
      return newData;
    });
  }, []);

  // Enhanced brief section updates
  const updateEnhancedBrief = useCallback((
    section: keyof EnhancedBriefSections,
    field: string,
    value: any
  ) => {
    setFormData(prev => {
      if (!prev.enhancedBrief) return prev;
      
      return {
        ...prev,
        enhancedBrief: {
          ...prev.enhancedBrief,
          [section]: {
            ...prev.enhancedBrief[section],
            [field]: value
          }
        }
      };
    });
  }, []);

  // Image output settings updates
  const updateImageOutput = useCallback((
    field: keyof ImageOutputSettings | 'types',
    value: any,
    subField?: keyof ImageOutputTypes | 'width' | 'height'
  ) => {
    setFormData(prev => {
      if (field === 'types' && subField) {
        return {
          ...prev,
          imageOutput: {
            ...prev.imageOutput,
            types: {
              ...prev.imageOutput.types,
              [subField]: value
            }
          }
        };
      } else if (field === 'pixelSize' && subField) {
        return {
          ...prev,
          imageOutput: {
            ...prev.imageOutput,
            pixelSize: {
              ...prev.imageOutput.pixelSize,
              [subField]: value
            }
          }
        };
      } else {
        return {
          ...prev,
          imageOutput: {
            ...prev.imageOutput,
            [field]: value
          }
        };
      }
    });
  }, []);

  // Campaign details updates
  const updateCampaignDetails = useCallback((field: keyof CampaignDetails, value: string) => {
    setFormData(prev => ({
      ...prev,
      campaignDetails: {
        purpose: prev.campaignDetails?.purpose || '',
        lookAndFeel: prev.campaignDetails?.lookAndFeel || '',
        poses: prev.campaignDetails?.poses || '',
        targetGroup: prev.campaignDetails?.targetGroup || '',
        ...prev.campaignDetails,
        [field]: value
      }
    }));
  }, []);

  // References updates
  const updateReferences = useCallback((type: keyof ReferenceSection, files: File[]) => {
    setFormData(prev => ({
      ...prev,
      references: {
        ...prev.references,
        [type]: files
      }
    }));
  }, []);

  // Reset form to default state
  const resetForm = useCallback(() => {
    setFormData(createDefaultFormData());
  }, []);

  return {
    formData,
    updateFormData,
    updateNestedField,
    updateEnhancedBrief,
    updateImageOutput,
    updateCampaignDetails,
    updateReferences,
    resetForm
  };
};
