import { useState, useCallback } from 'react';
import { useToast } from '../components/ui/use-toast';
import type { CollectionFormData } from './useCollectionForm';

interface StepValidationRule {
  step: number;
  validate: (formData: CollectionFormData) => {
    isValid: boolean;
    message?: string;
  };
}

interface UseStepNavigationOptions {
  totalSteps: number;
  initialStep?: number;
  initialCompletedSteps?: number[];
  validationRules?: StepValidationRule[];
}

interface UseStepNavigationReturn {
  currentStep: number;
  completedSteps: number[];
  canGoToStep: (step: number) => boolean;
  goToStep: (step: number, formData?: CollectionFormData) => boolean;
  nextStep: (formData?: CollectionFormData) => boolean;
  prevStep: () => void;
  markStepCompleted: (step: number) => void;
  isStepCompleted: (step: number) => boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
  progress: number;
}

const DEFAULT_VALIDATION_RULES: StepValidationRule[] = [
  {
    step: 1,
    validate: (formData) => ({
      isValid: formData.name?.trim() !== '' && formData.description?.trim() !== '',
      message: 'Please fill in the collection name and description'
    })
  },
  {
    step: 2,
    validate: (formData) => ({
      isValid: formData.collectionType !== '',
      message: 'Please select a collection type'
    })
  },
  {
    step: 3,
    validate: (formData) => ({
      isValid: formData.modelChoice !== '',
      message: 'Please select a model choice'
    })
  },
  {
    step: 4,
    validate: (formData) => {
      // Check product count for product type
      if (formData.collectionType === 'product' && !formData.productCount) {
        return {
          isValid: false,
          message: 'Please specify the number of products'
        };
      }

      // Check Target Garment Images requirement (FAS-60)
      const targetGarmentFiles = formData.enhancedBrief?.targetGarmentImages?.files || [];
      if (targetGarmentFiles.length < 5) {
        return {
          isValid: false,
          message: 'Please upload at least 5 target garment images (required)'
        };
      }

      return { isValid: true };
    }
  }
];

export const useStepNavigation = ({
  totalSteps,
  initialStep = 1,
  initialCompletedSteps = [],
  validationRules = DEFAULT_VALIDATION_RULES
}: UseStepNavigationOptions): UseStepNavigationReturn => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<number[]>(initialCompletedSteps);
  const { toast } = useToast();

  // Check if we can navigate to a specific step
  const canGoToStep = useCallback((step: number): boolean => {
    if (step < 1 || step > totalSteps) return false;
    if (step === currentStep) return true;
    if (step < currentStep) return true; // Can always go back

    // Allow forward navigation to the next step only
    if (step === currentStep + 1) return true;

    // For navigation beyond the next step, check if previous steps are completed
    for (let i = 1; i < step; i++) {
      if (!completedSteps.includes(i)) {
        return false;
      }
    }

    return true;
  }, [currentStep, completedSteps, totalSteps]);

  // Validate a specific step
  const validateStep = useCallback((step: number, formData?: CollectionFormData) => {
    if (!formData) return { isValid: true };

    const rule = validationRules.find(r => r.step === step);
    if (!rule) return { isValid: true };

    return rule.validate(formData);
  }, [validationRules]);

  // Mark a step as completed
  const markStepCompleted = useCallback((step: number) => {
    setCompletedSteps(prev => {
      if (prev.includes(step)) return prev;
      return [...prev, step].sort((a, b) => a - b);
    });
  }, []);

  // Navigate to a specific step
  const goToStep = useCallback((step: number, formData?: CollectionFormData): boolean => {
    if (!canGoToStep(step)) {
      toast({
        title: "Navigation blocked",
        description: "Please complete the previous steps first",
        variant: "destructive"
      });
      return false;
    }

    // If moving forward by more than one step, validate current step
    if (step > currentStep + 1 && formData) {
      const validation = validateStep(currentStep, formData);
      if (!validation.isValid) {
        toast({
          title: "Validation failed",
          description: validation.message || "Please complete the required fields",
          variant: "destructive"
        });
        return false;
      }

      // Mark current step as completed
      markStepCompleted(currentStep);
    }

    setCurrentStep(step);
    return true;
  }, [canGoToStep, currentStep, validateStep, toast, markStepCompleted]);

  // Move to next step
  const nextStep = useCallback((formData?: CollectionFormData): boolean => {
    if (currentStep >= totalSteps) return false;
    
    return goToStep(currentStep + 1, formData);
  }, [currentStep, totalSteps, goToStep]);

  // Move to previous step
  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  // Check if a step is completed
  const isStepCompleted = useCallback((step: number): boolean => {
    return completedSteps.includes(step);
  }, [completedSteps]);

  // Computed properties
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const progress = (currentStep / totalSteps) * 100;

  return {
    currentStep,
    completedSteps,
    canGoToStep,
    goToStep,
    nextStep,
    prevStep,
    markStepCompleted,
    isStepCompleted,
    isFirstStep,
    isLastStep,
    progress
  };
};
