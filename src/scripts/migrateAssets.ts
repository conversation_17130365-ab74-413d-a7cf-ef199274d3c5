/**
 * Migration script for processing existing assets in Supabase storage
 * to generate compressed versions and thumbnails
 * 
 * Usage:
 * 1. Run this script from the command line: npx ts-node src/scripts/migrateAssets.ts
 * 2. Provide your Supabase credentials when prompted
 * 3. The script will process all existing assets and update their database records
 */

import { supabase, STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { processImageFile, DEFAULT_COMPRESSION_OPTIONS, DEFAULT_THUMBNAIL_OPTIONS } from '../components/common/utils/imageProcessor';
import { Asset } from '../components/common/types/database.types';
import { AssetMetadata } from '../components/common/types/assetTypes';
import 'dotenv/config';

// Set up progress tracking
let totalAssets = 0;
let processedAssets = 0;
let failedAssets = 0;

/**
 * Main migration function
 */
async function migrateAssets() {
  try {
    console.log('Starting asset migration process...');
    console.log('This script will process existing assets to create compressed versions and thumbnails.');
    
    // Get all collections
    const { data: collections, error: collectionsError } = await supabase
      .from('collections')
      .select('id, name');
    
    if (collectionsError) {
      throw new Error(`Failed to fetch collections: ${collectionsError.message}`);
    }
    
    if (!collections || collections.length === 0) {
      console.log('No collections found.');
      return;
    }
    
    console.log(`Found ${collections.length} collections to process.`);
    
    // Process each collection
    for (const collection of collections) {
      console.log(`\nProcessing collection: ${collection.name} (${collection.id})`);
      
      // Get all assets in the collection
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('*')
        .eq('collection_id', collection.id);
      
      if (assetsError) {
        console.error(`Error fetching assets for collection ${collection.id}: ${assetsError.message}`);
        continue;
      }
      
      if (!assets || assets.length === 0) {
        console.log(`No assets found in collection ${collection.name}.`);
        continue;
      }
      
      totalAssets += assets.length;
      console.log(`Found ${assets.length} assets to process in collection ${collection.name}.`);
      
      // Process each asset
      for (const asset of assets) {
        try {
          await processAsset(asset);
          processedAssets++;
          
          // Log progress
          console.log(`Processed ${processedAssets}/${totalAssets} assets (${failedAssets} failed)`);
        } catch (error) {
          failedAssets++;
          console.error(`Failed to process asset ${asset.id}: ${error}`);
        }
      }
    }
    
    console.log('\nMigration completed!');
    console.log(`Total assets processed: ${processedAssets}/${totalAssets}`);
    console.log(`Failed assets: ${failedAssets}`);
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

/**
 * Process a single asset
 */
async function processAsset(asset: Asset) {
  console.log(`Processing asset: ${asset.file_name} (${asset.id})`);
  
  // Check if asset already has processing metadata
  if (asset.metadata && 
      typeof asset.metadata === 'object' && 
      asset.metadata !== null &&
      'imageProcessing' in asset.metadata) {
    console.log(`Asset ${asset.id} already has processing metadata, skipping.`);
    return;
  }
  
  try {
    // Download the original file
    const { data: fileData, error: downloadError } = await supabase.storage
      .from(STORAGE_BUCKETS.PRODUCT_ASSETS.name)
      .download(asset.file_path);
    
    if (downloadError || !fileData) {
      throw new Error(`Failed to download asset: ${downloadError?.message || 'No file data'}`);
    }
    
    // Convert to File object
    const file = new File([fileData], asset.file_name, { type: asset.file_type });
    
    // Process the file (compress and create thumbnail)
    console.log(`Compressing asset ${asset.id}...`);
    const processResult = await processImageFile(
      file,
      asset.collection_id,
      (progress) => {
        // Log progress every 25%
        if (progress % 25 === 0) {
          console.log(`Asset ${asset.id} processing: ${progress}%`);
        }
      }
    );
    
    // Upload compressed version
    const timestamp = new Date().getTime();
    const compressedFileName = `compressed_${timestamp}_${asset.file_name}`;
    const compressedPath = `${asset.collection_id}/compressed/${compressedFileName}`;
    
    console.log(`Uploading compressed version of asset ${asset.id}...`);
    const { data: compressedData, error: compressedError } = await supabase.storage
      .from(STORAGE_BUCKETS.PRODUCT_ASSETS.name)
      .upload(compressedPath, processResult.compressedFile, {
        cacheControl: '3600',
        upsert: true
      });
    
    if (compressedError) {
      throw new Error(`Failed to upload compressed file: ${compressedError.message}`);
    }
    
    // Upload thumbnail if available
    let thumbnailPath = null;
    if (processResult.thumbnailFile) {
      const thumbnailFileName = `thumb_${timestamp}_${asset.file_name}`;
      thumbnailPath = `${asset.collection_id}/thumbnails/${thumbnailFileName}`;
      
      console.log(`Uploading thumbnail for asset ${asset.id}...`);
      const { data: thumbnailData, error: thumbnailError } = await supabase.storage
        .from(STORAGE_BUCKETS.PRODUCT_ASSETS.name)
        .upload(thumbnailPath, processResult.thumbnailFile, {
          cacheControl: '3600',
          upsert: true
        });
      
      if (thumbnailError) {
        console.warn(`Warning: Failed to upload thumbnail: ${thumbnailError.message}`);
        thumbnailPath = null;
      }
    }
    
    // Calculate compression ratio
    const compressionRatio = processResult.originalSize > 0 
      ? Math.round((processResult.compressedSize / processResult.originalSize) * 100) 
      : 100;
    
    // Create asset metadata with image processing information
    const currentMetadata = asset.metadata || {};
    const updatedMetadata: AssetMetadata = {
      ...(typeof currentMetadata === 'object' ? currentMetadata : {}),
      imageProcessing: {
        originalSize: processResult.originalSize,
        compressedSize: processResult.compressedSize,
        thumbnailSize: processResult.thumbnailSize,
        compressionRatio: compressionRatio,
        originalPath: asset.file_path, // Original path remains the same
        compressedPath: compressedPath,
        thumbnailPath: thumbnailPath || undefined,
        processedAt: new Date().toISOString(),
      }
    };
    
    // Update asset record in database
    console.log(`Updating database record for asset ${asset.id}...`);
    const { data: updatedAsset, error: updateError } = await supabase
      .from('assets')
      .update({
        metadata: updatedMetadata,
      })
      .eq('id', asset.id)
      .select()
      .single();
    
    if (updateError) {
      throw new Error(`Failed to update asset record: ${updateError.message}`);
    }
    
    console.log(`Successfully processed asset ${asset.id}.`);
    console.log(`  Original size: ${(processResult.originalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`  Compressed size: ${(processResult.compressedSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`  Compression ratio: ${compressionRatio}%`);
    if (thumbnailPath) {
      console.log(`  Thumbnail size: ${(processResult.thumbnailSize / 1024).toFixed(2)} KB`);
    }
    
    return updatedAsset;
  } catch (error) {
    console.error(`Error processing asset ${asset.id}:`, error);
    throw error;
  }
}

// Execute the migration
migrateAssets()
  .then(() => {
    console.log('Migration script completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration script failed:', error);
    process.exit(1);
  }); 