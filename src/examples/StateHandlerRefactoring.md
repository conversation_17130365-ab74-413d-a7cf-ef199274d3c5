# State Handler Refactoring Example

This document shows the specific refactoring of the complex state update handlers from OrganizationCollectionCreation.

## Original Problem: Complex Nested State Updates

The original component had multiple handlers for updating deeply nested state objects, leading to repetitive and error-prone code:

<augment_code_snippet path="src/pages/OrganizationCollectionCreation.tsx" mode="EXCERPT">
````typescript
// <PERSON><PERSON> for enhanced brief section uploads
const handleEnhancedBriefUpload = (
  section: keyof EnhancedBriefSections,
  field: string,
  files: FileList | null | File[]
) => {
  if (!files) return;

  const fileArray = files instanceof FileList ? Array.from(files) : files;

  setFormData(prev => {
    // Ensure enhancedBrief exists
    if (!prev.enhancedBrief) {
      return prev;
    }

    return {
      ...prev,
      enhancedBrief: {
        ...prev.enhancedBrief,
        [section]: {
          ...prev.enhancedBrief[section],
          [field]: fileArray
        }
      }
    };
  });
};
````
</augment_code_snippet>

## Refactored Solution: Type-Safe Custom Hook

### 1. Custom Hook Implementation

<augment_code_snippet path="src/hooks/useCollectionForm.ts" mode="EXCERPT">
````typescript
// Enhanced brief section updates
const updateEnhancedBrief = useCallback((
  section: keyof EnhancedBriefSections,
  field: string,
  value: any
) => {
  setFormData(prev => {
    if (!prev.enhancedBrief) return prev;
    
    return {
      ...prev,
      enhancedBrief: {
        ...prev.enhancedBrief,
        [section]: {
          ...prev.enhancedBrief[section],
          [field]: value
        }
      }
    };
  });
}, []);
````
</augment_code_snippet>

### 2. Component Usage

<augment_code_snippet path="src/components/collection-creation/RefactoredCollectionCreation.tsx" mode="EXCERPT">
````typescript
// In the refactored component
const {
  formData,
  updateFormData,
  updateEnhancedBrief,
  updateImageOutput,
  updateCampaignDetails,
  updateReferences,
  resetForm
} = useCollectionForm(initialFormData || undefined);

// Usage in component
<EnhancedDropzone
  title="Upload Target Garment Images"
  description="Drag and drop or click to browse"
  onFilesChange={(files) => updateEnhancedBrief('targetGarmentImages', 'files', files)}
  currentFiles={formData.enhancedBrief?.targetGarmentImages.files || []}
  maxFiles={20}
  minFiles={5}
/>
````
</augment_code_snippet>

## Benefits of the Refactored Approach

### 1. **Reduced Complexity**
- **Before**: 25+ lines of nested state update logic
- **After**: Single function call with clear parameters

### 2. **Improved Type Safety**
- Centralized type checking in the hook
- Consistent handling of undefined/null values
- Better IntelliSense support

### 3. **Enhanced Reusability**
- Hook can be used across multiple components
- Consistent state update patterns
- Easier to test in isolation

### 4. **Better Error Handling**
- Centralized null/undefined checks
- Consistent error boundaries
- Easier debugging

## Additional Handler Examples

### Image Output Settings

<augment_code_snippet path="src/hooks/useCollectionForm.ts" mode="EXCERPT">
````typescript
// Image output settings updates
const updateImageOutput = useCallback((
  field: keyof ImageOutputSettings | 'types',
  value: any,
  subField?: keyof ImageOutputTypes | 'width' | 'height'
) => {
  setFormData(prev => {
    if (field === 'types' && subField) {
      return {
        ...prev,
        imageOutput: {
          ...prev.imageOutput,
          types: {
            ...prev.imageOutput.types,
            [subField]: value
          }
        }
      };
    } else if (field === 'pixelSize' && subField) {
      return {
        ...prev,
        imageOutput: {
          ...prev.imageOutput,
          pixelSize: {
            ...prev.imageOutput.pixelSize,
            [subField]: value
          }
        }
      };
    } else {
      return {
        ...prev,
        imageOutput: {
          ...prev.imageOutput,
          [field]: value
        }
      };
    }
  });
}, []);
````
</augment_code_snippet>

### Campaign Details

<augment_code_snippet path="src/hooks/useCollectionForm.ts" mode="EXCERPT">
````typescript
// Campaign details updates
const updateCampaignDetails = useCallback((field: keyof CampaignDetails, value: string) => {
  setFormData(prev => ({
    ...prev,
    campaignDetails: {
      purpose: prev.campaignDetails?.purpose || '',
      lookAndFeel: prev.campaignDetails?.lookAndFeel || '',
      poses: prev.campaignDetails?.poses || '',
      targetGroup: prev.campaignDetails?.targetGroup || '',
      ...prev.campaignDetails,
      [field]: value
    }
  }));
}, []);
````
</augment_code_snippet>

## Migration Strategy

### Step 1: Create the Hook
1. Extract all state update logic into `useCollectionForm.ts`
2. Add proper TypeScript types
3. Include comprehensive null/undefined handling

### Step 2: Update Component Usage
1. Replace direct `setFormData` calls with hook methods
2. Simplify component handlers
3. Remove redundant validation logic

### Step 3: Test and Validate
1. Ensure all state updates work correctly
2. Verify localStorage persistence
3. Test edge cases and error scenarios

## Testing Approach

### Hook Testing
```typescript
// Example test for the custom hook
import { renderHook, act } from '@testing-library/react';
import { useCollectionForm } from '../useCollectionForm';

test('updateEnhancedBrief updates nested state correctly', () => {
  const { result } = renderHook(() => useCollectionForm());
  
  act(() => {
    result.current.updateEnhancedBrief('targetGarmentImages', 'files', [mockFile]);
  });
  
  expect(result.current.formData.enhancedBrief?.targetGarmentImages.files).toEqual([mockFile]);
});
```

### Component Testing
```typescript
// Example test for component using the hook
import { render, screen } from '@testing-library/react';
import { RefactoredCollectionCreation } from '../RefactoredCollectionCreation';

test('file upload updates form state', async () => {
  render(<RefactoredCollectionCreation />);
  
  const dropzone = screen.getByText('Upload Target Garment Images');
  // Test file upload interaction
  // Verify state update through UI changes
});
```

This refactoring approach transforms complex, error-prone state management into clean, maintainable, and testable code that follows React best practices and our established patterns.
