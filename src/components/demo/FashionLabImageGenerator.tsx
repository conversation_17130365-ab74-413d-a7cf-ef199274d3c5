import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Textarea } from '../ui/textarea';
import { Alert, AlertDescription } from '../ui/alert';
import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { getAssetUrl } from '../common/utils/utils';
import { Loader2, Upload, X } from 'lucide-react';

interface FashionLabImageGeneratorProps {
  collectionId: string;
}

interface ImageUpload {
  file: File;
  preview: string;
  base64: string;
}

export function FashionLabImageGenerator({ collectionId }: FashionLabImageGeneratorProps) {
  const [prompt, setPrompt] = useState('');
  const [images, setImages] = useState<{
    face?: ImageUpload;
    image_2?: ImageUpload;
    image_3?: ImageUpload;
    image_4?: ImageUpload;
  }>({});
  
  const {
    generate,
    isGenerating,
    progress,
    generatedImages,
    isLoadingImages,
  } = useFashionLabImages({
    collectionId,
    onComplete: (imageUrls) => {
      console.log('Images ready:', imageUrls);
    },
  });

  const handleImageUpload = async (type: 'face' | 'image_2' | 'image_3' | 'image_4', file: File) => {
    const preview = URL.createObjectURL(file);
    
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      setImages(prev => ({
        ...prev,
        [type]: { file, preview, base64 }
      }));
    };
    reader.readAsDataURL(file);
  };

  const removeImage = (type: 'face' | 'image_2' | 'image_3' | 'image_4') => {
    setImages(prev => {
      const newImages = { ...prev };
      delete newImages[type];
      return newImages;
    });
  };

  const handleGenerate = () => {
    if (!prompt.trim()) return;
    if (!images.face || !images.image_2 || !images.image_3 || !images.image_4) {
      return;
    }
    
    generate({
      prompt,
      faceImage: images.face.base64,
      image2: images.image_2.base64,
      image3: images.image_3.base64,
      image4: images.image_4.base64,
      metadata: {
        source: 'fashion-lab-generator',
        timestamp: new Date().toISOString(),
      },
    });
  };

  const allImagesUploaded = images.face && images.image_2 && images.image_3 && images.image_4;

  return (
    <div className="space-y-6">
      {/* Generation Form */}
      <Card>
        <CardHeader>
          <CardTitle>Fashion Lab Image Generation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              Upload a face image + 3 reference images to generate fashion imagery.
              The AI will compose your model wearing the garments in the style you describe.
            </AlertDescription>
          </Alert>

          {/* Image Upload Grid */}
          <div className="space-y-4">
            <label className="text-sm font-medium">Upload Images (Required)</label>
            <div className="grid grid-cols-2 gap-4">
              {(['face', 'image_2', 'image_3', 'image_4'] as const).map((type) => (
                <div key={type} className="space-y-2">
                  <label className="text-xs font-medium text-gray-600">
                    {type === 'face' ? 'Face Image' : `Reference ${type.split('_')[1]}`}
                  </label>
                  {images[type] ? (
                    <div className="relative group">
                      <img 
                        src={images[type]!.preview} 
                        alt={type}
                        className="w-full h-32 object-cover rounded border"
                      />
                      <button
                        onClick={() => removeImage(type)}
                        className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded cursor-pointer hover:border-gray-400 transition-colors">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleImageUpload(type, file);
                        }}
                        className="hidden"
                        disabled={isGenerating}
                      />
                      <Upload className="w-8 h-8 text-gray-400 mb-2" />
                      <span className="text-xs text-gray-500">Click to upload</span>
                    </label>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium">Prompt</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe how the model should be styled with the uploaded garments..."
              rows={3}
              disabled={isGenerating}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              Example: "Place this brunette model wearing these grey pants with a sweater paired with black sandals in a beach background. Full height, 3/4 angle"
            </p>
          </div>
          
          {isGenerating && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Generating images...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} />
            </div>
          )}
          
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim() || !allImagesUploaded}
            className="w-full"
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : !allImagesUploaded ? (
              'Upload All 4 Images to Continue'
            ) : (
              'Generate Fashion Images'
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Images Gallery */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Images</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingImages ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : generatedImages.length === 0 ? (
            <p className="text-center text-gray-500 py-8">
              No generated images yet. Upload your images and create your first generation!
            </p>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {generatedImages
                .map((asset) => (
                  <div key={asset.id} className="relative group">
                    <img
                      src={getAssetUrl(asset, false)} // Use compressed version
                      alt={asset.file_name}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-opacity rounded-lg flex items-center justify-center">
                      <Button
                        variant="secondary"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => window.open(getAssetUrl(asset, false, true), '_blank')}
                      >
                        Download
                      </Button>
                    </div>
                    {asset.generation_metadata && (
                      <div className="mt-2 text-xs text-gray-600">
                        <p className="truncate">{asset.generation_prompt}</p>
                        <p>Queue: {asset.generation_queue_id?.slice(0, 8)}...</p>
                      </div>
                    )}
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}