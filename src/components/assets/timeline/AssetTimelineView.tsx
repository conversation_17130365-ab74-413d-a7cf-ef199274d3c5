import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageSquare, Eye, Download, ChevronRight, GitBranch, Layers } from 'lucide-react';
import { cn } from '@/components/common/utils/utils';

// This is a mockup component to demonstrate the timeline UI concept
// It shows how data relationships are visualized

interface TimelineAsset {
  id: string;
  thumbnailUrl: string;
  stage: string;
  commentCount: number;
  viewCount: number;
  isSelected?: boolean;
  isFinal?: boolean;
}

interface TimelineStage {
  name: string;
  assets: TimelineAsset[];
  stageType: 'upload' | 'ai-generated' | 'manual' | 'final';
}

export function AssetTimelineView() {
  // Mock data showing the relationship between assets
  const timelineData: TimelineStage[] = [
    {
      name: 'Upload',
      stageType: 'upload',
      assets: [
        { id: 'upload-1', thumbnailUrl: '/api/placeholder/200/200', stage: 'upload', commentCount: 3, viewCount: 12 },
        { id: 'upload-2', thumbnailUrl: '/api/placeholder/200/200', stage: 'upload', commentCount: 1, viewCount: 8 },
      ]
    },
    {
      name: 'AI Generated',
      stageType: 'ai-generated',
      assets: [
        { id: 'ai-1', thumbnailUrl: '/api/placeholder/200/200', stage: 'raw_ai', commentCount: 5, viewCount: 24 },
        { id: 'ai-2', thumbnailUrl: '/api/placeholder/200/200', stage: 'raw_ai', commentCount: 2, viewCount: 18 },
        { id: 'ai-3', thumbnailUrl: '/api/placeholder/200/200', stage: 'raw_ai', commentCount: 0, viewCount: 15 },
        { id: 'ai-4', thumbnailUrl: '/api/placeholder/200/200', stage: 'raw_ai', commentCount: 1, viewCount: 20 },
      ]
    },
    {
      name: 'Selected',
      stageType: 'manual',
      assets: [
        { id: 'selected-1', thumbnailUrl: '/api/placeholder/200/200', stage: 'selected', commentCount: 2, viewCount: 30, isSelected: true },
        { id: 'selected-2', thumbnailUrl: '/api/placeholder/200/200', stage: 'selected', commentCount: 0, viewCount: 25 },
      ]
    },
    {
      name: 'Refined',
      stageType: 'manual',
      assets: [
        { id: 'refined-1', thumbnailUrl: '/api/placeholder/200/200', stage: 'refined', commentCount: 4, viewCount: 35 },
      ]
    },
    {
      name: 'Final',
      stageType: 'final',
      assets: [
        { id: 'final-1', thumbnailUrl: '/api/placeholder/200/200', stage: 'final', commentCount: 1, viewCount: 50, isFinal: true },
      ]
    },
  ];

  return (
    <div className="w-full space-y-6">
      {/* Header showing the current filter context */}
      <div className="bg-muted/50 rounded-lg p-4">
        <div className="flex items-center text-sm text-muted-foreground">
          <span>Collection: Summer 2024</span>
          <ChevronRight className="w-4 h-4 mx-2" />
          <span>Product: T-Shirt Basic</span>
          <ChevronRight className="w-4 h-4 mx-2" />
          <span>Size: M</span>
          <ChevronRight className="w-4 h-4 mx-2" />
          <span>View: Front</span>
        </div>
        <div className="mt-2 text-xs text-muted-foreground">
          <Layers className="w-3 h-3 inline mr-1" />
          Showing 1 lineage group with 10 total assets across 5 workflow stages
        </div>
      </div>

      {/* Timeline visualization */}
      <Card className="p-6">
        <div className="flex items-start gap-2 overflow-x-auto pb-4">
          {timelineData.map((stage, stageIndex) => (
            <React.Fragment key={stage.name}>
              {/* Stage column */}
              <div className="flex-shrink-0 w-56">
                <div className="mb-4">
                  <h3 className="font-semibold text-sm">{stage.name}</h3>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stage.assets.length} asset{stage.assets.length !== 1 ? 's' : ''}
                  </p>
                </div>
                
                <div className="space-y-3">
                  {stage.assets.map((asset) => (
                    <AssetCard key={asset.id} asset={asset} stageType={stage.stageType} />
                  ))}
                </div>
              </div>

              {/* Connection arrows between stages */}
              {stageIndex < timelineData.length - 1 && (
                <div className="flex-shrink-0 flex items-center justify-center w-12 h-full">
                  <ConnectionArrow 
                    from={stage.stageType} 
                    to={timelineData[stageIndex + 1].stageType}
                    fromCount={stage.assets.length}
                    toCount={timelineData[stageIndex + 1].assets.length}
                  />
                </div>
              )}
            </React.Fragment>
          ))}
        </div>

        {/* Data source explanation */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
          <h4 className="text-sm font-semibold mb-2 flex items-center gap-2">
            <GitBranch className="w-4 h-4" />
            How Timeline Relationships Work
          </h4>
          <div className="text-xs space-y-2 text-muted-foreground">
            <p>• Each asset has a <code className="bg-muted px-1 rounded">lineage_id</code> that groups related assets together</p>
            <p>• The <code className="bg-muted px-1 rounded">parent_asset_id</code> tracks which asset it was derived from</p>
            <p>• <code className="bg-muted px-1 rounded">variant_group_id</code> links assets with same product/size/view</p>
            <p>• Comments are aggregated across all assets in the lineage</p>
          </div>
        </div>
      </Card>

      {/* Aggregated comments section */}
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Timeline Comments (All Stages)</h3>
        <div className="space-y-4">
          <CommentThread 
            stage="Upload" 
            comment="Color looks too dark in the source image"
            author="Sarah Designer"
            timestamp="2 days ago"
            assetId="upload-1"
          />
          <CommentThread 
            stage="AI Generated" 
            comment="Much better color, but the fabric texture needs work"
            author="Mike Reviewer"
            timestamp="1 day ago"
            assetId="ai-1"
          />
          <CommentThread 
            stage="Refined" 
            comment="Perfect! The texture is exactly what we wanted"
            author="Sarah Designer"
            timestamp="4 hours ago"
            assetId="refined-1"
            resolved
          />
        </div>
      </Card>
    </div>
  );
}

// Component showing individual asset cards
function AssetCard({ asset, stageType }: { asset: TimelineAsset; stageType: string }) {
  return (
    <Card className={cn(
      "p-3 cursor-pointer transition-all hover:shadow-md",
      asset.isSelected && "ring-2 ring-primary",
      asset.isFinal && "border-green-500"
    )}>
      <div className="aspect-square bg-muted rounded mb-3 relative overflow-hidden">
        <img 
          src={asset.thumbnailUrl} 
          alt={`Asset ${asset.id}`}
          className="w-full h-full object-cover"
        />
        {asset.isFinal && (
          <Badge className="absolute top-2 right-2 bg-green-500">
            Final
          </Badge>
        )}
      </div>
      
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-3">
          <span className="flex items-center gap-1">
            <MessageSquare className="w-3 h-3" />
            {asset.commentCount}
          </span>
          <span className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            {asset.viewCount}
          </span>
        </div>
        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
          <Download className="w-3 h-3" />
        </Button>
      </div>
      
      <div className="mt-2 text-xs text-muted-foreground">
        #{asset.id.split('-')[1]} • {stageType === 'ai-generated' ? 'AI' : 'Manual'}
      </div>
    </Card>
  );
}

// Component showing connection arrows between stages
function ConnectionArrow({ from, to, fromCount, toCount }: any) {
  const isAiGeneration = from === 'upload' && to === 'ai-generated';
  const isBranching = fromCount < toCount;
  const isConverging = fromCount > toCount;

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <svg width="40" height="100" className="overflow-visible">
        {isAiGeneration ? (
          // Dashed arrow for AI generation
          <line 
            x1="0" y1="50" x2="40" y2="50" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeDasharray="5,5"
            className="text-blue-500"
          />
        ) : isBranching ? (
          // Multiple arrows for branching
          <>
            <line x1="0" y1="50" x2="40" y2="30" stroke="currentColor" strokeWidth="1.5" />
            <line x1="0" y1="50" x2="40" y2="70" stroke="currentColor" strokeWidth="1.5" />
          </>
        ) : (
          // Solid arrow for normal progression
          <line x1="0" y1="50" x2="40" y2="50" stroke="currentColor" strokeWidth="2" />
        )}
        
        {/* Arrowhead */}
        <polygon 
          points="40,50 35,45 35,55" 
          fill="currentColor"
          className={isAiGeneration ? "text-blue-500" : ""}
        />
      </svg>
      
      {isAiGeneration && (
        <span className="absolute -top-6 text-xs text-blue-500 whitespace-nowrap">
          AI Generated
        </span>
      )}
    </div>
  );
}

// Component showing comment threads
function CommentThread({ stage, comment, author, timestamp, assetId, resolved = false }: any) {
  return (
    <div className={cn(
      "flex gap-3 p-3 rounded-lg",
      resolved ? "bg-green-50 dark:bg-green-950/20" : "bg-muted/50"
    )}>
      <MessageSquare className={cn(
        "w-5 h-5 mt-0.5",
        resolved ? "text-green-600" : "text-muted-foreground"
      )} />
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium text-sm">{author}</span>
          <Badge variant="outline" className="text-xs">
            {stage}
          </Badge>
          <span className="text-xs text-muted-foreground">{timestamp}</span>
          {resolved && (
            <Badge className="text-xs bg-green-500">Resolved</Badge>
          )}
        </div>
        <p className="text-sm">{comment}</p>
        <p className="text-xs text-muted-foreground mt-1">
          on asset #{assetId} → Links to specific asset in timeline
        </p>
      </div>
    </div>
  );
}