/**
 * Bulk Upload Processor for ZIP file handling
 * Integrates with optimized storage system and workflow stages
 */

import J<PERSON><PERSON><PERSON> from 'jszip';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from './supabase';
import { uploadAssetWithCompression } from './assetStorage';

// Workflow stage mapping to match database enum
export const WORKFLOW_STAGES = {
  upload: 'upload',
  raw_ai_images: 'raw_ai_images',
  upscale: 'upscale',
  retouch: 'retouch',
  final: 'final'
} as const;

export type WorkflowStage = keyof typeof WORKFLOW_STAGES;

// File naming pattern interfaces
export interface ParsedFileName {
  sku: string;
  stage: WorkflowStage;
  size?: string;
  type?: string;
  number?: number;
  version?: number;
  description?: string;
  originalName: string;
}

export interface BulkUploadResult {
  uploadId: string;
  totalFiles: number;
  processed: number;
  successful: Array<{
    fileName: string;
    assetId: string;
    productId: string;
    sku: string;
    stage: WorkflowStage;
  }>;
  failed: Array<{
    fileName: string;
    error: string;
    stage?: WorkflowStage;
  }>;
  stats: {
    processingTime: number;
    createdProducts: number;
    skippedFiles: number;
  };
}

export interface BulkUploadProgress {
  uploadId: string;
  stage: 'extracting' | 'validating' | 'processing' | 'uploading' | 'complete' | 'error';
  currentFile?: string;
  processed: number;
  total: number;
  eta?: number;
  errors: Array<{ file: string; error: string }>;
}

/**
 * Main Bulk Upload Processor
 */
export class BulkUploadProcessor {
  private collectionId: string;
  private uploadId: string;
  private progressCallback?: (progress: BulkUploadProgress) => void;
  private startTime: number = 0;

  constructor(
    collectionId: string,
    progressCallback?: (progress: BulkUploadProgress) => void
  ) {
    this.collectionId = collectionId;
    this.uploadId = uuidv4();
    this.progressCallback = progressCallback;
  }

  /**
   * Process ZIP file upload
   */
  async processZipUpload(zipFile: File): Promise<BulkUploadResult> {
    this.startTime = Date.now();
    
    const result: BulkUploadResult = {
      uploadId: this.uploadId,
      totalFiles: 0,
      processed: 0,
      successful: [],
      failed: [],
      stats: {
        processingTime: 0,
        createdProducts: 0,
        skippedFiles: 0
      }
    };

    try {
      // 1. Create bulk upload record
      await this.createBulkUploadRecord();

      // 2. Extract and validate ZIP
      this.updateProgress({ stage: 'extracting', processed: 0, total: 0 });
      const extractedFiles = await this.extractZipFile(zipFile);
      
      // 3. Validate structure and filenames
      this.updateProgress({ stage: 'validating', processed: 0, total: extractedFiles.length });
      const validatedFiles = await this.validateFiles(extractedFiles);
      
      result.totalFiles = validatedFiles.length;
      result.stats.skippedFiles = extractedFiles.length - validatedFiles.length;

      // 4. Group by products (SKU)
      const productGroups = this.groupFilesByProduct(validatedFiles);
      
      // 5. Process each product group
      this.updateProgress({ stage: 'processing', processed: 0, total: validatedFiles.length });
      
      for (const [sku, files] of productGroups.entries()) {
        try {
          // Create or find product
          const productId = await this.findOrCreateProduct(sku);
          if (productId) {
            result.stats.createdProducts++;
          }

          // Process all files for this product
          for (const fileData of files) {
            try {
              this.updateProgress({ 
                stage: 'uploading', 
                processed: result.processed, 
                total: result.totalFiles,
                currentFile: fileData.parsed.originalName
              });

              const assetId = await this.processAssetFile(fileData, productId);
              
              result.successful.push({
                fileName: fileData.parsed.originalName,
                assetId,
                productId,
                sku,
                stage: fileData.parsed.stage
              });

              result.processed++;

            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : 'Unknown error';
              result.failed.push({
                fileName: fileData.parsed.originalName,
                error: errorMessage,
                stage: fileData.parsed.stage
              });
              result.processed++;
            }
          }

        } catch (error) {
          // Product creation failed, mark all files as failed
          const errorMessage = `Product creation failed for SKU ${sku}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          files.forEach(fileData => {
            result.failed.push({
              fileName: fileData.parsed.originalName,
              error: errorMessage,
              stage: fileData.parsed.stage
            });
          });
          result.processed += files.length;
        }
      }

      // 6. Complete upload record
      result.stats.processingTime = Date.now() - this.startTime;
      await this.completeBulkUploadRecord(result);

      this.updateProgress({ 
        stage: 'complete', 
        processed: result.processed, 
        total: result.totalFiles 
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.updateProgress({ 
        stage: 'error', 
        processed: result.processed, 
        total: result.totalFiles,
        errors: [{ file: 'ZIP processing', error: errorMessage }]
      });

      await this.failBulkUploadRecord(errorMessage);
      throw error;
    }
  }

  /**
   * Extract files from ZIP with directory structure
   * Handles both organized (workflow folders) and flat directory structures
   */
  private async extractZipFile(zipFile: File): Promise<Array<{
    name: string;
    path: string;
    stage: string;
    file: File;
  }>> {
    const zip = new JSZip();
    const contents = await zip.loadAsync(zipFile);
    const extractedFiles: Array<{
      name: string;
      path: string; 
      stage: string;
      file: File;
    }> = [];

    for (const [path, zipEntry] of Object.entries(contents.files)) {
      if (zipEntry.dir) continue; // Skip directories

      // Skip system files and hidden files
      if (this.shouldSkipFile(path)) {
        console.log(`Skipping system file: ${path}`);
        continue;
      }

      const pathParts = path.split('/');
      const fileName = pathParts[pathParts.length - 1];

      // Skip non-image files
      if (!this.isImageFile(fileName)) {
        console.log(`Skipping non-image file: ${fileName}`);
        continue;
      }

      let stage: string;

      // Try to detect workflow stage from directory structure
      if (pathParts.length >= 2) {
        const firstDir = pathParts[0].toLowerCase();
        
        // Map common directory names to workflow stages
        const dirStageMap: Record<string, WorkflowStage> = {
          'input assets': 'upload',
          'input_assets': 'upload',
          'styling': 'upload',
          'raw': 'raw_ai_images',
          'refined': 'raw_ai_images',
          'upscaled': 'upscale',
          'upscale': 'upscale',
          'retouched': 'retouch',
          'retouch': 'retouch',
          'final': 'final',
          'finals': 'final'
        };
        
        // Check if first directory maps to a workflow stage
        if (dirStageMap[firstDir]) {
          stage = dirStageMap[firstDir];
        } else if (Object.keys(WORKFLOW_STAGES).includes(firstDir)) {
          stage = firstDir as WorkflowStage;
        } else {
          // Look for workflow stage in any part of the path
          const foundStage = pathParts.find(part => {
            const lowerPart = part.toLowerCase();
            return dirStageMap[lowerPart] || Object.keys(WORKFLOW_STAGES).includes(lowerPart);
          });
          
          if (foundStage) {
            const lowerFound = foundStage.toLowerCase();
            stage = dirStageMap[lowerFound] || (foundStage as WorkflowStage);
          } else {
            // Default stage - try to infer from filename
            stage = this.inferWorkflowStageFromFilename(fileName);
          }
        }
      } else {
        // Root file - infer stage from filename
        stage = this.inferWorkflowStageFromFilename(fileName);
      }

      try {
        // Convert to File object
        const blob = await zipEntry.async('blob');
        const file = new File([blob], fileName, { type: this.getMimeType(fileName) });

        extractedFiles.push({
          name: fileName,
          path,
          stage,
          file
        });
      } catch (error) {
        console.warn(`Failed to process file ${path}:`, error);
        continue;
      }
    }

    console.log(`Extracted ${extractedFiles.length} valid image files from ZIP`);
    return extractedFiles;
  }

  /**
   * Check if file should be skipped (system files, hidden files, etc.)
   */
  private shouldSkipFile(path: string): boolean {
    const fileName = path.split('/').pop() || '';
    
    // Skip macOS system files
    if (path.includes('__MACOSX/') || fileName.startsWith('._')) {
      return true;
    }
    
    // Skip hidden files
    if (fileName.startsWith('.') && fileName !== '.') {
      return true;
    }
    
    // Skip common non-image files
    const skipExtensions = ['.txt', '.md', '.json', '.xml', '.plist', '.DS_Store'];
    const ext = fileName.toLowerCase().split('.').pop();
    if (ext && skipExtensions.some(skipExt => skipExt.includes(ext))) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if file is an image
   */
  private isImageFile(fileName: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'tiff', 'bmp'];
    const ext = fileName.toLowerCase().split('.').pop();
    return ext ? imageExtensions.includes(ext) : false;
  }

  /**
   * Infer workflow stage from filename patterns
   */
  private inferWorkflowStageFromFilename(fileName: string): WorkflowStage {
    const lowerName = fileName.toLowerCase();
    
    // Check for stage keywords in filename or directory
    if (lowerName.includes('styling') || lowerName.includes('input')) {
      return 'upload';
    } else if (lowerName.includes('raw') || lowerName.includes('refined')) {
      return 'raw_ai_images';
    } else if (lowerName.includes('upscale')) {
      return 'upscale';
    } else if (lowerName.includes('retouch')) {
      return 'retouch';
    } else if (lowerName.includes('final')) {
      return 'final';
    } else {
      // Default to final for clean product images
      return 'final';
    }
  }

  /**
   * Validate files and parse filenames
   */
  private async validateFiles(extractedFiles: Array<{
    name: string;
    path: string;
    stage: string;
    file: File;
  }>): Promise<Array<{
    file: File;
    parsed: ParsedFileName;
  }>> {
    const validFiles: Array<{
      file: File;
      parsed: ParsedFileName;
    }> = [];

    for (const fileData of extractedFiles) {
      try {
        const parsed = this.parseFileName(fileData.name, fileData.stage as WorkflowStage);
        if (parsed) {
          validFiles.push({
            file: fileData.file,
            parsed
          });
        }
      } catch (error) {
        console.warn(`Invalid filename format: ${fileData.name}`, error);
        // File will be excluded from processing
      }
    }

    return validFiles;
  }

  /**
   * Parse filename according to naming conventions
   */
  private parseFileName(fileName: string, stage: WorkflowStage): ParsedFileName | null {
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    const parts = nameWithoutExt.split('_');

    if (parts.length < 2) return null;

    const parsed: ParsedFileName = {
      sku: '',
      stage,
      originalName: fileName
    };

    switch (stage) {
      case 'upload':
        // Patterns: {SKU}_styling_{NUMBER} or {DESCRIPTION}_{SKU}_{NUMBER}
        if (parts.includes('styling')) {
          const stylingIndex = parts.indexOf('styling');
          parsed.sku = parts.slice(0, stylingIndex).join('_');
          parsed.number = parseInt(parts[stylingIndex + 1]) || 1;
        } else if (parts.length >= 3) {
          // Assume last SKU-like part is the SKU
          parsed.sku = parts[parts.length - 2];
          parsed.description = parts.slice(0, -2).join('_');
          parsed.number = parseInt(parts[parts.length - 1]) || 1;
        }
        break;

      case 'draft':
        // Patterns: {SKU}_raw_{SIZE}_{TYPE}_{NUMBER} or {SKU}_refined_{SIZE}_{TYPE}_{NUMBER}
        if (parts[1] === 'raw' || parts[1] === 'refined') {
          parsed.sku = parts[0];
          parsed.size = parts[2];
          parsed.type = parts[3];
          parsed.number = parseInt(parts[4]) || 1;
        }
        break;

      case 'upscale':
        // Pattern: {SKU}_upscaled_{SIZE}_{TYPE}
        if (parts[1] === 'upscaled') {
          parsed.sku = parts[0];
          parsed.size = parts[2];
          parsed.type = parts[3];
        }
        break;

      case 'retouch':
        // Pattern: {SKU}_retouched_{SIZE}_{TYPE}_v{VERSION}
        if (parts[1] === 'retouched') {
          parsed.sku = parts[0];
          parsed.size = parts[2];
          parsed.type = parts[3];
          if (parts[4] && parts[4].startsWith('v')) {
            parsed.version = parseInt(parts[4].substring(1)) || 1;
          }
        }
        break;

      case 'final':
        // Pattern: {SKU}_{SIZE}_{TYPE}
        if (parts.length >= 3) {
          parsed.sku = parts[0];
          parsed.size = parts[1];
          parsed.type = parts[2];
        }
        break;
    }

    // Validate that we extracted a SKU
    if (!parsed.sku) {
      return null;
    }

    return parsed;
  }

  /**
   * Group files by product (SKU)
   */
  private groupFilesByProduct(files: Array<{
    file: File;
    parsed: ParsedFileName;
  }>): Map<string, Array<{ file: File; parsed: ParsedFileName }>> {
    const groups = new Map<string, Array<{ file: File; parsed: ParsedFileName }>>();

    for (const fileData of files) {
      const sku = fileData.parsed.sku;
      if (!groups.has(sku)) {
        groups.set(sku, []);
      }
      groups.get(sku)!.push(fileData);
    }

    return groups;
  }

  /**
   * Find or create product by SKU
   */
  private async findOrCreateProduct(sku: string): Promise<string> {
    // First, try to find existing product
    const { data: existingProducts, error: searchError } = await supabase
      .from('products')
      .select('id')
      .eq('sku', sku)
      .eq('collection_id', this.collectionId);

    // Handle search error
    if (searchError) {
      console.error(`Error searching for product with SKU ${sku}:`, searchError);
      throw new Error(`Failed to search for product: ${searchError.message}`);
    }

    // If product exists, return its ID
    if (existingProducts && existingProducts.length > 0) {
      return existingProducts[0].id;
    }

    // Create new product
    const { data: newProduct, error } = await supabase
      .from('products')
      .insert({
        collection_id: this.collectionId,
        name: `Product ${sku}`, // Default name, can be updated later
        sku,
        description: `Auto-created from bulk upload`
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create product for SKU ${sku}: ${error.message}`);
    }

    return newProduct.id;
  }

  /**
   * Process individual asset file
   */
  private async processAssetFile(
    fileData: { file: File; parsed: ParsedFileName },
    productId: string
  ): Promise<string> {
    const assetId = uuidv4();
    const { file, parsed } = fileData;

    // Upload file using optimized storage system
    const uploadResult = await uploadAssetWithCompression(
      file,
      this.collectionId,
      assetId
    );

    if (!uploadResult) {
      throw new Error('Upload failed - no result returned');
    }

    // Create asset record with workflow stage and metadata
    const metadata = {
      workflow_stage: parsed.stage,
      image_type: parsed.type,
      size: parsed.size,
      styling_number: parsed.number,
      version: parsed.version,
      description: parsed.description,
      original_filename: parsed.originalName,
      bulk_upload_id: this.uploadId,
      optimizedStorage: {
        originalPath: uploadResult.originalPath,
        compressedPath: uploadResult.compressedPath,
        thumbnailPath: uploadResult.thumbnailPath
      }
    };

    const { data: asset, error } = await supabase
      .from('assets')
      .insert({
        id: assetId,
        product_id: productId,
        collection_id: this.collectionId,
        file_name: parsed.originalName,
        file_path: uploadResult.compressedPath, // Main display path for backward compatibility
        original_path: uploadResult.originalPath, // Store original file path (FAS-73)
        compressed_path: uploadResult.compressedPath, // Store compressed file path (FAS-73)
        thumbnail_path: uploadResult.thumbnailPath, // Store thumbnail file path (FAS-73)
        file_type: file.type,
        file_size: file.size,
        workflow_stage: parsed.stage,
        metadata: metadata // JSONB columns accept objects directly
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create asset record: ${error.message}`);
    }

    return asset.id;
  }

  /**
   * Get MIME type from filename
   */
  private getMimeType(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'gif':
        return 'image/gif';
      default:
        return 'image/jpeg';
    }
  }

  /**
   * Database operations for tracking
   */
  private async createBulkUploadRecord(): Promise<void> {
    // Get current user id
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      throw new Error('User not authenticated');
    }
    
    const { error } = await supabase
      .from('bulk_uploads')
      .insert({
        id: this.uploadId,
        collection_id: this.collectionId,
        user_id: user.id,
        status: 'processing',
        total_files: 0,
        processed_files: 0,
        success_count: 0,
        error_count: 0
      });

    if (error) {
      throw new Error(`Failed to create bulk upload record: ${error.message}`);
    }
  }

  private async completeBulkUploadRecord(result: BulkUploadResult): Promise<void> {
    const { error } = await supabase
      .from('bulk_uploads')
      .update({
        status: 'completed',
        total_files: result.totalFiles,
        processed_files: result.processed,
        success_count: result.successful.length,
        error_count: result.failed.length,
        error_log: JSON.stringify(result.failed),
        completed_at: new Date().toISOString()
      })
      .eq('id', this.uploadId);

    if (error) {
      console.error('Failed to update bulk upload record:', error);
    }
  }

  private async failBulkUploadRecord(errorMessage: string): Promise<void> {
    const { error } = await supabase
      .from('bulk_uploads')
      .update({
        status: 'failed',
        error_log: JSON.stringify([{ error: errorMessage }]),
        completed_at: new Date().toISOString()
      })
      .eq('id', this.uploadId);

    if (error) {
      console.error('Failed to update bulk upload record:', error);
    }
  }

  /**
   * Progress reporting
   */
  private updateProgress(progress: Partial<BulkUploadProgress>): void {
    if (this.progressCallback) {
      this.progressCallback({
        uploadId: this.uploadId,
        errors: [],
        ...progress
      } as BulkUploadProgress);
    }
  }
}

/**
 * Convenience function for bulk upload
 */
export async function processBulkUpload(
  zipFile: File,
  collectionId: string,
  onProgress?: (progress: BulkUploadProgress) => void
): Promise<BulkUploadResult> {
  const processor = new BulkUploadProcessor(collectionId, onProgress);
  return processor.processZipUpload(zipFile);
}

/**
 * Validate ZIP file before processing
 */
export function validateZipFile(file: File): { valid: boolean; errors: string[]; warnings: string[] } {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check file type
  if (!file.type.includes('zip') && !file.name.endsWith('.zip')) {
    errors.push('File must be a ZIP archive');
  }

  // Check file size (increased limit for large collections)
  if (file.size > 2 * 1024 * 1024 * 1024) { // 2GB limit
    errors.push('ZIP file must be smaller than 2GB');
  } else if (file.size > 1 * 1024 * 1024 * 1024) { // 1GB warning
    warnings.push('Large file size detected. Processing may take 10+ minutes.');
  }

  // Check minimum size
  if (file.size < 1024) {
    errors.push('ZIP file appears to be empty or corrupted');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}