/**
 * Navigation utilities for generating consistent URLs throughout the application
 */

/**
 * Generate a URL for a collection
 * @param collectionId The collection ID
 * @param organizationId The organization ID
 * @returns The URL for the collection
 */
export function getCollectionUrl(collectionId: string, organizationId: string): string {
  return `/organizations/${organizationId}/collections/${collectionId}`;
}

/**
 * Generate a URL for an organization (formerly client)
 * @param organizationId The organization ID
 * @returns The URL for the organization
 */
export function getOrganizationUrl(organizationId: string): string {
  return `/organizations/${organizationId}`;
}

/**
 * Generate a URL for an asset
 * @param assetId The asset ID
 * @param options Optional parameters for generating the URL
 * @returns The URL for the asset
 */
export function getAssetUrl(
  assetId: string,
  options?: {
    organizationId?: string;
    collectionId?: string;
    useHierarchical?: boolean;
  }
): string {
  const { organizationId, collectionId, useHierarchical = false } = options || {};
  
  // If we have organization and collection IDs and useHierarchical is true, use the hierarchical URL
  if (useHierarchical && organizationId && collectionId) {
    return `/organizations/${organizationId}/collections/${collectionId}/assets/${assetId}`;
  }
  
  // Otherwise, use the direct URL
  return `/assets/${assetId}`;
}