import { supabase, STORAGE_BUCKETS } from './supabase';
import { sanitizeFileName } from './supabase';

// Asset types for URL generation
export type AssetType = 'thumbnail' | 'compressed' | 'original';
export type ProfileType = 'user-avatar' | 'org-logo';

/**
 * Generate the storage path for an asset
 */
export function generateAssetPath(
  collectionId: string,
  assetId: string,
  type: AssetType,
  fileExtension: string = 'webp'
): string {
  const fileName = `${assetId}.${fileExtension}`;
  return `collections/${collectionId}/${fileName}`;
}

/**
 * Generate the storage path for a profile image
 */
export function generateProfilePath(
  type: ProfileType,
  entityId: string,
  fileExtension: string = 'webp'
): string {
  const folder = type === 'user-avatar' ? 'users' : 'organizations';
  const fileName = sanitizeFileName(`${type}.${fileExtension}`);
  return `${folder}/${entityId}/${fileName}`;
}

/**
 * Get the optimized URL for an asset with fallback logic
 */
export function getOptimizedAssetUrl(
  assetId: string,
  collectionId: string,
  type: AssetType = 'compressed'
): string {
  if (!assetId || !collectionId) {
    console.warn('ASSET_URL: Missing assetId or collectionId');
    return '';
  }

  try {
    let bucketName: string;
    let fileExtension = 'webp';

    // Determine bucket and file extension based on type
    switch (type) {
      case 'thumbnail':
        bucketName = STORAGE_BUCKETS.MEDIA_THUMBNAILS.name;
        break;
      case 'compressed':
        bucketName = STORAGE_BUCKETS.MEDIA_COMPRESSED.name;
        break;
      case 'original':
        bucketName = STORAGE_BUCKETS.MEDIA_ORIGINALS.name;
        fileExtension = 'jpg'; // Originals might be in different formats
        break;
      default:
        bucketName = STORAGE_BUCKETS.MEDIA_COMPRESSED.name;
    }

    const assetPath = generateAssetPath(collectionId, assetId, type, fileExtension);

    const { data: urlData } = supabase.storage
      .from(bucketName)
      .getPublicUrl(assetPath);

    if (urlData?.publicUrl) {
      return urlData.publicUrl;
    }

    // Enhanced fallback logic for better asset availability
    if (type === 'original') {
      // If original not found, try compressed then thumbnail
      console.warn(`ASSET_URL: original not found, falling back to compressed for asset ${assetId}`);
      const compressedUrl = getOptimizedAssetUrl(assetId, collectionId, 'compressed');
      if (compressedUrl) return compressedUrl;

      console.warn(`ASSET_URL: compressed not found, falling back to thumbnail for asset ${assetId}`);
      return getOptimizedAssetUrl(assetId, collectionId, 'thumbnail');
    } else if (type === 'compressed') {
      // If compressed not found, try original then thumbnail
      console.warn(`ASSET_URL: compressed not found, falling back to original for asset ${assetId}`);
      const originalUrl = getOptimizedAssetUrl(assetId, collectionId, 'original');
      if (originalUrl) return originalUrl;

      console.warn(`ASSET_URL: original not found, falling back to thumbnail for asset ${assetId}`);
      return getOptimizedAssetUrl(assetId, collectionId, 'thumbnail');
    } else if (type === 'thumbnail') {
      // If thumbnail not found, try compressed then original
      console.warn(`ASSET_URL: thumbnail not found, falling back to compressed for asset ${assetId}`);
      const compressedUrl = getOptimizedAssetUrl(assetId, collectionId, 'compressed');
      if (compressedUrl) return compressedUrl;

      console.warn(`ASSET_URL: compressed not found, falling back to original for asset ${assetId}`);
      return getOptimizedAssetUrl(assetId, collectionId, 'original');
    }

    console.warn(`ASSET_URL: Failed to generate URL for asset ${assetId}`);
    return '';
  } catch (error) {
    console.error(`ASSET_URL: Error generating URL for asset ${assetId}:`, error);
    return '';
  }
}

/**
 * Get the URL for a profile image
 */
export function getProfileImageUrl(
  type: ProfileType,
  entityId: string
): string {
  if (!entityId) {
    console.warn('PROFILE_URL: Missing entityId');
    return '';
  }

  try {
    const profilePath = generateProfilePath(type, entityId);
    
    const { data: urlData } = supabase.storage
      .from(STORAGE_BUCKETS.PROFILES.name)
      .getPublicUrl(profilePath);

    if (urlData?.publicUrl) {
      return urlData.publicUrl;
    }

    console.warn(`PROFILE_URL: Failed to generate URL for ${type} ${entityId}`);
    return '';
  } catch (error) {
    console.error(`PROFILE_URL: Error generating URL for ${type} ${entityId}:`, error);
    return '';
  }
}

/**
 * Upload an asset with automatic compression pipeline
 */
export async function uploadAssetWithCompression(
  file: File,
  collectionId: string,
  assetId: string,
  onProgress?: (stage: string, progress: number) => void
): Promise<{
  originalPath: string;
  compressedPath: string;
  thumbnailPath: string;
} | null> {
  try {
    if (onProgress) onProgress('Uploading original', 10);

    // 1. Upload original file
    const originalPath = generateAssetPath(collectionId, assetId, 'original', 
      file.name.split('.').pop() || 'jpg');
    
    const { error: originalError } = await supabase.storage
      .from(STORAGE_BUCKETS.MEDIA_ORIGINALS.name)
      .upload(originalPath, file, { cacheControl: '3600', upsert: true });

    if (originalError) throw originalError;

    if (onProgress) onProgress('Processing compressed version', 40);

    // 2. Generate compressed version (this would typically be done server-side)
    const compressedFile = await compressImage(file, 1200);
    const compressedPath = generateAssetPath(collectionId, assetId, 'compressed');
    
    const { error: compressedError } = await supabase.storage
      .from(STORAGE_BUCKETS.MEDIA_COMPRESSED.name)
      .upload(compressedPath, compressedFile, { cacheControl: '3600', upsert: true });

    if (compressedError) throw compressedError;

    if (onProgress) onProgress('Generating thumbnail', 70);

    // 3. Generate thumbnail
    const thumbnailFile = await compressImage(file, 1000); // Increased from 150px to 1000px for larger displays
    const thumbnailPath = generateAssetPath(collectionId, assetId, 'thumbnail');
    
    const { error: thumbnailError } = await supabase.storage
      .from(STORAGE_BUCKETS.MEDIA_THUMBNAILS.name)
      .upload(thumbnailPath, thumbnailFile, { cacheControl: '3600', upsert: true });

    if (thumbnailError) throw thumbnailError;

    if (onProgress) onProgress('Upload complete', 100);

    return {
      originalPath,
      compressedPath, 
      thumbnailPath
    };

  } catch (error) {
    console.error('Error uploading asset with compression:', error);
    throw error;
  }
}

/**
 * Simple image compression utility (client-side)
 * In production, this should be done server-side for better quality
 */
async function compressImage(file: File, maxSize: number): Promise<File> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      const ratio = Math.min(maxSize / img.width, maxSize / img.height);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: 'image/webp',
            lastModified: Date.now()
          });
          resolve(compressedFile);
        } else {
          resolve(file); // Fallback to original
        }
      }, 'image/webp', 0.9); // Increased quality from 0.8 to 0.9
    };

    img.src = URL.createObjectURL(file);
  });
}

/**
 * Upload a profile image
 */
export async function uploadProfileImage(
  file: File,
  type: ProfileType,
  entityId: string,
  onProgress?: (progress: number) => void
): Promise<string | null> {
  try {
    if (onProgress) onProgress(10);

    // Compress the image for web use
    const compressedFile = await compressImage(file, 400);
    const profilePath = generateProfilePath(type, entityId);

    if (onProgress) onProgress(50);

    const { error } = await supabase.storage
      .from(STORAGE_BUCKETS.PROFILES.name)
      .upload(profilePath, compressedFile, { cacheControl: '3600', upsert: true });

    if (error) throw error;

    if (onProgress) onProgress(100);

    return profilePath;

  } catch (error) {
    console.error('Error uploading profile image:', error);
    throw error;
  }
}

/**
 * Delete an asset from all buckets
 */
export async function deleteAsset(
  assetId: string,
  collectionId: string
): Promise<void> {
  const deletions = [];

  // Delete from all three media buckets
  const thumbnailPath = generateAssetPath(collectionId, assetId, 'thumbnail');
  const compressedPath = generateAssetPath(collectionId, assetId, 'compressed');
  const originalPath = generateAssetPath(collectionId, assetId, 'original', 'jpg');

  deletions.push(
    supabase.storage.from(STORAGE_BUCKETS.MEDIA_THUMBNAILS.name).remove([thumbnailPath]),
    supabase.storage.from(STORAGE_BUCKETS.MEDIA_COMPRESSED.name).remove([compressedPath]),
    supabase.storage.from(STORAGE_BUCKETS.MEDIA_ORIGINALS.name).remove([originalPath])
  );

  await Promise.allSettled(deletions);
}