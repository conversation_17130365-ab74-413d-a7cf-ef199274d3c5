/**
 * Retouch Substage Utilities
 * Provides role-based filtering and configuration for retouch workflow substages
 */

import { UserRole } from '../../../contexts/UserRoleContext';
import { RetouchSubstage } from '../types/database.types';

// Complete retouch substage configuration with display information
export interface RetouchSubstageConfig {
  id: RetouchSubstage;
  label: string;
  icon: string; // Lucide icon name
  color: string;
  order: number;
}

// All available retouch substages with their configuration
export const ALL_RETOUCH_SUBSTAGES: RetouchSubstageConfig[] = [
  { id: 'internal_review', label: 'Internal Review', icon: 'Eye', color: 'orange', order: 1 },
  { id: 'revision_1', label: 'Revision 1', icon: 'Edit', color: 'blue', order: 2 },
  { id: 'revision_2', label: 'Revision 2', icon: 'Edit2', color: 'purple', order: 3 },
  { id: 'extra_revisions', label: 'Extra Revisions', icon: 'MoreHorizontal', color: 'gray', order: 4 }
];

// Platform and freelancer users see all substages
export const PLATFORM_RETOUCH_SUBSTAGES: RetouchSubstage[] = [
  'internal_review',
  'revision_1',
  'revision_2',
  'extra_revisions'
];

// Brand users don't see internal_review
export const BRAND_RETOUCH_SUBSTAGES: RetouchSubstage[] = [
  'revision_1',
  'revision_2',
  'extra_revisions'
];

/**
 * Get allowed retouch substages based on user role
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getAllowedRetouchSubstages(userRole: UserRole | null, isFreelancer: boolean = false): RetouchSubstage[] {
  if (!userRole) {
    return BRAND_RETOUCH_SUBSTAGES; // Default to most restrictive
  }

  // Platform users and freelancers see all substages
  if (
    userRole === 'platform_super' || 
    userRole === 'platform_admin' ||
    userRole === 'external_retoucher' ||
    userRole === 'external_prompter' ||
    (userRole === 'brand_admin' && isFreelancer)
  ) {
    return PLATFORM_RETOUCH_SUBSTAGES;
  }

  // Brand users don't see internal_review
  return BRAND_RETOUCH_SUBSTAGES;
}

/**
 * Get all retouch substage configurations
 */
export function getRetouchSubstageConfigs(): RetouchSubstageConfig[] {
  return ALL_RETOUCH_SUBSTAGES.sort((a, b) => a.order - b.order);
}

/**
 * Get retouch substage configurations filtered by user role
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getFilteredRetouchSubstageConfigs(userRole: UserRole | null, isFreelancer: boolean = false): RetouchSubstageConfig[] {
  const allowedSubstages = getAllowedRetouchSubstages(userRole, isFreelancer);
  
  return ALL_RETOUCH_SUBSTAGES
    .filter(config => allowedSubstages.includes(config.id))
    .sort((a, b) => a.order - b.order);
}

/**
 * Check if a retouch substage is allowed for a user role
 * @param substage - The retouch substage to check
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function isRetouchSubstageAllowed(substage: RetouchSubstage, userRole: UserRole | null, isFreelancer: boolean = false): boolean {
  const allowedSubstages = getAllowedRetouchSubstages(userRole, isFreelancer);
  return allowedSubstages.includes(substage);
}

/**
 * Get the display label for a retouch substage
 */
export function getRetouchSubstageLabel(substage: RetouchSubstage): string {
  const config = ALL_RETOUCH_SUBSTAGES.find(s => s.id === substage);
  return config?.label || substage;
}

/**
 * Get the color for a retouch substage
 */
export function getRetouchSubstageColor(substage: RetouchSubstage): string {
  const config = ALL_RETOUCH_SUBSTAGES.find(s => s.id === substage);
  return config?.color || 'gray';
}

/**
 * Get the next retouch substage in the sequence for a given role
 * @param currentSubstage - The current retouch substage
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getNextRetouchSubstage(currentSubstage: RetouchSubstage, userRole: UserRole | null, isFreelancer: boolean = false): RetouchSubstage | null {
  const allowedSubstages = getAllowedRetouchSubstages(userRole, isFreelancer);
  const currentIndex = allowedSubstages.indexOf(currentSubstage);
  
  if (currentIndex === -1 || currentIndex === allowedSubstages.length - 1) {
    return null; // Substage not found or already at the end
  }
  
  return allowedSubstages[currentIndex + 1];
}

/**
 * Get the previous retouch substage in the sequence for a given role
 * @param currentSubstage - The current retouch substage
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getPreviousRetouchSubstage(currentSubstage: RetouchSubstage, userRole: UserRole | null, isFreelancer: boolean = false): RetouchSubstage | null {
  const allowedSubstages = getAllowedRetouchSubstages(userRole, isFreelancer);
  const currentIndex = allowedSubstages.indexOf(currentSubstage);
  
  if (currentIndex <= 0) {
    return null; // Substage not found or already at the beginning
  }
  
  return allowedSubstages[currentIndex - 1];
}

/**
 * Get the default substage when an asset enters retouch stage
 * @param userRole - The user's role who is moving the asset to retouch
 * @param isFreelancer - Whether the user is a freelancer
 */
export function getDefaultRetouchSubstage(userRole: UserRole | null, isFreelancer: boolean = false): RetouchSubstage {
  // Platform users and freelancers start with internal_review
  if (
    userRole === 'platform_super' || 
    userRole === 'platform_admin' ||
    userRole === 'external_retoucher' ||
    userRole === 'external_prompter' ||
    (userRole === 'brand_admin' && isFreelancer)
  ) {
    return 'internal_review';
  }

  // Brand users start with revision_1
  return 'revision_1';
}

/**
 * Get badge styles for a retouch substage
 */
export function getRetouchSubstageBadgeClass(substage: RetouchSubstage): string {
  const config = ALL_RETOUCH_SUBSTAGES.find(s => s.id === substage);
  const color = config?.color || 'gray';
  
  const colorMap: Record<string, string> = {
    orange: 'bg-orange-50 text-orange-700 border-orange-200',
    blue: 'bg-blue-50 text-blue-700 border-blue-200',
    purple: 'bg-purple-50 text-purple-700 border-purple-200',
    gray: 'bg-gray-50 text-gray-700 border-gray-200'
  };
  
  return colorMap[color] || colorMap.gray;
}