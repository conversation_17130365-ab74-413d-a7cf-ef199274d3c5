import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

// These environment variables are set by Vite from the .env file
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate environment variables are available
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables. Please check your .env file');
}

// Create a Supabase client with explicit auth configuration
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  }
});

// Optimized storage bucket configuration
export const STORAGE_BUCKETS = {
  PROFILES: {
    name: 'profiles',
    public: true,
    mimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
    uploadLimit: 5 * 1024 * 1024 // 5MB
  },
  MEDIA_THUMBNAILS: {
    name: 'media-thumbnails', 
    public: true,
    mimeTypes: ['image/webp', 'image/jpeg', 'image/png'],
    uploadLimit: 1 * 1024 * 1024 // 1MB
  },
  MEDIA_COMPRESSED: {
    name: 'media-compressed',
    public: true,
    mimeTypes: ['image/webp', 'image/jpeg', 'image/png'],
    uploadLimit: 5 * 1024 * 1024 // 5MB
  },
  MEDIA_ORIGINALS: {
    name: 'media-originals',
    public: true,
    mimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/tiff'],
    uploadLimit: 50 * 1024 * 1024 // 50MB
  },
  AI_GENERATED: {
    name: 'ai-generated',
    public: true,
    mimeTypes: ['image/png', 'image/jpeg', 'image/webp'],
    uploadLimit: 10 * 1024 * 1024 // 10MB
  },
  GENERAL_UPLOADS: {
    name: 'general-uploads',
    public: true,
    mimeTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml', 'application/pdf', 'application/zip'],
    uploadLimit: 10 * 1024 * 1024 // 10MB
  }
};

/**
 * Sanitize a file name to make it safe for storage
 * Removes special characters, replaces spaces with underscores,
 * and ensures the name is not too long
 */
export function sanitizeFileName(fileName: string): string {
  if (!fileName) return '';
  
  // Log original filename for debugging
  console.log(`SANITIZE: Original filename: "${fileName}"`);
  
  // Get file extension
  const lastDotIndex = fileName.lastIndexOf('.');
  const extension = lastDotIndex !== -1 ? fileName.slice(lastDotIndex) : '';
  const nameWithoutExt = lastDotIndex !== -1 ? fileName.slice(0, lastDotIndex) : fileName;
  
  // First, handle filenames with "_ copy" or " copy" pattern (common in duplicated files)
  let nameToProcess = nameWithoutExt;
  if (nameWithoutExt.match(/[\s_]*copy$/i)) {
    console.log(`SANITIZE: Detected copy pattern in filename`);
    // Convert any variation of spaces/underscores with "copy" to "_copy"
    nameToProcess = nameWithoutExt.replace(/[\s_]*copy$/i, '_copy');
  }
  
  // Replace spaces and special characters
  const sanitized = nameToProcess
    .replace(/[^a-zA-Z0-9-_]/g, '_') // Replace special chars with underscore
    .replace(/_{2,}/g, '_')          // Replace multiple underscores with single
    .replace(/^_+|_+$/g, '')         // Remove leading/trailing underscores
    .toLowerCase();                  // Convert to lowercase
  
  // Limit length (max 100 chars including extension)
  const maxLength = 100 - extension.length;
  const truncated = sanitized.length > maxLength 
    ? sanitized.slice(0, maxLength) 
    : sanitized;
  
  const result = truncated + extension;
  console.log(`SANITIZE: Result: "${result}"`);
  
  return result;
}

/**
 * Upload a file to Supabase Storage
 * @param bucket The storage bucket to upload to
 * @param path The path within the bucket
 * @param file The file to upload
 * @param onProgress Optional callback for upload progress
 * @returns The uploaded file data with path information
 */
export async function uploadFile(
  bucket: string,
  path: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<{ path: string; sanitizedPath: string; originalPath: string; publicUrl?: string } | null> {
  try {
    // Sanitize the path to ensure it's valid for storage
    const sanitizedPath = path.split('/').map((segment, index, array) => {
      // Only sanitize the filename (last segment)
      return index === array.length - 1 ? sanitizeFileName(segment) : segment;
    }).join('/');
    
    // Create upload options
    const options = {
      cacheControl: '3600',
      upsert: true
    };
    
    // Upload the file
    const { error } = await supabase.storage
      .from(bucket)
      .upload(sanitizedPath, file, options);
      
    if (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
    
    // If progress callback is provided, simulate progress as we can't track it directly
    if (onProgress) {
      // Since we can't track real progress, at least indicate completion
      onProgress(100);
    }
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(sanitizedPath);
    
    return {
      path: sanitizedPath,
      sanitizedPath: sanitizedPath,
      originalPath: path,
      publicUrl: urlData.publicUrl
    };
  } catch (error) {
    console.error('Error in uploadFile:', error);
    throw error;
  }
}

/**
 * Delete a file from Supabase Storage
 * @param bucket The storage bucket name
 * @param path The file path within the bucket
 * @returns The result of the delete operation
 */
export const deleteFile = async (bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .remove([path]);
  
  if (error) {
    throw error;
  }
  
  return data;
};

/**
 * Get a public URL for a file in Supabase Storage
 * @param bucket The storage bucket name
 * @param path The file path within the bucket
 * @returns The public URL for the file
 */
export const getPublicUrl = (bucket: string, path: string) => {
  const { data } = supabase.storage
    .from(bucket)
    .getPublicUrl(path);
  
  return data.publicUrl;
};

export default supabase; 