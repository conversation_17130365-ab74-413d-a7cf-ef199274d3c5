import { supabase } from './supabase';

/**
 * Email service using Supabase Edge Functions with Resend
 * This ensures emails work in both development and production
 */

// Helper function to check if code is running in a browser environment
function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

// Helper function to check if we're in local development
function isLocalEnvironment(): boolean {
  if (!isBrowser()) return false;
  return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
}

// Email sending functions
export const emailService = {
  /**
   * Checks if email service is properly configured
   */
  isConfigured: () => {
    // Always return true since we're using Edge Functions
    return true;
  },

  /**
   * Sends a password reset email using Supabase Edge Function
   * @param email Recipient email address
   * @param resetLink Password reset link
   */
  async sendPasswordResetEmail(email: string, resetLink: string): Promise<boolean> {
    try {
      const htmlBody = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${window.location.origin}/fashionlab-logo.png" alt="FashionLab Logo" style="height: 50px;">
          </div>
          <h2 style="color: #1f2937; font-size: 24px; margin-bottom: 16px;">Reset Your Password</h2>
          <p style="color: #4b5563; font-size: 16px; margin-bottom: 24px;">
            You've requested to reset your password for FashionLab. Click the button below to create a new password:
          </p>
          <div style="text-align: center; margin: 32px 0;">
            <a href="${resetLink}" style="background-color: #5452F6; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: 500;">Reset Your Password</a>
          </div>
          <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
            Or copy and paste this URL into your browser:
          </p>
          <p style="color: #6b7280; font-size: 14px; margin-bottom: 24px; word-break: break-all;">
            ${resetLink}
          </p>
          <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
            If you didn't request a password reset, you can safely ignore this email.
          </p>
          <p style="color: #6b7280; font-size: 14px; margin-bottom: 24px;">
            The link will expire in 24 hours.
          </p>
          <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 30px 0;" />
          <p style="color: #9ca3af; font-size: 12px; text-align: center;">FashionLab - Asset Management for Fashion Brands</p>
        </div>
      `;

      const plainTextBody = `
Reset Your Password

You've requested to reset your password for FashionLab.

Reset your password by visiting:
${resetLink}

If you didn't request a password reset, you can safely ignore this email.
The link will expire in 24 hours.

FashionLab - Asset Management for Fashion Brands
      `;

      console.log(`🚀 Sending password reset email to ${email} via Supabase Edge Function`);

      // Use Supabase Edge Function to send the email
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: email,
          subject: 'Reset your FashionLab password',
          html: htmlBody,
          text: plainTextBody
        }
      });

      if (error) {
        console.error('❌ Failed to send password reset email via Edge Function:', error);
        
        // For local development, provide debugging info
        if (isLocalEnvironment()) {
          console.log('');
          console.log('📣 LOCAL DEVELOPMENT - PASSWORD RESET URL FOR TESTING 📣');
          console.log('=========================================================');
          console.log(`✅ Test the password reset with this URL:`);
          console.log(`👉 ${resetLink}`);
          console.log('=========================================================');
          console.log('');
        }
        
        return false;
      }

      console.log('✅ Password reset email sent successfully:', data);
      
      // For local development, also provide debugging info
      if (isLocalEnvironment()) {
        console.log('');
        console.log('📣 LOCAL DEVELOPMENT - PASSWORD RESET URL FOR TESTING 📣');
        console.log('=========================================================');
        console.log(`✅ Email sent via Edge Function AND you can test with this URL:`);
        console.log(`👉 ${resetLink}`);
        console.log('=========================================================');
        console.log('');
      }

      return true;
    } catch (error) {
      console.error('❌ Exception when sending password reset email:', error);
      
      // For local development, still provide the URL for manual testing
      if (isLocalEnvironment()) {
        console.log('');
        console.log('⚠️ Error occurred, but you can still test with this URL:');
        console.log(`👉 ${resetLink}`);
        console.log('');
      }
      
      return false;
    }
  },

  /**
   * Sends a welcome email using Supabase Edge Function
   * @param email Recipient email address
   * @param username Username or first name
   */
  async sendWelcomeEmail(email: string, username: string): Promise<boolean> {
    try {
      const htmlBody = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="${window.location.origin}/fashionlab-logo.png" alt="FashionLab Logo" style="height: 50px;">
          </div>
          <h2 style="color: #1f2937; font-size: 24px; margin-bottom: 16px;">Welcome to FashionLab!</h2>
          <p style="color: #4b5563; font-size: 16px; margin-bottom: 24px;">Hi ${username},</p>
          <p style="color: #4b5563; font-size: 16px; margin-bottom: 24px;">
            Thank you for joining FashionLab. We're excited to have you on board!
          </p>
          <p style="color: #4b5563; font-size: 16px; margin-bottom: 24px;">
            If you have any questions, please don't hesitate to reach out to our support team.
          </p>
          <div style="text-align: center; margin: 32px 0;">
            <a href="${window.location.origin}" style="background-color: #5452F6; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: 500;">Get Started</a>
          </div>
          <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 30px 0;" />
          <p style="color: #9ca3af; font-size: 12px; text-align: center;">FashionLab - Asset Management for Fashion Brands</p>
        </div>
      `;

      const plainTextBody = `
Welcome to FashionLab!

Hi ${username},

Thank you for joining FashionLab. We're excited to have you on board!

If you have any questions, please don't hesitate to reach out to our support team.

Get started: ${window.location.origin}

FashionLab - Asset Management for Fashion Brands
      `;

      console.log(`🚀 Sending welcome email to ${email} via Supabase Edge Function`);

      // Use Supabase Edge Function to send the email
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: email,
          subject: 'Welcome to FashionLab',
          html: htmlBody,
          text: plainTextBody
        }
      });

      if (error) {
        console.error('❌ Failed to send welcome email via Edge Function:', error);
        return false;
      }

      console.log('✅ Welcome email sent successfully:', data);
      return true;
    } catch (error) {
      console.error('❌ Exception when sending welcome email:', error);
      return false;
    }
  }
};

export default emailService;