import { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/database.types';

/**
 * Debug function to test what fields can be updated on assets
 */
export async function debugAssetUpdate(
  supabase: SupabaseClient<Database>,
  assetId: string
): Promise<void> {
  console.log('[debugAssetUpdate] Starting diagnostic test for asset:', assetId);

  try {
    // First, get the current asset data
    const { data: originalAsset, error: fetchError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', assetId)
      .single();

    if (fetchError || !originalAsset) {
      console.error('[debugAssetUpdate] Failed to fetch asset:', fetchError);
      return;
    }

    console.log('[debugAssetUpdate] Original asset:', {
      id: originalAsset.id,
      workflow_stage: originalAsset.workflow_stage,
      updated_at: originalAsset.updated_at,
      file_name: originalAsset.file_name
    });

    // Test 1: Try to update workflow_stage
    console.log('[debugAssetUpdate] Test 1: Updating workflow_stage...');
    const newStage = originalAsset.workflow_stage === 'upload' ? 'draft' : 'upload';
    
    const { data: test1Data, error: test1Error } = await supabase
      .from('assets')
      .update({ workflow_stage: newStage })
      .eq('id', assetId)
      .select('id, workflow_stage, updated_at');

    if (test1Error) {
      console.error('[debugAssetUpdate] Test 1 error:', test1Error);
    } else {
      console.log('[debugAssetUpdate] Test 1 result:', {
        returned: test1Data?.[0],
        stageChanged: test1Data?.[0]?.workflow_stage === newStage
      });
    }

    // Test 2: Try to update updated_at timestamp
    console.log('[debugAssetUpdate] Test 2: Updating updated_at...');
    const newTimestamp = new Date().toISOString();
    
    const { data: test2Data, error: test2Error } = await supabase
      .from('assets')
      .update({ updated_at: newTimestamp })
      .eq('id', assetId)
      .select('id, updated_at');

    if (test2Error) {
      console.error('[debugAssetUpdate] Test 2 error:', test2Error);
    } else {
      console.log('[debugAssetUpdate] Test 2 result:', {
        returned: test2Data?.[0],
        timestampChanged: test2Data?.[0]?.updated_at === newTimestamp
      });
    }

    // Test 3: Try to update both fields
    console.log('[debugAssetUpdate] Test 3: Updating both workflow_stage and updated_at...');
    
    const { data: test3Data, error: test3Error } = await supabase
      .from('assets')
      .update({ 
        workflow_stage: newStage,
        updated_at: new Date().toISOString()
      })
      .eq('id', assetId)
      .select('id, workflow_stage, updated_at');

    if (test3Error) {
      console.error('[debugAssetUpdate] Test 3 error:', test3Error);
    } else {
      console.log('[debugAssetUpdate] Test 3 result:', {
        returned: test3Data?.[0],
        stageChanged: test3Data?.[0]?.workflow_stage === newStage
      });
    }

    // Final check: Fetch the asset again to see final state
    const { data: finalAsset, error: finalError } = await supabase
      .from('assets')
      .select('id, workflow_stage, updated_at')
      .eq('id', assetId)
      .single();

    if (!finalError && finalAsset) {
      console.log('[debugAssetUpdate] Final asset state:', {
        id: finalAsset.id,
        workflow_stage: finalAsset.workflow_stage,
        updated_at: finalAsset.updated_at,
        stageChanged: finalAsset.workflow_stage !== originalAsset.workflow_stage,
        timestampChanged: finalAsset.updated_at !== originalAsset.updated_at
      });
    }

  } catch (error) {
    console.error('[debugAssetUpdate] Unexpected error:', error);
  }
}

/**
 * Test direct SQL update to bypass any RLS policies
 */
export async function debugDirectSQLUpdate(
  supabase: SupabaseClient<Database>,
  assetId: string
): Promise<void> {
  console.log('[debugDirectSQLUpdate] Testing direct SQL update for asset:', assetId);

  try {
    // Use RPC to execute raw SQL (if you have a function for this)
    // This would need a database function to be created first
    const { data, error } = await supabase.rpc('debug_update_asset', {
      asset_id: assetId,
      new_stage: 'draft'
    });

    if (error) {
      console.error('[debugDirectSQLUpdate] RPC error:', error);
      console.log('[debugDirectSQLUpdate] Note: This requires a database function to be created');
    } else {
      console.log('[debugDirectSQLUpdate] RPC result:', data);
    }
  } catch (error) {
    console.error('[debugDirectSQLUpdate] Error:', error);
  }
}