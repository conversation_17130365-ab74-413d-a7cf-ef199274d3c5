/**
 * Advanced Image Processing for Production Bulk Uploads
 * Optimized for handling large batches (200+ files x 10MB each)
 */

import imageCompression from 'browser-image-compression';
import PQueue from 'p-queue';
import { v4 as uuidv4 } from 'uuid';
import { uploadAssetWithCompression } from './assetStorage';

// Production-ready configuration
export const PRODUCTION_COMPRESSION_CONFIG = {
  // Batch processing
  BATCH_SIZE: 10,           // Process 10 files at a time
  CONCURRENT_UPLOADS: 4,    // 4 parallel uploads (up from 2)
  
  // Memory management
  MAX_MEMORY_FILES: 5,      // Keep max 5 processed files in memory
  CLEANUP_INTERVAL: 3000,   // Clean up processed files every 3s
  
  // Compression settings
  COMPRESSION: {
    maxSizeMB: 3,           // Slightly higher for fashion quality
    maxWidthOrHeight: 2400, // Higher res for fashion details
    useWebWorker: true,     // Essential for performance
    quality: 0.88,          // Higher quality for fashion
    alwaysKeepResolution: false, // Allow smart resizing
  },
  
  // Thumbnail settings
  THUMBNAIL: {
    maxSizeMB: 1.0,         // Increased for larger thumbnails
    maxWidthOrHeight: 1000, // Increased to 1000px for crisp display
    quality: 0.9,           // Higher quality for better appearance
    useWebWorker: true,
  },
  
  // Timeouts and retries
  UPLOAD_TIMEOUT: 120000,   // 2 minutes per upload
  MAX_RETRIES: 3,
  RETRY_DELAY: 2000,
};

export interface BulkUploadProgress {
  totalFiles: number;
  processedFiles: number;
  failedFiles: number;
  currentFile?: string;
  stage: 'queued' | 'processing' | 'uploading' | 'complete' | 'error';
  estimatedTimeRemaining?: number;
}

export interface BulkUploadResult {
  successful: Array<{
    fileId: string;
    fileName: string;
    assetId: string;
    paths: {
      originalPath: string;
      compressedPath: string;
      thumbnailPath: string;
    };
  }>;
  failed: Array<{
    fileId: string;
    fileName: string;
    error: string;
  }>;
  stats: {
    totalFiles: number;
    successCount: number;
    failureCount: number;
    totalProcessingTime: number;
    averageFileTime: number;
  };
}

/**
 * Production-ready bulk upload processor
 */
export class BulkImageProcessor {
  private queue: PQueue;
  private progressCallback?: (progress: BulkUploadProgress) => void;
  private startTime: number = 0;
  private processedCount: number = 0;
  private failedCount: number = 0;
  
  constructor(progressCallback?: (progress: BulkUploadProgress) => void) {
    this.queue = new PQueue({ 
      concurrency: PRODUCTION_COMPRESSION_CONFIG.CONCURRENT_UPLOADS,
      timeout: PRODUCTION_COMPRESSION_CONFIG.UPLOAD_TIMEOUT,
    });
    this.progressCallback = progressCallback;
  }

  /**
   * Process bulk upload with memory management and error recovery
   */
  async processBulkUpload(
    files: File[],
    collectionId: string
  ): Promise<BulkUploadResult> {
    this.startTime = Date.now();
    this.processedCount = 0;
    this.failedCount = 0;

    const result: BulkUploadResult = {
      successful: [],
      failed: [],
      stats: {
        totalFiles: files.length,
        successCount: 0,
        failureCount: 0,
        totalProcessingTime: 0,
        averageFileTime: 0,
      }
    };

    // Validate input
    if (files.length === 0) {
      throw new Error('No files provided for bulk upload');
    }

    if (files.length > 500) {
      throw new Error('Bulk upload limited to 500 files per batch');
    }

    // Process in batches to manage memory
    const batches = this.createBatches(files, PRODUCTION_COMPRESSION_CONFIG.BATCH_SIZE);
    
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      
      // Update progress
      this.updateProgress({
        totalFiles: files.length,
        processedFiles: this.processedCount,
        failedFiles: this.failedCount,
        stage: 'processing',
        estimatedTimeRemaining: this.calculateETA(files.length),
      });

      // Process batch
      const batchPromises = batch.map(file => this.processSingleFile(file, collectionId));
      const batchResults = await Promise.allSettled(batchPromises);

      // Collect results
      batchResults.forEach((batchResult, index) => {
        const file = batch[index];
        
        if (batchResult.status === 'fulfilled' && batchResult.value.success) {
          result.successful.push(batchResult.value.data);
          this.processedCount++;
        } else {
          const error = batchResult.status === 'rejected' 
            ? batchResult.reason.message 
            : batchResult.value.error;
          
          result.failed.push({
            fileId: `file-${batchIndex}-${index}`,
            fileName: file.name,
            error,
          });
          this.failedCount++;
        }
      });

      // Memory cleanup between batches
      if (batchIndex < batches.length - 1) {
        await this.cleanupMemory();
      }
    }

    // Final statistics
    const endTime = Date.now();
    result.stats = {
      totalFiles: files.length,
      successCount: result.successful.length,
      failureCount: result.failed.length,
      totalProcessingTime: endTime - this.startTime,
      averageFileTime: (endTime - this.startTime) / files.length,
    };

    // Final progress update
    this.updateProgress({
      totalFiles: files.length,
      processedFiles: this.processedCount,
      failedFiles: this.failedCount,
      stage: 'complete',
    });

    return result;
  }

  /**
   * Process a single file with retry logic
   */
  private async processSingleFile(
    file: File,
    collectionId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const assetId = uuidv4();
    let lastError: Error | null = null;

    // Retry logic
    for (let attempt = 1; attempt <= PRODUCTION_COMPRESSION_CONFIG.MAX_RETRIES; attempt++) {
      try {
        // Update progress for current file
        this.updateProgress({
          totalFiles: 0, // Will be filled by caller
          processedFiles: this.processedCount,
          failedFiles: this.failedCount,
          currentFile: file.name,
          stage: 'uploading',
        });

        // Advanced compression using browser-image-compression library
        const compressedFile = await imageCompression(file, PRODUCTION_COMPRESSION_CONFIG.COMPRESSION);
        const thumbnailFile = await imageCompression(file, PRODUCTION_COMPRESSION_CONFIG.THUMBNAIL);

        // Upload with the optimized system
        const result = await uploadAssetWithCompression(
          file,
          collectionId,
          assetId,
          (stage, progress) => {
            // Optional: More granular progress reporting
            console.log(`${file.name}: ${stage} - ${progress}%`);
          }
        );

        if (result) {
          return {
            success: true,
            data: {
              fileId: assetId,
              fileName: file.name,
              assetId,
              paths: result,
            }
          };
        } else {
          throw new Error('Upload returned null result');
        }

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < PRODUCTION_COMPRESSION_CONFIG.MAX_RETRIES) {
          console.warn(`Upload attempt ${attempt} failed for ${file.name}, retrying...`, error);
          await new Promise(resolve => setTimeout(resolve, PRODUCTION_COMPRESSION_CONFIG.RETRY_DELAY));
        }
      }
    }

    return {
      success: false,
      error: `Failed after ${PRODUCTION_COMPRESSION_CONFIG.MAX_RETRIES} attempts: ${lastError?.message}`,
    };
  }

  /**
   * Create batches for memory management
   */
  private createBatches<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateETA(totalFiles: number): number {
    if (this.processedCount === 0) return 0;
    
    const elapsed = Date.now() - this.startTime;
    const averageTimePerFile = elapsed / this.processedCount;
    const remainingFiles = totalFiles - this.processedCount;
    
    return Math.round(remainingFiles * averageTimePerFile / 1000); // Return in seconds
  }

  /**
   * Update progress callback
   */
  private updateProgress(progress: Partial<BulkUploadProgress>) {
    if (this.progressCallback) {
      this.progressCallback(progress as BulkUploadProgress);
    }
  }

  /**
   * Memory cleanup between batches
   */
  private async cleanupMemory(): Promise<void> {
    // Force garbage collection if available
    if ('gc' in window && typeof window.gc === 'function') {
      window.gc();
    }
    
    // Small delay to allow cleanup
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  /**
   * Cancel ongoing operations
   */
  async cancelProcessing(): Promise<void> {
    this.queue.clear();
    await this.queue.onIdle();
  }
}

/**
 * Convenience function for simple bulk uploads
 */
export async function bulkUploadImages(
  files: File[],
  collectionId: string,
  onProgress?: (progress: BulkUploadProgress) => void
): Promise<BulkUploadResult> {
  const processor = new BulkImageProcessor(onProgress);
  return processor.processBulkUpload(files, collectionId);
}

/**
 * Validate files before processing
 */
export function validateBulkUpload(files: File[]): {
  valid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check file count
  if (files.length === 0) {
    errors.push('No files selected');
  } else if (files.length > 500) {
    errors.push('Maximum 500 files allowed per batch');
  } else if (files.length > 100) {
    warnings.push('Large batch size may take significant time to process');
  }

  // Check total size
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const totalSizeGB = totalSize / (1024 * 1024 * 1024);
  
  if (totalSizeGB > 10) {
    errors.push('Total file size cannot exceed 10GB');
  } else if (totalSizeGB > 5) {
    warnings.push('Large total size may cause memory issues');
  }

  // Check individual file sizes
  const oversizedFiles = files.filter(file => file.size > 50 * 1024 * 1024); // 50MB
  if (oversizedFiles.length > 0) {
    errors.push(`${oversizedFiles.length} files exceed 50MB limit`);
  }

  // Check file types
  const invalidFiles = files.filter(file => !file.type.startsWith('image/'));
  if (invalidFiles.length > 0) {
    errors.push(`${invalidFiles.length} files are not images`);
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
  };
}