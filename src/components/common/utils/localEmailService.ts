import { supabase } from './supabase';

/**
 * Local email service that uses Supabase Auth for invitation emails
 * This ensures emails show up in Inbucket during local development
 */
export const sendInvitationEmailViaAuth = async (
  email: string,
  invitationToken: string,
  organizationName: string
) => {
  try {
    // For local development, we can use Supabase Auth's magic link feature
    // This will automatically send emails that appear in Inbucket
    const siteUrl = window.location.origin;
    const inviteUrl = `${siteUrl}/invite/${invitationToken}`;
    
    // Send a magic link that redirects to our invitation acceptance page
    const { error } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        emailRedirectTo: inviteUrl,
        data: {
          invitation_token: invitationToken,
          organization_name: organizationName
        }
      }
    });

    if (error) {
      console.error('Error sending invitation via Auth:', error);
      return false;
    }

    console.log('✅ Invitation email sent via Supabase Auth (will appear in Inbucket)');
    return true;
  } catch (error) {
    console.error('Error in sendInvitationEmailViaAuth:', error);
    return false;
  }
};

/**
 * Alternative: Use Supabase's admin API to send a custom email
 * This requires the service role key
 */
export const sendCustomEmailViaSupabase = async (
  to: string,
  subject: string,
  html: string,
  text: string
) => {
  // In local development, emails sent through Supabase Auth
  // automatically appear in Inbucket at http://127.0.0.1:54324
  
  // For now, we'll use the Auth magic link approach above
  // as it's simpler and works well for invitations
  console.log('📧 Email would be sent to:', to);
  console.log('📧 Subject:', subject);
  console.log('📧 Check Inbucket at: http://127.0.0.1:54324');
  
  return true;
};