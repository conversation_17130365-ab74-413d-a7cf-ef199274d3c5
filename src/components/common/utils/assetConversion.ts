import { Asset } from '../types/database.types';
import { AssetItem } from '../../../data/productAssets';
import { AssetType, WorkflowStage } from '../../../contexts/FilterContext';
import { getAssetUrl } from './utils';

/**
 * Convert a Supabase Asset to AssetItem format for UI display
 */
export function convertAssetToAssetItem(asset: Asset): AssetItem {
  // Map workflow_stage to the format expected by the UI
  const stageMap: Record<string, WorkflowStage> = {
    'upload': 'upload',
    'raw_ai_images': 'raw_ai_images',
    'selected': 'selected',
    'refined': 'refined',
    'upscale': 'upscale',
    'retouch': 'retouch',
    'final': 'final'
  };

  // Use the utility function to get the preview URL, preferring thumbnails
  const previewUrl = getAssetUrl(asset, true);

  const mappedStage = stageMap[asset.workflow_stage] || 'upload';

  // Debug logging for raw_ai_images assets
  if (asset.workflow_stage === 'raw_ai_images') {
    console.log('[convertAssetToAssetItem] Converting raw_ai_images asset:', {
      id: asset.id,
      file_name: asset.file_name,
      workflow_stage: asset.workflow_stage,
      mappedStage,
      previewUrl
    });
  }

  return {
    id: asset.id,
    productId: asset.product_id || '',
    name: asset.file_name,
    preview: previewUrl,
    type: 'product-front' as AssetType, // Default type - could be enhanced with metadata
    stage: mappedStage,
    dateUpdated: asset.updated_at || new Date().toISOString(),
    dateCreated: asset.created_at || new Date().toISOString(),
    size: asset.file_size.toString(),
    tags: [], // Tags are fetched separately via useAssetTags
    metadata: asset.metadata as Record<string, unknown> | undefined,
    retouch_substage: asset.retouch_substage || undefined
  };
}

/**
 * Get display name for workflow stages
 */
export function getStageDisplayName(stage: string): string {
  switch (stage) {
    case 'upload': return 'Input Assets';
    case 'raw_ai_images': return 'Raw AI Images';
    case 'selected': return 'Selected';
    case 'refined': return 'Refined';
    case 'upscale': return 'Upscaled';
    case 'retouch': return 'Retouched';
    case 'final': return 'Final';
    default: return stage;
  }
}

/**
 * Get style classes for workflow stage badges
 */
export function getStageStyles(stage: string): string {
  switch (stage) {
    case 'upload':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'raw_ai_images':
      return 'bg-indigo-50 text-indigo-700 border-indigo-200';
    case 'selected':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'refined':
      return 'bg-teal-50 text-teal-700 border-teal-200';
    case 'upscale':
      return 'bg-amber-50 text-amber-700 border-amber-200';
    case 'retouch':
      return 'bg-emerald-50 text-emerald-700 border-emerald-200';
    case 'final':
      return 'bg-purple-50 text-purple-700 border-purple-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
}