import { supabase } from './supabase';

/**
 * Email service using Supabase Edge Functions
 */
export const emailService = {
  /**
   * Sends a password reset email via Supabase Edge Function
   * @param email Recipient email address
   * @param resetLink Password reset link
   */
  async sendPasswordResetEmail(email: string, resetLink: string): Promise<boolean> {
    try {
      const { error } = await supabase.functions.invoke('send-email', {
        body: {
          type: 'password_reset',
          email,
          resetLink
        }
      });
      
      if (error) {
        console.error('Failed to send password reset email:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Exception when sending password reset email:', error);
      return false;
    }
  },

  /**
   * Sends a welcome email to a new user via Supabase Edge Function
   * @param email Recipient email address
   * @param username Username or first name
   */
  async sendWelcomeEmail(email: string, username: string): Promise<boolean> {
    try {
      const { error } = await supabase.functions.invoke('send-email', {
        body: {
          type: 'welcome',
          email,
          username
        }
      });
      
      if (error) {
        console.error('Failed to send welcome email:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Exception when sending welcome email:', error);
      return false;
    }
  }
};

export default emailService; 