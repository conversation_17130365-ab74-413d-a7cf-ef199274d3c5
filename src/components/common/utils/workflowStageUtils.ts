/**
 * Workflow Stage Utilities
 * Provides role-based filtering and configuration for workflow stages
 */

import { UserRole } from '../../../contexts/UserRoleContext';
import { WorkflowStage } from '../types/database.types';

// Complete workflow stage configuration with display information
export interface WorkflowStageConfig {
  id: WorkflowStage;
  label: string;
  icon: string; // Lucide icon name
  color: string;
  order: number;
}

// All available workflow stages with their configuration
export const ALL_WORKFLOW_STAGES: WorkflowStageConfig[] = [
  { id: 'upload', label: 'Input Assets', icon: 'ImageIcon', color: 'blue', order: 1 },
  { id: 'raw_ai_images', label: 'Raw AI Images', icon: 'Pencil', color: 'indigo', order: 2 },
  { id: 'selected', label: 'Selected', icon: 'CheckCircle', color: 'green', order: 3 },
  { id: 'refined', label: 'Refined', icon: 'Sparkles', color: 'teal', order: 4 },
  { id: 'upscale', label: 'Upscaled', icon: 'Zap', color: 'amber', order: 5 },
  { id: 'retouch', label: 'Retouched', icon: 'WandSparkles', color: 'emerald', order: 6 },
  { id: 'final', label: 'Final', icon: 'Check', color: 'purple', order: 7 }
];

// Brand user workflow stages (brand_admin and brand_member)
export const BRAND_USER_WORKFLOW_STAGES: WorkflowStage[] = [
  'upload',
  'raw_ai_images', 
  'selected',
  'retouch',
  'final'
];

// Platform user workflow stages (platform_super and platform_admin)
export const PLATFORM_USER_WORKFLOW_STAGES: WorkflowStage[] = [
  'upload',
  'raw_ai_images',
  'selected',
  'refined',
  'upscale',
  'retouch',
  'final'
];

// External user workflow stages (external_retoucher and external_prompter)
// Also used for freelancers (brand_admin with is_freelancer = true)
export const EXTERNAL_USER_WORKFLOW_STAGES: WorkflowStage[] = [
  'upload',
  'raw_ai_images',
  'selected',
  'refined',
  'upscale', 
  'retouch',
  'final'
];

/**
 * Get allowed workflow stages based on user role
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getAllowedWorkflowStages(userRole: UserRole | null, isFreelancer: boolean = false): WorkflowStage[] {
  if (!userRole) {
    return BRAND_USER_WORKFLOW_STAGES; // Default to most restrictive
  }

  // Freelancers (brand_admin with is_freelancer = true) should see external user stages
  if (userRole === 'brand_admin' && isFreelancer) {
    return EXTERNAL_USER_WORKFLOW_STAGES;
  }

  switch (userRole) {
    case 'platform_super':
    case 'platform_admin':
      return PLATFORM_USER_WORKFLOW_STAGES;
    
    case 'brand_admin':
    case 'brand_member':
      return BRAND_USER_WORKFLOW_STAGES;
    
    case 'external_retoucher':
    case 'external_prompter':
      return EXTERNAL_USER_WORKFLOW_STAGES;
    
    default:
      return BRAND_USER_WORKFLOW_STAGES;
  }
}

/**
 * Get workflow stage configurations filtered by user role
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getWorkflowStageConfigs(userRole: UserRole | null, isFreelancer: boolean = false): WorkflowStageConfig[] {
  const allowedStages = getAllowedWorkflowStages(userRole, isFreelancer);
  
  return ALL_WORKFLOW_STAGES
    .filter(config => allowedStages.includes(config.id))
    .sort((a, b) => a.order - b.order);
}

/**
 * Check if a workflow stage is allowed for a user role
 * @param stage - The workflow stage to check
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function isWorkflowStageAllowed(stage: WorkflowStage, userRole: UserRole | null, isFreelancer: boolean = false): boolean {
  const allowedStages = getAllowedWorkflowStages(userRole, isFreelancer);
  return allowedStages.includes(stage);
}

/**
 * Get the display label for a workflow stage
 */
export function getWorkflowStageLabel(stage: WorkflowStage): string {
  const config = ALL_WORKFLOW_STAGES.find(s => s.id === stage);
  return config?.label || stage;
}

/**
 * Get the next workflow stage in the sequence for a given role
 * @param currentStage - The current workflow stage
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getNextWorkflowStage(currentStage: WorkflowStage, userRole: UserRole | null, isFreelancer: boolean = false): WorkflowStage | null {
  const allowedStages = getAllowedWorkflowStages(userRole, isFreelancer);
  const currentIndex = allowedStages.indexOf(currentStage);
  
  if (currentIndex === -1 || currentIndex === allowedStages.length - 1) {
    return null; // Stage not found or already at the end
  }
  
  return allowedStages[currentIndex + 1];
}

/**
 * Get the previous workflow stage in the sequence for a given role
 * @param currentStage - The current workflow stage
 * @param userRole - The user's role
 * @param isFreelancer - Whether the user is a freelancer (only applies to brand_admin)
 */
export function getPreviousWorkflowStage(currentStage: WorkflowStage, userRole: UserRole | null, isFreelancer: boolean = false): WorkflowStage | null {
  const allowedStages = getAllowedWorkflowStages(userRole, isFreelancer);
  const currentIndex = allowedStages.indexOf(currentStage);
  
  if (currentIndex <= 0) {
    return null; // Stage not found or already at the beginning
  }
  
  return allowedStages[currentIndex - 1];
}
