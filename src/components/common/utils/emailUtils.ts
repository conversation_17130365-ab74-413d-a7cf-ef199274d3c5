import { supabase } from './supabase';

/**
 * Helper utility for dealing with email invitations
 * Uses Supabase Edge Functions to send emails via Resend
 */

export const isLocalEnvironment = () => {
  // Enhanced check to ensure we detect local environments reliably
  const isLocalHost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
  const isDevelopmentPort = window.location.port === '5173' || window.location.port === '8080';
  console.log('Local environment detection:', { isLocalHost, isDevelopmentPort, fullUrl: window.location.href });
  return isLocalHost;
};

export const getMailServerUrl = () => {
  // The default port for mail server in Supabase CLI
  // Current version uses Mailpit instead of Inbucket
  return 'http://127.0.0.1:54324';
};

/**
 * Sends an invitation email using Supabase Edge Function
 * This properly handles both local development and production environments
 */
export const sendInvitationEmail = async (
  recipientEmail: string,
  organizationName: string,
  invitationToken: string,
  inviterName: string
) => {
  console.log('📧 sendInvitationEmail called with:', {
    recipientEmail,
    organizationName,
    invitationToken: invitationToken ? 'present' : 'missing',
    inviterName
  });
  
  try {
    const inviteUrl = `${window.location.origin}/invite/${invitationToken}`;
    
    // Create a beautifully formatted HTML email
    const htmlBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <img src="${window.location.origin}/fashionlab-logo.png" alt="FashionLab Logo" style="height: 50px;">
        </div>
        <h1 style="color: #1f2937; font-size: 24px; margin-bottom: 16px; text-align: center;">You've been invited!</h1>
        <p style="color: #4b5563; font-size: 16px; margin-bottom: 24px;">
          ${inviterName || 'Someone'} has invited you to join <strong>${organizationName}</strong> on the FashionLab platform.
        </p>
        <div style="text-align: center; margin: 32px 0;">
          <a href="${inviteUrl}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; display: inline-block;">
            Accept Invitation
          </a>
        </div>
        <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
          Or copy and paste this URL into your browser:
        </p>
        <p style="color: #6b7280; font-size: 14px; margin-bottom: 24px; word-break: break-all;">
          ${inviteUrl}
        </p>
        <p style="color: #6b7280; font-size: 14px; margin-bottom: 8px;">
          This invitation will expire in 48 hours.
        </p>
        <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 24px 0;">
        <p style="color: #9ca3af; font-size: 12px; text-align: center;">
          This email was sent from FashionLab Platform. If you did not request this invitation, please ignore this email.
        </p>
      </div>
    `;
    
    const plainTextBody = `
You've been invited to join ${organizationName} on FashionLab

${inviterName || 'Someone'} has invited you to join ${organizationName} on the FashionLab platform.

Accept the invitation by visiting:
${inviteUrl}

This invitation will expire in 48 hours.

If you did not request this invitation, please ignore this email.
    `;
    
    console.log(`🚀 Sending invitation email to ${recipientEmail} via Supabase Edge Function`);
    console.log('🔍 Supabase URL:', supabase.supabaseUrl);
    console.log('🔍 Function payload:', {
      to: recipientEmail,
      subject: `You've been invited to join ${organizationName} on FashionLab`,
      hasHtml: !!htmlBody,
      hasText: !!plainTextBody
    });
    
    // Use Supabase Edge Function to send the email
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        to: recipientEmail,
        subject: `You've been invited to join ${organizationName} on FashionLab`,
        html: htmlBody,
        text: plainTextBody
      }
    });
    
    if (error) {
      console.error('❌ Edge function error:', error);
      
      // For local development, still provide debugging info
      if (isLocalEnvironment()) {
        console.log('');
        console.log('📣 LOCAL DEVELOPMENT - INVITATION URL FOR TESTING 📣');
        console.log('====================================================');
        console.log(`✅ Test the invitation with this URL:`);
        console.log(`👉 ${inviteUrl}`);
        console.log('====================================================');
        console.log('');
      }
      
      return false;
    }
    
    console.log('✅ Email sent successfully:', data);
    
    // For local development, also provide debugging info
    if (isLocalEnvironment()) {
      console.log('');
      console.log('📣 LOCAL DEVELOPMENT - INVITATION URL FOR TESTING 📣');
      console.log('====================================================');
      console.log(`✅ Email sent via Edge Function AND you can test with this URL:`);
      console.log(`👉 ${inviteUrl}`);
      console.log(`📧 Check local mail server at: ${getMailServerUrl()}`);
      console.log('====================================================');
      console.log('');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error sending invitation email:', error);
    
    // For local development, still provide the URL for manual testing
    if (isLocalEnvironment()) {
      const inviteUrl = `${window.location.origin}/invite/${invitationToken}`;
      console.log('');
      console.log('⚠️ Error occurred, but you can still test with this URL:');
      console.log(`👉 ${inviteUrl}`);
      console.log('');
    }
    
    return false;
  }
};

export default {
  isLocalEnvironment,
  getMailServerUrl,
  sendInvitationEmail
};