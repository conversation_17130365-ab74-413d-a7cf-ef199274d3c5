import imageCompression from 'browser-image-compression';
import PQueue from 'p-queue';
import { v4 as uuidv4 } from 'uuid';
import { supabase, STORAGE_BUCKETS, uploadFile, sanitizeFileName } from './supabase';
import { 
  uploadAssetWithCompression, 
  generateAssetPath,
  type AssetType 
} from './assetStorage';

// Configuration for image compression
export interface CompressionOptions {
  maxSizeMB: number;        // Maximum size in MB
  maxWidthOrHeight: number; // Max width/height in pixels
  useWebWorker: boolean;    // Use web worker for better performance
  quality: number;          // 0 to 1, where 1 is highest quality
  onProgress?: (progress: number) => void; // Progress callback
}

// Configuration for thumbnails
export interface ThumbnailOptions {
  width: number;
  height: number;
  quality: number;
}

// Types for file processing results
export interface ProcessedImageResult {
  compressedFile: File;
  thumbnailFile: File | null;
  originalSize: number;
  compressedSize: number;
  thumbnailSize: number;
}

export interface UploadResult {
  originalPath: string;
  compressedPath: string;
  thumbnailPath: string | null;
  originalSize: number;
  compressedSize: number;
  thumbnailSize: number;
}

// Default compression options
export const DEFAULT_COMPRESSION_OPTIONS: CompressionOptions = {
  maxSizeMB: 2,             // Compress to 2MB max
  maxWidthOrHeight: 2000,   // Higher resolution for fashion products (up from 1920)
  useWebWorker: true,       // Use web worker for performance
  quality: 0.85,            // 85% quality - better quality for fashion details (up from 0.8)
};

// Default thumbnail options
export const DEFAULT_THUMBNAIL_OPTIONS: ThumbnailOptions = {
  width: 1000,              // 1000px width for thumbnails (for crisp display on larger screens)
  height: 1000,             // 1000px height for thumbnails
  quality: 0.9              // 90% quality for thumbnails (for better quality)
};

// Queue for handling image processing with controlled concurrency
const processingQueue = new PQueue({ concurrency: 2 });

/**
 * Upload an asset using the new optimized bucket structure
 */
export async function uploadAssetOptimized(
  file: File,
  collectionId: string,
  assetId: string,
  onProgress?: (stage: string, progress: number) => void
): Promise<{
  originalPath: string;
  compressedPath: string;
  thumbnailPath: string;
}> {
  try {
    return await uploadAssetWithCompression(file, collectionId, assetId, onProgress);
  } catch (error) {
    console.error('Error uploading asset with optimized structure:', error);
    throw error;
  }
}

/**
 * Process a file for upload - compresses the image and generates a thumbnail
 */
export async function processImageFile(
  file: File, 
  collectionId: string,
  onCompressProgress?: (progress: number) => void,
  compressionOptions = DEFAULT_COMPRESSION_OPTIONS,
  thumbnailOptions = DEFAULT_THUMBNAIL_OPTIONS
): Promise<ProcessedImageResult> {
  try {
    console.log(`Starting compression process for file: ${file.name}, size: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
    
    // Create unique IDs for the files
    const fileId = uuidv4();
    
    // Start compression with progress tracking
    onCompressProgress?.(0);
    
    // Compress the image
    let compressedFile;
    try {
      compressedFile = await imageCompression(file, {
        ...compressionOptions,
        onProgress: (progress) => {
          console.log(`Compression progress for ${file.name}: ${progress}%`);
          // Progress is 0-100
          onCompressProgress?.(progress / 100 * 50); // First 50% of progress is compression
        }
      });
      console.log(`Compression complete for ${file.name}. Original: ${(file.size / 1024 / 1024).toFixed(2)} MB, Compressed: ${(compressedFile.size / 1024 / 1024).toFixed(2)} MB`);
    } catch (compressError) {
      console.error(`Error compressing file ${file.name}:`, compressError);
      throw new Error(`Compression failed: ${compressError instanceof Error ? compressError.message : 'Unknown error'}`);
    }
    
    // Signal 50% completion (compression done)
    onCompressProgress?.(50);

    // Generate a thumbnail
    let thumbnailFile: File | null = null;
    
    // Create a thumbnail with canvas
    console.log(`Starting thumbnail generation for ${file.name}`);
    try {
      const thumbnailBlob = await createThumbnail(
        file, 
        thumbnailOptions.width, 
        thumbnailOptions.height, 
        thumbnailOptions.quality
      );
      
      if (thumbnailBlob) {
        // Create a File from the Blob
        const fileExtension = file.name.split('.').pop() || 'jpg';
        thumbnailFile = new File(
          [thumbnailBlob], 
          `thumb_${fileId}.${fileExtension}`, 
          { type: file.type }
        );
        
        console.log(`Thumbnail created for ${file.name}. Size: ${(thumbnailFile.size / 1024).toFixed(2)} KB`);
        
        // Signal 75% completion (thumbnail created)
        onCompressProgress?.(75);
      } else {
        console.warn(`Failed to create thumbnail for ${file.name}`);
      }
    } catch (thumbnailError) {
      // Don't fail the whole process if thumbnail generation fails
      console.error(`Error generating thumbnail for ${file.name}:`, thumbnailError);
      console.log('Continuing without thumbnail...');
    }
    
    // Signal 100% completion (processing done)
    onCompressProgress?.(100);
    
    return {
      compressedFile: new File(
        [compressedFile], 
        file.name, 
        { type: compressedFile.type }
      ),
      thumbnailFile,
      originalSize: file.size,
      compressedSize: compressedFile.size,
      thumbnailSize: thumbnailFile?.size || 0,
    };
  } catch (error) {
    console.error('Error processing image:', error);
    throw error;
  }
}

/**
 * Creates a thumbnail from an image file
 */
async function createThumbnail(
  file: File, 
  width: number, 
  height: number, 
  quality: number = 0.7
): Promise<Blob | null> {
  return new Promise((resolve, reject) => {
    try {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        // Clean up object URL
        URL.revokeObjectURL(url);
        
        // Create canvas for resizing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }
        
        // Set canvas dimensions to maintain aspect ratio
        const aspectRatio = img.width / img.height;
        
        // Calculate dimensions based on aspect ratio
        let targetWidth = width;
        let targetHeight = height;
        
        if (aspectRatio > 1) {
          // Landscape
          targetHeight = width / aspectRatio;
        } else {
          // Portrait
          targetWidth = height * aspectRatio;
        }
        
        canvas.width = targetWidth;
        canvas.height = targetHeight;
        
        // Draw image to canvas with resizing
        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
        
        // Convert canvas to blob
        canvas.toBlob(
          (blob) => {
            resolve(blob);
          },
          file.type,
          quality
        );
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image for thumbnail'));
      };
      
      img.src = url;
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Handle batch uploads with optimized compression and bucket distribution
 */
export async function uploadImagesWithProcessing(
  files: File[],
  collectionId: string,
  onFileProgress: (fileId: string, type: 'compression' | 'upload', progress: number) => void,
  onFileComplete: (fileId: string, result: UploadResult & { assetId: string }) => void,
  onError: (fileId: string, error: Error) => void,
  assetIds?: string[]
): Promise<void> {
  // Validate inputs
  if (!files || files.length === 0) {
    throw new Error("No files provided for upload");
  }
  
  if (!collectionId) {
    throw new Error("Collection ID is required");
  }

  // Add each file to the processing queue
  files.forEach((file, index) => {
    const assetId = assetIds?.[index] || uuidv4(); // Use provided asset ID or generate one
    const fileId = index.toString();
    
    processingQueue.add(async () => {
      try {
        // Use the optimized upload function
        const result = await uploadAssetWithCompression(
          file,
          collectionId,
          assetId,
          (stage, progress) => {
            // Map stages to the expected format
            if (stage.includes('Uploading') || stage.includes('Processing')) {
              onFileProgress(fileId, 'compression', progress);
            } else {
              onFileProgress(fileId, 'upload', progress);
            }
          }
        );
        
        // Notify completion with the optimized result
        onFileComplete(fileId, {
          originalPath: result.originalPath,
          compressedPath: result.compressedPath,
          thumbnailPath: result.thumbnailPath,
          originalSize: file.size,
          compressedSize: 0, // Not available in optimized flow
          thumbnailSize: 0,  // Not available in optimized flow
          assetId: assetId,  // Include the asset ID in the result
        });
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        onError(fileId, error instanceof Error ? error : new Error('Unknown error'));
      }
    });
  });
  
  // Start processing queue
  await processingQueue.onIdle();
} 