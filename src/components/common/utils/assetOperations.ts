import { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '../types/database.types';

export interface UpdateAssetOptions {
  assetId: string;
  updates: Partial<Database['public']['Tables']['assets']['Update']>;
  verifyUpdate?: boolean;
  replicationDelay?: number;
  maxRetries?: number;
}

export interface BulkUpdateAssetOptions {
  assetIds: string[];
  updates: Partial<Database['public']['Tables']['assets']['Update']>;
  verifyUpdate?: boolean;
  replicationDelay?: number;
  maxRetries?: number;
}

/**
 * Update a single asset with verification and replication lag handling
 */
export async function updateAssetWithVerification(
  supabase: SupabaseClient<Database>,
  options: UpdateAssetOptions
): Promise<{ success: boolean; error?: string; data?: any }> {
  const {
    assetId,
    updates,
    verifyUpdate = true,
    replicationDelay = 1500,
    maxRetries = 3
  } = options;

  try {
    // Perform the update with .select() to get immediate feedback
    const { data: updateData, error: updateError } = await supabase
      .from('assets')
      .update(updates)
      .eq('id', assetId)
      .select()
      .single();

    if (updateError) {
      console.error('[updateAsset] Update error:', updateError);
      return { success: false, error: updateError.message };
    }

    // If no verification needed, return success
    if (!verifyUpdate) {
      return { success: true, data: updateData };
    }

    // Wait for replication
    await new Promise(resolve => setTimeout(resolve, replicationDelay));

    // Verify the update with retries
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      const { data: verifyData, error: verifyError } = await supabase
        .from('assets')
        .select('*')
        .eq('id', assetId)
        .single();

      if (verifyError) {
        console.error(`[updateAsset] Verification error (attempt ${attempt}):`, verifyError);
        continue;
      }

      // Check if all updates were applied
      const allUpdatesApplied = Object.entries(updates).every(
        ([key, value]) => verifyData[key as keyof typeof verifyData] === value
      );

      if (allUpdatesApplied) {
        console.log('[updateAsset] Update verified successfully');
        return { success: true, data: verifyData };
      }

      // Wait before retry
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    return { 
      success: false, 
      error: 'Update verification failed - changes may not have been applied' 
    };
  } catch (error) {
    console.error('[updateAsset] Unexpected error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'An unexpected error occurred' 
    };
  }
}

/**
 * Update multiple assets with verification and replication lag handling
 */
export async function bulkUpdateAssetsWithVerification(
  supabase: SupabaseClient<Database>,
  options: BulkUpdateAssetOptions
): Promise<{ success: boolean; error?: string; failedIds?: string[] }> {
  const {
    assetIds,
    updates,
    verifyUpdate = true,
    replicationDelay = 1500,
    maxRetries = 3
  } = options;

  if (assetIds.length === 0) {
    return { success: false, error: 'No assets to update' };
  }

  try {
    console.log('[bulkUpdateAssets] Performing update:', {
      assetIds,
      updates,
      assetCount: assetIds.length
    });

    // Perform the bulk update with .select() to get immediate feedback
    const { data: updateData, error: updateError } = await supabase
      .from('assets')
      .update(updates)
      .in('id', assetIds)
      .select('id, workflow_stage, updated_at');

    if (updateError) {
      console.error('[bulkUpdateAssets] Update error:', updateError);
      return { success: false, error: updateError.message };
    }

    console.log('[bulkUpdateAssets] Update response:', {
      updateData: updateData?.map(a => ({ 
        id: a.id, 
        workflow_stage: a.workflow_stage,
        updated_at: a.updated_at 
      })),
      expectedUpdates: updates,
      returnedCount: updateData?.length || 0
    });

    // Check if the update actually returned any data
    if (!updateData || updateData.length === 0) {
      console.error('[bulkUpdateAssets] Update returned no data - possible RLS issue');
      return { 
        success: false, 
        error: 'Update completed but returned no data - check permissions' 
      };
    }

    // Check if the returned data already shows the update wasn't applied
    const immediateFailures = updateData.filter(asset => {
      return Object.entries(updates).some(([key, value]) => {
        if (key in asset && asset[key as keyof typeof asset] !== value) {
          console.log(`[bulkUpdateAssets] Immediate failure detected for ${asset.id}:`, {
            field: key,
            expected: value,
            actual: asset[key as keyof typeof asset]
          });
          return true;
        }
        return false;
      });
    });

    if (immediateFailures.length > 0) {
      console.error('[bulkUpdateAssets] Update failed immediately - data not changed');
      return {
        success: false,
        error: 'Updates were not applied by the database',
        failedIds: immediateFailures.map(a => a.id)
      };
    }

    // If no verification needed, return success
    if (!verifyUpdate) {
      return { success: true };
    }

    // Wait for replication
    await new Promise(resolve => setTimeout(resolve, replicationDelay));

    // Verify updates with retries
    const failedIds: string[] = [];
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      // Check a sample of assets (up to 5) for verification
      const sampleIds = assetIds.slice(0, Math.min(5, assetIds.length));
      
      const { data: verifyData, error: verifyError } = await supabase
        .from('assets')
        .select('id, workflow_stage, updated_at')
        .in('id', sampleIds);

      if (verifyError) {
        console.error(`[bulkUpdateAssets] Verification error (attempt ${attempt}):`, verifyError);
        continue;
      }

      console.log(`[bulkUpdateAssets] Verification attempt ${attempt}:`, {
        sampleIds,
        verifyData: verifyData?.map(a => ({ 
          id: a.id, 
          workflow_stage: a.workflow_stage,
          updated_at: a.updated_at 
        })),
        expectedUpdates: updates
      });

      // Check which assets were successfully updated
      failedIds.length = 0;
      for (const sampleId of sampleIds) {
        const asset = verifyData?.find(a => a.id === sampleId);
        if (!asset) {
          console.log(`[bulkUpdateAssets] Asset not found in verification: ${sampleId}`);
          failedIds.push(sampleId);
          continue;
        }

        // Check if updates were applied
        const updatesApplied = Object.entries(updates).every(
          ([key, value]) => {
            if (key in asset) {
              const matches = asset[key as keyof typeof asset] === value;
              if (!matches) {
                console.log(`[bulkUpdateAssets] Update not applied for ${sampleId}:`, {
                  field: key,
                  expected: value,
                  actual: asset[key as keyof typeof asset]
                });
              }
              return matches;
            }
            return true;
          }
        );

        if (!updatesApplied) {
          failedIds.push(sampleId);
        }
      }

      if (failedIds.length === 0) {
        console.log('[bulkUpdateAssets] All sampled updates verified successfully');
        return { success: true };
      }

      // Wait before retry
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    if (failedIds.length > 0) {
      console.error('[bulkUpdateAssets] Verification failed after all retries:', {
        failedIds,
        totalAttempts: maxRetries,
        expectedUpdates: updates
      });
      return { 
        success: false, 
        error: 'Some updates could not be verified',
        failedIds 
      };
    }

    return { success: true };
  } catch (error) {
    console.error('[bulkUpdateAssets] Unexpected error:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'An unexpected error occurred' 
    };
  }
}

/**
 * Handle Supabase errors with specific messages
 */
export function handleSupabaseError(error: any): { type: string; message: string } {
  if (!error) {
    return { type: 'unknown', message: 'An unknown error occurred' };
  }

  // RLS policy violations
  if (error.code === '42501') {
    return { 
      type: 'permission', 
      message: 'You don\'t have permission to perform this action' 
    };
  }

  // Table not found
  if (error.code === '42P01') {
    return { 
      type: 'not_found', 
      message: 'The requested resource was not found' 
    };
  }

  // Unique constraint violation
  if (error.code === '23505') {
    return { 
      type: 'duplicate', 
      message: 'This item already exists' 
    };
  }

  // Foreign key violation
  if (error.code === '23503') {
    return { 
      type: 'reference', 
      message: 'This item is referenced by other data and cannot be modified' 
    };
  }

  // Generic error
  return { 
    type: 'unknown', 
    message: error.message || 'An unexpected error occurred' 
  };
}