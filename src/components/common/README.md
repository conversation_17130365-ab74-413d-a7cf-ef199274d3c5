# Common Components & Utilities

This folder contains shared utilities, hooks, types, and components that are used across the entire application. The structure provides centralized access to commonly needed functionality.

## 📁 Folder Structure

### `hooks/` - Custom React Hooks
Reusable React hooks for common functionality:

- **`useAssets.ts`** - Asset data fetching and management
- **`useCollections.ts`** - Collection data fetching and management  
- **`useEmailChange.ts`** - Email change functionality with security logging
- **`useImageAnnotation.ts`** - Image annotation functionality
- **`useMediaQuery.ts`** - Responsive design utility hook
- **`use-mobile.tsx`** - Mobile device detection
- **`useOrganizationRole.ts`** - Organization role management and permissions
- **`useOrganizations.ts`** - Organization data fetching and management
- **`useProducts.ts`** - Product data fetching and management
- **`useSecurityLogging.ts`** - Security event logging functionality
- **`useStorageUsage.ts`** - Storage usage tracking and calculations
- **`useTags.ts`** - Asset tagging functionality
- **`use-toast.ts`** - Toast notification management

### `utils/` - Utility Functions
Shared utility functions and services:

- **`emailService.ts`** - Email service integration and templates
- **`emailUtils.ts`** - Email formatting and validation utilities
- **`imageProcessor.ts`** - Image processing and optimization utilities
- **`navigation.ts`** - Navigation helpers and route management
- **`resend.ts`** - Resend email service configuration
- **`supabase.ts`** - Supabase client configuration and helpers
- **`utils.ts`** - General utility functions (cn, getAssetUrl, etc.)

### `types/` - TypeScript Type Definitions
Shared TypeScript types and interfaces:

- **`assetTypes.ts`** - Asset-related type definitions and interfaces
- **`database.types.ts`** - Database schema types (auto-generated from Supabase)
- **`database.types.js`** - JavaScript version of database types

### `forms/` - Reusable Form Components
*(Future expansion area for extracted form components)*

## 🎯 Usage Patterns

### **Importing from Common**

From components within the `components/` folder:
```typescript
// Hooks
import { useAssets } from '../common/hooks/useAssets';
import { useOrganizations } from '../common/hooks/useOrganizations';

// Utils
import { cn } from '../common/utils/utils';
import { supabase } from '../common/utils/supabase';

// Types
import type { Asset } from '../common/types/database.types';
import type { AssetMetadata } from '../common/types/assetTypes';
```

From pages or contexts:
```typescript
// Hooks
import { useAssets } from '../components/common/hooks/useAssets';

// Utils  
import { cn } from '../components/common/utils/utils';

// Types
import type { Asset } from '../components/common/types/database.types';
```

### **Key Utility Functions**

**`cn()` - Class Name Utility**
```typescript
import { cn } from '../common/utils/utils';

// Combine class names with conditional logic
const buttonClass = cn(
  "px-4 py-2 rounded",
  isActive && "bg-blue-500",
  isDisabled && "opacity-50"
);
```

**`getAssetUrl()` - Asset URL Generation**
```typescript
import { getAssetUrl } from '../common/utils/utils';

// Generate proper asset URLs with fallbacks
const imageUrl = getAssetUrl(asset, true); // true for thumbnail
```

**`supabase` - Database Client**
```typescript
import { supabase } from '../common/utils/supabase';

// Standard Supabase operations
const { data, error } = await supabase
  .from('assets')
  .select('*')
  .eq('collection_id', collectionId);
```

## 🔧 Best Practices

### **Adding New Utilities**
1. **Choose the right folder** based on functionality type
2. **Use descriptive names** that indicate purpose and scope
3. **Export properly** for easy importing
4. **Document complex functions** with JSDoc comments
5. **Follow existing patterns** for consistency

### **Hook Guidelines**
- **Single responsibility** - Each hook should have one clear purpose
- **Proper dependencies** - Include all necessary dependencies in effect arrays
- **Error handling** - Always handle potential errors gracefully
- **Type safety** - Use proper TypeScript types for parameters and returns

### **Utility Guidelines**
- **Pure functions** when possible - Avoid side effects
- **Consistent naming** - Use camelCase for functions, PascalCase for classes
- **Input validation** - Validate parameters when necessary
- **Performance** - Consider memoization for expensive operations

### **Type Guidelines**
- **Descriptive names** - Types should clearly indicate their purpose
- **Reusability** - Design types for use across multiple components
- **Documentation** - Add comments for complex type definitions
- **Import organization** - Group related types together

## 📚 Related Documentation

- **Component Architecture**: `/src/components/README.md`
- **Database Schema**: `database.types.ts` (auto-generated)
- **Development Setup**: `/CLAUDE.md`
- **Architecture Overview**: `/docs/architecture/`

## 🚀 Future Enhancements

### **Planned Additions**
1. **Form Components** - Extract reusable form components into `forms/` folder
2. **API Clients** - Centralized API clients for external services
3. **Constants** - Shared application constants and configuration
4. **Validators** - Common validation functions and schemas

### **Potential Optimizations**
1. **Tree Shaking** - Ensure optimal bundle size with proper exports
2. **Code Splitting** - Dynamic imports for large utilities
3. **Performance Monitoring** - Track usage of expensive utilities
4. **Cache Management** - Implement proper caching for data hooks

This structure provides a clean, scalable foundation for shared functionality across the FashionLab application.