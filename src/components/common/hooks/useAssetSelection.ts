import { useState, useCallback, useMemo } from 'react';

/**
 * Custom hook to manage asset selection state
 * Provides selection state and helper functions
 */
export function useAssetSelection(totalAssets?: string[]) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);

  const toggleAsset = useCallback((assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  }, []);

  const selectAll = useCallback(() => {
    if (totalAssets) {
      setSelectedAssets(totalAssets);
    }
  }, [totalAssets]);

  const clearSelection = useCallback(() => {
    setSelectedAssets([]);
  }, []);

  const toggleAll = useCallback(() => {
    if (!totalAssets) return;
    
    if (selectedAssets.length === totalAssets.length) {
      clearSelection();
    } else {
      selectAll();
    }
  }, [totalAssets, selectedAssets.length, clearSelection, selectAll]);

  const isSelected = useCallback((assetId: string) => {
    return selectedAssets.includes(assetId);
  }, [selectedAssets]);

  const selectionState = useMemo(() => ({
    hasSelection: selectedAssets.length > 0,
    selectionCount: selectedAssets.length,
    isAllSelected: totalAssets ? selectedAssets.length === totalAssets.length : false,
    isPartialSelection: totalAssets ? 
      selectedAssets.length > 0 && selectedAssets.length < totalAssets.length : false,
  }), [selectedAssets.length, totalAssets]);

  return {
    selectedAssets,
    setSelectedAssets,
    toggleAsset,
    selectAll,
    clearSelection,
    toggleAll,
    isSelected,
    ...selectionState,
  };
}