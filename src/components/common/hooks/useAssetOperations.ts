import { useState, useCallback } from 'react';
import { useSupabase } from '../../../contexts/SupabaseContext';
import { useToast } from '../../ui/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { STORAGE_BUCKETS } from '../utils/supabase';
import { getAssetUrl } from '../utils/utils';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

interface UseAssetOperationsProps {
  collectionId?: string;
  onAssetsDeleted?: (assetIds: string[]) => void;
}

/**
 * Custom hook to handle asset operations (delete, download, etc.)
 * Provides operation handlers with proper error handling and state management
 */
export function useAssetOperations({ collectionId, onAssetsDeleted }: UseAssetOperationsProps = {}) {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const deleteAssets = useCallback(async (assetIds: string[]) => {
    if (assetIds.length === 0) {
      toast({
        title: 'No assets selected',
        description: 'Please select assets to delete',
        variant: 'destructive',
      });
      return false;
    }

    setIsDeleting(true);

    try {
      // Delete from database
      const deleteQuery = assetIds.length === 1
        ? supabase.from('assets').delete().eq('id', assetIds[0]).select()
        : supabase.from('assets').delete().in('id', assetIds).select();

      const { data: deletedAssets, error: deleteError } = await deleteQuery;

      if (deleteError) {
        throw new Error(`Database error: ${deleteError.message}`);
      }

      // Clean up storage files (best effort, don't fail if storage deletion fails)
      if (deletedAssets && deletedAssets.length > 0) {
        const storageCleanupPromises = deletedAssets.map(async (asset: any) => {
          const filePaths = [
            asset.file_path,
            asset.compressed_path,
            asset.original_path,
            asset.thumbnail_path
          ].filter(Boolean);

          const buckets = [
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            STORAGE_BUCKETS.MEDIA_COMPRESSED.name,
            STORAGE_BUCKETS.MEDIA_THUMBNAILS.name
          ];

          // Try to delete from each bucket, but don't fail the operation
          for (const path of filePaths) {
            if (path) {
              for (const bucket of buckets) {
                try {
                  await supabase.storage.from(bucket).remove([path]);
                  break; // If successful, don't try other buckets
                } catch {
                  // Ignore storage errors
                }
              }
            }
          }
        });

        // Wait for all storage operations but don't fail if they error
        await Promise.allSettled(storageCleanupPromises);
      }

      toast({
        title: 'Assets Deleted',
        description: `Successfully deleted ${assetIds.length} ${assetIds.length === 1 ? 'asset' : 'assets'}`,
      });

      // Debug logging - commented out for production
      // if (import.meta.env.PROD) {
      //   console.log('[useAssetOperations] Starting cache invalidation after bulk delete...');
      // }
      
      // Invalidate ALL asset queries regardless of parameters
      // The useAssets hook has complex query keys, so we use a predicate
      await queryClient.invalidateQueries({ 
        predicate: (query) => query.queryKey[0] === 'assets'
      });
      if (collectionId) {
        await queryClient.invalidateQueries({ queryKey: ['assetTags', collectionId] });
        await queryClient.invalidateQueries({ queryKey: ['assetCounts', collectionId] });
      }
      
      // Force refetch all invalidated queries
      await queryClient.refetchQueries({ 
        predicate: (query) => query.queryKey[0] === 'assets'
      });
      if (collectionId) {
        await queryClient.refetchQueries({ queryKey: ['assetTags', collectionId] });
        await queryClient.refetchQueries({ queryKey: ['assetCounts', collectionId] });
      }
      
      // if (import.meta.env.PROD) {
      //   console.log('[useAssetOperations] Cache invalidation completed');
      // }

      // Notify parent if callback provided
      onAssetsDeleted?.(assetIds);

      return true;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to delete assets';
      toast({
        title: 'Deletion Failed',
        description: message,
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [supabase, toast, queryClient, collectionId, onAssetsDeleted]);

  const downloadAssets = useCallback(async (assetIds: string[]) => {
    if (assetIds.length === 0) {
      toast({
        title: 'No assets selected',
        description: 'Please select assets to download',
        variant: 'destructive',
      });
      return;
    }

    setIsDownloading(true);

    try {
      // Get asset details
      const { data: assets, error } = await supabase
        .from('assets')
        .select('*')
        .in('id', assetIds);

      if (error) throw error;
      if (!assets || assets.length === 0) {
        throw new Error('No assets found');
      }

      if (assets.length === 1) {
        // Single file download - prioritize original files (FAS-73)
        const asset = assets[0];

        // Use the optimized asset URL function to get original version
        const downloadUrl = getAssetUrl(asset, false, true); // true for download (original)

        if (!downloadUrl) {
          throw new Error('Could not generate download URL');
        }

        // Trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = asset.file_name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // Multiple files - create zip with original files prioritized (FAS-73)
        const zip = new JSZip();

        for (const asset of assets) {
          // Prioritize original files: original_path -> compressed_path -> file_path
          const filePath = asset.original_path || asset.compressed_path || asset.file_path;
          if (!filePath) continue;

          // Try to get the file - prioritize original bucket first
          const buckets = [
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            STORAGE_BUCKETS.MEDIA_COMPRESSED.name
          ];

          for (const bucket of buckets) {
            try {
              const { data, error } = await supabase.storage
                .from(bucket)
                .download(filePath);

              if (!error && data) {
                zip.file(asset.file_name, data);
                break;
              }
            } catch {
              // Try next bucket
            }
          }
        }

        // Generate and download zip
        const content = await zip.generateAsync({ type: 'blob' });
        saveAs(content, `assets-${new Date().toISOString().split('T')[0]}.zip`);
      }

      toast({
        title: 'Download Complete',
        description: `Downloaded ${assets.length} ${assets.length === 1 ? 'asset' : 'assets'}`,
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to download assets';
      toast({
        title: 'Download Failed',
        description: message,
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(false);
    }
  }, [supabase, toast]);

  return {
    deleteAssets,
    downloadAssets,
    isDeleting,
    isDownloading,
  };
}