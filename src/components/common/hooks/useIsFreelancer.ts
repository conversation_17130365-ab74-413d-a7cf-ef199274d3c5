import { useProfile } from '@/contexts/ProfileContext';
import { useUserRole } from '@/contexts/UserRoleContext';

/**
 * Hook to check if the current user is a freelancer
 * Freelancers are brand_admin users with the is_freelancer flag set to true
 */
export function useIsFreelancer(): boolean {
  const { profile } = useProfile();
  const { isBrandAdmin } = useUserRole();
  
  // Add debug logging
  console.log('useIsFreelancer debug:', {
    isBrandAdmin,
    profile: profile,
    is_freelancer: profile?.is_freelancer,
    result: isBrandAdmin && profile?.is_freelancer === true
  });
  
  // A freelancer must be a brand_admin with the is_freelancer flag
  return isBrandAdmin && profile?.is_freelancer === true;
}

/**
 * Hook to get freelancer status and permissions
 */
export function useFreelancerStatus() {
  const isFreelancer = useIsFreelancer();
  const { isBrandAdmin, isPlatformUser } = useUserRole();
  
  return {
    isFreelancer,
    // Platform users can always create collections, brand admins can unless they're freelancers
    canCreateCollections: isPlatformUser || (isBrandAdmin && !isFreelancer),
    // Only platform users can delete organizations
    canDeleteOrganizations: isPlatformUser,
    // Platform users can always manage settings, brand admins can unless they're freelancers
    canManageOrgSettings: isPlatformUser || (isBrandAdmin && !isFreelancer),
    // Platform users can always invite users, brand admins can unless they're freelancers
    canInviteUsers: isPlatformUser || (isBrandAdmin && !isFreelancer),
    // Everyone can work with assets
    canManageAssets: true,
    // Everyone can view collections
    canViewCollections: true,
  };
}