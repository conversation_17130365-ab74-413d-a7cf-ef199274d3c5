import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import type { Tables } from '../types/database.types';

type Organization = Tables<'organizations'>;

export function useOrganizations(options?: { 
  enabled?: boolean;
  searchTerm?: string;
  sortBy?: 'name' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}) {
  const { supabase } = useSupabase();
  const { 
    enabled = true, 
    searchTerm = '', 
    sortBy = 'name', 
    sortOrder = 'asc' 
  } = options || {};

  return useQuery<Organization[], Error>({
    queryKey: ['organizations', searchTerm, sortBy, sortOrder],
    enabled,
    queryFn: async () => {
      let query = supabase
        .from('organizations')
        .select('*');
      
      // Apply search filter if provided
      if (searchTerm) {
        query = query.ilike('name', `%${searchTerm}%`);
      }
      
      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      const { data, error } = await query;
      
      if (error) {
        throw new Error(`Error fetching organizations: ${error.message}`);
      }
      
      return data || [];
    }
  });
}

// Hook to fetch a single organization by ID
export function useOrganization(id: string | undefined) {
  const { supabase } = useSupabase();
  
  return useQuery<Organization, Error>({
    queryKey: ['organization', id],
    enabled: !!id,
    queryFn: async () => {
      if (!id) throw new Error('Organization ID is required');
      
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        throw new Error(`Error fetching organization: ${error.message}`);
      }
      
      if (!data) {
        throw new Error(`Organization with ID ${id} not found`);
      }
      
      return data;
    }
  });
}

// For compatibility during transition, export the same functions with the old names
export const useClients = useOrganizations;
export const useClient = useOrganization;