import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import type { Tables } from '../components/common/types/database.types';

type Collection = Tables<'collections'>;

export function useCollections(options?: { 
  enabled?: boolean;
  organizationId?: string;
  searchTerm?: string;
  sortBy?: 'name' | 'created_at';
  sortOrder?: 'asc' | 'desc';
}) {
  const { supabase, user } = useSupabase();
  const { 
    enabled = true, 
    organizationId,
    searchTerm = '', 
    sortBy = 'name', 
    sortOrder = 'asc' 
  } = options || {};

  return useQuery<Collection[], Error>({
    queryKey: ['collections', organizationId, searchTerm, sortBy, sortOrder],
    enabled,
    retry: 1, // Only retry once to avoid infinite retries on permission issues
    queryFn: async () => {
      try {
        // First check if the user is authenticated
        if (!user) {
          throw new Error('User is not authenticated');
        }

        let query = supabase
          .from('collections')
          .select('*');
        
        // Filter by organization_id
        if (organizationId) {
          query = query.eq('organization_id', organizationId);
        }
        
        // Apply search filter if provided
        if (searchTerm) {
          query = query.ilike('name', `%${searchTerm}%`);
        }
        
        // Apply sorting
        query = query.order(sortBy, { ascending: sortOrder === 'asc' });
        
        const { data, error, status } = await query;
        
        // Handle specific error cases
        if (error) {
          if (error.code === '42501') { // Permission denied
            console.error('Permission denied when fetching collections:', error);
            return []; // Return empty array for permission issues
          }
          if (error.code === '42P01') { // Table doesn't exist
            console.error('Collections table not found:', error);
            return []; // Return empty array if table doesn't exist
          }
          throw error;
        }

        // Handle case where data is null but no error (valid empty result)
        if (!data) {
          return [];
        }
        
        return data;
      } catch (error) {
        console.error('Error in useCollections:', error);
        // For any other errors, return empty array instead of throwing
        return [];
      }
    }
  });
}

// Hook to fetch a single collection by ID
export function useCollection(id: string | undefined) {
  const { supabase, user } = useSupabase();
  
  return useQuery<Collection | null, Error>({
    queryKey: ['collection', id],
    enabled: !!id,
    retry: 1, // Only retry once
    queryFn: async () => {
      try {
        if (!id) return null;
        if (!user) throw new Error('User is not authenticated');
        
        const { data, error } = await supabase
          .from('collections')
          .select('*')
          .eq('id', id)
          .single();
        
        if (error) {
          if (error.code === '42501') { // Permission denied
            console.error('Permission denied when fetching collection:', error);
            return null;
          }
          if (error.code === '42P01') { // Table doesn't exist
            console.error('Collections table not found:', error);
            return null;
          }
          throw error;
        }
        
        return data;
      } catch (error) {
        console.error('Error in useCollection:', error);
        return null;
      }
    }
  });
} 