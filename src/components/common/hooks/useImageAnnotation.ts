import { useState, useCallback, useRef } from 'react';

interface Coordinates {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

interface UseImageAnnotationProps {
  onAnnotate?: (coordinates: Coordinates) => void;
}

export function useImageAnnotation({ onAnnotate }: UseImageAnnotationProps = {}) {
  const [isAnnotating, setIsAnnotating] = useState(false);
  const [startPoint, setStartPoint] = useState<Coordinates | null>(null);
  const [currentPoint, setCurrentPoint] = useState<Coordinates | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const startAnnotation = useCallback((event: React.MouseEvent) => {
    if (!isAnnotating || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    setStartPoint({ x, y });
    setCurrentPoint({ x, y });
  }, [isAnnotating]);

  const updateAnnotation = useCallback((event: React.MouseEvent) => {
    if (!isAnnotating || !startPoint || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    setCurrentPoint({ x, y });
  }, [isAnnotating, startPoint]);

  const finishAnnotation = useCallback(() => {
    if (!startPoint || !currentPoint) return;

    const coordinates = {
      x: Math.min(startPoint.x, currentPoint.x),
      y: Math.min(startPoint.y, currentPoint.y),
      width: Math.abs(currentPoint.x - startPoint.x),
      height: Math.abs(currentPoint.y - startPoint.y),
    };

    onAnnotate?.(coordinates);
    setStartPoint(null);
    setCurrentPoint(null);
    setIsAnnotating(false);
  }, [startPoint, currentPoint, onAnnotate]);

  const cancelAnnotation = useCallback(() => {
    setStartPoint(null);
    setCurrentPoint(null);
    setIsAnnotating(false);
  }, []);

  const toggleAnnotationMode = useCallback(() => {
    setIsAnnotating(prev => !prev);
    setStartPoint(null);
    setCurrentPoint(null);
  }, []);

  const annotationBox = startPoint && currentPoint ? {
    left: `${Math.min(startPoint.x, currentPoint.x)}%`,
    top: `${Math.min(startPoint.y, currentPoint.y)}%`,
    width: `${Math.abs(currentPoint.x - startPoint.x)}%`,
    height: `${Math.abs(currentPoint.y - startPoint.y)}%`,
  } : null;

  return {
    isAnnotating,
    containerRef,
    annotationBox,
    toggleAnnotationMode,
    startAnnotation,
    updateAnnotation,
    finishAnnotation,
    cancelAnnotation,
  };
} 