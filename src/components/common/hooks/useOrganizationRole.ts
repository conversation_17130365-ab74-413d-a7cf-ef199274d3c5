import { useState, useEffect } from 'react';
import { useSupabase } from '../../../contexts/SupabaseContext';
import { useUserRole } from '../../../contexts/UserRoleContext';

export function useOrganizationRole(organizationId: string | undefined) {
  const { supabase } = useSupabase();
  const { user, isLoadingUser } = useSupabase();
  const { userRole, isPlatformUser, isBrandAdmin, isLoadingRole } = useUserRole();
  const [isOrgMember, setIsOrgMember] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastCheckedOrgId, setLastCheckedOrgId] = useState<string | undefined>(undefined);

  useEffect(() => {
    const fetchOrgRole = async () => {
      // Add debugging information
      console.log('Organization role check:', {
        organizationId,
        userId: user?.id,
        userRole,
        isPlatformUser,
        isBrandAdmin,
        isLoadingUser,
        isLoadingRole
      });
      
      // Reset states when organization changes
      if (lastCheckedOrgId !== organizationId) {
        setIsOrgMember(false);
        setError(null);
        setLastCheckedOrgId(organizationId);
      }

      // Wait for user data and role data to be loaded
      if (isLoadingUser || isLoadingRole) {
        console.log('Still loading user or role data, waiting...');
        return;
      }

      // Platform users get immediate access to all organizations
      if (isPlatformUser) {
        console.log('User has platform access, granting full organization access');
        setIsOrgMember(true);
        setIsLoading(false);
        return;
      }

      // If no organization ID or no user, set not loading and return
      if (!organizationId || !user) {
        console.log('No organization ID or user, cannot determine role');
        setIsLoading(false);
        return;
      }

      try {
        console.log('Checking organization membership for', user.id, 'in organization', organizationId);
        
        // Check if user is a member of this organization (no role needed since role is in users table)
        const { data, error } = await supabase
          .from('organization_memberships')
          .select('id')
          .eq('user_id', user.id)
          .eq('organization_id', organizationId)
          .single();

        if (error) {
          if (error.code === 'PGRST116') {
            // No membership found - user is not a member
            console.log('No membership found for this user in this organization');
            setIsLoading(false);
            return;
          }
          throw error;
        }

        // If we got data, user is a member
        if (data) {
          console.log('Found membership for user in organization');
          setIsOrgMember(true);
        }
      } catch (err: any) {
        console.error('Error fetching organization role:', err);
        setError(err.message || 'Failed to fetch organization role');
      } finally {
        setIsLoading(false);
      }
    };

    // Set loading state when dependencies change
    setIsLoading(true);
    fetchOrgRole();
  }, [organizationId, user, userRole, isPlatformUser, isBrandAdmin, supabase, isLoadingUser, isLoadingRole, lastCheckedOrgId]);

  // Add a custom method to force refresh the membership check
  const refreshRole = () => {
    setLastCheckedOrgId(undefined); // This will trigger a re-fetch
  };

  // Derived admin state based on user role and membership
  const isOrgAdmin = isPlatformUser || (isBrandAdmin && isOrgMember);

  return {
    userRole, // The actual role from users table
    isOrgAdmin, // Whether user has admin privileges in this org
    isOrgMember, // Whether user is a member of this org
    isLoading,
    error,
    refreshRole
  };
}