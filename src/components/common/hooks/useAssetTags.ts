import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';

/**
 * Custom hook to fetch and manage asset tags for a collection
 * Returns a map of asset IDs to their associated tag IDs
 */
export function useAssetTags(collectionId: string | undefined) {
  const { supabase } = useSupabase();

  return useQuery({
    queryKey: ['assetTags', collectionId],
    enabled: !!collectionId,
    queryFn: async () => {
      // Get all assets in this collection
      const { data: collectionAssets, error: assetsError } = await supabase
        .from('assets')
        .select('id')
        .eq('collection_id', collectionId);

      if (assetsError) throw assetsError;
      if (!collectionAssets || collectionAssets.length === 0) {
        return {};
      }

      const assetIds = collectionAssets.map(asset => asset.id);

      // Get all asset_tags for these assets
      const { data: assetTags, error: tagsError } = await supabase
        .from('asset_tags')
        .select('asset_id, tag_id')
        .in('asset_id', assetIds);

      if (tagsError) throw tagsError;
      if (!assetTags || assetTags.length === 0) {
        return {};
      }

      // Create a map of asset_id to tag_ids
      const tagMap: Record<string, string[]> = {};
      assetTags.forEach(item => {
        if (!tagMap[item.asset_id]) {
          tagMap[item.asset_id] = [];
        }
        tagMap[item.asset_id].push(item.tag_id);
      });

      return tagMap;
    },
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
  });
}