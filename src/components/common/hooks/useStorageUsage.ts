import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';

export interface BucketSize {
  bucket_id: string;
  bucket_name: string;
  total_size_bytes: number;
  total_files: number;
}

export interface StorageUsage {
  totalSizeBytes: number;
  totalFiles: number;
  buckets: BucketSize[];
}

interface FileObject {
  id: string;
  name: string;
  metadata: {
    size?: number;
    mimetype?: string;
    cacheControl?: string;
    lastModified?: string;
  };
}

export function useStorageUsage() {
  const { supabase } = useSupabase();

  return useQuery<StorageUsage, Error>({
    queryKey: ['storage-usage'],
    queryFn: async () => {
      try {
        console.log('Starting storage usage calculation...');
        
        // 1. Get list of buckets
        console.log('Fetching buckets...');
        const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
        
        if (bucketsError) {
          console.error('Error fetching buckets:', bucketsError);
          throw new Error(`Error fetching buckets: ${bucketsError.message}`);
        }
        
        console.log('Buckets fetched:', buckets);
        
        // Initialize storage usage data
        const storageUsage: StorageUsage = {
          totalSizeBytes: 0,
          totalFiles: 0,
          buckets: []
        };

        // 2. For each bucket, get files and calculate size
        for (const bucket of buckets) {
          console.log(`Processing bucket: ${bucket.name} (ID: ${bucket.id})`);
          let bucketSizeBytes = 0;
          let totalFiles = 0;
          
          // Process files in the root of the bucket
          await processFolder(bucket.id, '');
          
          console.log(`Bucket ${bucket.name} stats:`, {
            size: formatBytes(bucketSizeBytes),
            files: totalFiles
          });
          
          // Add bucket data to the result
          storageUsage.buckets.push({
            bucket_id: bucket.id,
            bucket_name: bucket.name,
            total_size_bytes: bucketSizeBytes,
            total_files: totalFiles
          });

          // Update totals
          storageUsage.totalSizeBytes += bucketSizeBytes;
          storageUsage.totalFiles += totalFiles;
          
          // Recursive function to process folders and files
          async function processFolder(bucketId: string, path: string) {
            console.log(`Listing files in ${bucketId}/${path || 'root'}`);
            
            // List files in the current folder
            const { data: files, error: filesError } = await supabase.storage
              .from(bucketId)
              .list(path, {
                sortBy: { column: 'name', order: 'asc' }
              });
            
            if (filesError) {
              console.error(`Error fetching files for bucket ${bucketId} at path ${path}:`, filesError);
              return;
            }
            
            if (!files || files.length === 0) {
              console.log(`No files found in ${bucketId}/${path || 'root'}`);
              return;
            }
            
            console.log(`Found ${files.length} items in ${bucketId}/${path || 'root'}:`, files);
            
            // Process each item (file or folder)
            for (const item of files) {
              // Log the item for debugging
              console.log(`Processing item:`, item);
              
              // Safely check if it's a folder
              const isFolder = item && 
                              (item.id ? item.id.endsWith && item.id.endsWith('/') : false) || 
                              !item.metadata;
              
              console.log(`Item ${item.name} is ${isFolder ? 'a folder' : 'a file'}`);
              
              // Check if it's a folder
              if (isFolder) {
                // It's a folder, process it recursively
                const folderPath = path ? `${path}/${item.name}` : item.name;
                console.log(`Found folder: ${folderPath}, processing recursively...`);
                await processFolder(bucketId, folderPath);
              } else {
                // It's a file, add its size
                const fileSize = item.metadata?.size || 0;
                console.log(`Found file: ${item.name}, size: ${formatBytes(fileSize)}`);
                bucketSizeBytes += fileSize;
                totalFiles++;
              }
            }
          }
        }

        console.log('Final storage usage calculation:', {
          totalSize: formatBytes(storageUsage.totalSizeBytes),
          totalFiles: storageUsage.totalFiles,
          buckets: storageUsage.buckets.map(b => ({
            id: b.bucket_id,
            name: b.bucket_name,
            size: formatBytes(b.total_size_bytes),
            files: b.total_files
          }))
        });

        return storageUsage;
      } catch (error) {
        console.error('Error in useStorageUsage:', error);
        throw error;
      }
    },
    // Cache for 5 minutes since storage usage doesn't change that frequently
    staleTime: 5 * 60 * 1000
  });
}

// Helper function to format bytes into human-readable format
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
} 