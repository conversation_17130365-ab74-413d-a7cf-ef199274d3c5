import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import { Database } from '../types/database.types';

// Define Tag type based on database schema
export interface Tag {
  id: string;
  name: string;
  category: Database['public']['Enums']['tag_category'];
  color: string;
  collection_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

// Extended Tag type with count
export interface TagWithCount extends Tag {
  count: number;
}

// Hook to fetch tags for a specific collection (including global tags)
export function useTags(collectionId?: string) {
  const { supabase } = useSupabase();
  
  return useQuery<Tag[], Error>({
    queryKey: ['tags', collectionId],
    queryFn: async () => {
      let query = supabase
        .from('tags')
        .select('*')
        .order('category')
        .order('name');

      // If in a collection context, get global tags + collection-specific tags
      if (collectionId) {
        query = query.or(`category.in.(global,angles,styling),collection_id.eq.${collectionId}`);
      } else {
        // Outside collection context, only show global tags
        query = query.in('category', ['global', 'angles', 'styling'])
          .is('collection_id', null);
      }
      
      const { data, error } = await query;
      
      if (error) {
        throw new Error(`Error fetching tags: ${error.message}`);
      }
      
      return data || [];
    }
  });
}

// Hook to fetch tags for a specific asset
export function useAssetTags(assetId: string | undefined) {
  const { supabase } = useSupabase();
  
  return useQuery<Tag[], Error>({
    queryKey: ['asset-tags', assetId],
    enabled: !!assetId,
    queryFn: async () => {
      if (!assetId) return [];
      
      const { data, error } = await supabase
        .from('asset_tags')
        .select('tag_id')
        .eq('asset_id', assetId);
      
      if (error) {
        throw new Error(`Error fetching asset tags: ${error.message}`);
      }
      
      if (!data || data.length === 0) return [];
      
      const tagIds = data.map(item => item.tag_id);
      
      const { data: tags, error: tagsError } = await supabase
        .from('tags')
        .select('*')
        .in('id', tagIds)
        .order('name');
      
      if (tagsError) {
        throw new Error(`Error fetching tags: ${tagsError.message}`);
      }
      
      return tags || [];
    }
  });
}

// Hook to fetch tags for a specific collection with counts
export function useCollectionTags(collectionId: string | undefined) {
  const { supabase } = useSupabase();
  
  return useQuery<TagWithCount[], Error>({
    queryKey: ['collection-tags', collectionId],
    enabled: !!collectionId,
    queryFn: async () => {
      if (!collectionId) return [];
      
      // First, get all tags available for this collection (global + collection-specific)
      const { data: availableTags, error: tagsError } = await supabase
        .from('tags')
        .select('*')
        .or(`category.in.(global,angles,styling),collection_id.eq.${collectionId}`)
        .order('category')
        .order('name');

      if (tagsError) {
        throw new Error(`Error fetching tags: ${tagsError.message}`);
      }

      if (!availableTags || availableTags.length === 0) return [];
      
      // Then get all assets in the collection
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('id')
        .eq('collection_id', collectionId);
      
      if (assetsError) {
        throw new Error(`Error fetching collection assets: ${assetsError.message}`);
      }
      
      if (!assets || assets.length === 0) {
        // Return all available tags with count 0
        return availableTags.map(tag => ({
          ...tag,
          count: 0
        }));
      }
      
      const assetIds = assets.map(asset => asset.id);
      
      // Get all tag_ids for these assets
      const { data: assetTags, error: assetTagsError } = await supabase
        .from('asset_tags')
        .select('tag_id')
        .in('asset_id', assetIds);
      
      if (assetTagsError) {
        throw new Error(`Error fetching asset tags: ${assetTagsError.message}`);
      }
      
      // Count occurrences of each tag
      const tagCounts: Record<string, number> = {};
      if (assetTags) {
        assetTags.forEach(item => {
          tagCounts[item.tag_id] = (tagCounts[item.tag_id] || 0) + 1;
        });
      }
      
      // Add counts to all available tags
      const tagsWithCounts = availableTags.map(tag => ({
        ...tag,
        count: tagCounts[tag.id] || 0
      }));
      
      return tagsWithCounts;
    }
  });
}

/**
 * Hook to fetch tags for assets belonging to a specific product
 */
export function useProductTags(collectionId: string | undefined, productId: string | undefined) {
  const { supabase } = useSupabase();

  return useQuery<TagWithCount[], Error>({
    queryKey: ['product-tags', collectionId, productId],
    enabled: !!collectionId && !!productId,
    queryFn: async () => {
      if (!collectionId || !productId) return [];

      // First get all assets for this specific product in the collection
      const { data: assets, error: assetsError } = await supabase
        .from('assets')
        .select('id')
        .eq('collection_id', collectionId)
        .eq('product_id', productId);

      if (assetsError) {
        throw new Error(`Error fetching product assets: ${assetsError.message}`);
      }

      if (!assets || assets.length === 0) return [];

      const assetIds = assets.map(asset => asset.id);

      // Then get all tag_ids for these assets
      const { data: assetTags, error: assetTagsError } = await supabase
        .from('asset_tags')
        .select('tag_id')
        .in('asset_id', assetIds);

      if (assetTagsError) {
        throw new Error(`Error fetching asset tags: ${assetTagsError.message}`);
      }

      if (!assetTags || assetTags.length === 0) return [];

      // Count occurrences of each tag
      const tagCounts: Record<string, number> = {};
      assetTags.forEach(item => {
        tagCounts[item.tag_id] = (tagCounts[item.tag_id] || 0) + 1;
      });

      // Get unique tag IDs
      const uniqueTagIds = Object.keys(tagCounts);

      // Finally get the tag details
      const { data: tags, error: tagsError } = await supabase
        .from('tags')
        .select('*')
        .in('id', uniqueTagIds)
        .order('name');

      if (tagsError) {
        throw new Error(`Error fetching tags: ${tagsError.message}`);
      }

      // Add counts to tags
      const tagsWithCounts = (tags || []).map(tag => ({
        ...tag,
        count: tagCounts[tag.id] || 0
      }));

      return tagsWithCounts;
    }
  });
}