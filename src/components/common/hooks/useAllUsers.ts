import { useState, useEffect } from 'react';
import { supabase } from '../utils/supabase';
import type { Database } from '../types/database.types';

type User = Database['public']['Tables']['users']['Row'];
type Organization = Database['public']['Tables']['organizations']['Row'];

export interface UserWithOrganizations extends User {
  organizations: {
    id: string;
    name: string;
    joined_at: string;
  }[];
}

export function useAllUsers() {
  const [users, setUsers] = useState<UserWithOrganizations[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAllUsers();
  }, []);

  const fetchAllUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First, fetch all users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (usersError) throw usersError;

      if (!usersData || usersData.length === 0) {
        setUsers([]);
        return;
      }

      // Then fetch organization memberships for all users
      const { data: membershipsData, error: membershipsError } = await supabase
        .from('organization_memberships')
        .select(`
          user_id,
          created_at,
          organization:organizations!inner(
            id,
            name
          )
        `);

      if (membershipsError) throw membershipsError;

      // Combine the data
      const usersWithOrgs: UserWithOrganizations[] = usersData.map(user => {
        const userMemberships = membershipsData?.filter(m => m.user_id === user.id) || [];
        
        return {
          ...user,
          organizations: userMemberships.map(m => ({
            id: m.organization.id,
            name: m.organization.name,
            joined_at: m.created_at
          }))
        };
      });

      setUsers(usersWithOrgs);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    } finally {
      setIsLoading(false);
    }
  };

  return { users, isLoading, error, refetch: fetchAllUsers };
}

// Hook to get user statistics
export function useUserStatistics() {
  const [stats, setStats] = useState({
    total: 0,
    byRole: {} as Record<string, number>,
    freelancers: 0,
    recentlyAdded: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      // Get total users
      const { count: totalCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true });

      // Get users by role
      const { data: roleData } = await supabase
        .from('users')
        .select('role');

      const byRole = roleData?.reduce((acc, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      // Get freelancer count
      const { count: freelancerCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .eq('is_freelancer', true);

      // Get recently added (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      
      const { count: recentCount } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', sevenDaysAgo.toISOString());

      setStats({
        total: totalCount || 0,
        byRole,
        freelancers: freelancerCount || 0,
        recentlyAdded: recentCount || 0
      });
    } catch (error) {
      console.error('Error fetching user statistics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return { stats, isLoading };
}