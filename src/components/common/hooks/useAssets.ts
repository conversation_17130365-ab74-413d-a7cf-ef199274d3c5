import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import type { Asset } from '../types/database.types';

export function useAssets(options?: {
  enabled?: boolean;
  productId?: string;
  collectionId?: string;
  workflowStage?: 'upload' | 'raw_ai_images' | 'upscale' | 'retouch' | 'final';
  searchTerm?: string;
  sortBy?: 'file_name' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  forceRefresh?: number;
}) {
  const { supabase } = useSupabase();
  const { 
    enabled = true, 
    productId,
    collectionId,
    workflowStage,
    searchTerm = '', 
    sortBy = 'created_at', 
    sortOrder = 'desc',
    forceRefresh
  } = options || {};

  return useQuery<Asset[], Error>({
    queryKey: ['assets', productId, collectionId, workflowStage, searchTerm, sortBy, sortOrder, forceRefresh],
    enabled,
    queryFn: async () => {
      let query = supabase
        .from('assets')
        .select('*');
      
      // Apply filters if provided
      if (productId) {
        query = query.eq('product_id', productId);
      }
      
      if (collectionId) {
        query = query.eq('collection_id', collectionId);
      }
      
      if (workflowStage) {
        query = query.eq('workflow_stage', workflowStage);
      }
      
      // Apply search filter if provided
      if (searchTerm) {
        query = query.ilike('file_name', `%${searchTerm}%`);
      }
      
      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
      
      // Force a fresh query by adding a timestamp to the query key
      // The timestamp in the query key will trigger a new fetch
      if (forceRefresh) {
        query = query.limit(1000); // Add a high limit to ensure we get all assets
      }
      
      const { data, error } = await query;
      
      if (error) {
        throw new Error(`Error fetching assets: ${error.message}`);
      }
      
      // Debug logging for production issue
      console.log('[useAssets] Query result:', {
        collectionId,
        workflowStage,
        assetCount: data?.length || 0,
        forceRefresh,
        sampleAssets: data?.slice(0, 3).map(a => ({ 
          id: a.id, 
          workflow_stage: a.workflow_stage,
          file_name: a.file_name 
        }))
      });
      
      return data || [];
    }
  });
}

// Hook to fetch a single asset by ID
export function useAsset(id: string | undefined) {
  const { supabase } = useSupabase();
  
  return useQuery<Asset, Error>({
    queryKey: ['asset', id],
    enabled: !!id,
    queryFn: async () => {
      if (!id) throw new Error('Asset ID is required');
      
      const { data, error } = await supabase
        .from('assets')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        throw new Error(`Error fetching asset: ${error.message}`);
      }
      
      if (!data) {
        throw new Error(`Asset with ID ${id} not found`);
      }
      
      return data;
    }
  });
} 