import { useEffect } from 'react'
import { useSupabase } from '../../../contexts/SupabaseContext'

type SecurityEventType = 'login' | 'logout' | 'password_change' | 'email_change'

export const useSecurityLogging = () => {
  const { supabase, user } = useSupabase()

  const logSecurityEvent = async (eventType: SecurityEventType) => {
    if (!user) return

    try {
      await supabase.rpc('log_security_event', {
        p_user_id: user.id,
        p_event_type: eventType,
        p_ip_address: null, // Would need to get from server
        p_user_agent: navigator.userAgent,
      })
    } catch (error) {
      console.error('Error logging security event:', error)
    }
  }

  // Log login events
  useEffect(() => {
    if (!user) return

    // Check if this is a new session (simple implementation)
    const lastLoginKey = `last_login_${user.id}`
    const lastLogin = localStorage.getItem(lastLoginKey)
    const now = new Date().toISOString()
    
    if (!lastLogin || new Date(lastLogin).getDate() !== new Date(now).getDate()) {
      logSecurityEvent('login')
      localStorage.setItem(lastLoginKey, now)
    }
  }, [user])

  return { logSecurityEvent }
}