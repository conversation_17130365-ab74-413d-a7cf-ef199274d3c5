import { useState } from 'react'
import { useSupabase } from '../../../contexts/SupabaseContext'
import { useSecurityLogging } from './useSecurityLogging'
import { toast } from 'sonner'

export const useEmailChange = () => {
  const { supabase } = useSupabase()
  const { logSecurityEvent } = useSecurityLogging()
  const [isChangingEmail, setIsChangingEmail] = useState(false)

  const changeEmail = async (newEmail: string) => {
    setIsChangingEmail(true)
    try {
      const { error } = await supabase.auth.updateUser({ email: newEmail })
      
      if (error) throw error
      
      toast.success('Verification email sent to your new email address')
      logSecurityEvent('email_change')
      return true
    } catch (error) {
      console.error('Error changing email:', error)
      toast.error('Failed to change email')
      return false
    } finally {
      setIsChangingEmail(false)
    }
  }

  return {
    changeEmail,
    isChangingEmail
  }
}