import { useQuery } from '@tanstack/react-query';
import { supabase } from '../utils/supabase';

export interface CommentCount {
  asset_id: string;
  count: number;
}

/**
 * Hook to fetch comment counts for a list of assets
 * @param assetIds - Array of asset IDs to fetch comment counts for
 * @param enabled - Whether to enable the query
 * @returns Object with comment counts keyed by asset ID
 */
export function useAssetComments(assetIds: string[], enabled = true) {
  return useQuery({
    queryKey: ['asset-comments', assetIds],
    queryFn: async () => {
      if (!assetIds || assetIds.length === 0) {
        return {};
      }

      // Query to get comment counts for each asset
      const { data, error } = await supabase
        .from('comments')
        .select('asset_id')
        .in('asset_id', assetIds);

      if (error) {
        console.error('Error fetching comment counts:', error);
        throw error;
      }

      // Count comments per asset
      const commentCounts: Record<string, number> = {};
      
      if (data) {
        data.forEach((comment) => {
          if (comment.asset_id) {
            commentCounts[comment.asset_id] = (commentCounts[comment.asset_id] || 0) + 1;
          }
        });
      }

      return commentCounts;
    },
    enabled: enabled && assetIds.length > 0,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });
}

/**
 * Hook to fetch comment count for a single asset
 * @param assetId - Asset ID to fetch comment count for
 * @param enabled - Whether to enable the query
 * @returns Comment count for the asset
 */
export function useAssetCommentCount(assetId: string | null, enabled = true) {
  return useQuery({
    queryKey: ['asset-comment-count', assetId],
    queryFn: async () => {
      if (!assetId) return 0;

      const { count, error } = await supabase
        .from('comments')
        .select('*', { count: 'exact', head: true })
        .eq('asset_id', assetId);

      if (error) {
        console.error('Error fetching comment count:', error);
        throw error;
      }

      return count || 0;
    },
    enabled: enabled && !!assetId,
    staleTime: 30000, // Consider data stale after 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });
}