import { useMemo } from 'react';
import { AssetItem } from '../../../data/productAssets';
import { Filters } from '../../../contexts/FilterContext';
import { Product } from '../types/database.types';

interface UseAssetFilteringProps {
  assets: AssetItem[];
  filters: Filters;
  assetTagMap: Record<string, string[]>;
  products?: Product[];
}

/**
 * Custom hook to filter and group assets
 * Provides filtered assets and various groupings with memoization
 */
export function useAssetFiltering({ assets, filters, assetTagMap, products = [] }: UseAssetFilteringProps) {
  // Ensure assets is always an array
  const safeAssets = assets || [];
  
  // Create a map of product IDs that match selected sizes
  const productIdsWithSelectedSizes = useMemo(() => {
    if (filters.sizes.length === 0 || products.length === 0) return new Set<string>();
    
    const matchingProductIds = new Set<string>();
    products.forEach(product => {
      if (product.sizes) {
        // Parse sizes if it's a JSONB string
        let productSizes: string[] = [];
        if (typeof product.sizes === 'string') {
          try {
            productSizes = JSON.parse(product.sizes);
          } catch (e) {
            console.warn('Failed to parse sizes for product:', product.id);
          }
        } else if (Array.isArray(product.sizes)) {
          productSizes = product.sizes;
        }
        
        // Check if product has any of the selected sizes
        const hasSelectedSize = productSizes.some(size => filters.sizes.includes(size));
        if (hasSelectedSize) {
          matchingProductIds.add(product.id);
        }
      }
    });
    
    return matchingProductIds;
  }, [filters.sizes, products]);
  // Apply filters to the assets
  const filteredAssets = useMemo(() => {
    console.log('[useAssetFiltering] Filtering assets:', {
      totalAssets: safeAssets.length,
      filters: filters.workflowStages,
      rawAiImagesAssets: safeAssets.filter(a => a.stage === 'raw_ai_images').length,
      sizeFilters: filters.sizes,
      productsWithSelectedSizes: productIdsWithSelectedSizes.size
    });

    return safeAssets.filter(asset => {
      // Filter by workflow stage
      if (filters.workflowStages.length > 0 && !filters.workflowStages.includes(asset.stage)) {
        if (asset.stage === 'raw_ai_images') {
          console.log('[useAssetFiltering] Filtering out raw_ai_images asset:', {
            assetId: asset.id,
            assetStage: asset.stage,
            filterStages: filters.workflowStages
          });
        }
        return false;
      }

      // Filter by tags
      if (filters.tagIds.length > 0) {
        const assetTags = assetTagMap[asset.id] || [];
        const hasSelectedTag = filters.tagIds.some(tagId => assetTags.includes(tagId));
        if (!hasSelectedTag) {
          return false;
        }
      }

      // Filter by product
      if (filters.productIds.length > 0) {
        const assetProductId = asset.productId || '';
        if (!assetProductId || !filters.productIds.includes(assetProductId)) {
          return false;
        }
      }

      // Filter by sizes - check both product sizes and individual asset metadata
      if (filters.sizes.length > 0) {
        // First check if asset has size in metadata
        let assetHasSelectedSize = false;
        
        if (asset.metadata && typeof asset.metadata === 'object') {
          const metadata = asset.metadata as any;
          const assetSize = metadata.size || metadata.productSize;
          if (assetSize && filters.sizes.includes(assetSize)) {
            assetHasSelectedSize = true;
          }
        }
        
        // If asset doesn't have size in metadata, check if its product has the selected size
        if (!assetHasSelectedSize && productIdsWithSelectedSizes.size > 0) {
          const assetProductId = asset.productId || '';
          if (!assetProductId || !productIdsWithSelectedSizes.has(assetProductId)) {
            return false;
          }
        } else if (!assetHasSelectedSize) {
          // Asset has no size metadata and no product with selected sizes
          return false;
        }
      }

      // Filter by retouch substage (only for retouch stage assets)
      if (filters.retouchSubstages && filters.retouchSubstages.length > 0 && asset.stage === 'retouch') {
        // If asset doesn't have a substage or its substage is not in the selected ones, filter it out
        if (!asset.retouch_substage || !filters.retouchSubstages.includes(asset.retouch_substage)) {
          return false;
        }
      }

      // Filter by time updated
      if (filters.timeUpdated !== 'all') {
        const now = new Date();
        const assetDate = new Date(asset.dateUpdated);

        switch (filters.timeUpdated) {
          case 'today':
            return assetDate.toDateString() === now.toDateString();
          case 'week': {
            const weekAgo = new Date(now);
            weekAgo.setDate(now.getDate() - 7);
            return assetDate >= weekAgo;
          }
          case 'month': {
            const monthAgo = new Date(now);
            monthAgo.setMonth(now.getMonth() - 1);
            return assetDate >= monthAgo;
          }
          case 'quarter': {
            const quarterAgo = new Date(now);
            quarterAgo.setMonth(now.getMonth() - 3);
            return assetDate >= quarterAgo;
          }
        }
      }

      return true;
    });
  }, [safeAssets, filters, assetTagMap, productIdsWithSelectedSizes]);

  // Group assets by workflow stage
  const assetsByStage = useMemo(() => {
    return filteredAssets.reduce((acc, asset) => {
      if (!acc[asset.stage]) {
        acc[asset.stage] = [];
      }
      acc[asset.stage].push(asset);
      return acc;
    }, {} as Record<string, AssetItem[]>);
  }, [filteredAssets]);

  // Group assets by product
  const assetsByProduct = useMemo(() => {
    return filteredAssets.reduce((acc, asset) => {
      if (!acc[asset.productId]) {
        acc[asset.productId] = [];
      }
      acc[asset.productId].push(asset);
      return acc;
    }, {} as Record<string, AssetItem[]>);
  }, [filteredAssets]);

  // Calculate stage counts
  const stageCounts = useMemo(() => {
    const workflowStages = ['upload', 'raw_ai_images', 'selected', 'upscale', 'retouch', 'final'];
    return workflowStages.reduce((acc, stage) => {
      acc[stage] = assetsByStage[stage]?.length || 0;
      return acc;
    }, {} as Record<string, number>);
  }, [assetsByStage]);

  // Get product names
  const productNames = useMemo(() => {
    return Object.keys(assetsByProduct);
  }, [assetsByProduct]);

  // Check if filtering is active
  const isFiltering = useMemo(() => {
    return filters.workflowStages.length > 0 ||
           filters.tagIds.length > 0 ||
           filters.timeUpdated !== 'all' ||
           filters.productIds.length > 0 ||
           filters.sizes.length > 0 ||
           (filters.retouchSubstages && filters.retouchSubstages.length > 0);
  }, [filters]);

  return {
    filteredAssets,
    assetsByStage,
    assetsByProduct,
    stageCounts,
    productNames,
    isFiltering,
  };
}