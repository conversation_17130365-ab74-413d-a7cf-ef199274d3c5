import { useQuery } from '@tanstack/react-query';
import { useSupabase } from '../../../contexts/SupabaseContext';
import { Filters } from '../../../contexts/FilterContext';

interface AssetCounts {
  workflowStages: Record<string, number>;
  retouchSubstages: Record<string, number>;
  products: Record<string, number>;
  tags: Record<string, number>;
  total: number;
}

interface UseAssetCountsOptions {
  filters?: Partial<Filters>;
}

/**
 * Custom hook to fetch and track asset counts for a collection
 * Automatically updates when assets change via React Query
 */
export function useAssetCounts(collectionId: string | undefined, options?: UseAssetCountsOptions) {
  const { supabase } = useSupabase();
  const filters = options?.filters;

  return useQuery<AssetCounts>({
    queryKey: ['assetCounts', collectionId, filters],
    enabled: !!collectionId,
    queryFn: async () => {
      let query = supabase
        .from('assets')
        .select('workflow_stage, retouch_substage, product_id, id, metadata, asset_tags(tag_id)')
        .eq('collection_id', collectionId);

      // Apply workflow stage filter if provided
      if (filters?.workflowStages && filters.workflowStages.length > 0) {
        query = query.in('workflow_stage', filters.workflowStages);
      }

      // Apply product filter if provided
      if (filters?.productIds && filters.productIds.length > 0) {
        query = query.in('product_id', filters.productIds);
      }

      // Apply tag filter if provided
      if (filters?.tagIds && filters.tagIds.length > 0) {
        // We need to get assets that have at least one of the selected tags
        const { data: taggedAssets, error: tagError } = await supabase
          .from('asset_tags')
          .select('asset_id')
          .in('tag_id', filters.tagIds);
        
        if (tagError) throw tagError;
        
        const assetIds = taggedAssets?.map(ta => ta.asset_id) || [];
        if (assetIds.length > 0) {
          query = query.in('id', assetIds);
        } else {
          // No assets match the tags, return empty counts
          return { workflowStages: {}, products: {}, total: 0 };
        }
      }

      // Apply time filter if provided
      if (filters?.timeUpdated && filters.timeUpdated !== 'all') {
        const now = new Date();
        let startDate: Date;

        switch (filters.timeUpdated) {
          case 'today':
            startDate = new Date(now);
            startDate.setHours(0, 0, 0, 0);
            break;
          case 'week':
            startDate = new Date(now);
            startDate.setDate(startDate.getDate() - 7);
            break;
          case 'month':
            startDate = new Date(now);
            startDate.setMonth(startDate.getMonth() - 1);
            break;
          case 'quarter':
            startDate = new Date(now);
            startDate.setMonth(startDate.getMonth() - 3);
            break;
          default:
            startDate = new Date(0);
        }

        query = query.gte('updated_at', startDate.toISOString());
      }

      // Fetch all assets first to apply size filtering on metadata
      const { data: assets, error } = await query;

      if (error) throw error;

      // Apply size filter if provided
      let filteredAssets = assets || [];
      if (filters?.sizes && filters.sizes.length > 0) {
        filteredAssets = filteredAssets.filter(asset => {
          if (!asset.metadata || typeof asset.metadata !== 'object') return false;
          const assetMetadata = asset.metadata as Record<string, unknown>;
          const assetSize = assetMetadata.size as string | undefined;
          return assetSize && filters.sizes.includes(assetSize);
        });
      }

      const workflowStages: Record<string, number> = {};
      const retouchSubstages: Record<string, number> = {};
      const products: Record<string, number> = {};
      const tags: Record<string, number> = {};
      let total = 0;

      filteredAssets.forEach(asset => {
        // Count by workflow stage
        const stage = asset.workflow_stage;
        workflowStages[stage] = (workflowStages[stage] || 0) + 1;

        // Count by retouch substage (only for retouch assets)
        if (stage === 'retouch' && asset.retouch_substage) {
          retouchSubstages[asset.retouch_substage] = (retouchSubstages[asset.retouch_substage] || 0) + 1;
        }

        // Count by product
        if (asset.product_id) {
          products[asset.product_id] = (products[asset.product_id] || 0) + 1;
        }

        // Count by tags
        if (asset.asset_tags && Array.isArray(asset.asset_tags)) {
          asset.asset_tags.forEach((tagRelation: { tag_id?: string }) => {
            if (tagRelation.tag_id) {
              tags[tagRelation.tag_id] = (tags[tagRelation.tag_id] || 0) + 1;
            }
          });
        }

        total++;
      });

      return { workflowStages, retouchSubstages, products, tags, total };
    },
    // Refetch every 30 seconds to keep counts fresh
    refetchInterval: 30000,
    // Keep data fresh for 10 seconds
    staleTime: 10000,
  });
}