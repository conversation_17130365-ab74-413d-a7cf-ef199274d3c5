import { Asset } from './database.types';

// Re-export Asset type for convenience
export type { Asset };

// Extended asset type with image processing information
export interface ProcessedAsset extends Asset {
  // Paths to different versions of the asset
  compressedPath?: string;
  thumbnailPath?: string;
  originalPath?: string;
  
  // Size information for analytics
  originalSize?: number;
  compressedSize?: number;
  thumbnailSize?: number;
  
  // Compression metadata
  compressionRatio?: number;
  hasBeenCompressed?: boolean;
  
  // Status information
  isCompressing?: boolean;
  isUploading?: boolean;
  compressionProgress?: number;
  uploadProgress?: number;
}

// Interface for asset with processing status
export interface AssetWithProcessingStatus {
  id: string;
  file: File;
  progress: number;
  compressionProgress: number;
  uploadProgress: number;
  error?: string;
  uploaded?: boolean;
  compressed?: boolean;
  isDuplicate?: boolean;
  thumbnailPath?: string;
  compressedPath?: string;
  originalPath?: string;
}

// Extended metadata that includes image processing information
export interface AssetMetadata {
  // Original client-provided metadata
  [key: string]: any;
  
  // Image processing information
  imageProcessing?: {
    originalSize: number;
    compressedSize: number;
    thumbnailSize?: number;
    compressionRatio: number;
    originalPath: string;
    compressedPath: string;
    thumbnailPath?: string;
    processedAt: string;
  };
} 