import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import { Eye, Image as ImageIcon, ChevronLeft, ChevronRight } from 'lucide-react';
import { useCompareActions } from './CompareContext';
import { getAssetUrl } from '../../components/common/utils/utils';
import type { Asset } from '../../components/common/types/assetTypes';

interface AssetStageCardProps {
  stage: {
    key: string;
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    description: string;
  };
  assets: Asset[];
  isLoading?: boolean;
  compact?: boolean; // For mobile layout
  className?: string;
}

export function AssetStageCard({
  stage,
  assets,
  isLoading = false,
  compact = false,
  className
}: AssetStageCardProps) {
  const { toggleAssetSelection, openModal } = useCompareActions();
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
  const [currentAssetIndex, setCurrentAssetIndex] = useState(0);

  // Handle image load error
  const handleImageError = (assetId: string) => {
    setImageErrors(prev => new Set(prev).add(assetId));
  };

  // Get asset thumbnail URL using proper utility
  const getAssetThumbnailUrl = (asset: Asset) => {
    try {
      const url = getAssetUrl({
        id: asset.id,
        collection_id: asset.collection_id,
        file_path: asset.file_path,
        compressed_path: asset.compressed_path || undefined,
        original_path: asset.original_path || undefined,
        thumbnail_path: asset.thumbnail_path || undefined
      }, true); // true = prefer thumbnail
      return url;
    } catch (error) {
      // Return empty string if URL generation fails
      return '';
    }
  };

  // Navigation functions for carousel
  const goToPrevious = () => {
    if (assets.length === 0) return;
    setCurrentAssetIndex(prev => prev === 0 ? assets.length - 1 : prev - 1);
  };

  const goToNext = () => {
    if (assets.length === 0) return;
    setCurrentAssetIndex(prev => prev === assets.length - 1 ? 0 : prev + 1);
  };

  // Reset carousel index when assets change
  useEffect(() => {
    setCurrentAssetIndex(0);
  }, [assets.length]);

  // Handle asset click for selection
  const handleAssetClick = (asset: Asset) => {
    toggleAssetSelection(asset.id);
  };

  // Handle view all assets in this stage
  const handleViewAll = () => {
    if (assets.length > 0) {
      openModal(assets);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <Card className={`${className || ''} ${compact ? 'h-32' : 'h-48'}`}>
        <CardHeader className={compact ? 'pb-2' : 'pb-3'}>
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-5 w-8" />
          </div>
        </CardHeader>
        <CardContent className={compact ? 'pt-0' : ''}>
          <Skeleton className={`w-full ${compact ? 'h-16' : 'h-24'} rounded`} />
        </CardContent>
      </Card>
    );
  }

  // Empty state
  if (assets.length === 0) {
    return (
      <Card className={`${className || ''} ${compact ? 'h-32' : 'h-48'} border-dashed`}>
        <CardHeader className={compact ? 'pb-2' : 'pb-3'}>
          <div className="flex items-center justify-between">
            <CardTitle className={`${compact ? 'text-sm' : 'text-base'} font-medium`}>
              {stage.label}
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              0
            </Badge>
          </div>
        </CardHeader>
        <CardContent className={`${compact ? 'pt-0' : ''} flex items-center justify-center`}>
          <div className="text-center">
            <ImageIcon className={`${compact ? 'h-6 w-6' : 'h-8 w-8'} mx-auto text-muted-foreground mb-2`} />
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
              No assets
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get current asset for carousel display - ensure index is within bounds
  const safeCurrentIndex = Math.min(Math.max(0, currentAssetIndex), assets.length - 1);
  const currentAsset = assets[safeCurrentIndex];
  const hasMultipleAssets = assets.length > 1;

  // Safety check - if no current asset, show error state
  if (!currentAsset) {
    return (
      <Card className={`${className || ''} ${compact ? 'h-32' : 'h-48'} border-dashed`}>
        <CardHeader className={compact ? 'pb-2' : 'pb-3'}>
          <div className="flex items-center justify-between">
            <CardTitle className={`${compact ? 'text-sm' : 'text-base'} font-medium`}>
              {stage.label}
            </CardTitle>
            <Badge variant="outline" className="text-xs">
              {assets.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className={`${compact ? 'pt-0' : ''} flex items-center justify-center`}>
          <div className="text-center">
            <ImageIcon className={`${compact ? 'h-6 w-6' : 'h-8 w-8'} mx-auto text-muted-foreground mb-2`} />
            <p className={`${compact ? 'text-xs' : 'text-sm'} text-muted-foreground`}>
              Error loading asset
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }


  return (
    <Card className={`${className || ''} ${compact ? 'h-32' : 'h-48'} hover:shadow-md transition-shadow`}>
      <CardHeader className={compact ? 'pb-2' : 'pb-3'}>
        <div className="flex items-center justify-between">
          <CardTitle className={`${compact ? 'text-sm' : 'text-base'} font-medium`}>
            {stage.label}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className={`text-xs ${stage.color}`}>
              {assets.length}
            </Badge>
            {!compact && assets.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleViewAll}
                className="h-6 w-6 p-0"
              >
                <Eye className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
        {!compact && (
          <p className="text-xs text-muted-foreground">{stage.description}</p>
        )}
      </CardHeader>
      
      <CardContent className={compact ? 'pt-0' : ''}>
        {/* Carousel layout - single asset with navigation */}
        <div className="relative">
          <div
            className={`${compact ? 'aspect-square' : 'aspect-[4/3]'} rounded cursor-pointer hover:opacity-80 transition-opacity relative overflow-hidden`}
            onClick={() => handleAssetClick(currentAsset)}
          >
            {imageErrors.has(currentAsset.id) ? (
              <div className="w-full h-full bg-muted rounded flex items-center justify-center">
                <ImageIcon className={`${compact ? 'h-4 w-4' : 'h-8 w-8'} text-muted-foreground`} />
              </div>
            ) : (
              <img
                src={getAssetThumbnailUrl(currentAsset)}
                alt={currentAsset.file_name}
                className="w-full h-full object-cover rounded"
                onError={() => handleImageError(currentAsset.id)}
              />
            )}

            {/* Navigation arrows - only show if multiple assets */}
            {hasMultipleAssets && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-black/50 hover:bg-black/70 text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    goToPrevious();
                  }}
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0 bg-black/50 hover:bg-black/70 text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    goToNext();
                  }}
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </>
            )}
          </div>

          {/* Asset counter */}
          {hasMultipleAssets && (
            <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
              {safeCurrentIndex + 1} / {assets.length}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
