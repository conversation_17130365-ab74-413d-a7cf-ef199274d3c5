import React, { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useCompareActions, useCompareState } from './CompareContext';

// Common clothing sizes for the size filter
const COMMON_SIZES = [
  'XXS', 'XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL',
  '28', '30', '32', '34', '36', '38', '40', '42', '44', '46',
  '6', '8', '10', '12', '14', '16', '18', '20'
];

interface ProductFiltersProps {
  className?: string;
  availableSizes?: string[]; // Sizes that actually exist in the products
  availableTags?: Array<{ id: string; name: string; color?: string }>; // Available tags
}

export function ProductFilters({ 
  className,
  availableSizes = [],
  availableTags = []
}: ProductFiltersProps) {
  const { productFilters } = useCompareState();
  const { setProductFilters, clearFilters } = useCompareActions();
  const [isExpanded, setIsExpanded] = useState(false);

  // Combine common sizes with available sizes from products
  const allSizes = Array.from(new Set([...COMMON_SIZES, ...availableSizes])).sort();

  // Handle SKU search
  const handleSkuSearch = (value: string) => {
    setProductFilters({ skuSearch: value });
  };

  // Handle size selection
  const handleSizeToggle = (size: string) => {
    const currentSizes = productFilters.sizes;
    const isSelected = currentSizes.includes(size);
    
    const newSizes = isSelected
      ? currentSizes.filter(s => s !== size)
      : [...currentSizes, size];
    
    setProductFilters({ sizes: newSizes });
  };

  // Handle tag selection
  const handleTagToggle = (tagId: string) => {
    const currentTags = productFilters.tagIds;
    const isSelected = currentTags.includes(tagId);
    
    const newTags = isSelected
      ? currentTags.filter(id => id !== tagId)
      : [...currentTags, tagId];
    
    setProductFilters({ tagIds: newTags });
  };

  // Check if any filters are active
  const hasActiveFilters = 
    productFilters.skuSearch !== '' ||
    productFilters.sizes.length > 0 ||
    productFilters.tagIds.length > 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Product Filters
          </CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Collapse' : 'Expand'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* SKU Search - Always visible */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Search by SKU</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Enter SKU..."
              value={productFilters.skuSearch}
              onChange={(e) => handleSkuSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Expanded filters */}
        {isExpanded && (
          <>
            {/* Size Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Filter by Sizes
                {productFilters.sizes.length > 0 && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    ({productFilters.sizes.length} selected)
                  </span>
                )}
              </label>
              <div className="flex flex-wrap gap-2">
                {allSizes.map((size) => {
                  const isSelected = productFilters.sizes.includes(size);
                  const isAvailable = availableSizes.includes(size);
                  
                  return (
                    <Button
                      key={size}
                      variant={isSelected ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleSizeToggle(size)}
                      className={`h-8 px-3 ${
                        !isAvailable && !isSelected 
                          ? 'opacity-50 cursor-not-allowed' 
                          : ''
                      }`}
                      disabled={!isAvailable && !isSelected}
                    >
                      {size}
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Tag Filter */}
            {availableTags.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Filter by Tags
                  {productFilters.tagIds.length > 0 && (
                    <span className="ml-2 text-xs text-muted-foreground">
                      ({productFilters.tagIds.length} selected)
                    </span>
                  )}
                </label>
                <div className="flex flex-wrap gap-2">
                  {availableTags.map((tag) => {
                    const isSelected = productFilters.tagIds.includes(tag.id);
                    
                    return (
                      <Badge
                        key={tag.id}
                        variant={isSelected ? "default" : "outline"}
                        className="cursor-pointer hover:bg-accent"
                        onClick={() => handleTagToggle(tag.id)}
                        style={tag.color && isSelected ? { backgroundColor: tag.color } : undefined}
                      >
                        {tag.name}
                      </Badge>
                    );
                  })}
                </div>
              </div>
            )}
          </>
        )}

        {/* Active filters summary */}
        {hasActiveFilters && (
          <div className="pt-2 border-t">
            <div className="flex flex-wrap gap-2">
              {productFilters.skuSearch && (
                <Badge variant="secondary" className="gap-1">
                  SKU: {productFilters.skuSearch}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleSkuSearch('')}
                  />
                </Badge>
              )}
              {productFilters.sizes.map((size) => (
                <Badge key={size} variant="secondary" className="gap-1">
                  Size: {size}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => handleSizeToggle(size)}
                  />
                </Badge>
              ))}
              {productFilters.tagIds.map((tagId) => {
                const tag = availableTags.find(t => t.id === tagId);
                return tag ? (
                  <Badge key={tagId} variant="secondary" className="gap-1">
                    {tag.name}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => handleTagToggle(tagId)}
                    />
                  </Badge>
                ) : null;
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
