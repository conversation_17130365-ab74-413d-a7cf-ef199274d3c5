import { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { AssetStageCard } from './AssetStageCard';
import { useCompareState } from './CompareContext';
import { getWorkflowStageConfigs } from '../../components/common/utils/workflowStageUtils';
import { useUserRole } from '../../contexts/UserRoleContext';
import { useIsFreelancer } from '../../components/common/hooks/useIsFreelancer';
import * as Icons from 'lucide-react';
import { Clipboard, Camera } from 'lucide-react';

interface WorkflowStageGridProps {
  className?: string;
}

export function WorkflowStageGrid({ className }: WorkflowStageGridProps) {
  const { selectedProductId, selectedProductAssets, isLoadingAssets } = useCompareState();
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get workflow stages based on user role and freelancer status
  const workflowStages = useMemo(() => {
    const configs = getWorkflowStageConfigs(userRole, isFreelancer);
    // Map the configs to include icon components and additional properties
    return configs.map(config => {
      // Get the icon component dynamically
      const IconComponent = Icons[config.icon as keyof typeof Icons] || Icons.ImageIcon;
      
      // Map color names to Tailwind classes
      const colorMap: Record<string, string> = {
        'blue': 'bg-blue-100 text-blue-800',
        'indigo': 'bg-indigo-100 text-indigo-800',
        'green': 'bg-green-100 text-green-800',
        'teal': 'bg-teal-100 text-teal-800',
        'amber': 'bg-amber-100 text-amber-800',
        'emerald': 'bg-emerald-100 text-emerald-800',
        'purple': 'bg-purple-100 text-purple-800'
      };

      return {
        key: config.id,
        label: config.label,
        icon: IconComponent,
        color: colorMap[config.color] || 'bg-gray-100 text-gray-800',
        description: `Assets in ${config.label} stage`
      };
    });
  }, [userRole, isFreelancer]);

  // Filter stages to only show those with assets
  const stagesWithAssets = workflowStages.filter(stage => {
    const assets = selectedProductAssets[stage.key] || [];
    return assets.length > 0;
  });

  // If no product is selected, show empty state
  if (!selectedProductId) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Workflow Stages</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Clipboard className="w-8 h-8 text-muted-foreground" />
              </div>
            </div>
            <h3 className="text-lg font-medium mb-2">Select a Product</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              Choose a product from the navigator above to view its assets across all workflow stages.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show empty state if no stages have assets
  if (stagesWithAssets.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Workflow Stages</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                <Camera className="w-8 h-8 text-muted-foreground" />
              </div>
            </div>
            <h3 className="text-lg font-medium mb-2">No Assets Found</h3>
            <p className="text-muted-foreground max-w-md mx-auto">
              This product doesn't have any assets in the workflow stages yet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Workflow Stages</CardTitle>
          <div className="flex items-center gap-2">
            {/* Total asset count from stages with assets */}
            <Badge variant="secondary">
              {stagesWithAssets.reduce((total, stage) =>
                total + (selectedProductAssets[stage.key]?.length || 0), 0
              )} total assets
            </Badge>
            <Badge variant="outline">
              {stagesWithAssets.length} stages
            </Badge>
            {isLoadingAssets && (
              <Badge variant="outline">Loading...</Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Desktop Grid Layout - Dynamic columns based on stages with assets */}
        <div
          className="hidden lg:grid gap-4"
          style={{
            gridTemplateColumns: `repeat(${Math.min(stagesWithAssets.length, 6)}, 1fr)`
          }}
        >
          {stagesWithAssets.map((stage) => (
            <AssetStageCard
              key={stage.key}
              stage={stage}
              assets={selectedProductAssets[stage.key] || []}
              isLoading={isLoadingAssets}
            />
          ))}
        </div>

        {/* Tablet Layout - Dynamic columns based on stages with assets */}
        <div
          className="hidden md:grid lg:hidden gap-4"
          style={{
            gridTemplateColumns: `repeat(${Math.min(stagesWithAssets.length, 3)}, 1fr)`
          }}
        >
          {stagesWithAssets.map((stage) => (
            <AssetStageCard
              key={stage.key}
              stage={stage}
              assets={selectedProductAssets[stage.key] || []}
              isLoading={isLoadingAssets}
            />
          ))}
        </div>

        {/* Mobile Layout - Dynamic columns based on stages with assets */}
        <div
          className="grid md:hidden gap-4"
          style={{
            gridTemplateColumns: `repeat(${Math.min(stagesWithAssets.length, 2)}, 1fr)`
          }}
        >
          {stagesWithAssets.map((stage) => (
            <AssetStageCard
              key={stage.key}
              stage={stage}
              assets={selectedProductAssets[stage.key] || []}
              isLoading={isLoadingAssets}
              compact
            />
          ))}
        </div>

        {/* Stage Summary - Only show stages with assets */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex flex-wrap gap-2">
            {stagesWithAssets.map((stage) => {
              const count = selectedProductAssets[stage.key]?.length || 0;
              return (
                <Badge
                  key={stage.key}
                  variant="default"
                  className={stage.color}
                >
                  {stage.label}: {count}
                </Badge>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
