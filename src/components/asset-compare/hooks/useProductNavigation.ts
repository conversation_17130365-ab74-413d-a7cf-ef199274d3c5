import { useCallback, useMemo } from 'react';
import type { ProductWithAssets } from '../types/compare.types';

/**
 * Hook for managing product navigation in the compare view
 * Provides next/previous navigation and current product information
 */
export function useProductNavigation(
  products: ProductWithAssets[],
  currentProductId: string | null
) {
  // Find current product index
  const currentIndex = useMemo(() => {
    if (!currentProductId || products.length === 0) return -1;
    return products.findIndex(p => p.id === currentProductId);
  }, [products, currentProductId]);

  // Get current product
  const currentProduct = useMemo(() => {
    if (currentIndex === -1) return null;
    return products[currentIndex] || null;
  }, [products, currentIndex]);

  // Navigation functions
  const navigateNext = useCallback(() => {
    if (currentIndex < products.length - 1) {
      return products[currentIndex + 1].id;
    }
    return null;
  }, [products, currentIndex]);

  const navigatePrev = useCallback(() => {
    if (currentIndex > 0) {
      return products[currentIndex - 1].id;
    }
    return null;
  }, [products, currentIndex]);

  // Navigation state
  const canNavigateNext = currentIndex < products.length - 1;
  const canNavigatePrev = currentIndex > 0;

  // Get next and previous products for preview
  const nextProduct = useMemo(() => {
    if (canNavigateNext) {
      return products[currentIndex + 1];
    }
    return null;
  }, [products, currentIndex, canNavigateNext]);

  const prevProduct = useMemo(() => {
    if (canNavigatePrev) {
      return products[currentIndex - 1];
    }
    return null;
  }, [products, currentIndex, canNavigatePrev]);

  // Get product position info
  const positionInfo = useMemo(() => {
    return {
      current: currentIndex + 1,
      total: products.length,
      hasProducts: products.length > 0,
      isFirst: currentIndex === 0,
      isLast: currentIndex === products.length - 1
    };
  }, [currentIndex, products.length]);

  return {
    // Current product
    currentProduct,
    currentIndex,
    
    // Navigation functions
    navigateNext,
    navigatePrev,
    
    // Navigation state
    canNavigateNext,
    canNavigatePrev,
    
    // Adjacent products
    nextProduct,
    prevProduct,
    
    // Position information
    positionInfo,
    
    // Convenience properties
    totalProducts: products.length,
    hasProducts: products.length > 0
  };
}

/**
 * Hook for managing product selection state
 */
export function useProductSelection(
  products: ProductWithAssets[],
  onProductChange?: (productId: string | null) => void
) {
  const selectProduct = useCallback((productId: string | null) => {
    onProductChange?.(productId);
  }, [onProductChange]);

  const selectProductByIndex = useCallback((index: number) => {
    if (index >= 0 && index < products.length) {
      const product = products[index];
      selectProduct(product.id);
    }
  }, [products, selectProduct]);

  const selectFirstProduct = useCallback(() => {
    if (products.length > 0) {
      selectProduct(products[0].id);
    }
  }, [products, selectProduct]);

  const selectLastProduct = useCallback(() => {
    if (products.length > 0) {
      selectProduct(products[products.length - 1].id);
    }
  }, [products, selectProduct]);

  const clearSelection = useCallback(() => {
    selectProduct(null);
  }, [selectProduct]);

  return {
    selectProduct,
    selectProductByIndex,
    selectFirstProduct,
    selectLastProduct,
    clearSelection
  };
}
