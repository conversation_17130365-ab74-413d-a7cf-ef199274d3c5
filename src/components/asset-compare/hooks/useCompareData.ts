import { useMemo } from 'react';
import { useProducts } from '../../../components/common/hooks/useProducts';
import { useAssets } from '../../../components/common/hooks/useAssets';
import { useSupabase } from '../../../contexts/SupabaseContext';
import type { ProductWithAssets } from '../types/compare.types';
import { groupAssetsByStage } from '../types/compare.types';

/**
 * Hook to fetch products with asset counts using existing useProducts hook
 * and enhance with asset count data
 */
export function useProductsWithAssets(
  collectionId: string,
  options?: { enabled?: boolean }
) {
  const { enabled = true } = options || {};

  // Use existing useProducts hook
  const productsQuery = useProducts({
    enabled: enabled && !!collectionId,
    collectionId
  });

  // Fetch asset counts for all products in the collection
  const assetsQuery = useAssets({
    enabled: enabled && !!collectionId && !!productsQuery.data,
    collectionId
  });

  // Transform the data to include asset counts
  const productsWithAssets = useMemo(() => {
    if (!productsQuery.data || !assetsQuery.data) {
      return [];
    }

    // Group asset counts by product and workflow stage
    const countsByProduct = assetsQuery.data.reduce((acc, asset) => {
      if (!asset.product_id) return acc;
      
      if (!acc[asset.product_id]) {
        acc[asset.product_id] = {
          total_assets: 0,
          upload_count: 0,
          raw_ai_count: 0,
          selected_count: 0,
          upscale_count: 0,
          retouch_count: 0,
          final_count: 0
        };
      }

      acc[asset.product_id].total_assets++;
      
      switch (asset.workflow_stage) {
        case 'upload':
          acc[asset.product_id].upload_count++;
          break;
        case 'raw_ai_images':
          acc[asset.product_id].raw_ai_count++;
          break;
        case 'selected':
          acc[asset.product_id].selected_count++;
          break;
        case 'upscale':
          acc[asset.product_id].upscale_count++;
          break;
        case 'retouch':
          acc[asset.product_id].retouch_count++;
          break;
        case 'final':
          acc[asset.product_id].final_count++;
          break;
      }

      return acc;
    }, {} as Record<string, {
      upload_count: number;
      raw_ai_count: number;
      selected_count: number;
      upscale_count: number;
      retouch_count: number;
      final_count: number;
    }>);

    // Combine products with their asset counts and parse sizes
    const result: ProductWithAssets[] = productsQuery.data
      .map(product => {
        const counts = countsByProduct[product.id] || {
          total_assets: 0,
          upload_count: 0,
          raw_ai_count: 0,
          selected_count: 0,
          upscale_count: 0,
          retouch_count: 0,
          final_count: 0
        };

        // Parse sizes from JSONB
        let parsedSizes: string[] = [];
        try {
          if (product.sizes && typeof product.sizes === 'object') {
            parsedSizes = Array.isArray(product.sizes) ? product.sizes : [];
          }
        } catch (e) {
          // Skip products with invalid sizes format
        }

        return {
          ...product,
          ...counts,
          sizes: parsedSizes
        };
      })
      .filter(product => product.total_assets > 0); // Only return products that have assets

    return result;
  }, [productsQuery.data, assetsQuery.data]);

  return {
    data: productsWithAssets,
    isLoading: productsQuery.isLoading || assetsQuery.isLoading,
    error: productsQuery.error || assetsQuery.error,
    refetch: async () => {
      await Promise.all([productsQuery.refetch(), assetsQuery.refetch()]);
    }
  };
}

/**
 * Hook to fetch all assets for a specific product, grouped by workflow stage
 * Uses the existing useAssets hook
 */
export function useProductAssets(
  productId: string | null,
  collectionId: string,
  options?: { enabled?: boolean }
) {
  const { enabled = true } = options || {};

  // Use existing useAssets hook with productId filter
  const assetsQuery = useAssets({
    enabled: enabled && !!productId && !!collectionId,
    productId: productId || undefined,
    collectionId
  });

  // Transform the data to group by workflow stage
  const assetsByStage = useMemo(() => {
    if (!assetsQuery.data) {
      return groupAssetsByStage([]);
    }
    return groupAssetsByStage(assetsQuery.data);
  }, [assetsQuery.data]);

  return {
    data: assetsByStage,
    isLoading: assetsQuery.isLoading,
    error: assetsQuery.error,
    refetch: assetsQuery.refetch
  };
}

/**
 * Main hook that combines products and assets data for the compare view
 * Now uses the existing hooks from the codebase
 */
export function useCompareData(
  collectionId: string,
  selectedProductId: string | null
) {
  const products = useProductsWithAssets(collectionId);
  const assets = useProductAssets(selectedProductId, collectionId);

  return {
    availableProducts: products.data || [],
    selectedProductAssets: assets.data || groupAssetsByStage([]),
    isLoadingProducts: products.isLoading,
    isLoadingAssets: assets.isLoading,
    isLoading: products.isLoading || assets.isLoading,
    error: products.error || assets.error,
    refetchProducts: products.refetch,
    refetchAssets: assets.refetch,
    refetchAll: async () => {
      await Promise.all([
        products.refetch(),
        assets.refetch()
      ]);
    }
  };
}