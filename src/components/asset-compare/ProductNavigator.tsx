import React from 'react';
import { ChevronLeft, ChevronRight, Package } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Badge } from '../ui/badge';
import { useCompareState, useCompareActions } from './CompareContext';
import { useProductNavigation } from './hooks/useProductNavigation';
import type { ProductWithAssets } from './types/compare.types';

interface ProductNavigatorProps {
  products: ProductWithAssets[];
  className?: string;
}

export function ProductNavigator({ products, className }: ProductNavigatorProps) {
  const { selectedProductId } = useCompareState();
  const { setSelectedProduct } = useCompareActions();
  
  const {
    currentProduct,
    navigateNext,
    navigatePrev,
    canNavigateNext,
    canNavigatePrev,
    positionInfo
  } = useProductNavigation(products, selectedProductId);

  // Handle product selection from dropdown
  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
  };

  // Handle navigation
  const handleNext = () => {
    const nextId = navigateNext();
    if (nextId) {
      setSelectedProduct(nextId);
    }
  };

  const handlePrev = () => {
    const prevId = navigatePrev();
    if (prevId) {
      setSelectedProduct(prevId);
    }
  };

  // If no products available
  if (products.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">
            No products found with the current filters.
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Try adjusting your search criteria or clearing filters.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Navigation Controls */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrev}
              disabled={!canNavigatePrev}
              className="h-9 w-9 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleNext}
              disabled={!canNavigateNext}
              className="h-9 w-9 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          {/* Product Selector */}
          <div className="flex-1 min-w-0">
            <Select
              value={selectedProductId || ''}
              onValueChange={handleProductSelect}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a product to compare...">
                  {currentProduct && (
                    <div className="flex items-center gap-2 min-w-0">
                      <Package className="h-4 w-4 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <div className="truncate font-medium">
                          {currentProduct.name}
                        </div>
                        {currentProduct.sku && (
                          <div className="text-xs text-muted-foreground">
                            SKU: {currentProduct.sku}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {products.map((product) => (
                  <SelectItem key={product.id} value={product.id}>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        <Package className="h-4 w-4 flex-shrink-0" />
                        <div className="min-w-0 flex-1">
                          <div className="truncate font-medium">
                            {product.name}
                          </div>
                          {product.sku && (
                            <div className="text-xs text-muted-foreground">
                              SKU: {product.sku}
                            </div>
                          )}
                        </div>
                      </div>
                      <Badge variant="secondary" className="ml-2 flex-shrink-0">
                        {product.total_assets} assets
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Position Info */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>
              {positionInfo.current} of {positionInfo.total}
            </span>
          </div>
        </div>

        {/* Selected Product Details */}
        {currentProduct && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h3 className="font-medium">{currentProduct.name}</h3>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  {currentProduct.sku && (
                    <span>SKU: {currentProduct.sku}</span>
                  )}
                  <span>{currentProduct.total_assets} total assets</span>
                  {currentProduct.sizes.length > 0 && (
                    <div className="flex items-center gap-1">
                      <span>Sizes:</span>
                      <div className="flex gap-1">
                        {currentProduct.sizes.slice(0, 5).map((size) => (
                          <Badge key={size} variant="outline" className="text-xs">
                            {size}
                          </Badge>
                        ))}
                        {currentProduct.sizes.length > 5 && (
                          <Badge variant="outline" className="text-xs">
                            +{currentProduct.sizes.length - 5}
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Asset counts by stage */}
              <div className="flex items-center gap-2">
                {[
                  { key: 'upload_count', label: 'Upload', color: 'bg-gray-100' },
                  { key: 'raw_ai_count', label: 'AI', color: 'bg-blue-100' },
                  { key: 'selected_count', label: 'Selected', color: 'bg-green-100' },
                  { key: 'upscale_count', label: 'Upscale', color: 'bg-purple-100' },
                  { key: 'retouch_count', label: 'Retouch', color: 'bg-orange-100' },
                  { key: 'final_count', label: 'Final', color: 'bg-emerald-100' }
                ].map(({ key, label, color }) => {
                  const count = currentProduct[key as keyof ProductWithAssets] as number;
                  return count > 0 ? (
                    <Badge key={key} variant="secondary" className={`text-xs ${color}`}>
                      {label}: {count}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
