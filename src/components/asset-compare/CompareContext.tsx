import React, { createContext, useContext, useReducer, useCallback, useMemo, ReactNode } from 'react';
import type { CompareState, CompareAction, ProductFilters } from './types/compare.types';
import { getEmptyAssetsByStage } from './types/compare.types';
import type { Asset } from '../../components/common/types/assetTypes';

// Initial state
const initialState: CompareState = {
  selectedProductId: null,
  selectedAssetIds: [],
  productFilters: {
    skuSearch: '',
    tagIds: [],
    sizes: []
  },
  isModalOpen: false,
  modalAssets: [],
  availableProducts: [],
  selectedProductAssets: getEmptyAssetsByStage(),
  isLoadingProducts: false,
  isLoadingAssets: false
};

// Reducer function
function compareReducer(state: CompareState, action: CompareAction): CompareState {
  switch (action.type) {
    case 'SET_SELECTED_PRODUCT':
      return {
        ...state,
        selectedProductId: action.payload,
        selectedAssetIds: [] // Clear asset selection when product changes
      };

    case 'SET_PRODUCT_FILTERS':
      return {
        ...state,
        productFilters: {
          ...state.productFilters,
          ...action.payload
        }
      };

    case 'SET_SELECTED_ASSETS':
      return {
        ...state,
        selectedAssetIds: action.payload
      };

    case 'TOGGLE_ASSET_SELECTION': {
      const assetId = action.payload;
      const isSelected = state.selectedAssetIds.includes(assetId);
      return {
        ...state,
        selectedAssetIds: isSelected
          ? state.selectedAssetIds.filter(id => id !== assetId)
          : [...state.selectedAssetIds, assetId]
      };
    }

    case 'OPEN_MODAL':
      return {
        ...state,
        isModalOpen: true,
        modalAssets: action.payload
      };

    case 'CLOSE_MODAL':
      return {
        ...state,
        isModalOpen: false,
        modalAssets: []
      };

    case 'SET_AVAILABLE_PRODUCTS':
      return {
        ...state,
        availableProducts: action.payload
      };

    case 'SET_SELECTED_PRODUCT_ASSETS':
      return {
        ...state,
        selectedProductAssets: action.payload
      };

    case 'SET_LOADING_PRODUCTS':
      return {
        ...state,
        isLoadingProducts: action.payload
      };

    case 'SET_LOADING_ASSETS':
      return {
        ...state,
        isLoadingAssets: action.payload
      };

    default:
      return state;
  }
}

// Context type
interface CompareContextType {
  state: CompareState;
  dispatch: React.Dispatch<CompareAction>;
  
  // Convenience action creators
  setSelectedProduct: (productId: string | null) => void;
  setProductFilters: (filters: Partial<ProductFilters>) => void;
  setSelectedAssets: (assetIds: string[]) => void;
  toggleAssetSelection: (assetId: string) => void;
  openModal: (assets: Asset[]) => void;
  closeModal: () => void;
  clearFilters: () => void;
  clearSelection: () => void;
}

// Create context
const CompareContext = createContext<CompareContextType | undefined>(undefined);

// Provider component
interface CompareProviderProps {
  children: ReactNode;
  initialProductId?: string | null;
  initialFilters?: Partial<ProductFilters>;
}

export function CompareProvider({ 
  children, 
  initialProductId = null,
  initialFilters = {}
}: CompareProviderProps) {
  const [state, dispatch] = useReducer(compareReducer, {
    ...initialState,
    selectedProductId: initialProductId,
    productFilters: {
      ...initialState.productFilters,
      ...initialFilters
    }
  });

  // Action creators
  const setSelectedProduct = useCallback((productId: string | null) => {
    dispatch({ type: 'SET_SELECTED_PRODUCT', payload: productId });
  }, []);

  const setProductFilters = useCallback((filters: Partial<ProductFilters>) => {
    dispatch({ type: 'SET_PRODUCT_FILTERS', payload: filters });
  }, []);

  const setSelectedAssets = useCallback((assetIds: string[]) => {
    dispatch({ type: 'SET_SELECTED_ASSETS', payload: assetIds });
  }, []);

  const toggleAssetSelection = useCallback((assetId: string) => {
    dispatch({ type: 'TOGGLE_ASSET_SELECTION', payload: assetId });
  }, []);

  const openModal = useCallback((assets: Asset[]) => {
    dispatch({ type: 'OPEN_MODAL', payload: assets });
  }, []);

  const closeModal = useCallback(() => {
    dispatch({ type: 'CLOSE_MODAL' });
  }, []);

  const clearFilters = useCallback(() => {
    dispatch({ 
      type: 'SET_PRODUCT_FILTERS', 
      payload: {
        skuSearch: '',
        tagIds: [],
        sizes: []
      }
    });
  }, []);

  const clearSelection = useCallback(() => {
    dispatch({ type: 'SET_SELECTED_PRODUCT', payload: null });
    dispatch({ type: 'SET_SELECTED_ASSETS', payload: [] });
  }, []);

  const contextValue: CompareContextType = useMemo(() => ({
    state,
    dispatch,
    setSelectedProduct,
    setProductFilters,
    setSelectedAssets,
    toggleAssetSelection,
    openModal,
    closeModal,
    clearFilters,
    clearSelection
  }), [
    state,
    setSelectedProduct,
    setProductFilters,
    setSelectedAssets,
    toggleAssetSelection,
    openModal,
    closeModal,
    clearFilters,
    clearSelection
  ]);

  return (
    <CompareContext.Provider value={contextValue}>
      {children}
    </CompareContext.Provider>
  );
}

// Hook to use the context
export function useCompareContext() {
  const context = useContext(CompareContext);
  if (context === undefined) {
    throw new Error('useCompareContext must be used within a CompareProvider');
  }
  return context;
}

// Hook to get just the state (for read-only access)
export function useCompareState() {
  const { state } = useCompareContext();
  return state;
}

// Hook to get just the actions (for components that only need to dispatch)
export function useCompareActions() {
  const { 
    setSelectedProduct,
    setProductFilters,
    setSelectedAssets,
    toggleAssetSelection,
    openModal,
    closeModal,
    clearFilters,
    clearSelection
  } = useCompareContext();
  
  return {
    setSelectedProduct,
    setProductFilters,
    setSelectedAssets,
    toggleAssetSelection,
    openModal,
    closeModal,
    clearFilters,
    clearSelection
  };
}
