import { useState } from 'react';
import { X, Download, ZoomIn, ZoomOut, RotateCw, ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { useCompareState, useCompareActions } from './CompareContext';
import type { Asset } from '../../components/common/types/assetTypes';
import { getWorkflowStageLabel } from '../../components/common/utils/workflowStageUtils';
import { getAssetUrl } from '../../components/common/utils/utils';
import { useToast } from '../../components/common/hooks/use-toast';

export function CompareModal() {
  const { isModalOpen, modalAssets } = useCompareState();
  const { closeModal } = useCompareActions();
  const { toast } = useToast();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [zoom, setZoom] = useState(1);

  // Get current asset
  const currentAsset = modalAssets[currentIndex];

  // Navigation functions
  const goToNext = () => {
    if (currentIndex < modalAssets.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setZoom(1); // Reset zoom when changing assets
    }
  };

  const goToPrev = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setZoom(1); // Reset zoom when changing assets
    }
  };

  // Zoom functions
  const zoomIn = () => setZoom(prev => Math.min(prev * 1.5, 5));
  const zoomOut = () => setZoom(prev => Math.max(prev / 1.5, 0.5));
  const resetZoom = () => setZoom(1);

  // Helper to call getAssetUrl with proper type conversion
  const getAssetUrlSafe = (asset: Asset, type: boolean | 'original' = true) => {
    return getAssetUrl({
      id: asset.id,
      collection_id: asset.collection_id,
      file_path: asset.file_path,
      compressed_path: asset.compressed_path || undefined,
      original_path: asset.original_path || undefined,
      thumbnail_path: asset.thumbnail_path || undefined
    }, type === 'original' ? false : type);
  };

  // Handle download
  const handleDownload = async (asset: Asset) => {
    try {
      const url = getAssetUrlSafe(asset, 'original');
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = asset.file_name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(downloadUrl);
      
      toast({
        title: 'Download Started',
        description: `Downloading ${asset.file_name}`,
      });
    } catch (error) {
      toast({
        title: 'Download Failed',
        description: 'Failed to download the asset. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle modal close
  const handleClose = () => {
    setCurrentIndex(0);
    setZoom(1);
    closeModal();
  };

  if (!isModalOpen || !currentAsset) {
    return null;
  }

  const stageLabel = getWorkflowStageLabel(currentAsset.workflow_stage);

  return (
    <Dialog open={isModalOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl h-[90vh] p-0">
        {/* Header */}
        <DialogHeader className="p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <DialogTitle className="text-lg">
                {currentAsset.file_name}
              </DialogTitle>
              <Badge variant="secondary">
                {stageLabel}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Navigation */}
              {modalAssets.length > 1 && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToPrev}
                    disabled={currentIndex === 0}
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-muted-foreground px-2">
                    {currentIndex + 1} of {modalAssets.length}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={goToNext}
                    disabled={currentIndex === modalAssets.length - 1}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              {/* Zoom controls */}
              <Button variant="outline" size="sm" onClick={zoomOut}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-muted-foreground px-2">
                {Math.round(zoom * 100)}%
              </span>
              <Button variant="outline" size="sm" onClick={zoomIn}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={resetZoom}>
                <RotateCw className="h-4 w-4" />
              </Button>
              
              {/* Download */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDownload(currentAsset)}
              >
                <Download className="h-4 w-4" />
              </Button>
              
              {/* Close */}
              <Button variant="outline" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Image viewer */}
        <div className="flex-1 overflow-hidden relative bg-gray-50">
          <div className="absolute inset-0 flex items-center justify-center p-4">
            <div 
              className="relative max-w-full max-h-full"
              style={{ transform: `scale(${zoom})` }}
            >
              <img
                src={getAssetUrlSafe(currentAsset)}
                alt={currentAsset.file_name}
                className="max-w-full max-h-full object-contain"
                style={{ maxHeight: 'calc(90vh - 120px)' }}
              />
            </div>
          </div>
        </div>

        {/* Asset info footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-4">
              <span>
                <strong>Size:</strong> {Math.round((currentAsset.file_size || 0) / 1024)} KB
              </span>
              <span>
                <strong>Type:</strong> {currentAsset.file_type}
              </span>
              <span>
                <strong>Stage:</strong> {stageLabel}
              </span>
            </div>
            
            <div className="text-muted-foreground">
              Created: {new Date(currentAsset.created_at || '').toLocaleDateString()}
            </div>
          </div>
        </div>

        {/* Thumbnail strip for multiple assets */}
        {modalAssets.length > 1 && (
          <div className="p-4 border-t">
            <div className="flex gap-2 overflow-x-auto">
              {modalAssets.map((asset, index) => (
                <button
                  key={asset.id}
                  onClick={() => {
                    setCurrentIndex(index);
                    setZoom(1);
                  }}
                  className={`flex-shrink-0 w-16 h-16 rounded border-2 overflow-hidden ${
                    index === currentIndex ? 'border-primary' : 'border-gray-200'
                  }`}
                >
                  <img
                    src={getAssetUrlSafe(asset)}
                    alt={asset.file_name}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
