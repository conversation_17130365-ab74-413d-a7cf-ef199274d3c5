import { Asset } from '../../../components/common/types/assetTypes';

// Define Product type based on database schema
export interface Product {
  id: string;
  collection_id: string | null;
  name: string;
  sku: string | null;
  description: string | null;
  sizes: any | null;  // JSON type
  created_at: string | null;
  updated_at: string | null;
}

// Product with asset counts for filtering and selection
export interface ProductWithAssets extends Product {
  total_assets: number;
  upload_count: number;
  raw_ai_count: number;
  selected_count: number;
  upscale_count: number;
  retouch_count: number;
  final_count: number;
  // Parsed sizes from JSONB
  sizes: string[];
}

// Assets grouped by workflow stage
export interface AssetsByStage {
  upload: Asset[];
  raw_ai_images: Asset[];
  selected: Asset[];
  upscale: Asset[];
  retouch: Asset[];
  final: Asset[];
}

// Product filters for compare view
export interface ProductFilters {
  skuSearch: string;
  tagIds: string[];
  sizes: string[];
}

// Compare view state
export interface CompareState {
  // Current selection
  selectedProductId: string | null;
  selectedAssetIds: string[];

  // Filters (for product selection)
  productFilters: ProductFilters;

  // UI state
  isModalOpen: boolean;
  modalAssets: Asset[];

  // Data
  availableProducts: ProductWithAssets[];
  selectedProductAssets: AssetsByStage;

  // Loading states
  isLoadingProducts: boolean;
  isLoadingAssets: boolean;
}

// Actions for compare context
export type CompareAction =
  | { type: 'SET_SELECTED_PRODUCT'; payload: string | null }
  | { type: 'SET_PRODUCT_FILTERS'; payload: Partial<ProductFilters> }
  | { type: 'SET_SELECTED_ASSETS'; payload: string[] }
  | { type: 'TOGGLE_ASSET_SELECTION'; payload: string }
  | { type: 'OPEN_MODAL'; payload: Asset[] }
  | { type: 'CLOSE_MODAL' }
  | { type: 'SET_AVAILABLE_PRODUCTS'; payload: ProductWithAssets[] }
  | { type: 'SET_SELECTED_PRODUCT_ASSETS'; payload: AssetsByStage }
  | { type: 'SET_LOADING_PRODUCTS'; payload: boolean }
  | { type: 'SET_LOADING_ASSETS'; payload: boolean };

// Note: Workflow stage configuration is now imported from workflowStageUtils
// We no longer define WORKFLOW_STAGES here to avoid duplication

// Helper function to get empty assets by stage object
export function getEmptyAssetsByStage(): AssetsByStage {
  return {
    upload: [],
    raw_ai_images: [],
    selected: [],
    upscale: [],
    retouch: [],
    final: []
  };
}

// Helper function to group assets by workflow stage
export function groupAssetsByStage(assets: Asset[]): AssetsByStage {
  const grouped = getEmptyAssetsByStage();
  
  assets.forEach(asset => {
    if (asset.workflow_stage && grouped[asset.workflow_stage]) {
      grouped[asset.workflow_stage].push(asset);
    }
  });
  
  return grouped;
}
