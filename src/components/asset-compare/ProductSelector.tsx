import React, { useState } from 'react';
import { Check, ChevronsUpDown, Search } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '../ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../ui/popover';
import { cn } from '../../components/common/utils/utils';
import type { ProductWithAssets } from './types/compare.types';

interface ProductSelectorProps {
  products: ProductWithAssets[];
  selectedProductId: string | null;
  onProductSelect: (productId: string) => void;
  disabled?: boolean;
  className?: string;
}

export function ProductSelector({
  products,
  selectedProductId,
  onProductSelect,
  disabled = false,
  className,
}: ProductSelectorProps) {
  const [open, setOpen] = useState(false);
  
  const selectedProduct = products.find(p => p.id === selectedProductId);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          disabled={disabled}
          className={cn(
            "w-[300px] justify-between",
            className
          )}
        >
          <span className="truncate">
            {selectedProduct ? (
              <>
                {selectedProduct.sku && (
                  <span className="font-mono text-xs text-muted-foreground mr-2">
                    {selectedProduct.sku}
                  </span>
                )}
                {selectedProduct.name}
              </>
            ) : (
              <span className="text-muted-foreground">Select a product...</span>
            )}
          </span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput placeholder="Search products..." />
          <CommandList>
            <CommandEmpty>No products found.</CommandEmpty>
            <CommandGroup>
              {products.map((product) => (
                <CommandItem
                  key={product.id}
                  value={`${product.sku || ''} ${product.name}`.toLowerCase()}
                  onSelect={() => {
                    onProductSelect(product.id);
                    setOpen(false);
                  }}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedProductId === product.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      {product.sku && (
                        <span className="font-mono text-xs text-muted-foreground">
                          {product.sku}
                        </span>
                      )}
                      <span className="font-medium">{product.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground mt-0.5">
                      {product.total_assets} assets
                      {product.sizes && product.sizes.length > 0 && (
                        <span className="ml-2">• Sizes: {product.sizes.join(', ')}</span>
                      )}
                    </div>
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}