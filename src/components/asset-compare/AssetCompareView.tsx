import React, { useEffect, useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Eye, ChevronLeft, ChevronRight, Upload } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';
import { useOrganization } from '../../components/common/hooks/useOrganizations';
import { useCollection } from '../../components/common/hooks/useCollections';
import { useUserRole } from '../../contexts/UserRoleContext';
import { CompareProvider, useCompareState, useCompareContext } from './CompareContext';
import { useCompareData } from './hooks/useCompareData';
import { CompareModal } from './CompareModal';
import { ProductSelector } from './ProductSelector';
import { getAssetUrl } from '../../components/common/utils/utils';
import { useProductTags } from '../../components/common/hooks/useTags';
import { useAssetTags } from '../../components/common/hooks/useAssetTags';
import { getWorkflowStageConfigs } from '../../components/common/utils/workflowStageUtils';
import { useIsFreelancer } from '../../components/common/hooks/useIsFreelancer';
import type { Asset } from '../../components/common/types/assetTypes';
import * as Icons from 'lucide-react';

interface AssetCompareViewProps {
  className?: string;
}

// Inner component that uses the context
function AssetCompareViewContent(_props: AssetCompareViewProps) {
  const { orgId, collectionId } = useParams<{ orgId: string; collectionId: string }>();
  const navigate = useNavigate();
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get compare state and actions
  const { selectedProductId } = useCompareState();
  const { setSelectedProduct } = useCompareContext();

  // Local state for UI
  const [selectedStages, setSelectedStages] = useState<string[]>(['upload', 'final']);
  const [currentImageIndexes, setCurrentImageIndexes] = useState<Record<string, number>>({});
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedSizes, setSelectedSizes] = useState<string[]>([]);

  // Fetch organization and collection data
  const { data: organization, isLoading: isLoadingOrg } = useOrganization(orgId || '');
  const { data: collection, isLoading: isLoadingCollection } = useCollection(collectionId || '');

  // Fetch compare data (no filters needed now)
  const {
    availableProducts: fetchedProducts,
    selectedProductAssets: fetchedAssets,
    isLoadingProducts,
    error
  } = useCompareData(collectionId || '', selectedProductId);

  // Fetch tags for the current product only
  const { data: productTags, isLoading: isLoadingTags } = useProductTags(collectionId || undefined, selectedProductId || undefined);
  const { data: assetTagsMap } = useAssetTags(collectionId);

  // Use fetched data directly instead of syncing to context
  // This prevents infinite re-render loops
  const currentAvailableProducts = fetchedProducts || [];

  // Filter assets by selected tags
  const filterAssetsByTags = (assets: Asset[], selectedTagIds: string[]) => {
    if (selectedTagIds.length === 0) return assets;
    if (!assetTagsMap) return assets;

    return assets.filter(asset => {
      const assetTagIds = assetTagsMap[asset.id] || [];
      // Asset must have at least one of the selected tags
      return selectedTagIds.some(tagId => assetTagIds.includes(tagId));
    });
  };

  // Filter assets by selected sizes
  const filterAssetsBySizes = (assets: Asset[], selectedSizeList: string[]) => {
    if (selectedSizeList.length === 0) return assets;
    
    return assets.filter(asset => {
      // Check if asset has size in metadata
      if (!asset.metadata || typeof asset.metadata !== 'object') return false;
      
      const assetMetadata = asset.metadata as any;
      const assetSize = assetMetadata.size || assetMetadata.productSize;
      
      // If asset has no size info, exclude it when size filter is active
      if (!assetSize) return false;
      
      // Check if asset's size matches any selected size
      return selectedSizeList.includes(assetSize);
    });
  };

  // Apply tag and size filtering to assets
  const currentSelectedProductAssets = useMemo(() => {
    if (!fetchedAssets) return {};

    const filtered: Record<string, Asset[]> = {};
    Object.entries(fetchedAssets).forEach(([stageKey, assets]) => {
      // Apply tag filter first
      let filteredAssets = filterAssetsByTags(assets, selectedTags);
      // Then apply size filter
      filteredAssets = filterAssetsBySizes(filteredAssets, selectedSizes);
      filtered[stageKey] = filteredAssets;
    });

    return filtered;
  }, [fetchedAssets, selectedTags, selectedSizes, assetTagsMap, filterAssetsByTags, filterAssetsBySizes]);

  // Auto-select first product if none selected and products are available
  useEffect(() => {
    if (!selectedProductId && currentAvailableProducts.length > 0 && !isLoadingProducts) {
      setSelectedProduct(currentAvailableProducts[0].id);
    }
  }, [selectedProductId, currentAvailableProducts.length, isLoadingProducts, setSelectedProduct]);

  // Reset selected sizes when product changes
  useEffect(() => {
    setSelectedSizes([]);
  }, [selectedProductId]);

  // Helper functions
  const toggleStage = (stageId: string) => {
    if (selectedStages.includes(stageId)) {
      if (selectedStages.length > 1) {
        setSelectedStages(selectedStages.filter((id) => id !== stageId));
      }
    } else {
      if (selectedStages.length < 3) {
        setSelectedStages([...selectedStages, stageId]);
      }
    }
  };

  const getCurrentProduct = () => {
    return currentAvailableProducts.find(p => p.id === selectedProductId);
  };

  const getCurrentProductIndex = () => {
    return currentAvailableProducts.findIndex(p => p.id === selectedProductId);
  };

  const navigateProduct = (direction: 'prev' | 'next') => {
    const currentIndex = getCurrentProductIndex();
    if (currentIndex === -1) return;

    let newIndex;
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : currentAvailableProducts.length - 1;
    } else {
      newIndex = currentIndex < currentAvailableProducts.length - 1 ? currentIndex + 1 : 0;
    }

    setSelectedProduct(currentAvailableProducts[newIndex].id);
  };

  // Image navigation functions
  const navigateImage = (stageKey: string, direction: 'prev' | 'next', totalImages: number) => {
    setCurrentImageIndexes(prev => {
      const currentIndex = prev[stageKey] || 0;
      let newIndex;

      if (direction === 'prev') {
        newIndex = currentIndex > 0 ? currentIndex - 1 : totalImages - 1;
      } else {
        newIndex = currentIndex < totalImages - 1 ? currentIndex + 1 : 0;
      }

      return { ...prev, [stageKey]: newIndex };
    });
  };

  const getCurrentImageIndex = (stageKey: string) => {
    return currentImageIndexes[stageKey] || 0;
  };

  // Tag filtering functions
  const toggleTag = (tagId: string) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  // Handle navigation back to collection
  const handleBackToCollection = () => {
    navigate(`/organizations/${orgId}/collections/${collectionId}`);
  };

  // Get workflow stages based on user role and freelancer status - must be before any conditional returns
  const workflowStages = useMemo(() => {
    const configs = getWorkflowStageConfigs(userRole, isFreelancer);
    return configs.map(config => {
      const IconComponent = Icons[config.icon as keyof typeof Icons] || Icons.ImageIcon;
      const colorMap: Record<string, string> = {
        'blue': 'bg-blue-100 text-blue-800',
        'indigo': 'bg-indigo-100 text-indigo-800',
        'green': 'bg-green-100 text-green-800',
        'amber': 'bg-amber-100 text-amber-800',
        'emerald': 'bg-emerald-100 text-emerald-800',
        'purple': 'bg-purple-100 text-purple-800'
      };

      return {
        key: config.id,
        label: config.label,
        icon: IconComponent,
        color: colorMap[config.color] || 'bg-gray-100 text-gray-800',
        description: `Assets in ${config.label} stage`
      };
    });
  }, [userRole, isFreelancer]);

  // Loading state
  if (isLoadingOrg || isLoadingCollection) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBackToCollection}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Collection
          </Button>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-destructive mb-2">Error loading compare data</p>
            <p className="text-sm text-muted-foreground">{error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Error state
  if (!organization || !collection) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBackToCollection}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Collection
          </Button>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              {!organization ? 'Organization not found' : 'Collection not found'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentProduct = getCurrentProduct();
  const currentIndex = getCurrentProductIndex();
  const totalAssets = currentSelectedProductAssets ? Object.values(currentSelectedProductAssets).flat().length : 0;


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" className="gap-2" onClick={handleBackToCollection}>
              <ArrowLeft className="w-4 h-4" />
              Back to Collection
            </Button>
            <div>
              <h1 className="text-xl font-semibold">Asset Compare View</h1>
              <p className="text-sm text-gray-600">
                {organization?.name} • {collection?.name}
              </p>
            </div>
          </div>

          {/* Product Selection */}
          <div className="flex items-center gap-4">
            <ProductSelector
              products={currentAvailableProducts}
              selectedProductId={selectedProductId}
              onProductSelect={setSelectedProduct}
              disabled={isLoadingProducts || currentAvailableProducts.length === 0}
            />
            
            {selectedProductId && (
              <>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigateProduct('prev')}
                    disabled={currentAvailableProducts.length <= 1}
                    className="h-8 w-8"
                  >
                    <ChevronLeft className="w-4 h-4" />
                  </Button>
                  <span className="text-sm text-gray-600">
                    {currentIndex + 1} of {currentAvailableProducts.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => navigateProduct('next')}
                    disabled={currentAvailableProducts.length <= 1}
                    className="h-8 w-8"
                  >
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
                
                <span className="text-sm font-medium">{totalAssets} assets</span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-72 bg-white border-r">
          {/* Workflow Stages */}
          <div className="p-4">
            <div className="space-y-3">
              <h3 className="font-medium text-gray-900">Stages</h3>
              <div className="space-y-2">
                {workflowStages.map((stage) => {
                  const isSelected = selectedStages.includes(stage.key);
                  const assetCount = currentSelectedProductAssets[stage.key]?.length || 0;
                  const hasAssets = assetCount > 0;

                  // Determine stage status
                  let status = 'empty';
                  let statusColor = 'text-gray-400';
                  let statusText = 'empty';

                  if (hasAssets) {
                    if (stage.key === 'upload') {
                      status = 'completed';
                      statusColor = 'text-green-600';
                      statusText = 'completed';
                    } else if (stage.key === 'selected' || stage.key === 'raw_ai_images') {
                      status = 'in_progress';
                      statusColor = 'text-yellow-600';
                      statusText = 'in progress';
                    } else if (stage.key === 'upscale' || stage.key === 'retouch') {
                      status = 'pending';
                      statusColor = 'text-orange-600';
                      statusText = 'pending';
                    } else {
                      status = 'completed';
                      statusColor = 'text-green-600';
                      statusText = 'completed';
                    }
                  }

                  return (
                    <div
                      key={stage.key}
                      onClick={() => hasAssets && toggleStage(stage.key)}
                      className={`p-3 rounded-lg border cursor-pointer transition-all ${
                        isSelected
                          ? "bg-blue-50 border-blue-200 shadow-sm"
                          : hasAssets
                            ? "bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300"
                            : "bg-gray-50 border-gray-200 cursor-not-allowed opacity-60"
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          status === 'completed' ? 'bg-green-100' :
                          status === 'in_progress' ? 'bg-yellow-100' :
                          status === 'pending' ? 'bg-orange-100' :
                          'bg-gray-100'
                        }`}>
                          {(() => {
                            const Icon = stage.icon as React.FC<{ className?: string }>;
                            return <Icon className={`w-4 h-4 ${
                              status === 'completed' ? 'text-green-600' :
                              status === 'in_progress' ? 'text-yellow-600' :
                              status === 'pending' ? 'text-orange-600' :
                              'text-gray-400'
                            }`} />;
                          })()}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className={`font-medium text-sm ${isSelected ? "text-blue-900" : "text-gray-900"}`}>
                              {stage.label}
                            </span>
                            <div className="flex items-center gap-2">
                              <Badge variant="secondary" className="text-xs">
                                {assetCount}
                              </Badge>
                              {hasAssets && (
                                <Eye className="w-4 h-4 text-gray-400" />
                              )}
                            </div>
                          </div>
                          <p className={`text-xs ${statusColor} mt-1`}>
                            {assetCount} assets • {statusText}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="p-4 border-t">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">Tags</h3>
                <Button variant="ghost" size="sm" className="text-xs h-6 px-2">
                  + Add
                </Button>
              </div>
              {isLoadingTags ? (
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3].map((i) => (
                    <Skeleton key={i} className="h-6 w-16" />
                  ))}
                </div>
              ) : productTags && productTags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {productTags.map((tag) => {
                    const isSelected = selectedTags.includes(tag.id);
                    return (
                      <Badge
                        key={tag.id}
                        variant={isSelected ? "default" : "secondary"}
                        className={`text-xs cursor-pointer transition-colors ${
                          isSelected
                            ? "bg-blue-600 hover:bg-blue-700 text-white"
                            : "hover:bg-gray-200"
                        }`}
                        onClick={() => toggleTag(tag.id)}
                      >
                        {tag.name}
                      </Badge>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-4">
                  <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-xs text-gray-500">No tags available</p>
                  <Button variant="link" size="sm" className="text-xs text-blue-600">
                    Add your first tag
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Size Filter */}
          <div className="p-4 border-t">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">Filter by Size</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs h-6 px-2"
                  onClick={() => {
                    const standardSizes = ['XS', 'S', 'M', 'L', 'XL'];
                    if (selectedSizes.length === standardSizes.length) {
                      // Clear all if all are selected
                      setSelectedSizes([]);
                    } else {
                      // Select all sizes
                      setSelectedSizes(standardSizes);
                    }
                  }}
                >
                  {selectedSizes.length === 5 ? 'Clear All' : 'Select All'}
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {['XS', 'S', 'M', 'L', 'XL'].map((size) => {
                  const isSelected = selectedSizes.includes(size);
                  return (
                    <Button
                      key={size}
                      variant={isSelected ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (isSelected) {
                          setSelectedSizes(selectedSizes.filter(s => s !== size));
                        } else {
                          setSelectedSizes([...selectedSizes, size]);
                        }
                      }}
                      className={`h-8 px-3 ${
                        isSelected 
                          ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                          : 'hover:bg-gray-100'
                      }`}
                    >
                      {size}
                    </Button>
                  );
                })}
              </div>
              
              {selectedSizes.length > 0 && (
                <div className="text-xs text-gray-600 space-y-1">
                  <div>
                    {selectedSizes.length} of 5 sizes selected
                  </div>
                  <div className="text-gray-500">
                    Showing assets for: {selectedSizes.join(', ')}
                  </div>
                  <div className="text-amber-600 font-medium">
                    Note: Only assets with size metadata will be shown
                  </div>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Main Content */}
        <div className="flex-1 bg-gray-50">
          {/* Workflow Stages Header */}
          <div className="bg-white border-b px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Workflow Stages</h2>
                <p className="text-sm text-gray-600">
                  Comparing {selectedStages.length} stages • {totalAssets} total assets
                </p>
              </div>
            </div>
          </div>

          {/* Stages Grid */}
          <div className="p-6">

            {selectedProductId && selectedStages.length > 0 ? (
              <div className="grid gap-6" style={{ gridTemplateColumns: `repeat(${Math.max(selectedStages.length, 2)}, 1fr)` }}>
                {selectedStages.map((stageKey) => {
                  const stage = workflowStages.find(s => s.key === stageKey);
                  const assets = currentSelectedProductAssets[stageKey] || [];
                  if (!stage) return null;

                  return (
                    <div key={stageKey} className="space-y-4">
                      {/* Stage Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                            {(() => {
                              const Icon = stage.icon as React.FC<{ className?: string }>;
                              return <Icon className="w-4 h-4 text-green-600" />;
                            })()}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">{stage.label}</h3>
                            <p className="text-sm text-gray-600">{assets.length} assets</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {assets.length}
                          </Badge>
                          <Eye className="w-4 h-4 text-gray-400" />
                        </div>
                      </div>

                      {/* Stage Content */}
                      <Card className="bg-white border-0 shadow-sm">
                        <CardContent className="p-0">
                          {assets.length > 0 ? (
                            <div className="relative aspect-[4/5] bg-gray-100 rounded-lg overflow-hidden">
                              {(() => {
                                const currentIndex = getCurrentImageIndex(stageKey);
                                const asset = assets[currentIndex];
                                const imageUrl = getAssetUrl({
                                  id: asset.id,
                                  collection_id: asset.collection_id,
                                  file_path: asset.file_path,
                                  compressed_path: asset.compressed_path || undefined,
                                  original_path: asset.original_path || undefined,
                                  thumbnail_path: asset.thumbnail_path || undefined
                                }, false); // false = prefer compressed

                                // Get size from asset metadata
                                const assetSize = asset.metadata && typeof asset.metadata === 'object' 
                                  ? (asset.metadata as any).size 
                                  : null;

                                return (
                                  <>
                                    <img
                                      src={imageUrl || '/placeholder.svg'}
                                      alt={`${stage.label} stage - ${currentIndex + 1} of ${assets.length}`}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = '/placeholder.svg';
                                      }}
                                    />
                                    {/* Size Badge */}
                                    {assetSize && (
                                      <div className="absolute top-4 right-4">
                                        <Badge className="bg-black/60 text-white border-0">
                                          {assetSize}
                                        </Badge>
                                      </div>
                                    )}
                                  </>
                                );
                              })()}

                              {/* Image Navigation */}
                              {assets.length > 1 && (
                                <>
                                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                                    <div className="flex items-center gap-2 px-3 py-1 bg-black/60 rounded-full text-white text-xs">
                                      <span>{getCurrentImageIndex(stageKey) + 1} / {assets.length}</span>
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/80 hover:bg-white"
                                    onClick={() => navigateImage(stageKey, 'prev', assets.length)}
                                  >
                                    <ChevronLeft className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/80 hover:bg-white"
                                    onClick={() => navigateImage(stageKey, 'next', assets.length)}
                                  >
                                    <ChevronRight className="w-4 h-4" />
                                  </Button>
                                </>
                              )}
                            </div>
                          ) : (
                            <div className="aspect-[4/5] bg-gray-100 rounded-lg flex flex-col items-center justify-center text-gray-500">
                              <Upload className="w-12 h-12 mb-3 text-gray-400" />
                              <p className="text-sm font-medium">No assets yet</p>
                              <p className="text-xs text-gray-400 mt-1">Assets will appear here once they reach the {stage.label} stage</p>
                              <Button variant="outline" size="sm" className="mt-3">
                                + Add Asset
                              </Button>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      {/* Thumbnail Strip */}
                      {assets.length > 1 && (
                        <div className="flex gap-2 overflow-x-auto pb-2">
                          {assets.slice(0, 5).map((asset, index) => (
                            <div
                              key={asset.id}
                              className={`flex-shrink-0 w-12 h-12 rounded border-2 cursor-pointer transition-all ${
                                getCurrentImageIndex(stageKey) === index
                                  ? 'border-blue-500 shadow-sm'
                                  : 'border-gray-200 hover:border-gray-300'
                              }`}
                              onClick={() => setCurrentImageIndexes(prev => ({ ...prev, [stageKey]: index }))}
                            >
                              <img
                                src={getAssetUrl({
                                  id: asset.id,
                                  collection_id: asset.collection_id,
                                  file_path: asset.file_path,
                                  compressed_path: asset.compressed_path || undefined,
                                  original_path: asset.original_path || undefined,
                                  thumbnail_path: asset.thumbnail_path || undefined
                                }, true) || '/placeholder.svg'}
                                alt={`Thumbnail ${index + 1}`}
                                className="w-full h-full object-cover rounded"
                              />
                            </div>
                          ))}
                          {assets.length > 5 && (
                            <div className="flex-shrink-0 w-12 h-12 rounded border-2 border-gray-200 bg-gray-100 flex items-center justify-center text-xs text-gray-500">
                              +{assets.length - 5}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
                {/* Add empty columns to maintain minimum of 2 columns */}
                {selectedStages.length < 2 && Array.from({ length: 2 - selectedStages.length }).map((_, index) => (
                  <div key={`empty-${index}`} className="space-y-4 opacity-30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                          <Upload className="w-4 h-4 text-gray-400" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-500">Empty Slot</h3>
                          <p className="text-sm text-gray-400">No stage selected</p>
                        </div>
                      </div>
                    </div>
                    <Card className="bg-white border-0 shadow-sm">
                      <CardContent className="p-0">
                        <div className="aspect-[4/5] bg-gray-100 rounded-lg flex flex-col items-center justify-center text-gray-400">
                          <Upload className="w-12 h-12 mb-3" />
                          <p className="text-sm font-medium">No stage selected</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <Upload className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {!selectedProductId ? 'Select a Product' : 'Select Workflow Stages'}
                  </h3>
                  <p className="text-gray-600">
                    {!selectedProductId
                      ? 'Choose a product from the navigation above to view its assets across workflow stages.'
                      : 'Select workflow stages from the sidebar to compare assets side by side.'
                    }
                  </p>
                </div>
              </div>
            )}

          </div>

        </div>
      </div>

      {/* Compare Modal */}
      <CompareModal />
    </div>
  );
}

// Main component with provider
export function AssetCompareView({ className }: AssetCompareViewProps) {
  return (
    <CompareProvider>
      <AssetCompareViewContent className={className} />
    </CompareProvider>
  );
}
