import { useEffect, useState } from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { TagIcon, Layers, Download, Trash, X, Package, Ruler } from 'lucide-react';
import { cn } from '../common/utils/utils';

interface FloatingActionBarProps {
  selectedCount: number;
  onManageTags?: () => void;
  onManageWorkflowStages?: () => void;
  onManageProducts?: () => void;
  onManageSizes?: () => void;
  onDownload?: () => void;
  onDelete?: () => void;
  onClearSelection?: () => void;
  isDeleting?: boolean;
  isDownloading?: boolean;
}

export function FloatingActionBar({
  selectedCount,
  onManageTags,
  onManageWorkflowStages,
  onManageProducts,
  onManageSizes,
  onDownload,
  onDelete,
  onClearSelection,
  isDeleting = false,
  isDownloading = false
}: FloatingActionBarProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(selectedCount > 0);
  }, [selectedCount]);

  if (!isVisible && selectedCount === 0) return null;

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 z-50 transition-all duration-300 ease-in-out",
        isVisible ? "translate-y-0" : "translate-y-full"
      )}
    >
      <div className="bg-background/95 backdrop-blur-lg border-t shadow-lg">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Selection info */}
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="text-sm font-medium">
                {selectedCount} selected
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={onManageTags}
                className="hidden sm:flex items-center gap-1"
              >
                <TagIcon className="h-4 w-4" />
                <span className="hidden md:inline">Manage Tags</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onManageTags}
                className="sm:hidden"
              >
                <TagIcon className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={onManageWorkflowStages}
                className="hidden sm:flex items-center gap-1"
              >
                <Layers className="h-4 w-4" />
                <span className="hidden md:inline">Workflow Stage</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onManageWorkflowStages}
                className="sm:hidden"
              >
                <Layers className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={onManageProducts}
                className="hidden sm:flex items-center gap-1"
              >
                <Package className="h-4 w-4" />
                <span className="hidden md:inline">Product</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onManageProducts}
                className="sm:hidden"
              >
                <Package className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={onManageSizes}
                className="hidden sm:flex items-center gap-1"
              >
                <Ruler className="h-4 w-4" />
                <span className="hidden md:inline">Size</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onManageSizes}
                className="sm:hidden"
              >
                <Ruler className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={onDownload}
                disabled={isDownloading}
                className="hidden sm:flex items-center gap-1"
              >
                <Download className="h-4 w-4" />
                <span className="hidden md:inline">
                  {isDownloading ? 'Downloading...' : 'Download'}
                </span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onDownload}
                disabled={isDownloading}
                className="sm:hidden"
              >
                <Download className="h-4 w-4" />
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={onDelete}
                disabled={isDeleting}
                className="hidden sm:flex items-center gap-1 text-destructive hover:text-destructive"
              >
                <Trash className="h-4 w-4" />
                <span className="hidden md:inline">
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={onDelete}
                disabled={isDeleting}
                className="sm:hidden text-destructive hover:text-destructive"
              >
                <Trash className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FloatingActionBar;