import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>Header, 
  <PERSON><PERSON><PERSON><PERSON>le, 
  DialogFooter 
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { X, Plus, Minus, Check, Tag as TagIcon, Search } from 'lucide-react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../common/hooks/use-toast';
import { Tag } from '../common/types/database.types';
import { Separator } from '../ui/separator';
import { useQueryClient } from '@tanstack/react-query';
import { cn } from '../common/utils/utils';

interface BulkTagManagerProps {
  assetIds: string[];
  collectionId?: string;
  isOpen: boolean;
  onClose: () => void;
  onTagsUpdated?: () => void;
}

// Tag state for each tag
type TagState = {
  tag: Tag;
  assetCount: number; // How many assets have this tag
  action: 'add' | 'remove' | 'none'; // What action to perform
};

export function BulkTagManager({ assetIds, collectionId, isOpen, onClose, onTagsUpdated }: BulkTagManagerProps) {
  const [tagStates, setTagStates] = useState<TagState[]>([]);
  const [tagsByCategory, setTagsByCategory] = useState<Record<string, TagState[]>>({});
  const [newTagName, setNewTagName] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const queryClient = useQueryClient();

  // Fetch all tags and asset-tag relationships
  useEffect(() => {
    const fetchTags = async () => {
      if (!isOpen || assetIds.length === 0) return;
      
      setIsLoading(true);
      try {
        // Fetch all tags
        const { data: allTags, error: tagsError } = await supabase
          .from('tags')
          .select('*')
          .order('name');
        
        if (tagsError) throw tagsError;
        
        // Fetch tags for all selected assets
        const { data: assetTagsData, error: assetTagsError } = await supabase
          .from('asset_tags')
          .select('asset_id, tag_id')
          .in('asset_id', assetIds);
        
        if (assetTagsError) throw assetTagsError;
        
        // Count how many assets have each tag
        const tagCounts: Record<string, number> = {};
        assetTagsData?.forEach(item => {
          tagCounts[item.tag_id] = (tagCounts[item.tag_id] || 0) + 1;
        });
        
        // Create tag states
        const states: TagState[] = allTags.map(tag => ({
          tag,
          assetCount: tagCounts[tag.id] || 0,
          action: 'none'
        }));
        
        // Group by category
        const byCategory: Record<string, TagState[]> = {};
        states.forEach(state => {
          const category = state.tag.category || 'other';
          if (!byCategory[category]) {
            byCategory[category] = [];
          }
          byCategory[category].push(state);
        });
        
        setTagStates(states);
        setTagsByCategory(byCategory);
      } catch (error: any) {
        console.error('Error fetching tags:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tags',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTags();
  }, [assetIds, isOpen, toast]);

  // Toggle tag action (add, remove, none)
  const toggleTagAction = (tagId: string) => {
    setTagStates(prev => 
      prev.map(state => {
        if (state.tag.id !== tagId) return state;
        
        // Cycle through actions:
        // If no assets have it -> add -> none
        // If all assets have it -> remove -> none
        // If some assets have it -> add -> remove -> none
        
        const hasAll = state.assetCount === assetIds.length;
        const hasNone = state.assetCount === 0;
        const hasSome = !hasAll && !hasNone;
        
        if (state.action === 'none') {
          if (hasAll) return { ...state, action: 'remove' };
          return { ...state, action: 'add' };
        } else if (state.action === 'add') {
          return hasSome 
            ? { ...state, action: 'remove' } 
            : { ...state, action: 'none' };
        } else { // action === 'remove'
          return { ...state, action: 'none' };
        }
      })
    );
  };

  // Create a new tag
  const createTag = async () => {
    if (!newTagName.trim()) return;
    
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert({
          name: newTagName.trim(),
          category: 'collection'
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Add to tag states
      const newState: TagState = {
        tag: data,
        assetCount: 0,
        action: 'add' // Automatically set to add
      };
      
      setTagStates(prev => [...prev, newState]);
      
      // Add to category grouping
      setTagsByCategory(prev => {
        const category = data.category || 'collection';
        return {
          ...prev,
          [category]: [...(prev[category] || []), newState]
        };
      });
      
      // Clear input
      setNewTagName('');
      
      toast({
        title: 'Tag Created',
        description: `Successfully created tag "${data.name}"`,
      });
      
      // Invalidate tags query to refresh everywhere (especially sidebar)
      queryClient.invalidateQueries({ queryKey: ['tags'] });
      if (collectionId) {
        queryClient.invalidateQueries({ queryKey: ['collection-tags', collectionId] });
      }
    } catch (error: any) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: `Failed to create tag: ${error.message}`,
        variant: 'destructive'
      });
    }
  };

  // Apply tag changes
  const applyChanges = async () => {
    if (assetIds.length === 0) return;
    
    const tagsToAdd = tagStates.filter(s => s.action === 'add').map(s => s.tag.id);
    const tagsToRemove = tagStates.filter(s => s.action === 'remove').map(s => s.tag.id);
    
    if (tagsToAdd.length === 0 && tagsToRemove.length === 0) {
      toast({
        title: 'No Changes',
        description: 'No tag changes were selected',
      });
      return;
    }
    
    setIsSaving(true);
    try {
      // Handle tag removal
      if (tagsToRemove.length > 0) {
        const { error: removeError } = await supabase
          .from('asset_tags')
          .delete()
          .in('asset_id', assetIds)
          .in('tag_id', tagsToRemove);
        
        if (removeError) throw removeError;
      }
      
      // Handle tag addition
      if (tagsToAdd.length > 0) {
        // First check which asset-tag pairs already exist to avoid duplicates
        const { data: existingPairs, error: checkError } = await supabase
          .from('asset_tags')
          .select('asset_id, tag_id')
          .in('asset_id', assetIds)
          .in('tag_id', tagsToAdd);
        
        if (checkError) throw checkError;
        
        // Create a set of existing pairs
        const existingPairsSet = new Set();
        existingPairs?.forEach(pair => {
          existingPairsSet.add(`${pair.asset_id}-${pair.tag_id}`);
        });
        
        // Create pairs to insert
        const tagsToInsert: { asset_id: string; tag_id: string }[] = [];
        
        for (const assetId of assetIds) {
          for (const tagId of tagsToAdd) {
            const pairKey = `${assetId}-${tagId}`;
            if (!existingPairsSet.has(pairKey)) {
              tagsToInsert.push({
                asset_id: assetId,
                tag_id: tagId
              });
            }
          }
        }
        
        if (tagsToInsert.length > 0) {
          const { error: insertError } = await supabase
            .from('asset_tags')
            .insert(tagsToInsert);
          
          if (insertError) throw insertError;
        }
      }
      
      // Success message
      const addCount = tagsToAdd.length;
      const removeCount = tagsToRemove.length;
      let message = '';
      
      if (addCount > 0 && removeCount > 0) {
        message = `Added ${addCount} and removed ${removeCount} tags`;
      } else if (addCount > 0) {
        message = `Added ${addCount} tags to ${assetIds.length} assets`;
      } else {
        message = `Removed ${removeCount} tags from ${assetIds.length} assets`;
      }
      
      toast({
        title: 'Tags Updated',
        description: message,
      });
      
      // Debug logging - commented out for production
      // if (import.meta.env.PROD) {
      //   console.log('[BulkTagManager] Starting cache invalidation...');
      // }
      
      // Invalidate queries to refresh data
      await queryClient.invalidateQueries({ queryKey: ['assetTags'] });
      await queryClient.invalidateQueries({ queryKey: ['tags'] });
      await queryClient.invalidateQueries({ queryKey: ['assetCounts'] });
      if (collectionId) {
        await queryClient.invalidateQueries({ queryKey: ['collection-tags', collectionId] });
      }
      
      // Force refetch all invalidated queries
      await queryClient.refetchQueries({ queryKey: ['assetTags'] });
      await queryClient.refetchQueries({ queryKey: ['tags'] });
      await queryClient.refetchQueries({ queryKey: ['assetCounts'] });
      if (collectionId) {
        await queryClient.refetchQueries({ queryKey: ['collection-tags', collectionId] });
      }
      
      // if (import.meta.env.PROD) {
      //   console.log('[BulkTagManager] Cache invalidation completed');
      // }
      
      // Notify parent component
      if (onTagsUpdated) onTagsUpdated();
      
      // Close dialog
      onClose();
    } catch (error: any) {
      console.error('Error updating tags:', error);
      toast({
        title: 'Error',
        description: `Failed to update tags: ${error.message}`,
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Get tag color based on category
  const getTagColor = (category: string) => {
    switch (category) {
      case 'view_type':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'workflow_stage':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'product_specific':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'custom':
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Get friendly category name
  const getCategoryName = (category: string) => {
    switch (category) {
      case 'view_type': return 'View Types';
      case 'workflow_stage': return 'Workflow Stages';
      case 'product_specific': return 'Product Tags';
      case 'custom': return 'Custom Tags';
      default: return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  // Get action icon
  const getActionIcon = (state: TagState) => {
    if (state.action === 'add') return <Plus className="h-3 w-3" />;
    if (state.action === 'remove') return <Minus className="h-3 w-3" />;

    // If no action but all assets have this tag
    if (state.assetCount === assetIds.length) return <Check className="h-3 w-3 text-green-600" />;

    return null;
  };

  // Filter tags based on search query
  const getFilteredTagsByCategory = () => {
    if (!searchQuery.trim()) return tagsByCategory;
    
    const filtered: Record<string, TagState[]> = {};
    Object.entries(tagsByCategory).forEach(([category, states]) => {
      const filteredStates = states.filter(state => 
        state.tag.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      if (filteredStates.length > 0) {
        filtered[category] = filteredStates;
      }
    });
    return filtered;
  };

  const filteredTagsByCategory = getFilteredTagsByCategory();
  const hasAnyTags = Object.keys(filteredTagsByCategory).length > 0;
  const pendingChanges = tagStates.filter(s => s.action !== 'none');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl h-[85vh] grid grid-rows-[auto_1fr_auto] gap-0 p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle>Tag Management for {assetIds.length} Assets</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center p-6">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mb-4"></div>
              <p className="text-sm text-muted-foreground">Loading tags...</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-rows-[auto_auto_1fr] gap-0 overflow-hidden">
            {/* Fixed top section */}
            <div className="px-6 py-4 space-y-4 border-b">
              {/* Create New Tag */}
              <div>
                <Label className="mb-2 block text-sm font-medium">Create New Tag</Label>
                <div className="flex gap-2">
                  <Input
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                    placeholder="Enter new tag name"
                    className="flex-1"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && newTagName.trim()) {
                        createTag();
                      }
                    }}
                  />
                  <Button
                    onClick={createTag}
                    disabled={!newTagName.trim()}
                    size="sm"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Create
                  </Button>
                </div>
              </div>

              {/* Search Tags */}
              <div>
                <Label className="mb-2 block text-sm font-medium">Search Tags</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search for tags..."
                    className="pl-9"
                  />
                </div>
              </div>
            </div>

            {/* Pending changes section */}
            {pendingChanges.length > 0 && (
              <div className="px-6 py-3 bg-muted/50 border-b">
                <Label className="mb-2 block text-sm font-medium">Pending Changes ({pendingChanges.length})</Label>
                <div className="flex flex-wrap gap-2">
                  {pendingChanges.map(state => (
                    <Badge
                      key={state.tag.id}
                      variant="outline"
                      className={cn(
                        "px-2 py-1 flex items-center gap-1",
                        state.action === 'add' ? 'border-green-500 bg-green-50 text-green-700' : 'border-red-500 bg-red-50 text-red-700'
                      )}
                    >
                      {state.action === 'add' ? <Plus className="h-3 w-3" /> : <Minus className="h-3 w-3" />}
                      {state.tag.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 ml-1 rounded-full hover:bg-black/10"
                        onClick={() => toggleTagAction(state.tag.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Scrollable tags section */}
            <div className="overflow-y-auto px-6 py-4">
              {hasAnyTags ? (
                <div className="space-y-6">
                  {Object.entries(filteredTagsByCategory).map(([category, states]) => (
                    <div key={category}>
                      <div className="sticky top-0 bg-background z-10 pb-2">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-semibold text-muted-foreground">
                            {getCategoryName(category)}
                          </h3>
                          <span className="text-xs text-muted-foreground">
                            {states.length} {states.length === 1 ? 'tag' : 'tags'}
                          </span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {states.map(state => (
                          <Badge
                            key={state.tag.id}
                            variant={state.action !== 'none' ? 'secondary' : 'outline'}
                            className={cn(
                              "px-3 py-1.5 cursor-pointer hover:shadow-sm transition-all duration-200",
                              getTagColor(state.tag.category),
                              state.action === 'add' && 'ring-2 ring-green-500 bg-green-50',
                              state.action === 'remove' && 'ring-2 ring-red-500 bg-red-50'
                            )}
                            onClick={() => toggleTagAction(state.tag.id)}
                          >
                            <span className="flex items-center gap-1.5">
                              <TagIcon className="h-3 w-3" />
                              <span className="font-medium">{state.tag.name}</span>
                              <span className="text-xs opacity-60">
                                ({state.assetCount}/{assetIds.length})
                              </span>
                              {getActionIcon(state) && (
                                <span className="ml-1">
                                  {getActionIcon(state)}
                                </span>
                              )}
                            </span>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchQuery ? (
                <div className="text-center py-8">
                  <p className="text-sm text-muted-foreground">
                    No tags found matching "{searchQuery}"
                  </p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-sm text-muted-foreground">
                    No tags available. Create your first tag above.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        
        <DialogFooter className="px-6 py-4 border-t">
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button 
            onClick={applyChanges} 
            disabled={isLoading || isSaving || pendingChanges.length === 0}
          >
            {isSaving ? 'Saving...' : `Apply Changes (${pendingChanges.length})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default BulkTagManager; 