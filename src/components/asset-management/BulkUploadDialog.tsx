/**
 * Bulk Upload Dialog Component
 * Handles ZIP file uploads with workflow stage organization
 */

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileArchive, AlertCircle, CheckCircle, X, Download } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Separator } from '../ui/separator';
import { 
  processBulkUpload, 
  validateZipFile, 
  type BulkUploadResult, 
  type BulkUploadProgress 
} from '../common/utils/bulkUploadProcessor';
import { formatFileSize } from '../common/utils/utils';

interface BulkUploadDialogProps {
  collectionId: string;
  onUploadComplete?: (result: BulkUploadResult) => void;
  trigger?: React.ReactNode;
}

interface UploadState {
  file: File | null;
  isUploading: boolean;
  progress: BulkUploadProgress | null;
  result: BulkUploadResult | null;
  error: string | null;
  warnings: string[];
}

export function BulkUploadDialog({ 
  collectionId, 
  onUploadComplete, 
  trigger 
}: BulkUploadDialogProps) {
  const [open, setOpen] = useState(false);
  const [uploadState, setUploadState] = useState<UploadState>({
    file: null,
    isUploading: false,
    progress: null,
    result: null,
    error: null,
    warnings: []
  });

  // Reset state when dialog opens/closes
  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen) {
      setUploadState({
        file: null,
        isUploading: false,
        progress: null,
        result: null,
        error: null,
        warnings: []
      });
    }
  }, []);

  // Handle file drop/selection
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    const file = acceptedFiles[0];
    const validation = validateZipFile(file);

    if (!validation.valid) {
      setUploadState(prev => ({
        ...prev,
        error: validation.errors.join(', ')
      }));
      return;
    }

    setUploadState(prev => ({
      ...prev,
      file,
      error: null,
      warnings: validation.warnings || []
    }));
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/zip': ['.zip'],
      'application/x-zip-compressed': ['.zip']
    },
    maxFiles: 1,
    disabled: uploadState.isUploading
  });

  // Start upload process
  const handleUpload = async () => {
    if (!uploadState.file) return;

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: null,
      progress: null,
      result: null
    }));

    try {
      const result = await processBulkUpload(
        uploadState.file,
        collectionId,
        (progress) => {
          setUploadState(prev => ({
            ...prev,
            progress
          }));
        }
      );

      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        result,
        progress: null
      }));

      onUploadComplete?.(result);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
        progress: null
      }));
    }
  };

  // Download error report
  const downloadErrorReport = () => {
    if (!uploadState.result?.failed.length) return;

    const report = {
      uploadId: uploadState.result.uploadId,
      timestamp: new Date().toISOString(),
      collection: collectionId,
      summary: {
        total: uploadState.result.totalFiles,
        successful: uploadState.result.successful.length,
        failed: uploadState.result.failed.length
      },
      errors: uploadState.result.failed
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `bulk-upload-errors-${uploadState.result.uploadId}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const getProgressPercentage = () => {
    if (!uploadState.progress) return 0;
    return Math.round((uploadState.progress.processed / uploadState.progress.total) * 100);
  };

  const getStageLabel = (stage: string) => {
    switch (stage) {
      case 'extracting': return 'Extracting ZIP file...';
      case 'validating': return 'Validating file structure...';
      case 'processing': return 'Processing images...';
      case 'uploading': return 'Uploading assets...';
      case 'complete': return 'Upload completed!';
      case 'error': return 'Upload failed';
      default: return 'Processing...';
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="gap-2">
            <Upload className="h-4 w-4" />
            Bulk Upload
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileArchive className="h-5 w-5" />
            Bulk Upload Images
          </DialogTitle>
          <DialogDescription>
            Upload a ZIP file containing images organized by workflow stage
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Instructions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">ZIP File Structure</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <div className="font-mono text-xs bg-muted p-3 rounded">
                {`/input_assets/
  ├── 5713732546603_styling_001.png
  └── 5713732546603_styling_002.jpg
/raw/
  ├── 5713732546603_raw_M_front_001.jpg
  └── 5713732546603_refined_M_front_001.jpg
/upscaled/
  ├── 5713732546603_upscaled_M_front.jpg
  └── 5713732546603_upscaled_M_back.jpg
/retouched/
  ├── 5713732546603_retouched_M_front_v1.jpg
  └── 5713732546603_retouched_M_front_v2.jpg
/final/
  ├── 5713732546603_M_front.jpg
  └── 5713732546603_M_back.jpg`}
              </div>
              <p className="text-muted-foreground">
                Products will be automatically created based on SKU in filename.
                Supported formats: JPG, PNG, WebP. Max file size: 500MB.
              </p>
            </CardContent>
          </Card>

          {/* Drop Zone */}
          {!uploadState.result && (
            <div
              {...getRootProps()}
              className={`
                border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
                ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
                ${uploadState.isUploading ? 'pointer-events-none opacity-50' : 'hover:border-primary hover:bg-primary/5'}
              `}
            >
              <input {...getInputProps()} />
              <div className="space-y-4">
                <FileArchive className="h-12 w-12 mx-auto text-muted-foreground" />
                <div>
                  <p className="text-lg font-medium">
                    {isDragActive ? 'Drop ZIP file here' : 'Click or drag ZIP file to upload'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Maximum file size: 500MB
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Selected File Info */}
          {uploadState.file && !uploadState.result && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <FileArchive className="h-8 w-8 text-primary" />
                    <div>
                      <p className="font-medium">{uploadState.file.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(uploadState.file.size)}
                      </p>
                    </div>
                  </div>
                  {!uploadState.isUploading && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setUploadState(prev => ({ ...prev, file: null }))}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {uploadState.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{uploadState.error}</AlertDescription>
            </Alert>
          )}

          {/* Progress Display */}
          {uploadState.isUploading && uploadState.progress && (
            <Card>
              <CardContent className="pt-6 space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{getStageLabel(uploadState.progress.stage)}</span>
                    <span>{uploadState.progress.processed} / {uploadState.progress.total}</span>
                  </div>
                  <Progress value={getProgressPercentage()} className="h-2" />
                </div>
                
                {uploadState.progress.currentFile && (
                  <p className="text-sm text-muted-foreground">
                    Processing: {uploadState.progress.currentFile}
                  </p>
                )}

                {uploadState.progress.eta && (
                  <p className="text-sm text-muted-foreground">
                    Estimated time remaining: {uploadState.progress.eta}s
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Results Display */}
          {uploadState.result && (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <CardTitle>Upload Complete</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {uploadState.result.successful.length}
                    </p>
                    <p className="text-sm text-muted-foreground">Successful</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-red-600">
                      {uploadState.result.failed.length}
                    </p>
                    <p className="text-sm text-muted-foreground">Failed</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold">
                      {uploadState.result.stats.createdProducts}
                    </p>
                    <p className="text-sm text-muted-foreground">Products Created</p>
                  </div>
                </div>

                <Separator />

                <div className="text-sm text-muted-foreground">
                  <p>Processing time: {(uploadState.result.stats.processingTime / 1000).toFixed(1)}s</p>
                  <p>Average per file: {(uploadState.result.stats.processingTime / uploadState.result.totalFiles / 1000).toFixed(1)}s</p>
                </div>

                {uploadState.result.failed.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <p className="font-medium text-red-600">Failed Files:</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadErrorReport}
                        className="gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Download Report
                      </Button>
                    </div>
                    <div className="max-h-32 overflow-y-auto space-y-1">
                      {uploadState.result.failed.slice(0, 5).map((error, index) => (
                        <div key={index} className="text-sm p-2 bg-red-50 rounded">
                          <p className="font-medium">{error.fileName}</p>
                          <p className="text-red-600">{error.error}</p>
                        </div>
                      ))}
                      {uploadState.result.failed.length > 5 && (
                        <p className="text-sm text-muted-foreground">
                          ...and {uploadState.result.failed.length - 5} more errors
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => handleOpenChange(false)}>
              {uploadState.result ? 'Close' : 'Cancel'}
            </Button>
            
            <div className="flex gap-2">
              {uploadState.result && (
                <Button
                  onClick={() => {
                    // Reset for new upload
                    setUploadState({
                      file: null,
                      isUploading: false,
                      progress: null,
                      result: null,
                      error: null
                    });
                  }}
                >
                  Upload Another
                </Button>
              )}
              
              {uploadState.file && !uploadState.isUploading && !uploadState.result && (
                <Button onClick={handleUpload} className="gap-2">
                  <Upload className="h-4 w-4" />
                  Start Upload
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}