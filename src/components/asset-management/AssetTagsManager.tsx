import { useState, useEffect } from 'react';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { X, Search, ChevronDown, ChevronUp } from 'lucide-react';
import { supabase } from '../common/utils/supabase';
import { useToast } from '../common/hooks/use-toast';
import { Tag, Asset } from '../common/types/database.types';

interface AssetTagsManagerProps {
  asset: Asset;
  onTagsUpdated?: () => void;
}

export function AssetTagsManager({ asset, onTagsUpdated }: AssetTagsManagerProps) {
  const [tags, setTags] = useState<Tag[]>([]);
  const [assetTags, setAssetTags] = useState<Tag[]>([]);
  const [newTag, setNewTag] = useState('');
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const [showAllAvailableTags, setShowAllAvailableTags] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Fetch all available tags
  useEffect(() => {
    const fetchTags = async () => {
      setIsLoading(true);
      try {
        // Fetch all tags
        const { data: allTags, error: tagsError } = await supabase
          .from('tags')
          .select('*')
          .order('name');
        
        if (tagsError) throw tagsError;
        
        // Fetch tags for this asset
        const { data: assetTagsData, error: assetTagsError } = await supabase
          .from('asset_tags')
          .select('tag_id')
          .eq('asset_id', asset.id);
        
        if (assetTagsError) throw assetTagsError;
        
        // Get the tag objects for this asset
        const assetTagIds = assetTagsData.map(at => at.tag_id);
        const assetTagObjects = allTags.filter(tag => assetTagIds.includes(tag.id));
        
        setTags(allTags || []);
        setAssetTags(assetTagObjects || []);
      } catch (error) {
        console.error('Error fetching tags:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tags',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    if (asset?.id) {
      fetchTags();
    }
  }, [asset?.id, toast]);

  // Add a tag to the asset
  const addTag = async (tagId: string) => {
    try {
      // Check if tag is already added
      const isTagAdded = assetTags.some(tag => tag.id === tagId);
      if (isTagAdded) return;
      
      // Add tag to asset
      const { error } = await supabase
        .from('asset_tags')
        .insert({
          asset_id: asset.id,
          tag_id: tagId
        });
      
      if (error) throw error;
      
      // Update local state
      const tagToAdd = tags.find(tag => tag.id === tagId);
      if (tagToAdd) {
        setAssetTags(prev => [...prev, tagToAdd]);
      }
      
      // Notify parent component
      if (onTagsUpdated) onTagsUpdated();
      
    } catch (error) {
      console.error('Error adding tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to add tag',
        variant: 'destructive'
      });
    }
  };

  // Remove a tag from the asset
  const removeTag = async (tagId: string) => {
    try {
      // Remove tag from asset
      const { error } = await supabase
        .from('asset_tags')
        .delete()
        .eq('asset_id', asset.id)
        .eq('tag_id', tagId);
      
      if (error) throw error;
      
      // Update local state
      setAssetTags(prev => prev.filter(tag => tag.id !== tagId));
      
      // Notify parent component
      if (onTagsUpdated) onTagsUpdated();
      
    } catch (error) {
      console.error('Error removing tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove tag',
        variant: 'destructive'
      });
    }
  };

  // Create a new tag
  const createTag = async () => {
    if (!newTag.trim()) return;
    
    try {
      // Create new tag
      const { data, error } = await supabase
        .from('tags')
        .insert({
          name: newTag.trim(),
          category: 'collection'
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Update local state
      setTags(prev => [...prev, data]);
      
      // Add the new tag to the asset
      await addTag(data.id);
      
      // Clear input
      setNewTag('');
      
    } catch (error) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to create tag',
        variant: 'destructive'
      });
    }
  };

  // Get available tags (tags not already added to the asset)
  const availableTags = tags.filter(tag => 
    !assetTags.some(assetTag => assetTag.id === tag.id)
  );

  // Filter available tags based on search query
  const filteredAvailableTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(tagSearchQuery.toLowerCase())
  );

  // Display logic for available tags
  const AVAILABLE_TAGS_LIMIT = 12;
  const availableTagsToDisplay = showAllAvailableTags 
    ? filteredAvailableTags 
    : filteredAvailableTags.slice(0, AVAILABLE_TAGS_LIMIT);
  const hasMoreAvailableTags = filteredAvailableTags.length > AVAILABLE_TAGS_LIMIT;

  // Get tag color based on category
  const getTagColor = (category: string) => {
    switch (category) {
      case 'view_type':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'workflow_stage':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'product_specific':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'custom':
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  if (isLoading) {
    return <div className="py-2">Loading tags...</div>;
  }

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="tags">Asset Tags</Label>
        <div className="flex flex-wrap gap-2 mt-2 min-h-[2rem]">
          {assetTags.length === 0 ? (
            <p className="text-sm text-muted-foreground">No tags added yet</p>
          ) : (
            assetTags.map(tag => (
              <Badge 
                key={tag.id} 
                variant="outline"
                className={`${getTagColor(tag.category)} px-2 py-1 flex items-center gap-1`}
              >
                <span className="truncate max-w-[150px]" title={tag.name}>
                  {tag.name}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 ml-1 rounded-full hover:bg-red-100"
                  onClick={() => removeTag(tag.id)}
                  title="Remove tag"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))
          )}
        </div>
      </div>
      
      <div>
        <Label htmlFor="new-tag">Add New Tag</Label>
        <div className="flex gap-2 mt-2">
          <Input
            id="new-tag"
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder="Enter new tag name"
            className="flex-1"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                createTag();
              }
            }}
          />
          <Button onClick={createTag} disabled={!newTag.trim()}>
            Add
          </Button>
        </div>
      </div>
      
      {availableTags.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label>Available Tags</Label>
            <span className="text-xs text-muted-foreground">
              {filteredAvailableTags.length} available
            </span>
          </div>

          {/* Search for available tags */}
          {availableTags.length > 8 && (
            <div className="mb-3">
              <div className="relative">
                <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search available tags..."
                  value={tagSearchQuery}
                  onChange={(e) => setTagSearchQuery(e.target.value)}
                  className="h-8 text-sm bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 pl-9"
                />
              </div>
            </div>
          )}

          <div className="space-y-2">
            <div className="flex flex-wrap gap-2">
              {availableTagsToDisplay.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  {tagSearchQuery ? `No tags found matching "${tagSearchQuery}"` : 'No available tags'}
                </p>
              ) : (
                availableTagsToDisplay.map(tag => (
                  <Badge
                    key={tag.id}
                    variant="outline"
                    className={`${getTagColor(tag.category)} px-2 py-1 cursor-pointer hover:shadow-sm transition-all duration-200`}
                    onClick={() => addTag(tag.id)}
                    title={`Click to add "${tag.name}" tag`}
                  >
                    <span className="truncate max-w-[120px]">
                      {tag.name}
                    </span>
                  </Badge>
                ))
              )}
            </div>

            {/* Show more/less button for available tags */}
            {hasMoreAvailableTags && !tagSearchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAllAvailableTags(!showAllAvailableTags)}
                className="w-full h-8 text-xs text-muted-foreground hover:text-foreground mt-2"
              >
                {showAllAvailableTags ? (
                  <>
                    <ChevronUp size={14} className="mr-1" />
                    Show Less ({filteredAvailableTags.length - AVAILABLE_TAGS_LIMIT} hidden)
                  </>
                ) : (
                  <>
                    <ChevronDown size={14} className="mr-1" />
                    Show {filteredAvailableTags.length - AVAILABLE_TAGS_LIMIT} More Tags
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default AssetTagsManager; 