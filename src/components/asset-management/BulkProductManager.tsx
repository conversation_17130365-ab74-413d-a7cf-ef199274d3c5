import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  Di<PERSON>Header, 
  <PERSON><PERSON>Title, 
  DialogFooter,
  DialogDescription 
} from '../ui/dialog';
import { Button } from '../ui/button';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../common/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { bulkUpdateAssetsWithVerification, handleSupabaseError } from '../common/utils/assetOperations';
import ProductSelector from '../products/ProductSelector';

interface BulkProductManagerProps {
  assetIds: string[];
  isOpen: boolean;
  onClose: () => void;
  onProductsUpdated?: () => void;
  collectionId: string;
}

export function BulkProductManager({ 
  assetIds, 
  isOpen, 
  onClose, 
  onProductsUpdated,
  collectionId
}: BulkProductManagerProps) {
  const [selectedProductId, setSelectedProductId] = useState<string | undefined>(undefined);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const queryClient = useQueryClient();

  // Apply product change to all selected assets
  const applyProductChange = async () => {
    if (assetIds.length === 0) return;
    
    setIsSaving(true);
    try {
      console.log('[BulkProductManager] Starting update...', {
        assetIds,
        productId: selectedProductId,
        collectionId
      });

      // Use the standardized update function with verification
      const result = await bulkUpdateAssetsWithVerification(supabase, {
        assetIds,
        updates: { product_id: selectedProductId || null },
        verifyUpdate: true,
        replicationDelay: 1500,
        maxRetries: 3
      });

      if (!result.success) {
        const errorInfo = handleSupabaseError({ message: result.error });
        throw new Error(errorInfo.message);
      }

      toast({
        title: 'Products Updated',
        description: `Updated ${assetIds.length} assets${selectedProductId ? ' to the selected product' : ' (removed product assignment)'}`,
      });
      
      console.log('[BulkProductManager] Update verified successfully');
      
      // Invalidate queries to ensure UI updates
      await queryClient.invalidateQueries({ 
        predicate: (query) => {
          return Array.isArray(query.queryKey) && query.queryKey[0] === 'assets';
        }
      });
      
      // Also invalidate related queries
      await queryClient.invalidateQueries({ queryKey: ['assetCounts'] });
      await queryClient.invalidateQueries({ queryKey: ['products'] });
      
      // Notify parent component
      if (onProductsUpdated) onProductsUpdated();
      
      // Close dialog
      onClose();
    } catch (error) {
      console.error('[BulkProductManager] Error updating products:', error);
      
      const errorInfo = handleSupabaseError(error as Error);
      toast({
        title: 'Update Failed',
        description: errorInfo.message,
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update Product for {assetIds.length} Assets</DialogTitle>
          <DialogDescription>
            Select a product to assign to all selected assets, or leave empty to remove product assignment.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <ProductSelector 
            collectionId={collectionId}
            selectedProductId={selectedProductId}
            onProductSelect={(productId) => setSelectedProductId(productId || undefined)}
          />
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button 
            onClick={applyProductChange} 
            disabled={isSaving}
          >
            {isSaving ? 'Updating...' : 'Update Product'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default BulkProductManager;