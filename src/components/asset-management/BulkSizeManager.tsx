import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>alog<PERSON>ontent, 
  Di<PERSON>Header, 
  <PERSON><PERSON>T<PERSON>le, 
  DialogFooter 
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Ruler, Check } from 'lucide-react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../common/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { cn } from '../common/utils/utils';

interface BulkSizeManagerProps {
  assetIds: string[];
  collectionId?: string;
  isOpen: boolean;
  onClose: () => void;
  onSizesUpdated?: () => void;
}

// Fixed set of standard sizes
const STANDARD_SIZES = ['XS', 'S', 'M', 'L', 'XL'];

export function BulkSizeManager({ 
  assetIds, 
  collectionId, 
  isO<PERSON>, 
  onClose, 
  onSizesUpdated 
}: BulkSizeManagerProps) {
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [currentSizes, setCurrentSizes] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { supabase } = useSupabase();
  const queryClient = useQueryClient();

  // Fetch current asset sizes
  useEffect(() => {
    const fetchData = async () => {
      if (!isOpen || assetIds.length === 0) return;
      
      setIsLoading(true);
      try {
        // Fetch assets to get their current sizes
        const { data: assets, error: assetsError } = await supabase
          .from('assets')
          .select('id, metadata')
          .in('id', assetIds);
        
        if (assetsError) throw assetsError;
        
        // Extract current sizes from metadata
        const sizes: Record<string, string> = {};
        assets?.forEach(asset => {
          if (asset.metadata && typeof asset.metadata === 'object') {
            const metadata = asset.metadata as any;
            if (metadata.size) {
              sizes[asset.id] = metadata.size;
            }
          }
        });
        setCurrentSizes(sizes);
      } catch (error: any) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load size information',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [isOpen, assetIds, supabase, toast]);

  const handleSave = async () => {
    if (!selectedSize) {
      toast({
        title: 'No size selected',
        description: 'Please select a size to assign',
        variant: 'destructive'
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Update metadata for all selected assets
      const updates = assetIds.map(async (assetId) => {
        // First fetch current metadata
        const { data: asset, error: fetchError } = await supabase
          .from('assets')
          .select('metadata')
          .eq('id', assetId)
          .single();
        
        if (fetchError) throw fetchError;
        
        // Merge with existing metadata
        const currentMetadata = asset.metadata || {};
        const updatedMetadata = {
          ...currentMetadata,
          size: selectedSize
        };
        
        // Update the asset
        const { error: updateError } = await supabase
          .from('assets')
          .update({ metadata: updatedMetadata })
          .eq('id', assetId);
        
        if (updateError) throw updateError;
      });
      
      await Promise.all(updates);
      
      toast({
        title: 'Success',
        description: `Updated size for ${assetIds.length} asset${assetIds.length !== 1 ? 's' : ''}`,
      });
      
      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['assets'] });
      
      if (onSizesUpdated) {
        onSizesUpdated();
      }
      
      onClose();
    } catch (error: any) {
      console.error('Error updating sizes:', error);
      toast({
        title: 'Error',
        description: 'Failed to update asset sizes',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    setSelectedSize(null);
    onClose();
  };

  // Count assets with each size
  const sizeCounts = Object.values(currentSizes).reduce((acc, size) => {
    acc[size] = (acc[size] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Ruler className="h-5 w-5" />
            Assign Size to {assetIds.length} Asset{assetIds.length !== 1 ? 's' : ''}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {isLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading sizes...
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label>Select size to assign:</Label>
                <div className="grid grid-cols-3 gap-2">
                  {STANDARD_SIZES.map(size => {
                    const count = sizeCounts[size] || 0;
                    const percentage = assetIds.length > 0 
                      ? Math.round((count / assetIds.length) * 100) 
                      : 0;
                    
                    return (
                      <Button
                        key={size}
                        variant={selectedSize === size ? "default" : "outline"}
                        onClick={() => setSelectedSize(size)}
                        className={cn(
                          "relative",
                          selectedSize === size && "ring-2 ring-offset-2"
                        )}
                      >
                        {size}
                        {count > 0 && (
                          <span className="absolute -top-2 -right-2 text-xs bg-muted px-1.5 py-0.5 rounded-full">
                            {percentage}%
                          </span>
                        )}
                      </Button>
                    );
                  })}
                </div>
              </div>

              {Object.keys(sizeCounts).length > 0 && (
                <div className="text-sm text-muted-foreground space-y-1">
                  <p className="font-medium">Current sizes:</p>
                  {Object.entries(sizeCounts).map(([size, count]) => (
                    <div key={size} className="flex justify-between">
                      <span>{size}:</span>
                      <span>{count} asset{count !== 1 ? 's' : ''}</span>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={isSaving || !selectedSize || isLoading}
          >
            {isSaving ? (
              <>Updating...</>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Assign Size
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}