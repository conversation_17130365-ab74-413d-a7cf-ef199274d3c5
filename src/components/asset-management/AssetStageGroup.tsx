import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Checkbox } from '../ui/checkbox';
import { AssetItem } from '../../data/productAssets';
import { supabase } from '../common/utils/supabase';
import AssetActions from './AssetActions';
import { Asset, Tag, Product, RetouchSubstage } from '../common/types/database.types';
import { Package, Ruler, MessageCircle, Eye, Edit, Edit2, MoreHorizontal } from 'lucide-react';
import { getAssetUrl } from '../common/utils/utils';
import { getWorkflowStageLabel } from '../common/utils/workflowStageUtils';
import { getRetouchSubstageLabel, getRetouchSubstageBadgeClass, getRetouchSubstageConfigs } from '../common/utils/retouchSubstageUtils';
import { useAssetComments } from '../common/hooks/useAssetComments';
import { useUserRole } from '../../contexts/UserRoleContext';
import { useIsFreelancer } from '../common/hooks/useIsFreelancer';

interface AssetStageGroupProps {
  stage: string;
  assets: AssetItem[];
  selectedAssets: string[];
  onAssetSelect: (assetId: string) => void;
  onAssetDeleted?: (assetId: string) => void;
  productRetouched: number;
  onAssetClick?: (assetId: string) => void;
  clientId?: string;
  orgId?: string;
}

export function AssetStageGroup({
  stage,
  assets,
  selectedAssets,
  onAssetSelect,
  onAssetDeleted,
  productRetouched,
  onAssetClick,
  clientId: propsClientId,
  orgId: propsOrgId
}: AssetStageGroupProps) {
  // Get route params - could be either orgId or clientId depending on the route
  const { orgId: routeOrgId, clientId: routeClientId, collectionId } = useParams();
  
  // Use props first, then fall back to route params
  const orgId = propsOrgId || routeOrgId;
  const clientId = propsClientId || routeClientId;
  const navigate = useNavigate();
  const [assetTags, setAssetTags] = useState<Record<string, Tag[]>>({});
  const [productData, setProductData] = useState<Record<string, Product>>({});
  
  // Get user role for filtering substages
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();
  
  // Get comment counts for all assets
  const assetIds = assets.map(asset => asset.id);
  const { data: commentCounts = {} } = useAssetComments(assetIds);
  
  // Define fetchAssetTags with useCallback
  const fetchAssetTags = useCallback(async () => {
    if (assets.length === 0) return;
    
    try {
      const assetIds = assets.map(asset => asset.id);
      
      // Get all asset_tags for these assets
      const { data: assetTagsData, error: tagsError } = await supabase
        .from('asset_tags')
        .select('asset_id, tag_id')
        .in('asset_id', assetIds);
      
      if (tagsError) throw tagsError;
      if (!assetTagsData || assetTagsData.length === 0) return;
      
      // Group tag_ids by asset_id
      const tagIdsByAsset: Record<string, string[]> = {};
      assetTagsData.forEach(item => {
        if (!tagIdsByAsset[item.asset_id]) {
          tagIdsByAsset[item.asset_id] = [];
        }
        tagIdsByAsset[item.asset_id].push(item.tag_id);
      });
      
      // Get all unique tag_ids
      const allTagIds = [...new Set(assetTagsData.map(item => item.tag_id))];
      
      // Fetch all tags at once
      const { data: tags, error: tagsFetchError } = await supabase
        .from('tags')
        .select('*')
        .in('id', allTagIds);
      
      if (tagsFetchError) throw tagsFetchError;
      if (!tags) return;
      
      // Create a map of tag_id to tag
      const tagMap: Record<string, Tag> = {};
      tags.forEach(tag => {
        tagMap[tag.id] = tag;
      });
      
      // Create a map of asset_id to tags
      const assetTagsMap: Record<string, Tag[]> = {};
      Object.entries(tagIdsByAsset).forEach(([assetId, tagIds]) => {
        assetTagsMap[assetId] = tagIds.map(tagId => tagMap[tagId]).filter(Boolean);
      });
      
      setAssetTags(assetTagsMap);
    } catch (error) {
      console.error('Error fetching asset tags:', error);
    }
  }, [assets]);
  
  // Fetch tags for assets
  useEffect(() => {
    fetchAssetTags();
  }, [fetchAssetTags, selectedAssets]);
  
  // Fetch product data for assets
  useEffect(() => {
    const fetchProductData = async () => {
      if (assets.length === 0) return;
      
      // Get unique product IDs from assets
      const productIds = assets
        .filter(asset => asset.productId)
        .map(asset => asset.productId.toString());
      
      if (productIds.length === 0) return;
      
      try {
        const { data: products, error } = await supabase
          .from('products')
          .select('*')
          .in('id', productIds);
        
        if (error) throw error;
        
        // Create a map of product ID to product data
        const productMap: Record<string, Product> = {};
        products?.forEach(product => {
          productMap[product.id] = product;
        });
        
        setProductData(productMap);
      } catch (error) {
        console.error('Error fetching product data:', error);
      }
    };
    
    fetchProductData();
  }, [assets]);
  
  // Get stage display name using the utility function
  const getStageDisplayName = (stage: string) => {
    return getWorkflowStageLabel(stage as any) || stage;
  };
  
  // Get stage color
  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'upload': return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'raw_ai_images': return 'bg-indigo-50 text-indigo-700 border-indigo-200';
      case 'selected': return 'bg-green-50 text-green-700 border-green-200';
      case 'refined': return 'bg-teal-50 text-teal-700 border-teal-200';
      case 'upscale': return 'bg-amber-50 text-amber-700 border-amber-200';
      case 'retouch': return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'final': return 'bg-purple-50 text-purple-700 border-purple-200';
      default: return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };
  
  // Get tag color based on category
  const getTagColor = (category: string) => {
    switch (category) {
      case 'view_type':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'workflow_stage':
        return 'bg-emerald-50 text-emerald-700 border-emerald-200';
      case 'product_specific':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'custom':
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };
  
  // Get asset preview URL
  const getAssetPreviewUrl = (asset: AssetItem) => {
    // If the asset has a URL that starts with http, use it directly
    if (asset.preview && asset.preview.startsWith('http')) {
      return asset.preview;
    }
    
    try {
      console.log('Trying to get thumbnail for:', asset.name);
      console.log('Asset metadata:', asset.metadata);
      
      // Use our utility function if the asset has expected structure
      if (asset.metadata) {
        console.log('Asset has metadata, attempting to use getAssetUrl');
        const url = getAssetUrl({
          file_path: asset.preview,
          metadata: asset.metadata
        }, true); // true = prefer thumbnail
        
        console.log('Asset grid thumbnail URL:', url);
        return url;
      }
      
      // Fallback to original asset preview
      console.log('Falling back to original path:', asset.preview);
      
      // Use the optimized asset URL function for the fallback
      const regularUrl = getAssetUrl({
        id: asset.id,
        collection_id: collectionId || ''
      }, true); // true for thumbnail
      
      console.log('Regular URL constructed:', regularUrl);
      return regularUrl;
    } catch (error) {
      console.error('Error getting asset preview URL:', error);
      return ''; // Return empty string if there's an error
    }
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };
  
  // Handle asset click to navigate to detail page
  const handleAssetClick = (assetId: string, event: React.MouseEvent) => {
    // Prevent navigation if clicking on checkbox or actions menu
    if (
      event.target instanceof HTMLElement && 
      (event.target.closest('.asset-checkbox') || 
       event.target.closest('.asset-actions') ||
       event.target.closest('[role="menuitem"]') ||  // For dropdown menu items
       event.target.closest('[role="menu"]'))        // For the dropdown menu itself
    ) {
      return;
    }
    
    // Prioritize the passed-in handler from the parent component
    if (onAssetClick) {
      console.log("AssetStageGroup: Calling onAssetClick prop for asset:", assetId);
      onAssetClick(assetId);
    } else {
      // Fallback navigation (should ideally not be needed if prop is passed correctly)
      console.warn("AssetStageGroup: onAssetClick prop not provided, using fallback navigate.");
      // Use orgId if available (new routes), otherwise use clientId (for backward compatibility)
      const organizationId = orgId || clientId;
      
      if (organizationId && collectionId) {
        navigate(`/organizations/${organizationId}/collections/${collectionId}/assets/${assetId}`);
      } else if (collectionId) {
        // For routes without organizationId in the URL
        navigate(`/collections/${collectionId}/assets/${assetId}`);
      }
    }
  };

  // Handle checkbox click
  const handleCheckboxClick = (e: React.MouseEvent, assetId: string) => {
    e.stopPropagation();
    onAssetSelect(assetId);
  };

  // Handle asset deletion
  const handleAssetDeleted = (assetId: string) => {
    // Call the parent component's onAssetDeleted callback if provided
    if (onAssetDeleted) {
      onAssetDeleted(assetId);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-medium">{getStageDisplayName(stage)}</h3>
          <Badge variant="outline" className={`px-2 py-0.5 text-xs ${getStageColor(stage)}`}>
            {assets.length}
          </Badge>
        </div>
        
        {stage === 'retouched' && productRetouched > 0 && (
          <Badge variant="outline" className="px-2 py-0.5 text-xs">
            {productRetouched}% Complete
          </Badge>
        )}
      </div>
      
      <div className="grid gap-4 md:gap-6" style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
        justifyContent: 'start'
      }}>
        {assets.map(asset => (
          <Card 
            key={asset.id} 
            className="overflow-hidden group cursor-pointer transition-all duration-300 ease-out hover:shadow-2xl hover:-translate-y-1 bg-white border border-gray-200 rounded-xl"
            onClick={(e) => handleAssetClick(asset.id, e)}
          >
            <CardContent className="p-0">
              <div className="relative">
                {/* Asset preview */}
                <div className="aspect-[4/5] overflow-hidden bg-gray-50 rounded-t-xl">
                  <img 
                    src={getAssetPreviewUrl(asset)} 
                    alt={asset.name}
                    className="w-full h-full object-cover transition-all duration-300 group-hover:scale-105"
                  />
                  
                  {/* Gradient overlay on hover */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                
                {/* Selection overlay */}
                <div 
                  className="absolute top-3 left-3 asset-checkbox z-20"
                  onClick={(e) => handleCheckboxClick(e, asset.id)}
                >
                  <div className={`${selectedAssets.includes(asset.id) ? 'bg-blue-600 text-white' : 'bg-white/90 backdrop-blur-sm border border-gray-300'} w-7 h-7 flex items-center justify-center rounded-lg transition-all duration-200 shadow-lg hover:scale-110`}>
                    <Checkbox 
                      checked={selectedAssets.includes(asset.id)}
                      className="h-5 w-5 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                  </div>
                </div>
                
                {/* Actions menu */}
                <div className="absolute top-3 right-3 asset-actions opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <AssetActions 
                    asset={asset as unknown as Asset} 
                    onDelete={() => handleAssetDeleted(asset.id)}
                  />
                </div>
                
                {/* Comment indicator */}
                {commentCounts[asset.id] > 0 && (
                  <div className="absolute bottom-3 right-3 flex items-center gap-1.5 bg-blue-500 text-white rounded-full px-2.5 py-1.5 shadow-lg transform transition-transform hover:scale-110">
                    <MessageCircle className="h-4 w-4 fill-white" />
                    <span className="text-sm font-semibold">{commentCounts[asset.id]}</span>
                  </div>
                )}
              </div>
              
              {/* Asset info */}
              <div className="p-5">
                <h4 className="font-semibold text-base text-gray-900 truncate mb-2">{asset.name}</h4>
                
                {/* Product information */}
                {asset.productId && productData[asset.productId.toString()] && (
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-md">
                      <Package className="h-3 w-3 text-gray-600" />
                    </div>
                    <span className="text-sm text-gray-700 truncate font-medium">
                      {productData[asset.productId.toString()].name}
                      {productData[asset.productId.toString()].sku && (
                        <span className="text-xs text-gray-500 font-normal ml-1">({productData[asset.productId.toString()].sku})</span>
                      )}
                    </span>
                  </div>
                )}
                
                {/* Size information */}
                {asset.metadata && typeof asset.metadata === 'object' && (asset.metadata as Record<string, unknown>).size && (
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-md">
                      <Ruler className="h-3 w-3 text-blue-600" />
                    </div>
                    <span className="text-sm text-gray-700 font-medium">
                      Size: {(asset.metadata as Record<string, unknown>).size as string}
                    </span>
                  </div>
                )}
                
                {/* Retouch substage information */}
                {stage === 'retouch' && asset.retouch_substage && (
                  <div className="mb-3">
                    <Badge 
                      variant="outline" 
                      className={`${getRetouchSubstageBadgeClass(asset.retouch_substage as RetouchSubstage)} text-xs font-medium flex items-center gap-1 w-fit`}
                    >
                      {asset.retouch_substage === 'internal_review' && <Eye className="h-3 w-3" />}
                      {asset.retouch_substage === 'revision_1' && <Edit className="h-3 w-3" />}
                      {asset.retouch_substage === 'revision_2' && <Edit2 className="h-3 w-3" />}
                      {asset.retouch_substage === 'extra_revisions' && <MoreHorizontal className="h-3 w-3" />}
                      {getRetouchSubstageLabel(asset.retouch_substage as RetouchSubstage)}
                    </Badge>
                  </div>
                )}
                
                <p className="text-xs text-gray-500 mb-3">
                  Updated {formatDate(asset.dateUpdated)}
                </p>
                
                {/* Asset tags */}
                {assetTags[asset.id] && assetTags[asset.id].length > 0 && (
                  <div className="flex flex-wrap gap-1.5">
                    {assetTags[asset.id].slice(0, 2).map(tag => (
                      <Badge 
                        key={tag.id} 
                        variant="outline"
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getTagColor(tag.category)} border-current`}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                    {assetTags[asset.id].length > 2 && (
                      <Badge 
                        variant="outline"
                        className="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-700 border-gray-300"
                      >
                        +{assetTags[asset.id].length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default AssetStageGroup;
