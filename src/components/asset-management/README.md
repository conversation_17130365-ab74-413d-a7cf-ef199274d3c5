# Asset Management Components

This folder contains components for **bulk asset operations** - managing multiple assets simultaneously, asset grid views, and collection-level asset management.

> **Note**: This folder was renamed from `assets/` to `asset-management/` for better clarity of purpose.

## 📋 Component Overview

### `AssetActions.tsx`
- **Purpose**: Action buttons and controls for selected assets
- **Features**: Delete, move, tag, workflow actions for multiple assets
- **Usage**: Toolbar component when assets are selected in grid view

### `AssetSelectionHeader.tsx`
- **Purpose**: Header interface for multi-asset selection
- **Features**: Select all/none, count display, bulk action triggers
- **Usage**: Top bar in asset grid views with selection capability

### `AssetStageGroup.tsx`
- **Purpose**: Groups assets by workflow stage (draft, upscale, retouch, final)
- **Features**: Visual grouping, stage-based filtering, drag-drop between stages
- **Usage**: Kanban-style workflow view for assets

### `AssetTagsManager.tsx`
- **Purpose**: Tag management interface for assets
- **Features**: Add/remove tags, tag suggestions, bulk tagging
- **Usage**: Tagging interface in asset management views

### `BulkWorkflowStageManager.tsx`
- **Purpose**: Bulk workflow stage changes for multiple assets
- **Features**: Move multiple assets through workflow stages
- **Usage**: Bulk operations toolbar for workflow management

### `EmptyAssetsState.tsx`
- **Purpose**: Empty state when no assets exist in a collection
- **Features**: Upload prompt, helpful messaging, action buttons
- **Usage**: Placeholder in asset grid views when no assets are found

### `BulkTagManager.tsx`
- **Purpose**: Bulk tagging operations for multiple assets
- **Features**: Multi-select tagging with add/remove/neutral states, tag creation, shows tag counts
- **Usage**: Advanced bulk tagging interface (newer implementation)

## 🔄 Relationship with `../asset-detail/`

**Important Distinction**:
- **`asset-management/` (this folder)**: Components for **bulk operations** on multiple assets
- **`../asset-detail/`**: Components for viewing/managing **single assets** in detail

### When to use `asset-management/` components:
- Asset grid/list views (`/collections/:id/assets`)
- Bulk selection and operations
- Asset management dashboards
- Collection-level asset organization
- Multi-asset workflows

### When to use `../asset-detail/` components:
- Individual asset detail pages (`/assets/:id`)
- Single asset editing and viewing
- Asset-specific comments and annotations
- Detailed metadata management

## 🎨 Design Patterns

### Bulk Selection Pattern
Components follow a consistent pattern for multi-asset selection:

```typescript
interface BulkComponentProps {
  selectedAssets: Asset[];
  onSelectionChange: (assets: Asset[]) => void;
  onBulkAction: (action: string, assetIds: string[]) => void;
}
```

### Role-Based Operations
Bulk operations respect user permissions:

```typescript
const { isPlatformUser, isBrandAdmin } = useUserRole();

// Example: Operation availability
const canBulkDelete = isPlatformUser;
const canBulkTag = isPlatformUser || isBrandAdmin;
const canChangeWorkflow = isPlatformUser;
```

### Progress Indication
Long-running bulk operations show progress:

```typescript
const [progress, setProgress] = useState({ current: 0, total: 0 });

// Update progress during bulk operations
setProgress({ current: index + 1, total: selectedAssets.length });
```

## 🚀 Usage Examples

### Asset Grid with Bulk Selection
```typescript
import { AssetSelectionHeader, AssetActions } from './assets';

function AssetGrid() {
  const [selectedAssets, setSelectedAssets] = useState<Asset[]>([]);
  
  return (
    <div>
      <AssetSelectionHeader 
        selectedCount={selectedAssets.length}
        totalCount={allAssets.length}
        onSelectAll={handleSelectAll}
        onClearSelection={handleClearSelection}
      />
      
      {selectedAssets.length > 0 && (
        <AssetActions
          selectedAssets={selectedAssets}
          onBulkAction={handleBulkAction}
        />
      )}
      
      {/* Asset grid component */}
    </div>
  );
}
```

### Workflow Stage Management
```typescript
import { AssetStageGroup, BulkWorkflowStageManager } from './assets';

function WorkflowView() {
  return (
    <div>
      <AssetStageGroup 
        assets={assets}
        onStageChange={handleStageChange}
        onBulkStageChange={handleBulkStageChange}
      />
      
      <BulkWorkflowStageManager
        selectedAssets={selectedAssets}
        onStageUpdate={handleStageUpdate}
      />
    </div>
  );
}
```

### Bulk Tagging Interface
```typescript
import { BulkTagManager } from './assets';

function AssetManagement() {
  return (
    <BulkTagManager
      selectedAssets={selectedAssets}
      availableTags={tags}
      onTagsUpdated={handleTagsUpdated}
      onTagCreated={handleTagCreated}
    />
  );
}
```

## 🔧 Performance Considerations

### Virtualization
- **Large asset lists** use virtual scrolling
- **Bulk operations** are batched to prevent UI blocking
- **Selection state** is optimized for large datasets

### Debouncing
- **Search/filter operations** are debounced
- **Tag input** has debounced API calls
- **Bulk updates** are batched and debounced

### Memory Management
- **Asset thumbnails** are lazy-loaded
- **Large selections** use efficient data structures
- **Cleanup** of event listeners and subscriptions

## 🎯 State Management

### Selection State
```typescript
// Centralized selection management
const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
const [selectionMode, setSelectionMode] = useState<'none' | 'some' | 'all'>('none');
```

### Bulk Operation State
```typescript
// Progress tracking for bulk operations
const [bulkOperation, setBulkOperation] = useState<{
  type: string;
  progress: number;
  total: number;
  isRunning: boolean;
} | null>(null);
```

## 🧪 Testing Strategies

### Unit Tests
- **Individual component** functionality
- **Selection logic** correctness
- **Permission-based** feature availability

### Integration Tests
- **Multi-component** workflows
- **Bulk operation** completion
- **Error handling** in bulk scenarios

### Performance Tests
- **Large dataset** rendering
- **Bulk operation** performance
- **Memory usage** with many selections

## 🔮 Future Enhancements

### Planned Improvements
- **Undo/redo** for bulk operations
- **Keyboard shortcuts** for power users
- **Custom bulk actions** via plugins
- **Real-time collaboration** on bulk edits

### Architecture Considerations
- **Command pattern** for undoable operations
- **Event sourcing** for audit trails
- **Worker threads** for heavy bulk processing
- **WebSocket updates** for real-time sync