import { useState } from 'react';
import { Button } from '../ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuPortal
} from '../ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { useToast } from '../common/hooks/use-toast';
import { supabase, deleteFile } from '../common/utils/supabase';
import { deleteAsset } from '../common/utils/assetStorage';
import { Asset } from '../common/types/database.types';
import { useQueryClient } from '@tanstack/react-query';

interface AssetActionsProps {
  asset: Asset;
  onDelete?: () => void;
}

export function AssetActions({ asset, onDelete }: AssetActionsProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Handle asset deletion
  const handleDelete = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!asset) return;
    
    setIsDeleting(true);
    
    try {
      // First delete from database (this should cascade to related tables)
      const { error } = await supabase
        .from('assets')
        .delete()
        .eq('id', asset.id);
      
      if (error) {
        console.error('Database deletion error:', error);
        throw error;
      }
      
      // Then try to clean up storage (best effort - don't fail if this errors)
      try {
        await deleteAsset(asset.id, asset.collection_id);
      } catch (storageError) {
        console.warn('Storage cleanup failed:', storageError);
        // Don't throw - we've already deleted from database
      }
      
      toast({
        title: 'Asset Deleted',
        description: `Successfully deleted ${asset.file_name}`,
      });
      
      // Debug logging - commented out for production
      // if (import.meta.env.PROD) {
      //   console.log('[AssetActions] Starting cache invalidation after delete...');
      // }
      
      // Invalidate ALL asset queries regardless of parameters
      await queryClient.invalidateQueries({ 
        predicate: (query) => query.queryKey[0] === 'assets'
      });
      if (asset.collection_id) {
        await queryClient.invalidateQueries({ queryKey: ['assetTags', asset.collection_id] });
        await queryClient.invalidateQueries({ queryKey: ['assetCounts', asset.collection_id] });
      }
      
      // Force refetch all invalidated queries
      await queryClient.refetchQueries({ 
        predicate: (query) => query.queryKey[0] === 'assets'
      });
      if (asset.collection_id) {
        await queryClient.refetchQueries({ queryKey: ['assetTags', asset.collection_id] });
        await queryClient.refetchQueries({ queryKey: ['assetCounts', asset.collection_id] });
      }
      
      // if (import.meta.env.PROD) {
      //   console.log('[AssetActions] Cache invalidation completed');
      // }
      
      // Call onDelete callback if provided
      if (onDelete) onDelete();
      
    } catch (error: any) {
      console.error('Error deleting asset:', error);
      
      toast({
        title: 'Delete Failed',
        description: error.message || 'There was an error deleting the asset',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };
  
  const handleMenuClick = (e: React.MouseEvent<HTMLDivElement | HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };
  
  const handleDeleteClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  };
  
  return (
    <div onClick={handleMenuClick} className="asset-actions">
      <DropdownMenu modal={true}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={handleMenuClick}
          >
            •••
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuPortal>
          <DropdownMenuContent align="end" onClick={handleMenuClick}>
            <DropdownMenuItem 
              className="text-destructive focus:text-destructive"
              onClick={handleDeleteClick}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenuPortal>
      </DropdownMenu>
      
      {/* Delete confirmation dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the asset "{asset.file_name}". 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              disabled={isDeleting}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default AssetActions; 