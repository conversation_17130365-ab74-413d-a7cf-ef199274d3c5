import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
interface EmptyAssetsStateProps {
  isFiltering: boolean;
  collectionId: string;
  clientId?: string;
  orgId?: string;
}

export function EmptyAssetsState({ isFiltering, collectionId, clientId, orgId }: EmptyAssetsStateProps) {
  const navigate = useNavigate();
  
  const handleUploadClick = () => {
    // If orgId is present, use organization route
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}/upload`);
    } else if (clientId) {
      // Use organization route with clientId (which is actually an organization ID)
      navigate(`/organizations/${clientId}/collections/${collectionId}/upload`);
    } else {
      // Default route
      navigate(`/collections/${collectionId}/upload`);
    }
  };
  
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="text-5xl mb-4">
        {isFiltering ? '🔍' : '📁'}
      </div>
      <h3 className="text-xl font-medium mb-2">
        {isFiltering ? 'No matching assets found' : 'No assets found'}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        {isFiltering 
          ? 'Try adjusting your filters or clearing them to see more assets.'
          : 'Upload some assets to get started with this collection.'}
      </p>
      
      {isFiltering ? (
        <Button variant="outline" onClick={() => window.location.reload()}>
          Clear Filters
        </Button>
      ) : (
        <Button onClick={handleUploadClick}>
          Upload Assets
        </Button>
      )}
    </div>
  );
}

export default EmptyAssetsState;
