import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';

interface AssetSelectionHeaderProps {
  selectedCount: number;
  totalCount: number;
  isFiltering: boolean;
  onSelectAll: () => void;
}

export function AssetSelectionHeader({
  selectedCount,
  totalCount,
  isFiltering,
  onSelectAll
}: AssetSelectionHeaderProps) {
  return (
    <div className="flex items-center justify-between py-2 px-4 bg-muted/20 border rounded-md">
      <div className="flex items-center space-x-4">
        <div 
          className="flex items-center space-x-2 cursor-pointer hover:bg-accent px-2 py-1 rounded-md h-8"
          onClick={onSelectAll}
        >
          <Checkbox 
            checked={selectedCount > 0 && selectedCount === totalCount}
            className="h-4 w-4 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
          />
          <span className="text-sm select-none">
            {selectedCount > 0 ? `${selectedCount} of ${totalCount} selected` : `Select all (${totalCount})`}
          </span>
        </div>
        
        {isFiltering && (
          <Badge variant="outline" className="px-2 py-0.5 text-xs">
            Filtered view
          </Badge>
        )}
      </div>
    </div>
  );
}

export default AssetSelectionHeader;
