import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Camera, Trash2, Save } from 'lucide-react'
import { useProfile } from '@/contexts/ProfileContext'
import { toast } from 'sonner'

export const ProfileSection: React.FC = () => {
  const { profile, updateProfile, uploadAvatar, deleteAvatar } = useProfile()
  const [isEditing, setIsEditing] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
  })

  // Update formData when profile changes
  useEffect(() => {
    if (profile) {
      setFormData({
        first_name: profile.first_name || '',
        last_name: profile.last_name || '',
      })
    }
  }, [profile])

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setUploading(true)
    try {
      await uploadAvatar(file)
    } catch (error) {
      // Error is handled in context
    } finally {
      setUploading(false)
    }
  }

  const handleDeleteAvatar = async () => {
    if (!confirm('Are you sure you want to delete your profile picture?')) return
    
    try {
      await deleteAvatar()
    } catch (error) {
      // Error is handled in context
    }
  }

  const handleSave = async () => {
    // Validate form data
    if (formData.first_name.length > 50) {
      toast.error('First name must be less than 50 characters')
      return
    }
    
    if (formData.last_name.length > 50) {
      toast.error('Last name must be less than 50 characters')
      return
    }

    setSaving(true)
    try {
      await updateProfile(formData)
      setIsEditing(false)
      toast.success('Profile updated successfully')
    } catch (error) {
      // Error is handled in context
    } finally {
      setSaving(false)
    }
  }

  const getInitials = () => {
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name[0]}${profile.last_name[0]}`.toUpperCase()
    } else if (profile?.first_name) {
      return profile.first_name.slice(0, 2).toUpperCase()
    } else if (profile?.last_name) {
      return profile.last_name.slice(0, 2).toUpperCase()
    }
    return profile?.email?.[0].toUpperCase() || 'U'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>Update your profile information and preferences</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avatar Section */}
        <div className="flex items-center gap-6">
          <div className="relative">
            <Avatar className="h-24 w-24">
              <AvatarImage src={profile?.avatar_url} alt={profile?.email} />
              <AvatarFallback className="text-xl">{getInitials()}</AvatarFallback>
            </Avatar>
            <label className="absolute bottom-0 right-0">
              <input
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleAvatarUpload}
                disabled={uploading}
              />
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 rounded-full shadow-md"
                disabled={uploading}
                asChild
              >
                <span>
                  <Camera className="h-4 w-4" />
                </span>
              </Button>
            </label>
          </div>
          
          <div className="space-y-2">
            <p className="text-sm font-medium">Profile Picture</p>
            <p className="text-sm text-muted-foreground">JPG, PNG or WEBP. Max 5MB.</p>
            {profile?.avatar_url && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDeleteAvatar}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove
              </Button>
            )}
          </div>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="first_name">First Name</Label>
            <Input
              id="first_name"
              value={formData.first_name}
              onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
              disabled={!isEditing}
              placeholder="Enter your first name"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="last_name">Last Name</Label>
            <Input
              id="last_name"
              value={formData.last_name}
              onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
              disabled={!isEditing}
              placeholder="Enter your last name"
            />
          </div>
        </div>


        {/* Action Buttons */}
        <div className="flex justify-end gap-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={() => {
                  setIsEditing(false)
                  // Reset to current profile data
                  setFormData({
                    first_name: profile?.first_name || '',
                    last_name: profile?.last_name || '',
                  })
                }}
              >
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Changes'}
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              Edit Profile
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}