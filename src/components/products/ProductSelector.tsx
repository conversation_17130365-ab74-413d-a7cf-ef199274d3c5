import { useState } from 'react';
import { useProducts } from '../common/hooks/useProducts';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../common/utils/supabase';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '../ui/command';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { useToast } from '../common/hooks/use-toast';

interface ProductSelectorProps {
  collectionId: string;
  selectedProductId?: string;
  onProductSelect: (productId: string | null) => void;
}

export function ProductSelector({ 
  collectionId, 
  selectedProductId, 
  onProductSelect 
}: ProductSelectorProps) {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newProductName, setNewProductName] = useState('');
  const [newProductSku, setNewProductSku] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  // Fetch products for the collection
  const { 
    data: products = [], 
    isLoading,
    refetch
  } = useProducts({
    collectionId,
    searchTerm,
    sortBy: 'name',
    sortOrder: 'asc'
  });
  
  // Find the selected product
  const selectedProduct = products.find(p => p.id === selectedProductId);
  
  // Handle product selection
  const handleSelectProduct = (productId: string) => {
    onProductSelect(productId);
    setOpen(false);
  };
  
  // Handle clearing selection
  const handleClearSelection = () => {
    onProductSelect(null);
    setOpen(false);
  };
  
  // Handle creating a new product
  const handleCreateProduct = async () => {
    if (!newProductName.trim() || !collectionId) return;
    
    setIsCreating(true);
    
    try {
      // Create new product in database
      const { data, error } = await supabase
        .from('products')
        .insert({
          name: newProductName.trim(),
          sku: newProductSku.trim() || null,
          collection_id: collectionId,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Invalidate products query to refresh data
      queryClient.invalidateQueries({ queryKey: ['products', collectionId] });
      
      // Select the newly created product
      onProductSelect(data.id);
      
      // Close dialog
      setCreateDialogOpen(false);
      
      // Reset form
      setNewProductName('');
      setNewProductSku('');
      
      // Show success toast
      toast({
        title: 'Product Created',
        description: `Successfully created product: ${data.name}`
      });
      
    } catch (error: any) {
      console.error('Error creating product:', error);
      
      toast({
        title: 'Failed to Create Product',
        description: error.message || 'There was an error creating the product',
        variant: 'destructive'
      });
    } finally {
      setIsCreating(false);
    }
  };
  
  return (
    <div className="space-y-2">
      <Label htmlFor="product">Product</Label>
      
      <div className="flex gap-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={open}
              className="justify-between w-full"
            >
              {selectedProduct 
                ? `${selectedProduct.name}${selectedProduct.sku ? ` (${selectedProduct.sku})` : ''}`
                : "Select a product..."}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="p-0 w-[300px]">
            <Command>
              <CommandInput 
                placeholder="Search products..." 
                value={searchTerm}
                onValueChange={setSearchTerm}
              />
              <CommandList>
                <CommandEmpty>
                  {isLoading ? 'Loading...' : 'No products found.'}
                </CommandEmpty>
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setCreateDialogOpen(true);
                      setOpen(false);
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create new product
                  </CommandItem>
                  {selectedProductId && (
                    <CommandItem onSelect={handleClearSelection}>
                      Clear selection
                    </CommandItem>
                  )}
                </CommandGroup>
                <CommandGroup>
                  {products.map((product) => (
                    <CommandItem
                      key={product.id}
                      value={product.id}
                      onSelect={() => handleSelectProduct(product.id)}
                    >
                      <Check
                        className={`mr-2 h-4 w-4 ${
                          selectedProductId === product.id ? "opacity-100" : "opacity-0"
                        }`}
                      />
                      <div className="flex flex-col">
                        <span>
                          {product.name}
                          {product.sku && <span className="text-muted-foreground"> ({product.sku})</span>}
                        </span>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
      
      {/* Create Product Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Product</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="product-name">Product Name</Label>
              <Input
                id="product-name"
                placeholder="Enter product name"
                value={newProductName}
                onChange={(e) => setNewProductName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="product-sku">SKU (Optional)</Label>
              <Input
                id="product-sku"
                placeholder="Enter product SKU"
                value={newProductSku}
                onChange={(e) => setNewProductSku(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCreateDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateProduct}
              disabled={isCreating || !newProductName.trim()}
            >
              {isCreating ? 'Creating...' : 'Create Product'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default ProductSelector; 