
import { cn } from '../common/utils/utils';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Check, Info, MoreHorizontal, Trash } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '../../components/ui/dropdown-menu';

export interface AssetItem {
  id: string;
  name: string;
  type: string;
  preview: string;
  size: string;
  dateCreated: string;
  tags: string[];
}

interface EnhancedAssetGridProps {
  assets: AssetItem[];
  title: string;
  emptyMessage: string;
  onAssetClick?: (asset: AssetItem) => void;
  selectedAssets?: string[];
}

function AssetCard({ asset, isSelected = false, onClick }: {
  asset: AssetItem;
  isSelected?: boolean;
  onClick?: () => void;
}) {
  return (
    <Card 
      className={cn(
        "overflow-hidden transition-all border hover:border-primary/50", 
        isSelected && "ring-2 ring-primary border-primary"
      )}
    >
      <div 
        className="relative aspect-square cursor-pointer bg-muted" 
        onClick={onClick}
      >
        <img 
          src={asset.preview} 
          alt={asset.name} 
          className="h-full w-full object-cover transition-opacity hover:opacity-90"
        />
        {isSelected && (
          <div className="absolute top-2 right-2 bg-primary text-white rounded-full p-1">
            <Check size={14} />
          </div>
        )}
      </div>
      
      <CardContent className="p-3">
        <div className="flex items-start justify-between gap-2">
          <div className="truncate">
            <p className="font-medium truncate text-sm" title={asset.name}>
              {asset.name}
            </p>
            <div className="flex flex-wrap gap-1 mt-1">
              {asset.tags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs font-normal">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>View Details</DropdownMenuItem>
              <DropdownMenuItem>Download</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash size={14} className="mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground mt-2">
          <span>{asset.size}</span>
          <span>{asset.dateCreated}</span>
        </div>
      </CardContent>
    </Card>
  );
}

export function EnhancedAssetGrid({
  assets,
  title,
  emptyMessage,
  onAssetClick,
  selectedAssets = [],
}: EnhancedAssetGridProps) {
  if (assets.length === 0) {
    return (
      <div className="py-8 text-center">
        <div className="mb-2 text-muted-foreground">
          <Info size={36} className="mx-auto opacity-20" />
        </div>
        <h3 className="text-lg font-medium">{title} (0)</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {emptyMessage}
        </p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-medium mb-4">
        {title} ({assets.length})
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {assets.map((asset) => (
          <AssetCard
            key={asset.id}
            asset={asset}
            onClick={() => onAssetClick?.(asset)}
            isSelected={selectedAssets.includes(asset.id)}
          />
        ))}
      </div>
    </div>
  );
}

EnhancedAssetGrid.AssetCard = AssetCard;
