import React, { useEffect, useState } from 'react';
import { cn } from '../common/utils/utils';
import { ChevronRight, Home } from 'lucide-react';
import { Link, useLocation, useParams } from 'react-router-dom';
import { useOrganization } from '../common/hooks/useOrganizations';
import { useCollection } from '../common/hooks/useCollections';
import { useAsset } from '../common/hooks/useAssets';

interface SafeFragmentProps {
  children: React.ReactNode;
  // We don't include key in the interface as <PERSON>act handles it specially
}

// Safe Fragment wrapper that only accepts children prop
const SafeFragment = ({ children }: SafeFragmentProps) => {
  // Key is handled by React automatically when used as <SafeFragment key="value">
  return <React.Fragment>{children}</React.Fragment>;
};

interface BreadcrumbItem {
  label: string;
  href: string;
}

interface PageTitleProps {
  title: React.ReactNode;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
  breadcrumbs?: BreadcrumbItem[];
  hideBreadcrumbs?: boolean;
}

export function PageTitle({ 
  title, 
  subtitle, 
  action, 
  className,
  breadcrumbs,
  hideBreadcrumbs = false
}: PageTitleProps) {
  const location = useLocation();
  const params = useParams();
  const isDashboard = location.pathname === '/dashboard';
  const [organizationName, setOrganizationName] = useState<string | null>(null);
  const [collectionName, setCollectionName] = useState<string | null>(null);

  // Parse path segments
  const pathSegments = location.pathname.split('/').filter(Boolean);
  const isClientPage = pathSegments[0] === 'clients' && pathSegments.length > 1;
  const isOrgPage = pathSegments[0] === 'organizations' && pathSegments.length > 1;
  const clientId = isClientPage ? pathSegments[1] : null;
  const orgId = isOrgPage ? pathSegments[1] : null;
  const isCollectionPage = (isClientPage || isOrgPage) && pathSegments[2] === 'collections' && pathSegments.length > 3;
  const collectionId = isCollectionPage ? pathSegments[3] : null;
  const isAssetPage = isCollectionPage && pathSegments[4] === 'assets' && pathSegments.length > 5;
  const assetId = isAssetPage ? pathSegments[5] : null;

  // Always fetch client and collection data, but they'll only return data if IDs are defined
  const { data: organization } = useOrganization(clientId || orgId || undefined);
  const { data: collection } = useCollection(collectionId || undefined);
  const { data: asset } = useAsset(assetId || undefined);

  // Update names when data is available
  useEffect(() => {
    if (organization) {
      setOrganizationName(organization.name);
    }
  }, [organization]);

  useEffect(() => {
    if (collection) {
      setCollectionName(collection.name);
    }
  }, [collection]);

  // Don't show breadcrumbs on dashboard
  const shouldShowBreadcrumbs = !isDashboard && !hideBreadcrumbs;

  const renderBreadcrumbItem = (segment: string, index: number, array: string[]) => {
    const path = `/${array.slice(0, index + 1).join('/')}`;
    
    // Skip "collections" and "assets" segments
    if (segment === 'collections' || segment === 'assets') {
      return null;
    }
    
    // Special case for organization ID - use organization name if available
    if (index === 1 && (array[0] === 'clients' || array[0] === 'organizations') && organizationName && segment === (clientId || orgId)) {
      return (
        <SafeFragment key={`org-${path}`}>
          <ChevronRight size={14} className="mx-1.5" />
          {index === array.length - 1 ? (
            <span className="font-medium text-foreground">{organizationName}</span>
          ) : (
            <Link 
              to={path}
              className="hover:text-foreground transition-colors"
            >
              {organizationName}
            </Link>
          )}
        </SafeFragment>
      );
    }
    
    // Special case for collection ID - use collection name if available
    if ((array[0] === 'clients' || array[0] === 'organizations') && collectionName && segment === collectionId) {
      return (
        <SafeFragment key={`collection-${path}`}>
          <ChevronRight size={14} className="mx-1.5" />
          {index === array.length - 1 ? (
            <span className="font-medium text-foreground">{collectionName}</span>
          ) : (
            <Link 
              to={path}
              className="hover:text-foreground transition-colors"
            >
              {collectionName}
            </Link>
          )}
        </SafeFragment>
      );
    }

    // Special case for asset ID - use asset filename if available
    if ((array[0] === 'clients' || array[0] === 'organizations') && asset && segment === assetId) {
      return (
        <SafeFragment key={`asset-${path}`}>
          <ChevronRight size={14} className="mx-1.5" />
          <span className="font-medium text-foreground">
            {asset.file_name || segment}
          </span>
        </SafeFragment>
      );
    }

    // Format the segment for display (capitalize, replace hyphens)
    const label = segment
      .replace(/-/g, ' ')
      .replace(/^\w/, c => c.toUpperCase());
    
    // Skip rendering if it's a filtered segment
    if (segment === 'collections' || segment === 'assets') {
      return null;
    }
    
    return (
      <SafeFragment key={`segment-${path}`}>
        <ChevronRight size={14} className="mx-1.5" />
        {index === array.length - 1 ? (
          <span className="font-medium text-foreground">{label}</span>
        ) : (
          <Link 
            to={path}
            className="hover:text-foreground transition-colors"
          >
            {label}
          </Link>
        )}
      </SafeFragment>
    );
  };

  return (
    <div className={cn("space-y-3 mb-6", className)}>
      {shouldShowBreadcrumbs && (
        <nav className="flex items-center text-sm text-muted-foreground">
          <Link 
            to="/" 
            className="flex items-center hover:text-foreground transition-colors"
          >
            <Home size={14} className="mr-1" />
            Home
          </Link>
          
          {breadcrumbs ? (
            // Use provided breadcrumbs
            breadcrumbs.map((item, index) => (
              <SafeFragment key={item.href}>
                <ChevronRight size={14} className="mx-1.5" />
                {index === breadcrumbs.length - 1 ? (
                  <span className="font-medium text-foreground">{item.label}</span>
                ) : (
                  <Link 
                    to={item.href}
                    className="hover:text-foreground transition-colors"
                  >
                    {item.label}
                  </Link>
                )}
              </SafeFragment>
            ))
          ) : (
            // Auto-generate breadcrumb from current path
            pathSegments
              .map((segment, index, array) => renderBreadcrumbItem(segment, index, array))
              .filter(Boolean)
          )}
        </nav>
      )}
      
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
        <div className="space-y-1">
          <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          {subtitle && <p className="text-muted-foreground">{subtitle}</p>}
        </div>
        {action && <div className="flex-shrink-0">{action}</div>}
      </div>
    </div>
  );
}

export default PageTitle;