import React, { useState } from 'react';
import { User } from 'lucide-react';
import { cn } from '../common/utils/utils';

interface ModelImageProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  showPlaceholder?: boolean;
  onError?: () => void;
  onLoad?: () => void;
}

export function ModelImage({ 
  src, 
  alt, 
  className = '', 
  fallbackSrc,
  showPlaceholder = true,
  onError,
  onLoad 
}: ModelImageProps) {
  const [imageError, setImageError] = useState(false);
  const [fallbackError, setFallbackError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleFallbackError = () => {
    setFallbackError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // If both main image and fallback failed, show placeholder
  if ((imageError && fallbackError) || (imageError && !fallbackSrc)) {
    if (!showPlaceholder) return null;
    
    return (
      <div className={cn(
        "flex items-center justify-center bg-gray-100 text-gray-400",
        className
      )}>
        <User className="w-8 h-8" />
      </div>
    );
  }

  // If main image failed but we have a fallback, show fallback
  if (imageError && fallbackSrc && !fallbackError) {
    return (
      <img
        src={fallbackSrc}
        alt={alt}
        className={className}
        onError={handleFallbackError}
        onLoad={handleImageLoad}
      />
    );
  }

  // Show main image
  return (
    <>
      {isLoading && showPlaceholder && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400",
          className
        )}>
          <User className="w-8 h-8" />
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={cn(className, isLoading ? 'opacity-0' : 'opacity-100')}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
    </>
  );
}
