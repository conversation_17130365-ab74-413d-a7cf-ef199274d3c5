import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Eye, AlertCircle, CheckCircle, FileImage } from 'lucide-react';
import { Button } from './button';
import { Progress } from './progress';
import { Badge } from './badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';

interface EnhancedDropzoneProps {
  onFilesChange: (files: File[]) => void;
  currentFiles: File[];
  accept?: Record<string, string[]>;
  maxFiles?: number;
  minFiles?: number;
  maxSize?: number;
  title: string;
  description?: string;
  className?: string;
  disabled?: boolean;
  showPreviews?: boolean;
  validationRules?: {
    minFiles?: number;
    maxFiles?: number;
    maxSize?: number;
    allowedTypes?: string[];
  };
}

interface FileWithPreview extends File {
  preview?: string;
  id: string;
  error?: string;
}

export function EnhancedDropzone({
  onFilesChange,
  currentFiles,
  accept = { 'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif'] },
  maxFiles = 10,
  minFiles = 0,
  maxSize = 10 * 1024 * 1024, // 10MB
  title,
  description,
  className = '',
  disabled = false,
  showPreviews = true,
  validationRules
}: EnhancedDropzoneProps) {
  const [filesWithPreview, setFilesWithPreview] = useState<FileWithPreview[]>([]);
  const [previewFile, setPreviewFile] = useState<{ file: File; url: string } | null>(null);
  const [dragActive, setDragActive] = useState(false);

  // Validate individual file
  const validateFile = (file: File): string | null => {
    const rules = validationRules || { minFiles, maxFiles, maxSize };
    
    // Check file size
    if (rules.maxSize && file.size > rules.maxSize) {
      return `File size must be less than ${(rules.maxSize / (1024 * 1024)).toFixed(1)}MB`;
    }

    // Check file type
    if (rules.allowedTypes) {
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || !rules.allowedTypes.includes(fileExtension)) {
        return `File type not allowed. Allowed types: ${rules.allowedTypes.join(', ')}`;
      }
    }

    return null;
  };

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    const newFiles: FileWithPreview[] = [];
    
    // Process accepted files
    acceptedFiles.forEach(file => {
      const error = validateFile(file);
      const fileWithPreview: FileWithPreview = {
        ...file,
        id: Math.random().toString(36).substring(2, 9),
        error: error || undefined
      };

      // Create preview URL for images
      if (showPreviews && file.type.startsWith('image/')) {
        fileWithPreview.preview = URL.createObjectURL(file);
      }

      newFiles.push(fileWithPreview);
    });

    // Process rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      const fileWithPreview: FileWithPreview = {
        ...file,
        id: Math.random().toString(36).substring(2, 9),
        error: errors.map((e: any) => e.message).join(', ')
      };
      newFiles.push(fileWithPreview);
    });

    // Update state
    const updatedFiles = [...filesWithPreview, ...newFiles];
    setFilesWithPreview(updatedFiles);
    
    // Only pass valid files to parent
    const validFiles = updatedFiles.filter(f => !f.error);
    onFilesChange(validFiles);
  }, [filesWithPreview, onFilesChange, showPreviews, validationRules, minFiles, maxFiles, maxSize]);

  // Remove file
  const removeFile = (fileId: string) => {
    const updatedFiles = filesWithPreview.filter(f => f.id !== fileId);
    setFilesWithPreview(updatedFiles);
    
    const validFiles = updatedFiles.filter(f => !f.error);
    onFilesChange(validFiles);
  };

  // Preview file
  const previewFileHandler = (file: File) => {
    if (file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setPreviewFile({ file, url });
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept,
    maxFiles,
    maxSize,
    disabled,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  });

  const hasMinimumFiles = minFiles > 0 && filesWithPreview.filter(f => !f.error).length < minFiles;
  const hasErrors = filesWithPreview.some(f => f.error);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200
          ${isDragActive || dragActive ? 'border-primary bg-primary/5 scale-[1.02]' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
          ${hasErrors ? 'border-red-300 bg-red-50' : ''}
          ${hasMinimumFiles ? 'border-orange-300 bg-orange-50' : ''}
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-3">
          <div className="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
            <Upload className={`h-6 w-6 ${isDragActive ? 'text-primary' : 'text-gray-400'}`} />
          </div>
          <div>
            <p className="text-sm font-medium">
              {isDragActive ? `Drop files here` : title}
            </p>
            {description && (
              <p className="text-xs text-gray-500 mt-1">{description}</p>
            )}
          </div>
          <div className="flex justify-center gap-2 text-xs text-gray-500">
            <span>Max {maxFiles} files</span>
            <span>•</span>
            <span>Up to {(maxSize / (1024 * 1024)).toFixed(0)}MB each</span>
          </div>
        </div>
      </div>

      {/* File List */}
      {filesWithPreview.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">
              Uploaded Files ({filesWithPreview.filter(f => !f.error).length})
            </h4>
            {hasMinimumFiles && (
              <Badge variant="outline" className="text-orange-600 border-orange-300">
                Need {minFiles - filesWithPreview.filter(f => !f.error).length} more
              </Badge>
            )}
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filesWithPreview.map((file) => (
              <div
                key={file.id}
                className={`relative group border rounded-lg overflow-hidden ${
                  file.error ? 'border-red-200 bg-red-50' : 'border-gray-200 bg-white hover:shadow-md transition-shadow'
                }`}
              >
                {/* File preview/icon */}
                <div className="aspect-square relative">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <FileImage className="h-12 w-12 text-gray-400" />
                    </div>
                  )}

                  {/* Status indicator overlay */}
                  <div className="absolute top-2 right-2">
                    {!file.error ? (
                      <div className="bg-green-500 rounded-full p-1">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    ) : (
                      <div className="bg-red-500 rounded-full p-1">
                        <AlertCircle className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  {/* Action buttons overlay */}
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                    {showPreviews && file.type?.startsWith('image/') && !file.error && (
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => previewFileHandler(file)}
                        className="h-8 w-8 p-0 bg-white/90 hover:bg-white"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      className="h-8 w-8 p-0 bg-white/90 hover:bg-white text-red-500 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* File info */}
                <div className="p-3">
                  <p className="text-sm font-medium truncate" title={file.name}>
                    {file.name}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {(file.size / 1024).toFixed(1)} KB
                  </p>
                  {file.error && (
                    <p className="text-xs text-red-600 mt-1">{file.error}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Preview Dialog */}
      {previewFile && (
        <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>{previewFile.file.name}</DialogTitle>
            </DialogHeader>
            <div className="flex justify-center">
              <img
                src={previewFile.url}
                alt={previewFile.file.name}
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
