
import * as React from "react"
import * as TabsPrimitive from "@radix-ui/react-tabs"

import { cn } from "../common/utils/utils"

const Tabs = TabsPrimitive.Root

const TabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
      className
    )}
    {...props}
  />
))
TabsList.displayName = TabsPrimitive.List.displayName

const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm border-b-2 border-transparent data-[state=active]:border-primary",
      className
    )}
    {...props}
  />
))
TabsTrigger.displayName = TabsPrimitive.Trigger.displayName

const TabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 animate-fade-in",
      className
    )}
    {...props}
  />
))
TabsContent.displayName = TabsPrimitive.Content.displayName

// Updated WorkflowTabs styling to match the design in the screenshot
const WorkflowTabs = TabsPrimitive.Root

const WorkflowTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex w-full items-center justify-between rounded-md bg-transparent text-muted-foreground",
      className
    )}
    {...props}
  />
))
WorkflowTabsList.displayName = "WorkflowTabsList"

const WorkflowTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    completed?: boolean;
    active?: boolean;
    icon?: React.ReactNode;
  }
>(({ className, completed, active, icon, children, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "group relative flex flex-col items-center justify-center gap-1 whitespace-nowrap px-4 py-2 text-sm transition-all focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50",
      active ? "text-foreground" : "text-muted-foreground",
      completed ? "text-primary" : "",
      "border-b-2 border-transparent data-[state=active]:border-primary",
      className
    )}
    {...props}
  >
    {icon && (
      <span className="inline-flex items-center justify-center mb-1">
        {icon}
      </span>
    )}
    <span>{children}</span>
  </TabsPrimitive.Trigger>
))
WorkflowTabsTrigger.displayName = "WorkflowTabsTrigger"

// Add new simplified tab components for asset tabs
const AssetTabs = TabsPrimitive.Root

const AssetTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex w-full items-center justify-center border-b",
      className
    )}
    {...props}
  />
))
AssetTabsList.displayName = "AssetTabsList"

const AssetTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    icon?: React.ReactNode;
  }
>(({ className, icon, children, ...props }, ref) => (
  <TabsPrimitive.Trigger
    ref={ref}
    className={cn(
      "inline-flex flex-col items-center justify-center px-6 py-2.5 text-sm font-medium text-muted-foreground transition-all hover:text-foreground focus-visible:outline-none relative",
      "data-[state=active]:text-foreground",
      "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:w-full after:bg-transparent",
      "data-[state=active]:after:bg-primary",
      className
    )}
    {...props}
  >
    {icon && (
      <span className="inline-flex items-center justify-center mb-1">
        {icon}
      </span>
    )}
    <span>{children}</span>
  </TabsPrimitive.Trigger>
))
AssetTabsTrigger.displayName = "AssetTabsTrigger"

export { 
  Tabs, 
  TabsList, 
  TabsTrigger, 
  TabsContent, 
  WorkflowTabs, 
  WorkflowTabsList, 
  WorkflowTabsTrigger,
  AssetTabs,
  AssetTabsList,
  AssetTabsTrigger
}
