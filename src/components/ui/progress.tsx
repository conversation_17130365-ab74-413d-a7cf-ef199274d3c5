import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"
import { cn } from "../common/utils/utils"

interface ProgressProps extends 
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  indicatorClassName?: string;
  milestones?: number[];
  showPercentageOverlay?: boolean;
  showStepNames?: boolean;
  stepNames?: string[];
  currentStep?: number;
  totalSteps?: number;
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ 
  className, 
  value, 
  indicatorClassName, 
  milestones, 
  showPercentageOverlay = false,
  showStepNames = false,
  stepNames = [],
  currentStep = 0,
  totalSteps = 0,
  ...props 
}, ref) => (
  <div className="space-y-1">
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative h-2 w-full overflow-hidden rounded-full bg-muted",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className="h-full w-full flex-1 bg-primary transition-all"
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
      
      {/* Milestone markers */}
      {milestones?.map((milestone, index) => (
        <div
          key={index}
          className="absolute top-0 h-full w-0.5 bg-background z-10"
          style={{ left: `${milestone}%` }}
        />
      ))}

      {/* Percentage overlay */}
      {showPercentageOverlay && (
        <div className="absolute inset-0 flex items-center justify-center text-[10px] font-medium text-background">
          {value}%
        </div>
      )}
    </ProgressPrimitive.Root>

    {/* Step indicators with current step highlight */}
    {showStepNames && stepNames.length > 0 && (
      <div className="flex justify-between items-center mt-1">
        {stepNames.map((step, index) => {
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;
          
          return (
            <div 
              key={index} 
              className={cn(
                "text-xs transition-colors flex flex-col items-center", 
                isCompleted ? "text-primary" : 
                isCurrent ? "text-foreground font-medium" : "text-muted-foreground"
              )}
            >
              <div className={cn(
                "w-2 h-2 mb-1 rounded-full",
                isCompleted ? "bg-primary" : 
                isCurrent ? "bg-primary" : "bg-muted"
              )} />
              {step}
            </div>
          );
        })}
      </div>
    )}
  </div>
))
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }
