import React from 'react';
import { Check, Circle } from 'lucide-react';
import { cn } from '../common/utils/utils';

interface Step {
  title: string;
  description?: string;
}

interface StepIndicatorProps {
  steps: Step[];
  currentStep: number;
  className?: string;
  onStepClick?: (stepIndex: number) => void;
}

export function StepIndicator({ steps, currentStep, className, onStepClick }: StepIndicatorProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            {/* Step indicator */}
            <div 
              className={cn(
                "flex flex-col items-center", 
                index < currentStep && onStepClick ? "cursor-pointer" : ""
              )}
              onClick={() => {
                if (index < currentStep && onStepClick) {
                  onStepClick(index + 1);
                }
              }}
            >
              <div 
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors",
                  index < currentStep 
                    ? "bg-primary border-primary text-primary-foreground hover:bg-primary/90" 
                    : index === currentStep 
                      ? "border-primary text-primary" 
                      : "border-muted-foreground text-muted-foreground"
                )}
              >
                {index < currentStep ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-semibold">{index + 1}</span>
                )}
              </div>
              <span 
                className={cn(
                  "text-xs mt-1 text-center max-w-[80px]",
                  index === currentStep ? "text-primary font-medium" : "text-muted-foreground"
                )}
              >
                {step.title}
              </span>
            </div>
            
            {/* Connecting line (not for last item) */}
            {index < steps.length - 1 && (
              <div 
                className={cn(
                  "h-[2px] flex-1 mx-1",
                  index < currentStep ? "bg-primary" : "bg-muted"
                )}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

export default StepIndicator; 