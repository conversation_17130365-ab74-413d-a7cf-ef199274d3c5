import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from './button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';
import { Badge } from './badge';

interface AngleOption {
  id: string;
  name: string;
  description: string;
  imagePath: string;
  category: 'halfbody' | 'fullheight';
}

interface AngleSelectorProps {
  selectedAngles: string[];
  onAnglesChange: (angles: string[]) => void;
  requiredCount?: number;
  className?: string;
}

const ANGLE_OPTIONS: AngleOption[] = [
  {
    id: 'halfbody_front',
    name: 'Half-body Front',
    description: 'Half-body view from the front',
    imagePath: 'images/angles/halfbody_front.jpg',
    category: 'halfbody'
  },
  {
    id: 'halfbody_back',
    name: 'Half-body Back',
    description: 'Half-body view from the back',
    imagePath: 'images/angles/halfbody_back.jpg',
    category: 'halfbody'
  },
  {
    id: 'halfbody_3_4_left',
    name: 'Half-body 3/4 Left',
    description: 'Half-body three-quarter view from the left',
    imagePath: 'images/angles/halfbody_3:4_left.jpg',
    category: 'halfbody'
  },
  {
    id: 'halfbody_3_4_right',
    name: 'Half-body 3/4 Right',
    description: 'Half-body three-quarter view from the right',
    imagePath: 'images/angles/halfbody_3:4_right.jpg',
    category: 'halfbody'
  },
  {
    id: 'fullheight_front',
    name: 'Full-height Front',
    description: 'Full-height view from the front',
    imagePath: 'images/angles/fullheight_front.jpg',
    category: 'fullheight'
  },
  {
    id: 'fullheight_back',
    name: 'Full-height Back',
    description: 'Full-height view from the back',
    imagePath: 'images/angles/fullheight_back.jpg',
    category: 'fullheight'
  },
  {
    id: 'fullheight_side_left',
    name: 'Full-height Side Left',
    description: 'Full-height side view from the left',
    imagePath: 'images/angles/fullheight_side_left.jpg',
    category: 'fullheight'
  },
  {
    id: 'fullheight_side_right',
    name: 'Full-height Side Right',
    description: 'Full-height side view from the right',
    imagePath: 'images/angles/fullheight_side_right.jpg',
    category: 'fullheight'
  },
  {
    id: 'fullheight_3_4_left',
    name: 'Full-height 3/4 Left',
    description: 'Full-height three-quarter view from the left',
    imagePath: 'images/angles/fullheight_3:4_left.jpg',
    category: 'fullheight'
  },
  {
    id: 'fullheight_3_4_right',
    name: 'Full-height 3/4 Right',
    description: 'Full-height three-quarter view from the right',
    imagePath: 'images/angles/fullheight_3:4_right.jpg',
    category: 'fullheight'
  }
];

export function AngleSelector({ selectedAngles, onAnglesChange, requiredCount, className = '' }: AngleSelectorProps) {
  const [previewAngle, setPreviewAngle] = useState<AngleOption | null>(null);

  const toggleAngle = (angleId: string) => {
    if (selectedAngles.includes(angleId)) {
      onAnglesChange(selectedAngles.filter(id => id !== angleId));
    } else {
      onAnglesChange([...selectedAngles, angleId]);
    }
  };

  const previewImage = (angle: AngleOption) => {
    setPreviewAngle(angle);
  };

  const halfbodyAngles = ANGLE_OPTIONS.filter(angle => angle.category === 'halfbody');
  const fullheightAngles = ANGLE_OPTIONS.filter(angle => angle.category === 'fullheight');

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Counter Display and Validation */}
      {requiredCount && (
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            You have chosen <span className="font-semibold">{requiredCount}</span> AI images per product. 
            Please specify below which angles we should use.
          </p>
          <div className={`p-3 rounded-lg border ${
            selectedAngles.length === requiredCount 
              ? 'bg-green-50 border-green-200' 
              : 'bg-amber-50 border-amber-200'
          }`}>
            <p className="text-sm">
              <span className="font-medium">AI images per product:</span> {requiredCount}<br />
              <span className="font-medium">Angles selected:</span> {selectedAngles.length} 
              {selectedAngles.length !== requiredCount && (
                <span className="text-amber-700 ml-2">
                  (must match AI images per product)
                </span>
              )}
            </p>
          </div>
        </div>
      )}

      {/* Selected angles summary */}
      {selectedAngles.length > 0 && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Selected Angles ({selectedAngles.length})</h4>
          <div className="flex flex-wrap gap-2">
            {selectedAngles.map(angleId => {
              const angle = ANGLE_OPTIONS.find(a => a.id === angleId);
              return angle ? (
                <Badge key={angleId} variant="secondary" className="bg-blue-100 text-blue-700">
                  {angle.name}
                </Badge>
              ) : null;
            })}
          </div>
        </div>
      )}

      {/* Half-body angles */}
      <div>
        <h4 className="text-lg font-medium mb-4">Half-body Angles</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {halfbodyAngles.map((angle) => {
            const isSelected = selectedAngles.includes(angle.id);
            return (
              <div
                key={angle.id}
                className={`
                  relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200
                  ${isSelected ? 'border-primary bg-primary/5 ring-2 ring-primary/20' : 'border-gray-200 hover:border-gray-300'}
                `}
                onClick={() => toggleAngle(angle.id)}
              >
                {/* Image */}
                <div className="aspect-[3/4] bg-gray-100 relative">
                  <img
                    src={angle.imagePath}
                    alt={angle.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden w-full h-full flex items-center justify-center text-gray-400 text-sm">
                    Image not found
                  </div>
                  
                  {/* Selection indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  )}
                  
                  {/* Preview button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 left-2 w-8 h-8 p-0 bg-black/50 hover:bg-black/70 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      previewImage(angle);
                    }}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Label */}
                <div className="p-3">
                  <p className="text-sm font-medium text-center">{angle.name}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Full-height angles */}
      <div>
        <h4 className="text-lg font-medium mb-4">Full-height Angles</h4>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {fullheightAngles.map((angle) => {
            const isSelected = selectedAngles.includes(angle.id);
            return (
              <div
                key={angle.id}
                className={`
                  relative border-2 rounded-lg overflow-hidden cursor-pointer transition-all duration-200
                  ${isSelected ? 'border-primary bg-primary/5 ring-2 ring-primary/20' : 'border-gray-200 hover:border-gray-300'}
                `}
                onClick={() => toggleAngle(angle.id)}
              >
                {/* Image */}
                <div className="aspect-[3/4] bg-gray-100 relative">
                  <img
                    src={angle.imagePath}
                    alt={angle.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                  <div className="hidden w-full h-full flex items-center justify-center text-gray-400 text-sm">
                    Image not found
                  </div>
                  
                  {/* Selection indicator */}
                  {isSelected && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <Check className="w-4 h-4 text-white" />
                    </div>
                  )}
                  
                  {/* Preview button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 left-2 w-8 h-8 p-0 bg-black/50 hover:bg-black/70 text-white"
                    onClick={(e) => {
                      e.stopPropagation();
                      previewImage(angle);
                    }}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Label */}
                <div className="p-3">
                  <p className="text-sm font-medium text-center">{angle.name}</p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Preview Dialog */}
      {previewAngle && (
        <Dialog open={!!previewAngle} onOpenChange={() => setPreviewAngle(null)}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>{previewAngle.name}</DialogTitle>
            </DialogHeader>
            <div className="flex justify-center">
              <img
                src={previewAngle.imagePath}
                alt={previewAngle.name}
                className="max-w-full max-h-[70vh] object-contain"
              />
            </div>
            <p className="text-sm text-gray-600 text-center">{previewAngle.description}</p>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
