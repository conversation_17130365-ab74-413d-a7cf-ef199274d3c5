# Asset Detail Components

This folder contains components specifically for **individual asset management** - viewing, commenting, and managing single assets in detail.

> **Note**: This folder was renamed from `asset/` to `asset-detail/` for better clarity of purpose.

## 📋 Component Overview

### `AssetComments.tsx`
- **Purpose**: Comment system for individual assets
- **Features**: Add, view, edit comments with optional coordinate annotations
- **Usage**: Used in asset detail pages for feedback and collaboration

### `AssetDetailTabs.tsx`
- **Purpose**: Tab navigation for asset detail views  
- **Features**: Switches between different views (metadata, comments, actions)
- **Usage**: Main navigation component for asset detail pages

### `AssetMetadata.tsx`
- **Purpose**: Display asset metadata and properties
- **Features**: Shows file info, dimensions, workflow stage, tags
- **Usage**: Metadata tab in asset detail view

### `AssetRelated.tsx`  
- **Purpose**: Display related or similar assets
- **Features**: Asset recommendations and related content
- **Usage**: Suggestions section in asset detail view

## 📁 AssetDetailPage/
Complete asset detail page implementation with tabbed interface:

### `index.tsx` - Main Asset Detail Page
- **Purpose**: Main container for asset detail view
- **Features**: Coordinates all tabs and asset data loading
- **Usage**: Primary page component for `/assets/:id` routes

### `ActionsTab.tsx`
- **Purpose**: Asset action controls (approve, reject, request changes)
- **Features**: Workflow actions, asset status management
- **Permissions**: Role-based action availability

### `AnnotationLayer.tsx`
- **Purpose**: Visual annotation system for asset images
- **Features**: Click-to-add comments with coordinates, visual markers
- **Usage**: Interactive layer over asset images

### `AssetPreviewSection.tsx`
- **Purpose**: Large asset image display with zoom/pan
- **Features**: Image viewer with annotation support
- **Usage**: Main visual area of asset detail page

### `CollapsibleCard.tsx`
- **Purpose**: Reusable collapsible container for detail sections
- **Features**: Expandable/collapsible content areas
- **Usage**: Organizing asset detail information

### `CommentsTab.tsx`
- **Purpose**: Complete comments interface for asset
- **Features**: Comment threads, replies, mentions, annotation viewing
- **Usage**: Comments tab in asset detail view

### `MetadataTab.tsx`
- **Purpose**: Comprehensive metadata display and editing
- **Features**: File properties, custom metadata, tag management
- **Usage**: Metadata tab in asset detail view

### `TabbedInterface.tsx`
- **Purpose**: Tab container and management logic
- **Features**: Tab switching, state management, responsive layout
- **Usage**: Container for all asset detail tabs

## 🔄 Relationship with `../asset-management/`

**Important Distinction**:
- **`asset-detail/` (this folder)**: Components for viewing/managing **single assets** in detail
- **`../asset-management/`**: Components for **bulk operations** on multiple assets

### When to use `asset-detail/` components:
- Asset detail pages (`/assets/:id`)
- Individual asset editing
- Single asset comments/annotations
- Asset metadata viewing

### When to use `../asset-management/` components:
- Asset grid/list views
- Bulk tagging operations
- Multi-asset selection
- Bulk workflow changes
- Asset management dashboards

## 🎨 Design Patterns

### Role-Based Features
Components respect user roles for feature availability:

```typescript
const { isPlatformUser, isBrandAdmin } = useUserRole();

// Example: Action availability
const canApprove = isPlatformUser || isBrandAdmin;
const canDelete = isPlatformUser;
```

### Asset Data Flow
1. **Asset ID** from route parameters
2. **Asset data** fetched in main component
3. **Shared state** passed to child components
4. **Updates** propagated back through callbacks

### Comment System
- **Coordinate-based** comments for visual feedback
- **Thread-based** discussions for general feedback
- **Mention system** for user notifications
- **Role-based** comment permissions

## 🚀 Usage Examples

### Basic Asset Detail Page
```typescript
import AssetDetailPage from './AssetDetailPage';

// In your route component
<Route 
  path="/assets/:id" 
  element={<AssetDetailPage />} 
/>
```

### Custom Asset Comments
```typescript
import AssetComments from './AssetComments';

<AssetComments 
  assetId={assetId}
  canComment={userCanComment}
  onCommentAdded={handleCommentAdded}
/>
```

### Asset Metadata Display
```typescript
import AssetMetadata from './AssetMetadata';

<AssetMetadata 
  asset={assetData}
  editable={canEditMetadata}
  onUpdate={handleMetadataUpdate}
/>
```

## 🔧 Development Notes

### Performance Considerations
- **Lazy loading** for large asset images
- **Virtualization** for long comment threads  
- **Debounced** metadata updates
- **Cached** asset data to prevent refetching

### Accessibility
- **Keyboard navigation** for tab interfaces
- **Screen reader** support for annotations
- **Focus management** in modal dialogs
- **Alt text** for asset images

### Testing
- **Unit tests** for individual components
- **Integration tests** for tab interactions
- **Visual tests** for annotation placement
- **Permission tests** for role-based features