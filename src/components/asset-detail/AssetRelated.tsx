import { useState, useEffect } from 'react';
import { Asset } from '../common/types/database.types';
import { supabase } from '../common/utils/supabase';
import { useToast } from '../common/hooks/use-toast';
import { Loader2, ImageIcon } from 'lucide-react';
import { cn, getAssetUrl } from '../common/utils/utils';
import { AssetMetadata } from '../common/types/assetTypes';

interface AssetRelatedProps {
  asset: Asset;
}

 export function AssetRelated({ asset }: AssetRelatedProps) {
  const { toast } = useToast();
  const [relatedAssets, setRelatedAssets] = useState<Asset[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRelatedAssets = async () => {
      if (!asset || !asset.product_id) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('assets')
          .select('*')
          .eq('product_id', asset.product_id)
          .neq('id', asset.id)
          .limit(8);

        if (error) throw error;
        setRelatedAssets(data || []);
      } catch (error) {
        console.error('Error fetching related assets:', error);
        toast({
          title: 'Error',
          description: 'Could not load related assets',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelatedAssets();
  }, [asset, toast]);

  // Get asset preview URL (use thumbnail if available)
  const getAssetPreviewUrl = (asset: Asset) => {
    try {
      // Check if the asset has metadata with thumbnail path
      // Use the optimized asset URL function
      return getAssetUrl(asset, true); // true for thumbnail
    } catch (error) {
      console.error('Error getting asset preview URL:', error);
      return '';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (relatedAssets.length === 0) {
    return (
      <div className="text-center py-8">
        <ImageIcon className="h-12 w-12 mx-auto text-muted-foreground/30" />
        <p className="text-muted-foreground mt-2">No related assets found</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
      {relatedAssets.map((relatedAsset) => (
        <div
          key={relatedAsset.id}
          className="group relative aspect-square overflow-hidden rounded-lg bg-muted"
        >
          <img
            src={getAssetPreviewUrl(relatedAsset)}
            alt={relatedAsset.file_name}
            className={cn(
              "h-full w-full object-cover transition-all",
              "group-hover:scale-105"
            )}
          />
          <div className={cn(
            "absolute inset-0 bg-black/50 flex items-end p-2",
            "opacity-0 group-hover:opacity-100 transition-opacity"
          )}>
            <p className="text-xs text-white truncate">
              {relatedAsset.file_name}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
} 