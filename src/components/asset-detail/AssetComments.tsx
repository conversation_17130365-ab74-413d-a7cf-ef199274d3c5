import React, { useState, useEffect, forwardRef, useImperativeHandle, useRef } from 'react';
import { Asset, Comment, CommentStatus } from '../common/types/database.types';
import { supabase } from '../common/utils/supabase';
import { useToast } from '../common/hooks/use-toast';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Avatar } from '../ui/avatar';
import { Plus, MessageSquare, Send } from 'lucide-react';
import { cn } from '../common/utils/utils';

interface Coordinates {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface AssetCommentsProps {
  asset: Asset;
  onAnnotationStart?: () => void;
  onAnnotationEnd?: () => void;
}

interface CommentWithUser extends Omit<Comment, 'user'> {
  user: {
    name: string;
    avatar_url: string;
  };
}

export const AssetComments = forwardRef<
  { handleAnnotate: (coordinates: Coordinates) => void },
  AssetCommentsProps
>(({ asset, onAnnotationStart, onAnnotationEnd }, ref) => {
  const { toast } = useToast();
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAnnotating, setIsAnnotating] = useState(false);
  const [pendingAnnotation, setPendingAnnotation] = useState<Coordinates | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const commentsEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchComments();
  }, [asset.id]);

  useEffect(() => {
    scrollToBottom();
  }, [comments]);

  const scrollToBottom = () => {
    if (commentsEndRef.current && scrollAreaRef.current) {
      commentsEndRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  };

  const fetchComments = async () => {
    try {
      const { data: commentsData, error: commentsError } = await supabase
        .from('comments')
        .select('*')
        .eq('asset_id', asset.id)
        .order('created_at', { ascending: true });

      if (commentsError) throw commentsError;

      // Get the current authenticated user
      const { data: { user: currentUser } } = await supabase.auth.getUser();

      const commentsWithUser = commentsData.map(comment => ({
        ...comment,
        user: {
          name: comment.user_id === currentUser?.id ? 
            'You' : 
            'User ' + comment.user_id.slice(0, 4),
          avatar_url: comment.user_id === currentUser?.id ?
            currentUser.user_metadata?.avatar_url || '' :
            ''
        }
      })) as CommentWithUser[];

      setComments(commentsWithUser);
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to load comments: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      // Get the current authenticated user
      const { data: { user: currentUser } } = await supabase.auth.getUser();
      
      if (!currentUser) {
        throw new Error('You must be logged in to comment');
      }

      const commentData = {
        asset_id: asset.id,
        user_id: currentUser.id,
        content: newComment.trim(),
        is_annotation: !!pendingAnnotation,
        coordinates: pendingAnnotation ? {
          x: pendingAnnotation.x,
          y: pendingAnnotation.y,
          width: pendingAnnotation.width,
          height: pendingAnnotation.height,
        } : null,
        status: 'open' as CommentStatus,
      };

      const { error } = await supabase.from('comments').insert(commentData);

      if (error) throw error;

      setNewComment('');
      setPendingAnnotation(null);
      setIsAnnotating(false);
      fetchComments();
      toast({
        title: 'Success',
        description: 'Comment added successfully',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to add comment: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAnnotate = (coordinates: Coordinates) => {
    setPendingAnnotation(coordinates);
  };

  const toggleAnnotationMode = () => {
    const newIsAnnotating = !isAnnotating;
    setIsAnnotating(newIsAnnotating);
    if (newIsAnnotating) {
      onAnnotationStart?.();
    } else {
      onAnnotationEnd?.();
    }
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
    });
  };

  // Expose handleAnnotate to parent through ref
  useImperativeHandle(ref, () => ({
    handleAnnotate: (coordinates: Coordinates) => {
      setPendingAnnotation(coordinates);
    }
  }));

  // Handle Enter key to submit comment
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (newComment.trim()) {
        handleSubmitComment();
      }
    }
  };

  return (
    <div className="flex flex-col h-full max-h-full">
      {/* Header */}
      <div className="p-2 border-b flex items-center justify-between shrink-0">
        <h3 className="font-medium">Comments & Annotations</h3>
        <Button
          variant={isAnnotating ? "secondary" : "outline"}
          size="sm"
          onClick={toggleAnnotationMode}
          className="gap-1"
        >
          <Plus className="h-3.5 w-3.5" />
          {isAnnotating ? "Cancel" : "Add Annotation"}
        </Button>
      </div>

      {/* Comment History - Set to grow but allow shrinking */}
      <div 
        className="flex-1 overflow-y-auto min-h-0"
        ref={scrollAreaRef}
      >
        <div className="p-2 space-y-2 pb-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <span className="text-sm text-muted-foreground">Loading comments...</span>
            </div>
          ) : comments.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-4 text-center text-muted-foreground">
              <MessageSquare className="h-6 w-6 mb-1" />
              <p className="text-sm">No comments yet</p>
            </div>
          ) : (
            <>
              {comments.map((comment) => {
                const isCurrentUser = comment.user.name === 'You';
                return (
                  <div 
                    key={comment.id} 
                    className={cn(
                      "flex gap-2",
                      isCurrentUser ? "justify-end" : "justify-start"
                    )}
                  >
                    {!isCurrentUser && (
                      <Avatar className="h-6 w-6 mt-1 flex-shrink-0">
                        <div className="h-full w-full flex items-center justify-center bg-primary/10 text-primary text-xs font-medium">
                          {comment.user.name.charAt(0)}
                        </div>
                      </Avatar>
                    )}
                    <div 
                      className={cn(
                        "max-w-[80%] rounded-lg p-2 text-sm",
                        isCurrentUser 
                          ? "bg-primary text-primary-foreground" 
                          : "bg-muted"
                      )}
                    >
                      {!isCurrentUser && (
                        <div className="font-medium text-xs mb-1">{comment.user.name}</div>
                      )}
                      <p>{comment.content}</p>
                      {comment.is_annotation && (
                        <div className="mt-1 text-xs opacity-80">
                          <span>📍 Annotation</span>
                        </div>
                      )}
                      <div className="text-xs opacity-70 mt-1 text-right">
                        {comment.created_at ? formatDate(comment.created_at) : 'Unknown date'}
                      </div>
                    </div>
                  </div>
                );
              })}
              <div ref={commentsEndRef} />
            </>
          )}
        </div>
      </div>

      {/* Comment Input - Fixed at bottom */}
      <div className="border-t p-2 shrink-0 bg-background">
        <div className="flex items-end gap-2">
          <Textarea
            placeholder={isAnnotating ? "Describe this annotation..." : "Add a comment..."}
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            onKeyDown={handleKeyDown}
            className="min-h-[36px] max-h-[60px] resize-none flex-1 py-1 text-sm"
            rows={1}
          />
          <Button
            onClick={handleSubmitComment}
            disabled={isSubmitting || !newComment.trim()}
            size="icon"
            className="h-9 w-9 flex-shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        {pendingAnnotation && (
          <div className="mt-1 text-xs text-muted-foreground">
            <span className="bg-primary/10 text-primary px-2 py-0.5 rounded">
              Annotation ready to be posted
            </span>
          </div>
        )}
      </div>
    </div>
  );
}); 