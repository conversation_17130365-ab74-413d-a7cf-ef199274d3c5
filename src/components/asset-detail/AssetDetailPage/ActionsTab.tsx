import { useState } from 'react';
import { Tables } from '../../common/types/database.types';
import { Button } from '../../ui/button';
import { CollapsibleCard } from './CollapsibleCard';
import { useToast } from '../../common/hooks/use-toast';
import { supabase } from '../../common/utils/supabase';
import { getAssetUrl } from '../../common/utils/utils';
import { useNavigate } from 'react-router-dom';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '../../ui/alert-dialog';
import { Download, Share, Trash, Copy, FileUp } from 'lucide-react';

interface ActionsTabProps {
  asset: Tables<'assets'>;
  clientId: string;
  collectionId: string;
}

export function ActionsTab({ asset, clientId: orgId, collectionId }: ActionsTabProps) {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDownload = async () => {
    if (!asset) return;

    try {
      // Get the public URL using the optimized storage - now defaults to original (FAS-73)
      const url = getAssetUrl(asset, false, true); // true for download (original version)
      if (!url) throw new Error("Could not get asset URL.");

      // Show loading toast
      toast({
        title: 'Download starting...',
        description: `Preparing ${asset.file_name} (best available quality)`,
      });

      // Fetch the file to force download
      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to fetch file');
      
      const blob = await response.blob();
      
      // Create a blob URL and download
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = asset.file_name || 'download';
      
      // Force download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up blob URL
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

      toast({
        title: 'Download complete',
        description: `Downloaded ${asset.file_name}`,
      });

    } catch (error: any) {
      console.error("Download error:", error);
      toast({
        title: 'Error',
        description: `Failed to download: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const handleCopyLink = async () => {
    try {
      // For copy link, use compressed version for better sharing performance
      const url = getAssetUrl(asset, false); // false for compressed version

      await navigator.clipboard.writeText(url);

      toast({
        title: 'Link copied',
        description: 'Asset link copied to clipboard',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to copy link: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      // Delete the asset record
      const { error: deleteError } = await supabase
        .from('assets')
        .delete()
        .eq('id', asset.id);

      if (deleteError) throw deleteError;

      // Delete files from all storage buckets
      // Note: File deletion is handled by deleteAsset function in assetStorage.ts
      // For now, we'll skip storage deletion as the RLS policies will cascade

      if (storageError) {
        console.error('Failed to delete file from storage:', storageError);
        // Continue anyway as the database record is deleted
      }

      toast({
        title: 'Asset deleted',
        description: 'The asset has been successfully deleted',
      });

      // Navigate back to the collection using organization-based routing
      navigate(`/organizations/${orgId}/collections/${collectionId}`);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to delete asset: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  return (
    <div className="space-y-4 p-4">
      <CollapsibleCard title="Download & Share">
        <div className="grid grid-cols-2 gap-4">
          <Button 
            variant="outline" 
            className="w-full flex items-center gap-2"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4" />
            Download
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full flex items-center gap-2"
            onClick={handleCopyLink}
          >
            <Copy className="h-4 w-4" />
            Copy Link
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full flex items-center gap-2"
            onClick={() => {
              // This would typically open a share dialog
              toast({
                title: 'Share feature',
                description: 'Sharing functionality to be implemented',
              });
            }}
          >
            <Share className="h-4 w-4" />
            Share
          </Button>
          
          <Button 
            variant="outline" 
            className="w-full flex items-center gap-2"
            onClick={() => {
              // This would typically open a file upload dialog for a new version
              toast({
                title: 'Upload new version',
                description: 'Version control to be implemented',
              });
            }}
          >
            <FileUp className="h-4 w-4" />
            New Version
          </Button>
        </div>
      </CollapsibleCard>

      <CollapsibleCard title="Danger Zone">
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Deleting this asset will permanently remove it from the system. This action cannot be undone.
          </p>
          
          <Button 
            variant="destructive" 
            className="w-full flex items-center gap-2"
            onClick={() => setShowDeleteConfirm(true)}
            disabled={isDeleting}
          >
            <Trash className="h-4 w-4" />
            {isDeleting ? 'Deleting...' : 'Delete Asset'}
          </Button>
        </div>
      </CollapsibleCard>

      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the asset
              <strong> {asset.file_name} </strong>
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 