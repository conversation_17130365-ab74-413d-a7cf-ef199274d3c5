import { useState } from 'react';
import { Asset } from '../../common/types/database.types';
import { CollapsibleCard } from './CollapsibleCard';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Clock, Download, Eye, Star, Upload, GitBranch, CheckCircle2, AlertCircle, FileImage } from 'lucide-react';
import { formatDate, formatFileSize } from '../../common/utils/utils';
import { getAssetUrl } from '../../common/utils/utils';
import { useToast } from '../../common/hooks/use-toast';

interface VersionsTabProps {
  asset: Asset;
}

interface Version {
  id: string;
  versionNumber: string;
  uploadedAt: string;
  uploadedBy: string;
  fileSize: number;
  changes: string;
  status: 'current' | 'archived' | 'draft';
  tags: string[];
}

// Mock data for demonstration
const getMockVersions = (asset: Asset): Version[] => {
  const baseSize = asset.file_size || 2500000;
  return [
    {
      id: '1',
      versionNumber: 'v3.0',
      uploadedAt: new Date().toISOString(),
      uploadedBy: 'John Designer',
      fileSize: baseSize * 1.1,
      changes: 'Final retouched version with color correction and brand guidelines applied',
      status: 'current',
      tags: ['final', 'approved', 'high-res', 'client-approved']
    },
    {
      id: '2',
      versionNumber: 'v2.1',
      uploadedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      uploadedBy: 'Sarah Editor',
      fileSize: baseSize * 0.95,
      changes: 'Adjusted lighting, enhanced contrast, and removed distracting background elements',
      status: 'archived',
      tags: ['review', 'client-feedback', 'pending-approval']
    },
    {
      id: '3',
      versionNumber: 'v2.0',
      uploadedAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
      uploadedBy: 'Mike Photographer',
      fileSize: baseSize * 0.9,
      changes: 'Applied basic retouching, skin smoothing, and composition cropping',
      status: 'archived',
      tags: ['wip', 'internal-review']
    },
    {
      id: '4',
      versionNumber: 'v1.0',
      uploadedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      uploadedBy: 'Mike Photographer',
      fileSize: baseSize * 1.4,
      changes: 'Original upload from photoshoot - Studio Session #4521',
      status: 'archived',
      tags: ['original', 'raw', 'unedited']
    }
  ];
};

export function VersionsTab({ asset }: VersionsTabProps) {
  const { toast } = useToast();
  const [selectedVersion, setSelectedVersion] = useState<string>('1');
  const [showComparison, setShowComparison] = useState(false);
  const [compareVersions, setCompareVersions] = useState<[string, string]>(['1', '2']);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  
  const mockVersions = getMockVersions(asset);

  const handleDownloadVersion = async (version: Version) => {
    try {
      // For demo purposes, we'll use the same asset URL - now defaults to original (FAS-73)
      const url = getAssetUrl(asset, false, true); // true for download (original version)
      if (!url) throw new Error("Could not get asset URL.");

      toast({
        title: 'Download starting...',
        description: `Preparing ${asset.file_name} (${version.versionNumber}) - best available quality`,
      });

      const response = await fetch(url);
      if (!response.ok) throw new Error('Failed to fetch file');
      
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${asset.file_name?.split('.')[0]}_${version.versionNumber}.${asset.file_name?.split('.').pop()}`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);

      toast({
        title: 'Download complete',
        description: `Downloaded ${version.versionNumber}`,
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to download: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  const getStatusBadgeVariant = (status: Version['status']) => {
    switch (status) {
      case 'current':
        return 'default';
      case 'draft':
        return 'secondary';
      case 'archived':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-4 p-4">
      <div className="mb-4 p-4 bg-muted/50 rounded-lg border">
        <div className="flex items-center gap-2 mb-2">
          <GitBranch className="h-4 w-4 text-primary" />
          <h3 className="font-semibold text-sm">Version Control</h3>
        </div>
        <p className="text-xs text-muted-foreground">
          Track all iterations of this asset. Each version maintains a complete history with change descriptions and metadata.
        </p>
      </div>

      <CollapsibleCard title="Version History" defaultCollapsed={false}>
        <div className="mb-4 flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-xs"
            onClick={() => {
              toast({
                title: 'Timeline View',
                description: 'Visual timeline coming soon',
              });
            }}
          >
            <Clock className="w-3 h-3 mr-1" />
            Timeline
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="text-xs"
            onClick={() => {
              toast({
                title: 'Download All',
                description: 'Downloading all versions as ZIP',
              });
            }}
          >
            <Download className="w-3 h-3 mr-1" />
            Download All
          </Button>
        </div>
        
        <div className="relative">
          {mockVersions.map((version, index) => (
            <div key={version.id} className="relative">
              {index < mockVersions.length - 1 && (
                <div className="absolute left-6 top-12 bottom-0 w-0.5 bg-muted-foreground/20" />
              )}
              <div
                className={`relative border rounded-lg p-4 cursor-pointer transition-all duration-200 mb-3 ${
                  selectedVersion === version.id 
                    ? 'border-primary bg-primary/5 shadow-sm' 
                    : 'hover:bg-muted/50 hover:shadow-sm'
                }`}
                onClick={() => setSelectedVersion(version.id)}
              >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      version.status === 'current' ? 'bg-green-500' : 
                      version.status === 'draft' ? 'bg-yellow-500' : 
                      'bg-gray-400'
                    }`} />
                    <h4 className="font-semibold text-sm">{version.versionNumber}</h4>
                  </div>
                  <Badge variant={getStatusBadgeVariant(version.status)} className="text-xs">
                    {version.status === 'current' && <Star className="w-3 h-3 mr-1" />}
                    {version.status}
                  </Badge>
                  {version.id === '1' && (
                    <Badge variant="outline" className="text-xs">
                      <CheckCircle2 className="w-3 h-3 mr-1" />
                      Latest
                    </Badge>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      // In a real app, this would show a preview
                      toast({
                        title: 'Preview',
                        description: `Viewing ${version.versionNumber}`,
                      });
                    }}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownloadVersion(version);
                    }}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground mb-3">{version.changes}</p>
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  {formatDate(version.uploadedAt)}
                </span>
                <span>{version.uploadedBy}</span>
                <span className="flex items-center gap-1">
                  <FileImage className="w-3 h-3" />
                  {formatFileSize(version.fileSize)}
                </span>
              </div>
              
              {version.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {version.tags.map((tag) => (
                    <Badge 
                      key={tag} 
                      variant={tag === 'final' || tag === 'approved' ? 'default' : 'secondary'} 
                      className="text-xs"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
              </div>
            </div>
          ))}
        </div>
      </CollapsibleCard>

      <CollapsibleCard title="Upload New Version">
        <div className="space-y-4">
          <div 
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer group"
            onClick={() => {
              setIsUploading(true);
              setUploadProgress(0);
              
              // Simulate upload progress
              const interval = setInterval(() => {
                setUploadProgress(prev => {
                  if (prev >= 100) {
                    clearInterval(interval);
                    setIsUploading(false);
                    toast({
                      title: 'Upload complete',
                      description: 'New version v3.1 has been uploaded successfully',
                    });
                    return 100;
                  }
                  return prev + 10;
                });
              }, 200);
            }}
          >
            {!isUploading ? (
              <>
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Upload className="w-6 h-6 text-primary" />
                </div>
                <p className="text-sm font-medium mb-1">
                  Upload a new version
                </p>
                <p className="text-xs text-muted-foreground mb-3">
                  Drag and drop or click to browse
                </p>
                <Button variant="secondary" size="sm" onClick={(e) => e.stopPropagation()}>
                  <FileImage className="w-4 h-4 mr-2" />
                  Choose File
                </Button>
              </>
            ) : (
              <>
                <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-primary/20 flex items-center justify-center animate-pulse">
                  <Upload className="w-6 h-6 text-primary" />
                </div>
                <p className="text-sm font-medium mb-2">Uploading new version...</p>
                <div className="w-full max-w-xs mx-auto">
                  <div className="h-2 bg-muted rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-primary transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">{uploadProgress}%</p>
                </div>
              </>
            )}
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p className="font-medium mb-1">Version Guidelines:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>New versions should maintain the same aspect ratio</li>
              <li>File size limit: 50MB</li>
              <li>Supported formats: JPG, PNG, WebP</li>
              <li>Include a brief description of changes</li>
            </ul>
          </div>
        </div>
      </CollapsibleCard>

      <CollapsibleCard title="Version Comparison">
        {!showComparison ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
              <GitBranch className="w-8 h-8 text-muted-foreground" />
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Compare different versions to see what changed
            </p>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowComparison(true)}
            >
              Start Comparison
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Version A</label>
                <select 
                  className="w-full border rounded-md p-2 text-sm"
                  value={compareVersions[0]}
                  onChange={(e) => setCompareVersions([e.target.value, compareVersions[1]])}
                >
                  {mockVersions.map(v => (
                    <option key={v.id} value={v.id}>{v.versionNumber}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Version B</label>
                <select 
                  className="w-full border rounded-md p-2 text-sm"
                  value={compareVersions[1]}
                  onChange={(e) => setCompareVersions([compareVersions[0], e.target.value])}
                >
                  {mockVersions.map(v => (
                    <option key={v.id} value={v.id}>{v.versionNumber}</option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="border rounded-lg p-4 bg-muted/30">
              <div className="flex items-center gap-2 mb-3">
                <AlertCircle className="w-4 h-4 text-muted-foreground" />
                <p className="text-sm font-medium">Comparison View</p>
              </div>
              <p className="text-xs text-muted-foreground mb-3">
                In the full implementation, this would show a side-by-side or overlay comparison of the selected versions.
              </p>
              <div className="grid grid-cols-2 gap-4">
                <div className="aspect-square bg-muted rounded-md flex items-center justify-center">
                  <FileImage className="w-8 h-8 text-muted-foreground" />
                </div>
                <div className="aspect-square bg-muted rounded-md flex items-center justify-center">
                  <FileImage className="w-8 h-8 text-muted-foreground" />
                </div>
              </div>
            </div>
            
            <div className="flex justify-end gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowComparison(false)}
              >
                Close Comparison
              </Button>
            </div>
          </div>
        )}
      </CollapsibleCard>
    </div>
  );
}