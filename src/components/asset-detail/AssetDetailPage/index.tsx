import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAsset } from '../../common/hooks/useAssets';
import { useCollection } from '../../common/hooks/useCollections';
import { useOrganization } from '../../common/hooks/useOrganizations';
import { supabase } from '../../common/utils/supabase';
import { Comment } from '../../common/types/database.types';
import { Button } from '../../ui/button';
import PageTitle from '../../ui/PageTitle';
import { AssetPreviewSection } from './AssetPreviewSection';
import { TabbedInterface } from './TabbedInterface';
import { CommentsTab } from './CommentsTab';
import { MetadataTab } from './MetadataTab';
import { ActionsTab } from './ActionsTab';
import { VersionsTab } from './VersionsTab';
import { MessageSquare, Info, Settings, Layers } from 'lucide-react';
import { useToast } from '../../common/hooks/use-toast';

interface Coordinates {
  x: number;
  y: number;
  width: number;
  height: number;
}

export function AssetDetailPage() {
  const { orgId, collectionId, assetId } = useParams<{
    orgId: string;
    collectionId: string;
    assetId: string;
  }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [isAnnotating, setIsAnnotating] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [activeAnnotationId, setActiveAnnotationId] = useState<string | undefined>(undefined);
  const [pendingAnnotation, setPendingAnnotation] = useState<Coordinates | null>(null);
  const [activeTab, setActiveTab] = useState('comments');
  
  // Debug logging for state changes
  useEffect(() => {
    console.log("AssetDetailPage - State Update:");
    console.log("- isAnnotating:", isAnnotating);
    console.log("- pendingAnnotation:", pendingAnnotation);
    console.log("- activeTab:", activeTab);
    console.log("- activeAnnotationId:", activeAnnotationId);
  }, [isAnnotating, pendingAnnotation, activeTab, activeAnnotationId]);
  
  // Fetch data
  const { 
    data: asset, 
    isLoading: isLoadingAsset, 
    isError: isAssetError, 
    refetch: refreshAsset 
  } = useAsset(assetId);
  
  const { data: collection } = useCollection(collectionId);
  
  // The CommentsTab component handles fetching comments with user data
  // We only need to track comments for annotations display
  const fetchAnnotations = async () => {
    if (!assetId) return;
    
    try {
      const { data, error } = await supabase
        .from('comments')
        .select('*')
        .eq('asset_id', assetId)
        .eq('is_annotation', true)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      console.log("Fetched annotations:", data);
      setComments(data || []);
    } catch (error) {
      console.error('Error fetching annotations:', error);
    }
  };
  
  useEffect(() => {
    fetchAnnotations();
  }, [assetId]);
  
  const handleBack = () => {
    navigate(`/organizations/${orgId}/collections/${collectionId}`);
  };
  
  const handleAnnotationCreate = async (coordinates: Coordinates) => {
    console.log("Annotation created with coordinates:", coordinates);
    setPendingAnnotation(coordinates);
    setIsAnnotating(false);
    
    // Switch to comments tab
    setActiveTab('comments');
    
    // Show a toast to guide the user
    toast({
      title: 'Annotation Created',
      description: 'Please add a comment to describe this annotation',
      duration: 5000,
    });
  };
  
  const handleAnnotationCancel = () => {
    console.log("Annotation cancelled");
    setIsAnnotating(false);
    setPendingAnnotation(null);
  };
  
  const handlePendingAnnotationClear = () => {
    console.log("Pending annotation cleared");
    setPendingAnnotation(null);
    
    // Refresh annotations to see the newly added annotation
    fetchAnnotations();
  };
  
  const handleAnnotationToggle = (annotating: boolean) => {
    console.log("Annotation mode toggled:", annotating);
    setIsAnnotating(annotating);
    if (!annotating) {
      setPendingAnnotation(null);
    }
  };
  
  const handleAnnotationSelect = (annotationId: string) => {
    console.log("Annotation selected:", annotationId);
    setActiveAnnotationId(annotationId);
    setActiveTab('comments');
  };
  
  const handleCommentSelect = (commentId: string) => {
    console.log("Comment selected:", commentId);
    setActiveAnnotationId(commentId);
    
    // Find the comment to see if it's an annotation
    const comment = comments.find(c => c.id === commentId);
    if (comment?.is_annotation) {
      console.log("Selected comment is an annotation with coordinates:", comment.coordinates);
      // Scroll to the annotation in the preview
      const annotationElement = document.getElementById(`annotation-${commentId}`);
      if (annotationElement) {
        annotationElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };
  
  // Handle tab change
  const handleTabChange = (tabId: string) => {
    console.log("Tab changed to:", tabId);
    setActiveTab(tabId);
  };
  
  if (isLoadingAsset) {
    return <div className="p-8 text-center">Loading asset details...</div>;
  }
  
  if (isAssetError || !asset) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-bold mb-2">Error Loading Asset</h2>
        <p className="text-muted-foreground mb-4">
          There was a problem loading this asset. It may have been deleted or you may not have permission to view it.
        </p>
        <Button onClick={handleBack}>Back to Collection</Button>
      </div>
    );
  }
  
  const tabs = [
    {
      id: 'comments',
      label: 'Comments',
      icon: <MessageSquare className="h-4 w-4" />,
      content: (
        <CommentsTab
          asset={asset}
          pendingAnnotation={pendingAnnotation}
          onPendingAnnotationClear={handlePendingAnnotationClear}
          activeAnnotationId={activeAnnotationId}
          onCommentSelect={handleCommentSelect}
        />
      ),
    },
    {
      id: 'metadata',
      label: 'Details',
      icon: <Info className="h-4 w-4" />,
      content: (
        <MetadataTab
          asset={asset}
          onAssetUpdated={refreshAsset}
        />
      ),
    },
    {
      id: 'versions',
      label: 'Versions',
      icon: <Layers className="h-4 w-4" />,
      content: (
        <VersionsTab
          asset={asset}
        />
      ),
    },
    {
      id: 'actions',
      label: 'Actions',
      icon: <Settings className="h-4 w-4" />,
      content: (
        <ActionsTab
          asset={asset}
          clientId={orgId!}
          collectionId={collectionId!}
        />
      ),
    },
  ];
  
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <div className="p-3 border-b shrink-0">
        <PageTitle
          title={asset.file_name || 'Asset Details'}
          subtitle={collection ? `From collection: ${collection.name}` : ''}
          action={
            <Button variant="outline" onClick={handleBack}>
              Back to Collection
            </Button>
          }
        />
      </div>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Asset Preview */}
        <AssetPreviewSection
          asset={asset}
          annotations={comments.filter(c => c.is_annotation)}
          isAnnotating={isAnnotating}
          onAnnotationCreate={handleAnnotationCreate}
          onAnnotationCancel={handleAnnotationCancel}
          onAnnotationSelect={handleAnnotationSelect}
          onAnnotationToggle={handleAnnotationToggle}
        />
        
        {/* Tabbed Interface */}
        <div className="flex-1 border-t">
          <TabbedInterface
            tabs={tabs}
            defaultTabId={activeTab}
            onTabChange={handleTabChange}
          />
        </div>
      </div>
    </div>
  );
} 