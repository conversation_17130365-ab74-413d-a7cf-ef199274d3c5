import React, { useState, useRef, useEffect } from 'react';
import { Asset, Comment, CommentStatus } from '../../common/types/database.types';
import { Button } from '../../ui/button';
import { Textarea } from '../../ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '../../ui/avatar';
import { MessageSquare, Send, Plus, Trash2, AlertCircle, AtSign } from 'lucide-react';
import { cn } from '../../common/utils/utils';
import { Badge } from '../../ui/badge';
import { supabase } from '../../common/utils/supabase';
import { useToast } from '../../common/hooks/use-toast';
import { ScrollArea } from '../../ui/scroll-area';
import { useSupabase } from '../../../contexts/SupabaseContext';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../ui/alert-dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '../../ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../ui/popover';

interface Coordinates {
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
}

interface CommentWithUser extends Comment {
  user: {
    id: string;
    name: string;
    avatar_url: string | null;
    email: string;
  };
  mentions?: Array<{
    id: string;
    mentioned_user: {
      id: string;
      name: string;
      email: string;
    };
  }>;
}

interface CommentsTabProps {
  asset: Asset;
  pendingAnnotation: Coordinates | null;
  onPendingAnnotationClear: () => void;
  activeAnnotationId?: string;
  onCommentSelect?: (commentId: string) => void;
}

export function CommentsTab({
  asset,
  pendingAnnotation,
  onPendingAnnotationClear,
  activeAnnotationId,
  onCommentSelect,
}: CommentsTabProps) {
  const { toast } = useToast();
  const { user } = useSupabase();
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [commentToDelete, setCommentToDelete] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const commentInputRef = useRef<HTMLTextAreaElement>(null);
  
  // User mentions state
  const [mentionOpen, setMentionOpen] = useState(false);
  const [mentionSearch, setMentionSearch] = useState('');
  const [organizationUsers, setOrganizationUsers] = useState<Array<{
    id: string;
    email: string;
    profile: {
      name: string | null;
      avatar_url: string | null;
    } | null;
  }>>([]);
  const [selectedMentions, setSelectedMentions] = useState<string[]>([]);
  const [selectedMentionIndex, setSelectedMentionIndex] = useState(0);
  
  const currentUserId = user?.id || null;

  // Fetch organization users for mentions
  useEffect(() => {
    const fetchOrganizationUsers = async () => {
      if (!user) return;
      
      try {
        // First get the collection_id from the asset
        const { data: assetData } = await supabase
          .from('assets')
          .select('collection_id')
          .eq('id', asset.id)
          .single();
          
        if (!assetData || !assetData.collection_id) return;
        
        // Then get the organization_id from the collection
        const { data: collectionData } = await supabase
          .from('collections')
          .select('organization_id')
          .eq('id', assetData.collection_id)
          .single();
          
        if (!collectionData) return;
        
        const organizationId = collectionData.organization_id;
        
        // Fetch users in the same organization
        const { data: memberships } = await supabase
          .from('organization_memberships')
          .select('user_id')
          .eq('organization_id', organizationId)
          .neq('user_id', user.id); // Exclude current user
          
        // Also fetch superadmins and admins
        const { data: adminUsers } = await supabase
          .from('users')
          .select('id')
          .in('role', ['platform_admin', 'platform_super']);
          
        const allUserIds = [
          ...(memberships || []).map(m => m.user_id),
          ...(adminUsers || []).map(u => u.id)
        ].filter((id, index, self) => self.indexOf(id) === index && id !== user.id); // Unique IDs and exclude self
          
        if (allUserIds.length > 0) {
          // Get user details for all users
          const { data: usersData } = await supabase
            .from('users')
            .select('id, email, first_name, last_name, avatar_url, role')
            .in('id', allUserIds);
            
          const users = (usersData || []).map((userData) => {
            const fullName = userData.first_name && userData.last_name 
              ? `${userData.first_name} ${userData.last_name}`
              : userData.first_name || userData.last_name || userData.email.split('@')[0];
            
            return {
              id: userData.id,
              email: userData.email,
              profile: {
                name: fullName + (userData.role === 'platform_admin' ? ' (Platform Admin)' : userData.role === 'platform_super' ? ' (Platform Super)' : ''),
                avatar_url: userData.avatar_url
              }
            };
          });
          
          setOrganizationUsers(users as typeof organizationUsers);
        }
      } catch (error) {
        console.error("Error fetching organization users:", error);
      }
    };
    
    fetchOrganizationUsers();
  }, [user, asset.id]);
  
  // Debug logging for props
  useEffect(() => {
    console.log("CommentsTab - Props Update:");
    console.log("- pendingAnnotation:", pendingAnnotation);
    console.log("- activeAnnotationId:", activeAnnotationId);
  }, [pendingAnnotation, activeAnnotationId]);

  // Focus the comment input when a pending annotation is set
  useEffect(() => {
    if (pendingAnnotation && commentInputRef.current) {
      console.log("Focusing comment input due to pending annotation");
      // If the annotation has text, set it as the newComment
      if (pendingAnnotation.text) {
        setNewComment(pendingAnnotation.text);
        // Parse mentions from the text
        const mentionRegex = /@(\S+)/g;
        const mentions = [];
        let match;
        while ((match = mentionRegex.exec(pendingAnnotation.text)) !== null) {
          const mentionedName = match[1];
          const user = organizationUsers.find(u => 
            u.profile?.name?.toLowerCase().includes(mentionedName.toLowerCase()) ||
            u.email.toLowerCase().includes(mentionedName.toLowerCase())
          );
          if (user) {
            mentions.push(user.id);
          }
        }
        setSelectedMentions(mentions);
      }
      commentInputRef.current.focus();
    }
  }, [pendingAnnotation, organizationUsers]);

  // Scroll to active annotation comment
  useEffect(() => {
    if (activeAnnotationId) {
      console.log("Scrolling to active annotation comment:", activeAnnotationId);
      const commentElement = document.getElementById(`comment-${activeAnnotationId}`);
      if (commentElement) {
        commentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        commentElement.classList.add('highlight-comment');
        setTimeout(() => {
          commentElement.classList.remove('highlight-comment');
        }, 2000);
      } else {
        console.log("Comment element not found for ID:", activeAnnotationId);
      }
    }
  }, [activeAnnotationId, comments]);

  const fetchComments = async () => {
    try {
      console.log("Fetching comments for asset:", asset.id);
      
      // First fetch comments
      const { data: commentsData, error: commentsError } = await supabase
        .from('comments')
        .select('*')
        .eq('asset_id', asset.id)
        .order('created_at', { ascending: true });

      if (commentsError) throw commentsError;

      // Then fetch user data for each comment
      const commentsWithUsers = await Promise.all(
        (commentsData || []).map(async (comment) => {
          // Fetch user data including profile fields
          const { data: userData } = await supabase
            .from('users')
            .select('id, email, first_name, last_name, avatar_url')
            .eq('id', comment.user_id)
            .single();

          // Fetch mentions
          const { data: mentionsData } = await supabase
            .from('comment_mentions')
            .select('id, mentioned_user_id')
            .eq('comment_id', comment.id);

          // Get mentioned user details
          const mentions = await Promise.all(
            (mentionsData || []).map(async (mention) => {
              const { data: mentionedUser } = await supabase
                .from('users')
                .select('id, email, first_name, last_name')
                .eq('id', mention.mentioned_user_id)
                .single();

              const mentionedName = mentionedUser?.first_name && mentionedUser?.last_name 
                ? `${mentionedUser.first_name} ${mentionedUser.last_name}`
                : mentionedUser?.first_name || mentionedUser?.last_name || mentionedUser?.email?.split('@')[0] || 'Unknown';
              
              return {
                id: mention.id,
                mentioned_user: {
                  id: mention.mentioned_user_id,
                  name: mentionedName,
                  email: mentionedUser?.email || ''
                }
              };
            })
          );

          const userName = userData?.first_name && userData?.last_name 
            ? `${userData.first_name} ${userData.last_name}`
            : userData?.first_name || userData?.last_name || comment.author || userData?.email?.split('@')[0] || 'Unknown';
          
          return {
            ...comment,
            user: {
              id: comment.user_id,
              name: userName,
              avatar_url: userData?.avatar_url,
              email: userData?.email || ''
            },
            mentions
          };
        })
      );

      console.log("Processed comments with user data:", commentsWithUsers);
      setComments(commentsWithUsers as CommentWithUser[]);
    } catch (error) {
      console.error("Error in fetchComments:", error);
      toast({
        title: 'Error',
        description: error instanceof Error ? `Failed to load comments: ${error.message}` : 'Failed to load comments',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const loadComments = async () => {
      await fetchComments();
    };
    loadComments();
  }, [asset.id]);

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      console.log("Comment is empty, not submitting");
      return;
    }

    if (!currentUserId) {
      toast({
        title: 'Error',
        description: 'You must be logged in to comment',
        variant: 'destructive',
      });
      return;
    }

    console.log("Starting comment submission process");
    setIsSubmitting(true);
    try {
      console.log("Submitting comment with annotation:", pendingAnnotation);
      
      // Get current user's profile for author field
      const { data: userData } = await supabase
        .from('users')
        .select('email, first_name, last_name')
        .eq('id', currentUserId)
        .single();
      // Some databases require JSON to be stored as a string
      const coordinates = pendingAnnotation ? {
        x: pendingAnnotation.x,
        y: pendingAnnotation.y,
        width: pendingAnnotation.width,
        height: pendingAnnotation.height,
      } : null;

      const authorName = userData?.first_name && userData?.last_name 
        ? `${userData.first_name} ${userData.last_name}`
        : userData?.first_name || userData?.last_name || userData?.email?.split('@')[0] || 'Unknown';
      
      const commentData = {
        asset_id: asset.id,
        user_id: currentUserId,
        author: authorName,
        content: newComment.trim(),
        is_annotation: !!pendingAnnotation,
        coordinates: coordinates,
        status: 'open' as CommentStatus,
      };

      console.log("Comment data being sent to database:", commentData);

      const { data: commentResult, error } = await supabase
        .from('comments')
        .insert(commentData)
        .select()
        .single();
      
      if (error) throw error;
      
      // If there are mentions, insert them
      if (selectedMentions.length > 0 && commentResult) {
        const mentionData = selectedMentions.map(userId => ({
          comment_id: commentResult.id,
          mentioned_user_id: userId
        }));
        
        const { error: mentionsError } = await supabase
          .from('comment_mentions')
          .insert(mentionData);
          
        if (mentionsError) {
          console.error("Error inserting mentions:", mentionsError);
        }
      }

      console.log("Comment successfully saved:", commentResult);

      setNewComment('');
      setSelectedMentions([]);
      onPendingAnnotationClear();
      
      // Refresh comments to show the new one
      await fetchComments();
      
      toast({
        title: 'Success',
        description: pendingAnnotation 
          ? 'Annotation and comment added successfully' 
          : 'Comment added successfully',
      });
    } catch (error) {
      console.error("Error saving comment:", error);
      toast({
        title: 'Error',
        description: error instanceof Error ? `Failed to add comment: ${error.message}` : 'Failed to add comment',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Submit on Ctrl+Enter or Cmd+Enter
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSubmitComment();
    }
    
    // Handle mention navigation
    if (mentionOpen) {
      const filteredUsers = organizationUsers.filter(u => 
        u.email.toLowerCase().includes(mentionSearch.toLowerCase()) ||
        (u.profile?.name && u.profile.name.toLowerCase().includes(mentionSearch.toLowerCase()))
      ).slice(0, 5);
      
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedMentionIndex(prev => Math.min(prev + 1, filteredUsers.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedMentionIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredUsers[selectedMentionIndex]) {
            const selected = filteredUsers[selectedMentionIndex];
            handleMentionSelect(selected.id, selected.profile?.name || selected.email.split('@')[0]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          setMentionOpen(false);
          break;
      }
    }
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setNewComment(value);
    
    // Check for @ mentions
    const lastAtIndex = value.lastIndexOf('@');
    const hasRecentAt = lastAtIndex >= 0 && lastAtIndex >= value.length - 20;
    
    if (hasRecentAt) {
      const afterAt = value.slice(lastAtIndex + 1);
      const spaceIndex = afterAt.indexOf(' ');
      const newlineIndex = afterAt.indexOf('\n');
      const endIndex = Math.min(
        spaceIndex === -1 ? Infinity : spaceIndex,
        newlineIndex === -1 ? Infinity : newlineIndex
      );
      
      if (endIndex === Infinity) {
        // Still typing the mention
        setMentionSearch(afterAt);
        setMentionOpen(true);
        setSelectedMentionIndex(0);
      } else {
        setMentionOpen(false);
        setSelectedMentionIndex(0);
      }
    } else {
      setMentionOpen(false);
      setSelectedMentionIndex(0);
    }
  };

  const handleMentionSelect = (userId: string, userName: string) => {
    const lastAtIndex = newComment.lastIndexOf('@');
    if (lastAtIndex >= 0) {
      const beforeAt = newComment.slice(0, lastAtIndex);
      const afterAt = newComment.slice(lastAtIndex + 1);
      const spaceIndex = afterAt.indexOf(' ');
      const afterMention = spaceIndex >= 0 ? afterAt.slice(spaceIndex) : '';
      
      setNewComment(`${beforeAt}@${userName} ${afterMention}`);
      setSelectedMentions([...selectedMentions, userId]);
    }
    setMentionOpen(false);
    commentInputRef.current?.focus();
  };

  const handleDeleteComment = async (commentId: string) => {
    setCommentToDelete(commentId);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteComment = async () => {
    if (!commentToDelete) return;
    
    setIsDeleting(true);
    try {
      console.log("Deleting comment:", commentToDelete);
      
      const { error } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentToDelete);
      
      if (error) throw error;
      
      console.log("Comment deleted successfully");
      
      // Refresh comments to update the list
      await fetchComments();
      
      toast({
        title: 'Success',
        description: 'Comment deleted successfully',
      });
    } catch (error) {
      console.error("Error deleting comment:", error);
      toast({
        title: 'Error',
        description: error instanceof Error ? `Failed to delete comment: ${error.message}` : 'Failed to delete comment',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setCommentToDelete(null);
    }
  };

  const cancelDeleteComment = () => {
    setDeleteDialogOpen(false);
    setCommentToDelete(null);
  };

  // Check if the user can delete a comment (only the creator can)
  const canDeleteComment = (comment: CommentWithUser) => {
    return comment.user_id === currentUserId;
  };

  const renderCommentContent = (comment: CommentWithUser) => {
    // Parse mentions in comment content
    const content = comment.content;
    const mentionRegex = /@(\S+)/g;
    const parts = [];
    let lastIndex = 0;
    let match;
    
    while ((match = mentionRegex.exec(content)) !== null) {
      // Add text before mention
      if (match.index > lastIndex) {
        parts.push(content.slice(lastIndex, match.index));
      }
      
      // Check if this is a valid mention
      const mentionName = match[1];
      const mentionedUser = comment.mentions?.find(m => 
        m.mentioned_user.name.toLowerCase().includes(mentionName.toLowerCase()) ||
        m.mentioned_user.email.split('@')[0].toLowerCase() === mentionName.toLowerCase()
      );
      
      // Add mention as a styled span
      if (mentionedUser) {
        parts.push(
          <span key={match.index} className="bg-primary/10 text-primary px-1 rounded font-medium">
            @{match[1]}
          </span>
        );
      } else {
        parts.push(
          <span key={match.index} className="text-muted-foreground">
            @{match[1]}
          </span>
        );
      }
      
      lastIndex = mentionRegex.lastIndex;
    }
    
    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(content.slice(lastIndex));
    }
    
    return parts.length > 0 ? <>{parts}</> : content;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Comments List */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="text-center py-8">Loading comments...</div>
        ) : comments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="mx-auto h-8 w-8 mb-2 opacity-50" />
            <p>No comments yet</p>
            <p className="text-sm">Be the first to add a comment</p>
          </div>
        ) : (
          <div className="space-y-4">
            {comments.map((comment) => (
              <div
                key={comment.id}
                id={`comment-${comment.id}`}
                className={cn(
                  "p-3 rounded-lg border transition-colors",
                  comment.id === activeAnnotationId
                    ? "border-primary bg-primary/5"
                    : "border-border bg-card",
                  "hover:border-primary/50"
                )}
                onClick={() => onCommentSelect?.(comment.id)}
              >
                <div className="flex items-start gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={comment.user.avatar_url || undefined} alt={comment.user.name} />
                    <AvatarFallback>
                      {comment.user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-sm">{comment.user.name}</div>
                      <div className="flex items-center gap-2">
                        {canDeleteComment(comment) && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 text-muted-foreground hover:text-destructive"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteComment(comment.id);
                            }}
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </Button>
                        )}
                        <div className="text-xs text-muted-foreground">
                          {comment.created_at ? new Date(comment.created_at).toLocaleString() : 'Unknown date'}
                        </div>
                      </div>
                    </div>
                    <div className="text-sm">
                      {renderCommentContent(comment)}
                    </div>
                    {comment.is_annotation && (
                      <Badge variant="outline" className="mt-1 text-xs bg-primary/10">
                        Annotation
                      </Badge>
                    )}
                    {comment.mentions && comment.mentions.length > 0 && (
                      <div className="flex gap-1 mt-1">
                        {comment.mentions.map(mention => (
                          <Badge key={mention.id} variant="secondary" className="text-xs">
                            <AtSign className="h-3 w-3 mr-1" />
                            {mention.mentioned_user.name}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Comment Input */}
      <div className="p-4 border-t">
        {pendingAnnotation && (
          <div className="mb-2 p-2 bg-primary/10 rounded text-sm flex items-center">
            <Plus className="h-4 w-4 mr-2 text-primary" />
            <span>Adding comment for annotation</span>
          </div>
        )}
        <div className="relative">
          <div className="flex gap-2">
            <Textarea
              ref={commentInputRef}
              value={newComment}
              onChange={handleCommentChange}
              onKeyDown={handleKeyDown}
              placeholder="Add a comment... (use @ to mention users)"
              className="flex-1 min-h-[80px]"
            />
            <Button
              onClick={handleSubmitComment}
              disabled={isSubmitting || !newComment.trim()}
              className="self-end"
            >
              {isSubmitting ? (
                <div className="h-4 w-4 border-2 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {/* Mention Dropdown */}
          {mentionOpen && (
            <div className="absolute bottom-full mb-2 left-0 w-64 max-h-48 bg-background border rounded-md shadow-lg overflow-hidden">
              <div className="p-1">
                {organizationUsers
                  .filter(u => 
                    u.email.toLowerCase().includes(mentionSearch.toLowerCase()) ||
                    (u.profile?.name && u.profile.name.toLowerCase().includes(mentionSearch.toLowerCase()))
                  )
                  .slice(0, 5)
                  .map((u, index) => (
                    <div
                      key={u.id}
                      className={cn(
                        "flex items-center gap-2 p-2 rounded cursor-pointer transition-colors",
                        index === selectedMentionIndex ? "bg-primary/10" : "hover:bg-muted"
                      )}
                      onClick={() => handleMentionSelect(u.id, u.profile?.name || u.email.split('@')[0])}
                    >
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={u.profile?.avatar_url || undefined} />
                        <AvatarFallback>
                          {(u.profile?.name || u.email).charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col min-w-0">
                        <span className="text-sm font-medium truncate">
                          {u.profile?.name || u.email.split('@')[0]}
                        </span>
                        <span className="text-xs text-muted-foreground truncate">{u.email}</span>
                      </div>
                    </div>
                  ))}
                {organizationUsers
                  .filter(u => 
                    u.email.toLowerCase().includes(mentionSearch.toLowerCase()) ||
                    (u.profile?.name && u.profile.name.toLowerCase().includes(mentionSearch.toLowerCase()))
                  ).length === 0 && (
                  <div className="text-sm text-muted-foreground p-2">
                    No users found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="mt-2 text-xs text-muted-foreground">
          Press Ctrl+Enter to submit
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Comment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this comment? This action cannot be undone.
              {commentToDelete && comments.find(c => c.id === commentToDelete)?.is_annotation && (
                <div className="mt-2 flex items-center text-amber-500">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span>This will also delete the associated annotation.</span>
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteComment} disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDeleteComment}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <div className="h-4 w-4 border-2 border-t-transparent rounded-full animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 