import React, { useState, useEffect, useRef } from 'react';
import { Asset, Comment } from '../../common/types/database.types';
import { Button } from '../../ui/button';
import { ZoomIn, ZoomOut, Maximize, Minimize, Move, Plus, X } from 'lucide-react';
import { cn, getAssetUrl } from '../../common/utils/utils';
import { supabase } from '../../common/utils/supabase';
import { AnnotationLayer } from './AnnotationLayer';
import { useToast } from '../../common/hooks/use-toast';

interface Coordinates {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface AssetPreviewSectionProps {
  asset: Asset;
  annotations: Comment[];
  isAnnotating: boolean;
  onAnnotationCreate: (coordinates: Coordinates) => void;
  onAnnotationCancel: () => void;
  onAnnotationSelect?: (annotationId: string) => void;
  onAnnotationToggle: (isAnnotating: boolean) => void;
}

export function AssetPreviewSection({
  asset,
  annotations,
  isAnnotating,
  onAnnotationCreate,
  onAnnotationCancel,
  onAnnotationSelect,
  onAnnotationToggle,
}: AssetPreviewSectionProps) {
  const { toast } = useToast();
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Load preview URL when asset changes
  useEffect(() => {
    if (asset) {
      try {
        // Use the getAssetUrl utility function instead of manual path cleaning
        const url = getAssetUrl(asset, false); // false = use original image, not thumbnail
        setPreviewUrl(url);
      } catch (error) {
        console.error('Error getting asset preview URL:', error);
        setPreviewUrl('');
      }
    }
  }, [asset]);

  // Handle escape key to cancel fullscreen
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen]);

  // Reset position and zoom when toggling fullscreen
  useEffect(() => {
    if (!isFullscreen) {
      setZoomLevel(1);
      setPosition({ x: 0, y: 0 });
    }
  }, [isFullscreen]);

  // Handle image dragging for panning
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isAnnotating) return;

    if (zoomLevel > 1) {
      setIsDragging(true);
      setStartPos({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isAnnotating) return;

    if (isDragging && zoomLevel > 1) {
      setPosition({
        x: e.clientX - startPos.x,
        y: e.clientY - startPos.y,
      });
    }
  };

  const handleMouseUp = () => {
    if (isAnnotating) return;
    setIsDragging(false);
  };

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => {
      const newZoom = Math.max(prev - 0.25, 1);
      if (newZoom === 1) {
        setPosition({ x: 0, y: 0 });
      }
      return newZoom;
    });
  };

  const handleResetZoom = () => {
    setZoomLevel(1);
    setPosition({ x: 0, y: 0 });
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const toggleAnnotationMode = () => {
    onAnnotationToggle(!isAnnotating);
  };

  const handleAnnotationCreate = (coordinates: Coordinates) => {
    console.log("AssetPreviewSection: Annotation created with coordinates:", coordinates);
    
    // Disable annotation mode
    onAnnotationToggle(false);
    
    // Pass the coordinates to the parent component
    onAnnotationCreate(coordinates);
    
    // Show a toast notification
    toast({
      title: 'Annotation Created',
      description: 'Now add a comment to describe this annotation',
      duration: 5000,
    });
  };

  return (
    <div className="flex flex-col">
      {/* Image Preview Container */}
      <div
        className={cn(
          'relative transition-all duration-300 ease-in-out',
          isFullscreen
            ? 'fixed inset-0 z-50 bg-background p-4'
            : 'w-full h-[60vh]'
        )}
      >
        <div
          ref={containerRef}
          className={cn(
            'relative w-full h-full overflow-hidden bg-muted/30 rounded-md',
            isDragging && 'cursor-grabbing',
            zoomLevel > 1 && !isAnnotating && !isDragging && 'cursor-grab'
          )}
          onMouseDown={isAnnotating ? undefined : handleMouseDown}
          onMouseMove={isAnnotating ? undefined : handleMouseMove}
          onMouseUp={isAnnotating ? undefined : handleMouseUp}
          onMouseLeave={() => !isAnnotating && setIsDragging(false)}
        >
          {previewUrl && (
            <div
              className="w-full h-full flex items-center justify-center"
              style={{
                transform: `scale(${zoomLevel})`,
                transition: isDragging ? 'none' : 'transform 0.2s ease-out',
              }}
            >
              <img
                src={previewUrl}
                alt={asset.file_name}
                className="max-w-full max-h-full object-contain"
                style={{
                  transform: `translate(${position.x / zoomLevel}px, ${
                    position.y / zoomLevel
                  }px)`,
                  transition: isDragging ? 'none' : 'transform 0.2s ease-out',
                }}
                draggable={false}
              />
            </div>
          )}

          {/* Annotation Layer */}
          <AnnotationLayer
            isAnnotating={isAnnotating}
            annotations={annotations}
            onAnnotationCreate={handleAnnotationCreate}
            onAnnotationCancel={onAnnotationCancel}
            onAnnotationSelect={onAnnotationSelect}
          />
        </div>

        {/* Controls overlay */}
        <div className="absolute bottom-4 right-4 flex gap-2 bg-background/80 p-1 rounded-md backdrop-blur-sm">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleZoomIn}
            disabled={zoomLevel >= 3 || isAnnotating}
            className="h-8 w-8"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleZoomOut}
            disabled={zoomLevel <= 1 || isAnnotating}
            className="h-8 w-8"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          {zoomLevel > 1 && !isAnnotating && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleResetZoom}
              className="h-8 w-8"
            >
              <Move className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleFullscreen}
            disabled={isAnnotating}
            className="h-8 w-8"
          >
            {isFullscreen ? (
              <Minimize className="h-4 w-4" />
            ) : (
              <Maximize className="h-4 w-4" />
            )}
          </Button>
        </div>

        {isFullscreen && !isAnnotating && (
          <Button
            variant="outline"
            size="sm"
            className="absolute top-4 right-4"
            onClick={() => setIsFullscreen(false)}
          >
            Exit Fullscreen
          </Button>
        )}
      </div>

      {/* Annotation Controls - Directly below image */}
      <div className="py-3 px-4 border-b flex items-center justify-between">
        <Button
          variant={isAnnotating ? "secondary" : "outline"}
          size="sm"
          onClick={toggleAnnotationMode}
          className="gap-1"
        >
          {isAnnotating ? (
            <>
              <X className="h-3.5 w-3.5" />
              Cancel Annotation
            </>
          ) : (
            <>
              <Plus className="h-3.5 w-3.5" />
              Add Annotation
            </>
          )}
        </Button>
        
        {isAnnotating && (
          <span className="text-sm text-muted-foreground">
            Click anywhere on the image to add a point annotation
          </span>
        )}
      </div>
    </div>
  );
} 