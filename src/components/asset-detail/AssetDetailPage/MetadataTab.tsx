import { Asset, WorkflowStage } from '../../common/types/database.types';
import { formatFileSize, formatDate } from '../../common/utils/utils';
import { CollapsibleCard } from './CollapsibleCard';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { useToast } from '../../common/hooks/use-toast';
import { supabase } from '../../common/utils/supabase';
import AssetTagsManager from '../../asset-management/AssetTagsManager';
import ProductSelector from '../../products/ProductSelector';
import { useUserRole } from '../../../contexts/UserRoleContext';
import { getWorkflowStageConfigs } from '../../common/utils/workflowStageUtils';
import { useIsFreelancer } from '../../common/hooks/useIsFreelancer';

interface MetadataTabProps {
  asset: Asset;
  onAssetUpdated: () => void;
}

export function MetadataTab({ asset, onAssetUpdated }: MetadataTabProps) {
  const { toast } = useToast();

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stage configurations, considering freelancer status
  const workflowStageConfigs = getWorkflowStageConfigs(userRole, isFreelancer);

  const handleWorkflowStageChange = async (value: WorkflowStage) => {
    try {
      const { error } = await supabase
        .from('assets')
        .update({ workflow_stage: value })
        .eq('id', asset.id);

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Workflow stage updated successfully',
      });
      
      onAssetUpdated();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: `Failed to update workflow stage: ${error.message}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-4 p-4">
      <CollapsibleCard title="File Information">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-1">File Name</h4>
            <p className="text-sm break-all">{asset.file_name}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-1">File Type</h4>
            <p className="text-sm">{asset.file_type || 'Unknown'}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-1">File Size</h4>
            <p className="text-sm">{formatFileSize(asset.file_size || 0)}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-1">Uploaded</h4>
            <p className="text-sm">{asset.created_at ? formatDate(asset.created_at) : 'Not available'}</p>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-muted-foreground mb-1">Last Modified</h4>
            <p className="text-sm">{asset.updated_at ? formatDate(asset.updated_at) : 'Not available'}</p>
          </div>
          
          {/* Status badge removed as is_active doesn't exist in the Asset type */}
        </div>
      </CollapsibleCard>

      <CollapsibleCard title="Workflow Stage">
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground mb-1">Current Stage</h4>
          <Select 
            value={asset.workflow_stage} 
            onValueChange={handleWorkflowStageChange as (value: string) => void}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select workflow stage" />
            </SelectTrigger>
            <SelectContent>
              {workflowStageConfigs.map(config => (
                <SelectItem key={config.id} value={config.id}>
                  {config.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CollapsibleCard>

      <CollapsibleCard title="Product Association">
        <ProductSelector 
          collectionId={asset.collection_id}
          selectedProductId={asset.product_id || undefined} 
          onProductSelect={async (productId) => {
            try {
              const { error } = await supabase
                .from('assets')
                .update({ product_id: productId })
                .eq('id', asset.id);

              if (error) throw error;

              toast({
                title: 'Success',
                description: 'Product association updated successfully',
              });
              
              onAssetUpdated();
            } catch (error: any) {
              toast({
                title: 'Error',
                description: `Failed to update product association: ${error.message}`,
                variant: 'destructive',
              });
            }
          }}
        />
      </CollapsibleCard>

      <CollapsibleCard title="Tags">
        <AssetTagsManager asset={asset} onTagsUpdated={onAssetUpdated} />
      </CollapsibleCard>
    </div>
  );
} 