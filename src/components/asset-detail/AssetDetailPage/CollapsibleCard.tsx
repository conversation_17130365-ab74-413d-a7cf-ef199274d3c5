import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '../../common/utils/utils';

interface CollapsibleCardProps {
  title: string;
  children: React.ReactNode;
  defaultCollapsed?: boolean;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
}

export function CollapsibleCard({
  title,
  children,
  defaultCollapsed = false,
  className,
  headerClassName,
  contentClassName,
}: CollapsibleCardProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardHeader className={cn('py-3 flex flex-row items-center justify-between', headerClassName)}>
        <CardTitle className="text-base">{title}</CardTitle>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          {isCollapsed ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronUp className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <div
        className={cn(
          'transition-all duration-200 ease-in-out',
          isCollapsed ? 'max-h-0 opacity-0 overflow-hidden' : 'max-h-[1000px] opacity-100'
        )}
      >
        <CardContent className={cn('pt-0', contentClassName)}>
          {children}
        </CardContent>
      </div>
    </Card>
  );
} 