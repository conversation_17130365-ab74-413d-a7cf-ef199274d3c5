import React, { useRef, useEffect } from 'react';
import { cn } from '../../common/utils/utils';
import { Comment } from '../../common/types/database.types';

interface Coordinates {
  x: number;
  y: number;
  width: number;
  height: number;
}

interface AnnotationLayerProps {
  isAnnotating: boolean;
  annotations: Comment[];
  onAnnotationCreate: (coordinates: Coordinates) => void;
  onAnnotationCancel: () => void;
  onAnnotationSelect?: (annotationId: string) => void;
}

export function AnnotationLayer({
  isAnnotating,
  annotations,
  onAnnotationCreate,
  onAnnotationCancel,
  onAnnotationSelect,
}: AnnotationLayerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Debug logging
  useEffect(() => {
    console.log("Annotation mode active:", isAnnotating);
    console.log("Existing annotations:", annotations);
  }, [isAnnotating, annotations]);
  
  // Handle escape key to cancel annotation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isAnnotating) {
        onAnnotationCancel();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isAnnotating, onAnnotationCancel]);
  
  const handleClick = (e: React.MouseEvent) => {
    if (!isAnnotating || !containerRef.current) return;
    
    console.log("Creating point annotation");
    e.preventDefault(); // Prevent text selection
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
    const y = Math.max(0, Math.min(100, ((e.clientY - rect.top) / rect.height) * 100));
    
    // Create a small point annotation (the size is just for the data model, 
    // we'll render it as a circle)
    const pointSize = 2; // Small size for data storage
    const coordinates = {
      x: x - pointSize/2,
      y: y - pointSize/2,
      width: pointSize,
      height: pointSize,
    };
    
    console.log("Creating annotation at coordinates:", coordinates);
    onAnnotationCreate(coordinates);
  };
  
  return (
    <div 
      ref={containerRef}
      className={cn(
        "absolute inset-0",
        isAnnotating ? "cursor-crosshair z-10" : ""
      )}
      onClick={handleClick}
    >
      {/* Existing annotations */}
      {annotations
        .filter((comment) => comment.is_annotation && comment.coordinates)
        .map((comment) => {
          const coords = comment.coordinates as unknown as Coordinates;
          // Calculate center point of the annotation
          const centerX = coords.x + coords.width / 2;
          const centerY = coords.y + coords.height / 2;
          
          return (
            <div
              key={comment.id}
              id={`annotation-${comment.id}`}
              className="absolute group cursor-pointer"
              style={{
                left: `${centerX}%`,
                top: `${centerY}%`,
                transform: 'translate(-50%, -50%)',
              }}
              onClick={(e) => {
                e.stopPropagation();
                console.log("Annotation clicked:", comment.id);
                onAnnotationSelect?.(comment.id);
              }}
            >
              {/* Circle annotation point */}
              <div className="h-4 w-4 rounded-full bg-primary border-2 border-white shadow-md flex items-center justify-center text-[10px] text-white font-bold">
                {/* Optional: Add a number or icon here */}
              </div>
              
              {/* Tooltip with comment content */}
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity z-10 pointer-events-none">
                <div className="bg-background/95 px-3 py-2 rounded shadow-md text-xs max-w-[200px] whitespace-normal break-words">
                  {comment.content}
                </div>
              </div>
            </div>
          );
        })}
    </div>
  );
} 