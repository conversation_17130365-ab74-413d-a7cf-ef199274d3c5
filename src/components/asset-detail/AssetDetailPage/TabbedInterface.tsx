import React, { useState, useEffect } from 'react';
import { cn } from '../../common/utils/utils';

interface Tab {
  id: string;
  label: React.ReactNode;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface TabbedInterfaceProps {
  tabs: Tab[];
  defaultTabId?: string;
  className?: string;
  tabsListClassName?: string;
  tabContentClassName?: string;
  onTabChange?: (tabId: string) => void;
}

export function TabbedInterface({
  tabs,
  defaultTabId,
  className,
  tabsListClassName,
  tabContentClassName,
  onTabChange,
}: TabbedInterfaceProps) {
  const [activeTab, setActiveTab] = useState(defaultTabId || tabs[0]?.id);
  
  // Update active tab when defaultTabId changes
  useEffect(() => {
    if (defaultTabId && defaultTabId !== activeTab) {
      setActiveTab(defaultTabId);
    }
  }, [defaultTabId]);
  
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  return (
    <div className={cn('flex flex-col h-full', className)}>
      <div className={cn('flex border-b', tabsListClassName)}>
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={cn(
              'px-4 py-2 font-medium text-sm transition-colors flex items-center gap-2',
              activeTab === tab.id
                ? 'border-b-2 border-primary text-primary'
                : 'text-muted-foreground hover:text-foreground'
            )}
            onClick={() => handleTabChange(tab.id)}
          >
            {tab.icon && <span className="h-4 w-4">{tab.icon}</span>}
            {tab.label}
          </button>
        ))}
      </div>

      <div className={cn('flex-1 overflow-auto', tabContentClassName)}>
        {tabs.find((tab) => tab.id === activeTab)?.content}
      </div>
    </div>
  );
} 