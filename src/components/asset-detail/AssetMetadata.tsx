import { Asset } from '../common/types/database.types';
import { formatFileSize } from '../common/utils/utils';
import { Badge } from '../ui/badge';
import { CalendarClock, FileType, HardDrive, Image } from 'lucide-react';

interface AssetMetadataProps {
  asset: Asset;
}

export function AssetMetadata({ asset }: AssetMetadataProps) {
  return (
    <div className="space-y-6">
      {/* Basic Info */}
      <div>
        <h3 className="text-sm font-medium mb-4">Basic Information</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-start gap-2">
            <Image className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">File Name</h4>
              <p className="text-sm">{asset.file_name}</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <FileType className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">File Type</h4>
              <p className="text-sm">{asset.file_type}</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <HardDrive className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">File Size</h4>
              <p className="text-sm">{formatFileSize(asset.file_size)}</p>
            </div>
          </div>
          <div className="flex items-start gap-2">
            <CalendarClock className="h-4 w-4 mt-0.5 text-muted-foreground" />
            <div>
              <h4 className="text-sm font-medium text-muted-foreground">Last Updated</h4>
              <p className="text-sm">{asset.updated_at ? new Date(asset.updated_at).toLocaleString() : 'Not available'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Stage */}
      <div>
        <h3 className="text-sm font-medium mb-4">Workflow Stage</h3>
        <Badge variant="secondary" className="capitalize">
          {asset.workflow_stage}
        </Badge>
      </div>

      {/* Additional Metadata */}
      {asset.metadata && (
        <div>
          <h3 className="text-sm font-medium mb-4">Additional Metadata</h3>
          <div className="bg-muted rounded-lg p-4 overflow-auto max-h-[400px]">
            <pre className="text-sm whitespace-pre-wrap">
              {JSON.stringify(asset.metadata, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 