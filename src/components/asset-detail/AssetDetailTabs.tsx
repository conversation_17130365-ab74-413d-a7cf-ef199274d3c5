import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { Asset } from '../common/types/database.types';
import { AssetMetadata } from './AssetMetadata';
import { AssetRelated } from './AssetRelated';
import { AssetComments } from './AssetComments';

interface AssetDetailTabsProps {
  asset: Asset;
  className?: string;
}

export function AssetDetailTabs({ asset, className }: AssetDetailTabsProps) {
  return (
    <Tabs defaultValue="metadata" className={className}>
      <TabsList className="w-full border-b rounded-none">
        <TabsTrigger value="metadata">Metadata</TabsTrigger>
        <TabsTrigger value="related">Related assets</TabsTrigger>
        <TabsTrigger value="comments">Comments and annotations</TabsTrigger>
      </TabsList>
      
      <TabsContent value="metadata" className="mt-4">
        <AssetMetadata asset={asset} />
      </TabsContent>
      
      <TabsContent value="related" className="mt-4">
        <AssetRelated asset={asset} />
      </TabsContent>
      
      <TabsContent value="comments" className="mt-4">
        <AssetComments asset={asset} />
      </TabsContent>
    </Tabs>
  );
} 