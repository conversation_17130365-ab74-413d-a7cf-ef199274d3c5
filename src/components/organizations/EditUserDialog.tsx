import React, { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { supabase } from '@/components/common/utils/supabase';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, Building2 } from 'lucide-react';
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert';
import type { Database } from '@/components/common/types/database.types';

type UserRole = Database['public']['Enums']['user_role'];
type Organization = Database['public']['Tables']['organizations']['Row'];

interface EditUserDialogProps {
  user: {
    id: string;
    email: string;
    first_name: string | null;
    last_name: string | null;
    display_name: string | null;
    role: UserRole;
    is_freelancer: boolean;
    organizations: { id: string; name: string }[];
  };
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  currentUserId: string;
}

const roleOptions: { value: UserRole; label: string; description: string }[] = [
  {
    value: 'platform_super',
    label: 'Platform Super Admin',
    description: 'Full system access, can manage everything'
  },
  {
    value: 'platform_admin',
    label: 'Platform Admin',
    description: 'Can manage brands and users'
  },
  {
    value: 'brand_admin',
    label: 'Brand Admin',
    description: 'Can manage brand campaigns and members'
  },
  {
    value: 'brand_member',
    label: 'Brand Member',
    description: 'Can view and work with brand campaigns'
  },
  {
    value: 'external_retoucher',
    label: 'External Retoucher',
    description: 'External user for image retouching'
  },
  {
    value: 'external_prompter',
    label: 'External Prompter',
    description: 'External user for prompt creation'
  }
];

const formSchema = z.object({
  first_name: z.string().min(1, 'First name is required'),
  last_name: z.string().min(1, 'Last name is required'),
  display_name: z.string().optional(),
  role: z.enum([
    'platform_super',
    'platform_admin',
    'brand_admin',
    'brand_member',
    'external_retoucher',
    'external_prompter'
  ] as const),
  is_freelancer: z.boolean(),
  organization_ids: z.array(z.string()),
});

type FormData = z.infer<typeof formSchema>;

export default function EditUserDialog({
  user,
  open,
  onOpenChange,
  onSuccess,
  currentUserId,
}: EditUserDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [allOrganizations, setAllOrganizations] = useState<Organization[]>([]);
  const [isLoadingOrgs, setIsLoadingOrgs] = useState(true);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      display_name: user.display_name || '',
      role: user.role,
      is_freelancer: user.is_freelancer,
      organization_ids: user.organizations.map(org => org.id),
    },
  });

  const isEditingSelf = user.id === currentUserId;
  const canChangeRole = !isEditingSelf; // Can't change your own role
  const isPlatformUser = user.role === 'platform_admin' || user.role === 'platform_super';

  // Watch for role and freelancer changes
  const watchedRole = form.watch('role');
  const watchedFreelancer = form.watch('is_freelancer');

  useEffect(() => {
    if (open) {
      fetchOrganizations();
    }
  }, [open]);

  const fetchOrganizations = async () => {
    try {
      setIsLoadingOrgs(true);
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('name');

      if (error) throw error;
      setAllOrganizations(data || []);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast.error('Failed to load organizations');
    } finally {
      setIsLoadingOrgs(false);
    }
  };

  const handleSubmit = async (values: FormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Check if trying to remove the last platform admin
      if (user.role === 'platform_admin' && values.role !== 'platform_admin') {
        const { data: adminCount } = await supabase
          .from('users')
          .select('id')
          .in('role', ['platform_admin', 'platform_super']);

        if (adminCount && adminCount.length <= 1) {
          setError('Cannot remove the last platform admin');
          setIsSubmitting(false);
          return;
        }
      }

      // Validate freelancer settings
      if (values.is_freelancer && values.role !== 'brand_admin') {
        setError('Freelancers must have the Brand Admin role');
        setIsSubmitting(false);
        return;
      }

      // Validate that user belongs to at least one organization (except platform admins)
      if (values.role !== 'platform_admin' && values.role !== 'platform_super' && values.organization_ids.length === 0) {
        setError('User must belong to at least one organization');
        setIsSubmitting(false);
        return;
      }

      // Update user details
      const { error: updateError } = await supabase
        .from('users')
        .update({
          first_name: values.first_name,
          last_name: values.last_name,
          display_name: values.display_name || null,
          role: values.role,
          is_freelancer: values.is_freelancer,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (updateError) throw updateError;

      // Update organization memberships (only if not platform admin/super)
      if (values.role !== 'platform_admin' && values.role !== 'platform_super') {
        // Get current memberships
        const { data: currentMemberships } = await supabase
          .from('organization_memberships')
          .select('organization_id')
          .eq('user_id', user.id);

        const currentOrgIds = currentMemberships?.map(m => m.organization_id) || [];
        const newOrgIds = values.organization_ids;

        // Find organizations to add and remove
        const toAdd = newOrgIds.filter(id => !currentOrgIds.includes(id));
        const toRemove = currentOrgIds.filter(id => !newOrgIds.includes(id));

        // Remove memberships
        if (toRemove.length > 0) {
          const { error: removeError } = await supabase
            .from('organization_memberships')
            .delete()
            .eq('user_id', user.id)
            .in('organization_id', toRemove);

          if (removeError) throw removeError;
        }

        // Add memberships
        if (toAdd.length > 0) {
          const newMemberships = toAdd.map(orgId => ({
            user_id: user.id,
            organization_id: orgId,
          }));

          const { error: addError } = await supabase
            .from('organization_memberships')
            .insert(newMemberships);

          if (addError) throw addError;
        }
      }

      toast.success('User updated successfully');
      onSuccess();
      onOpenChange(false);
    } catch (err) {
      console.error('Error updating user:', err);
      setError(err instanceof Error ? err.message : 'Failed to update user');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user details, role, and organization memberships.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input value={user.email} disabled />
              <p className="text-xs text-muted-foreground">
                Email addresses cannot be changed
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="first_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="last_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="display_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Display Name (Optional)</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    If set, this will be shown instead of first + last name
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    disabled={!canChangeRole}
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {roleOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex flex-col">
                            <span>{option.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {option.description}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {!canChangeRole && (
                    <FormDescription>
                      You cannot change your own role
                    </FormDescription>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedRole === 'brand_admin' && (
              <FormField
                control={form.control}
                name="is_freelancer"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>
                        External Freelancer
                      </FormLabel>
                      <FormDescription>
                        Mark this user as an external freelancer working across multiple brands
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            )}

            {!isPlatformUser && watchedRole !== 'platform_admin' && watchedRole !== 'platform_super' && (
              <FormField
                control={form.control}
                name="organization_ids"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Organization Memberships
                    </FormLabel>
                    <FormDescription>
                      Select the organizations this user belongs to. Users can be members of multiple organizations.
                    </FormDescription>
                    {isLoadingOrgs ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    ) : allOrganizations.length === 0 ? (
                      <p className="text-sm text-muted-foreground p-4 text-center">
                        No organizations available
                      </p>
                    ) : (
                      <div className="border rounded-md p-3 max-h-[200px] overflow-y-auto space-y-2">
                        {allOrganizations.map((org) => (
                          <div key={org.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`org-${org.id}`}
                              checked={field.value.includes(org.id)}
                              onCheckedChange={(checked) => {
                                const newValue = checked
                                  ? [...field.value, org.id]
                                  : field.value.filter(id => id !== org.id);
                                
                                // All users can now belong to multiple organizations
                                field.onChange(newValue);
                              }}
                            />
                            <Label 
                              htmlFor={`org-${org.id}`}
                              className="text-sm font-normal cursor-pointer flex-1"
                            >
                              {org.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    )}
                    {field.value.length === 0 && (
                      <p className="text-sm text-amber-600">
                        Warning: User must belong to at least one organization
                      </p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {(watchedRole === 'platform_admin' || watchedRole === 'platform_super') && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Platform admins have access to all organizations and don't need specific memberships.
                </AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}