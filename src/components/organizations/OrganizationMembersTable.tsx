import React, { useState, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../ui/use-toast';
import { useUserRole } from '../../contexts/UserRoleContext';
import { UserRole } from '../../contexts/UserRoleContext';
import { formatDistance } from 'date-fns';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ArrowUpDown, MoreHorizontal } from 'lucide-react';

interface OrganizationMemberWithProfile {
  id: string;
  user_id: string;
  organization_id: string;
  created_at: string;
  users: {
    email: string;
    first_name: string | null;
    last_name: string | null;
    avatar_url: string | null;
    role: UserRole; // Role now comes from users table
  };
}

interface OrganizationMembersTableProps {
  organizationId: string;
  allowInvite?: boolean;
  filter?: UserRole | null;
}

export function OrganizationMembersTable({
  organizationId,
  allowInvite = true,
  filter = null
}: OrganizationMembersTableProps) {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { isPlatformUser } = useUserRole();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [members, setMembers] = useState<OrganizationMemberWithProfile[]>([]);
  const [sortColumn, setSortColumn] = useState<'name' | 'email' | 'role' | 'date'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');

  // Function to fetch organization members with their profiles
  useEffect(() => {
    const fetchMembers = async () => {
      if (!organizationId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch memberships first
        const { data: membershipData, error: membershipError } = await supabase
          .from('organization_memberships')
          .select(`
            id,
            user_id,
            organization_id,
            created_at
          `)
          .eq('organization_id', organizationId)
          .order('created_at', { ascending: false });
          
        if (membershipError) throw membershipError;
        
        if (!membershipData || membershipData.length === 0) {
          setMembers([]);
          setIsLoading(false);
          return;
        }
        
        // Get user IDs from memberships
        const userIds = membershipData.map(m => m.user_id);
        
        // Fetch user profiles
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, email, first_name, last_name, avatar_url, role')
          .in('id', userIds);
          
        if (userError) throw userError;
        
        // Combine membership and user data
        const membersWithProfiles = membershipData.map(membership => {
          const user = userData?.find(u => u.id === membership.user_id);
          return {
            ...membership,
            users: user || {
              email: 'Unknown',
              first_name: null,
              last_name: null,
              avatar_url: null,
              role: 'brand_member' as UserRole
            }
          };
        });
        setMembers(membersWithProfiles as OrganizationMemberWithProfile[]);
      } catch (err: any) {
        console.error('Error fetching organization members:', err);
        setError(err.message || 'Failed to load organization members');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchMembers();
  }, [organizationId, supabase]);

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Function to get user display name
  const getUserDisplayName = (member: OrganizationMemberWithProfile) => {
    if (member.users.first_name && member.users.last_name) {
      return `${member.users.first_name} ${member.users.last_name}`;
    } else if (member.users.first_name) {
      return member.users.first_name;
    } else if (member.users.last_name) {
      return member.users.last_name;
    }
    return member.users.email.split('@')[0];
  };

  // Function to get user initials for avatar fallback
  const getUserInitials = (member: OrganizationMemberWithProfile) => {
    if (member.users.first_name && member.users.last_name) {
      return `${member.users.first_name[0]}${member.users.last_name[0]}`.toUpperCase();
    } else if (member.users.first_name) {
      return member.users.first_name.slice(0, 2).toUpperCase();
    } else if (member.users.last_name) {
      return member.users.last_name.slice(0, 2).toUpperCase();
    }
    
    const emailName = member.users.email.split('@')[0];
    const nameParts = emailName.split(/[._-]/);
    
    if (nameParts.length > 1) {
      return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
    }
    
    return emailName.slice(0, 2).toUpperCase();
  };

  // Function to get time since member joined
  const getTimeSinceJoined = (dateString: string) => {
    return formatDistance(new Date(dateString), new Date(), { addSuffix: true });
  };

  // Function to get role display name
  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case 'platform_super':
        return 'Platform Super';
      case 'platform_admin':
        return 'Platform Admin';
      case 'brand_admin':
        return 'Brand Admin';
      case 'brand_member':
        return 'Brand Member';
      case 'external_retoucher':
        return 'External Retoucher';
      case 'external_prompter':
        return 'External Prompter';
      default:
        return role;
    }
  };

  // Function to get role badge variant
  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'platform_super':
      case 'platform_admin':
        return 'destructive';
      case 'brand_admin':
        return 'default';
      case 'brand_member':
        return 'secondary';
      case 'external_retoucher':
      case 'external_prompter':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  // Function to handle role change
  const handleRoleChange = async (userId: string, newRole: UserRole) => {
    try {
      // Only platform users can change roles
      if (!isPlatformUser) {
        toast({
          title: "Insufficient permissions",
          description: "Only platform administrators can change user roles.",
          variant: "destructive",
        });
        return;
      }

      const { error } = await supabase
        .from('users')
        .update({ role: newRole })
        .eq('id', userId);
        
      if (error) throw error;
      
      // Update local state
      setMembers(prevMembers => 
        prevMembers.map(member => 
          member.user_id === userId 
            ? { ...member, users: { ...member.users, role: newRole } } 
            : member
        )
      );
      
      toast({
        title: "Role updated",
        description: "User role has been successfully updated.",
      });
    } catch (err: any) {
      console.error('Error updating user role:', err);
      toast({
        title: "Error updating role",
        description: err.message || "An error occurred while updating the user role.",
        variant: "destructive",
      });
    }
  };

  // Function to handle member removal
  const handleRemoveMember = async (memberId: string, memberName: string) => {
    if (!confirm(`Are you sure you want to remove ${memberName} from this organization?`)) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from('organization_memberships')
        .delete()
        .eq('id', memberId);
        
      if (error) throw error;
      
      // Update local state
      setMembers(prevMembers => prevMembers.filter(member => member.id !== memberId));
      
      toast({
        title: "Member removed",
        description: `${memberName} has been removed from the organization.`,
      });
    } catch (err: any) {
      console.error('Error removing member:', err);
      toast({
        title: "Error removing member",
        description: err.message || "An error occurred while removing the member.",
        variant: "destructive",
      });
    }
  };

  // Function to sort members
  const sortedMembers = [...members]
    .filter(member => {
      // Apply role filter if specified
      if (filter && member.role !== filter) {
        return false;
      }
      
      // Apply search filter if present
      if (searchTerm) {
        const displayName = getUserDisplayName(member).toLowerCase();
        const email = member.users.email.toLowerCase();
        const searchLower = searchTerm.toLowerCase();
        
        return displayName.includes(searchLower) || email.includes(searchLower);
      }
      
      return true;
    })
    .sort((a, b) => {
      if (sortColumn === 'name') {
        const nameA = getUserDisplayName(a).toLowerCase();
        const nameB = getUserDisplayName(b).toLowerCase();
        return sortDirection === 'asc' 
          ? nameA.localeCompare(nameB)
          : nameB.localeCompare(nameA);
      }
      
      if (sortColumn === 'email') {
        const emailA = a.users.email.toLowerCase();
        const emailB = b.users.email.toLowerCase();
        return sortDirection === 'asc'
          ? emailA.localeCompare(emailB)
          : emailB.localeCompare(emailA);
      }
      
      if (sortColumn === 'role') {
        const roleA = a.users.role;
        const roleB = b.users.role;
        return sortDirection === 'asc'
          ? roleA.localeCompare(roleB)
          : roleB.localeCompare(roleA);
      }
      
      if (sortColumn === 'date') {
        const dateA = new Date(a.created_at).getTime();
        const dateB = new Date(b.created_at).getTime();
        return sortDirection === 'asc'
          ? dateA - dateB
          : dateB - dateA;
      }
      
      return 0;
    });

  // Function to toggle sort direction
  const toggleSort = (column: 'name' | 'email' | 'role' | 'date') => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Helper function to render role badge
  const getRoleBadge = (role: OrganizationRole) => {
    switch (role) {
      case 'org_admin':
        return <Badge>Admin</Badge>;
      case 'org_member':
        return <Badge variant="secondary">Member</Badge>;
      case 'org_retoucher':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Retoucher</Badge>;
      case 'org_prompter':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Prompter</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Members</h3>
          
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2, 3, 4, 5].map((_, index) => (
                <TableRow key={index} className="animate-pulse">
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full bg-muted"></div>
                      <div className="h-4 w-32 bg-muted rounded"></div>
                    </div>
                  </TableCell>
                  <TableCell><div className="h-4 w-40 bg-muted rounded"></div></TableCell>
                  <TableCell><div className="h-6 w-16 bg-muted rounded"></div></TableCell>
                  <TableCell><div className="h-4 w-20 bg-muted rounded"></div></TableCell>
                  <TableCell className="text-right"><div className="h-8 w-8 bg-muted rounded ml-auto"></div></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12 text-center">
        <p className="text-red-500">Error loading members: {error}</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Members</h3>
          <Badge variant="outline" className="ml-2">{sortedMembers.length}</Badge>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('name')}
                >
                  Name
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead>
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('email')}
                >
                  Email
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead>
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('role')}
                >
                  Role
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead>
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('date')}
                >
                  Joined
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead className="text-right w-[50px]">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedMembers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                  No members found
                </TableCell>
              </TableRow>
            ) : (
              sortedMembers.map(member => {
                const displayName = getUserDisplayName(member);
                // Only platform users can modify member roles
                const canModify = isPlatformUser;
                
                return (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.users.avatar_url || ""} alt={displayName} />
                          <AvatarFallback>{getUserInitials(member)}</AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{displayName}</span>
                      </div>
                    </TableCell>
                    <TableCell>{member.users.email}</TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(member.users.role)}>
                        {getRoleDisplayName(member.users.role)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">{formatDate(member.created_at)}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      {canModify && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'brand_admin')}>
                              Make Brand Admin
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'brand_member')}>
                              Make Brand Member
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'external_retoucher')}>
                              Make External Retoucher
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleRoleChange(member.id, 'external_prompter')}>
                              Make External Prompter
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-destructive focus:text-destructive"
                              onClick={() => handleRemoveMember(member.id, displayName)}
                            >
                              Remove from Organization
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default OrganizationMembersTable; 