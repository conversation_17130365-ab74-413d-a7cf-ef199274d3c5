import React, { useState, useMemo, useEffect } from 'react';
import { useAllUsers } from '../common/hooks/useAllUsers';
import { useToast } from '../ui/use-toast';
import { supabase } from '../common/utils/supabase';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Skeleton } from "../ui/skeleton";
import { 
  Search, 
  Mail, 
  Shield, 
  Building2, 
  User, 
  RefreshCw,
  MoreHorizontal,
  KeyRound,
  UserMinus,
  Edit
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { formatDistanceToNow } from 'date-fns';
import type { UserRole } from '../../contexts/UserRoleContext';
import EditUserDialog from './EditUserDialog';
import AdminPasswordResetDialog from './AdminPasswordResetDialog';

const roleColors: Record<UserRole, string> = {
  platform_super: 'bg-purple-100 text-purple-800',
  platform_admin: 'bg-blue-100 text-blue-800',
  brand_admin: 'bg-green-100 text-green-800',
  brand_member: 'bg-gray-100 text-gray-800',
  external_retoucher: 'bg-orange-100 text-orange-800',
  external_prompter: 'bg-yellow-100 text-yellow-800'
};

const roleIcons: Record<UserRole, any> = {
  platform_super: Shield,
  platform_admin: Shield,
  brand_admin: Building2,
  brand_member: User,
  external_retoucher: User,
  external_prompter: User
};

export function AllUsersTable() {
  const { users, isLoading, error, refetch } = useAllUsers();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [organizationFilter, setOrganizationFilter] = useState<string>('all');
  const [editingUser, setEditingUser] = useState<any>(null);
  const [resetPasswordUser, setResetPasswordUser] = useState<any>(null);
  const [currentUserId, setCurrentUserId] = useState<string>('');

  // Get current user ID
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setCurrentUserId(user.id);
      }
    };
    getCurrentUser();
  }, []);

  // Get unique organizations for filter
  const uniqueOrganizations = useMemo(() => {
    const orgs = new Map<string, string>();
    users.forEach(user => {
      user.organizations.forEach(org => {
        orgs.set(org.id, org.name);
      });
    });
    return Array.from(orgs, ([id, name]) => ({ id, name }));
  }, [users]);

  // Filter users based on search and filters
  const filteredUsers = useMemo(() => {
    return users.filter(user => {
      // Search filter
      const matchesSearch = searchQuery === '' || 
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchQuery.toLowerCase());

      // Role filter
      const matchesRole = roleFilter === 'all' || user.role === roleFilter;

      // Organization filter
      const matchesOrg = organizationFilter === 'all' || 
        user.organizations.some(org => org.id === organizationFilter);

      return matchesSearch && matchesRole && matchesOrg;
    });
  }, [users, searchQuery, roleFilter, organizationFilter]);

  const handleSendPasswordReset = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/update-password`
      });

      if (error) throw error;

      toast({
        title: "Password reset sent",
        description: `Password reset email sent to ${email}`,
      });
    } catch (err) {
      console.error('Error sending password reset:', err);
      toast({
        title: "Error",
        description: "Failed to send password reset email",
        variant: "destructive",
      });
    }
  };

  const getRoleDisplay = (role: UserRole) => {
    const labels: Record<UserRole, string> = {
      platform_super: 'Platform Super',
      platform_admin: 'Platform Admin',
      brand_admin: 'Brand Admin',
      brand_member: 'Brand Member',
      external_retoucher: 'Retoucher',
      external_prompter: 'Prompter'
    };
    return labels[role] || role;
  };

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Error loading users: {error}</p>
        <Button onClick={refetch} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>
        <Select value={roleFilter} onValueChange={setRoleFilter}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Filter by role" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="platform_super">Platform Super</SelectItem>
            <SelectItem value="platform_admin">Platform Admin</SelectItem>
            <SelectItem value="brand_admin">Brand Admin</SelectItem>
            <SelectItem value="brand_member">Brand Member</SelectItem>
          </SelectContent>
        </Select>
        <Select value={organizationFilter} onValueChange={setOrganizationFilter}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="Filter by organization" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Organizations</SelectItem>
            {uniqueOrganizations.map(org => (
              <SelectItem key={org.id} value={org.id}>{org.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button onClick={refetch} variant="outline" size="icon">
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Organizations</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 5 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell><Skeleton className="h-10 w-full" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                  {searchQuery || roleFilter !== 'all' || organizationFilter !== 'all' 
                    ? 'No users found matching your filters' 
                    : 'No users found'}
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map(user => {
                const Icon = roleIcons[user.role] || User;
                return (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                          <Icon className="h-5 w-5 text-muted-foreground" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {user.first_name || user.last_name 
                              ? `${user.first_name || ''} ${user.last_name || ''}`.trim()
                              : 'No name'
                            }
                          </div>
                          <div className="text-sm text-muted-foreground flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge className={roleColors[user.role]}>
                          {getRoleDisplay(user.role)}
                        </Badge>
                        {user.is_freelancer && (
                          <Badge variant="outline">Freelancer</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {user.organizations.length === 0 ? (
                        <span className="text-sm text-muted-foreground">No organizations</span>
                      ) : (
                        <div className="flex flex-wrap gap-1">
                          {user.organizations.map(org => (
                            <Badge key={org.id} variant="secondary" className="text-xs">
                              {org.name}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleSendPasswordReset(user.email)}
                          >
                            <KeyRound className="h-4 w-4 mr-2" />
                            Send Password Reset Email
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => setResetPasswordUser(user)}
                          >
                            <KeyRound className="h-4 w-4 mr-2" />
                            Update Password (Admin)
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => setEditingUser(user)}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="text-red-600"
                            disabled
                          >
                            <UserMinus className="h-4 w-4 mr-2" />
                            Delete User (Coming Soon)
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredUsers.length} of {users.length} users
      </div>

      {/* Edit User Dialog */}
      {editingUser && (
        <EditUserDialog
          user={editingUser}
          open={!!editingUser}
          onOpenChange={(open) => !open && setEditingUser(null)}
          onSuccess={() => {
            refetch();
            setEditingUser(null);
          }}
          currentUserId={currentUserId}
        />
      )}

      {/* Admin Password Reset Dialog */}
      {resetPasswordUser && (
        <AdminPasswordResetDialog
          user={resetPasswordUser}
          open={!!resetPasswordUser}
          onOpenChange={(open) => !open && setResetPasswordUser(null)}
          onSuccess={() => {
            setResetPasswordUser(null);
            toast({
              title: "Password reset successfully",
              description: "The user's password has been updated.",
            });
          }}
        />
      )}
    </div>
  );
}