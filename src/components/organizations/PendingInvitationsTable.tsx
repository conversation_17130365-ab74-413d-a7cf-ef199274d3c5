import React, { useState, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../ui/use-toast';
import { useUserRole } from '../../contexts/UserRoleContext';
import { UserRole } from '../../contexts/UserRoleContext';
import { formatDistance } from 'date-fns';
import { sendInvitationEmail } from '../common/utils/emailUtils';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import { ArrowUpDown, Clock, MoreH<PERSON>zontal, RefreshCw, X } from 'lucide-react';


interface PendingInvitation {
  id: string;
  email: string;
  organization_id: string;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  token: string;
  accepted: boolean;
  message?: string;
  inviter?: {
    email: string;
    first_name: string | null;
    last_name: string | null;
  };
}

interface PendingInvitationsTableProps {
  organizationId: string;
}

export function PendingInvitationsTable({
  organizationId,
}: PendingInvitationsTableProps) {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { isPlatformUser, isBrandAdmin } = useUserRole();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [invitations, setInvitations] = useState<PendingInvitation[]>([]);
  const [sortColumn, setSortColumn] = useState<'email' | 'date'>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [isResending, setIsResending] = useState<string | null>(null);
  const [isCancelling, setIsCancelling] = useState<string | null>(null);

  // Function to fetch pending invitations
  useEffect(() => {
    const fetchInvitations = async () => {
      if (!organizationId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch pending invitations
        const { data: invitationData, error: invitationError } = await supabase
          .from('pending_invitations')
          .select(`
            id,
            email,
            organization_id,
            invited_by,
            invited_at,
            expires_at,
            token,
            accepted,
            message
          `)
          .eq('organization_id', organizationId)
          .eq('accepted', false);
          
          // Add debugging to see what we're getting
          console.log('Invitations query results:', { invitationData, invitationError });
          // No expires_at filter - show all pending invitations
          
        if (invitationError) throw invitationError;
        
        if (!invitationData || invitationData.length === 0) {
          setInvitations([]);
          setIsLoading(false);
          return;
        }
        
        // Get the inviter user profiles
        const inviterIds = invitationData.map(invitation => invitation.invited_by);
        
        const { data: inviterData, error: inviterError } = await supabase
          .from('users')
          .select(`
            id,
            email,
            first_name,
            last_name
          `)
          .in('id', inviterIds);
          
        if (inviterError) throw inviterError;
        
        // Combine the data
        const combinedData = invitationData.map(invitation => {
          const inviter = inviterData?.find(user => user.id === invitation.invited_by);
          return {
            ...invitation,
            inviter: inviter ? {
              email: inviter.email,
              first_name: inviter.first_name,
              last_name: inviter.last_name
            } : undefined
          };
        });
        
        setInvitations(combinedData as PendingInvitation[]);
      } catch (err: any) {
        console.error('Error fetching pending invitations:', err);
        setError(err.message || 'Failed to load pending invitations');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchInvitations();
  }, [organizationId, supabase]);

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Function to get inviter display name
  const getInviterDisplayName = (invitation: PendingInvitation) => {
    if (!invitation.inviter) return 'Unknown';
    
    if (invitation.inviter.first_name && invitation.inviter.last_name) {
      return `${invitation.inviter.first_name} ${invitation.inviter.last_name}`;
    } else if (invitation.inviter.first_name) {
      return invitation.inviter.first_name;
    } else if (invitation.inviter.last_name) {
      return invitation.inviter.last_name;
    }
    
    return invitation.inviter.email.split('@')[0];
  };

  // Function to get time until expiration
  const getTimeUntilExpiration = (expiresAt: string) => {
    const expirationDate = new Date(expiresAt);
    const now = new Date();
    
    if (expirationDate <= now) {
      return 'Expired';
    }
    
    return formatDistance(expirationDate, now, { addSuffix: false });
  };
  
  // Helper function to check if an invitation is expired
  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) <= new Date();
  };


  // Get role display name
  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case 'platform_super': return 'Platform Super';
      case 'platform_admin': return 'Platform Admin';
      case 'brand_admin': return 'Brand Admin';
      case 'brand_member': return 'Brand Member';
      case 'external_retoucher': return 'External Retoucher';
      case 'external_prompter': return 'External Prompter';
      default: return role;
    }
  };

  // Get badge variant for role
  const getRoleBadgeVariant = (role: UserRole) => {
    switch (role) {
      case 'platform_super': return 'destructive' as const;
      case 'platform_admin': return 'destructive' as const;
      case 'brand_admin': return 'default' as const;
      case 'brand_member': return 'secondary' as const;
      case 'external_retoucher': return 'outline' as const;
      case 'external_prompter': return 'outline' as const;
      default: return 'secondary' as const;
    }
  };

  // Function to resend invitation
  const handleResendInvitation = async (invitationId: string) => {
    setIsResending(invitationId);
    
    try {
      // Get the invitation details
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) {
        throw new Error('Invitation not found');
      }
      
      // Get the organization name
      const { data: organizationData, error: organizationError } = await supabase
        .from('organizations')
        .select('name')
        .eq('id', invitation.organization_id)
        .single();
        
      if (organizationError) throw organizationError;
      
      // Get inviter's name
      const { data: inviterData, error: inviterError } = await supabase
        .from('users')
        .select('first_name, last_name')
        .eq('id', invitation.invited_by)
        .single();
        
      if (inviterError) throw inviterError;
      
      const inviterName = inviterData ? 
        `${inviterData.first_name || ''} ${inviterData.last_name || ''}`.trim() : '';
      
      // Update the invitation expiration date
      const newExpiresAt = new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(); // 48 hours from now
      const newInvitedAt = new Date().toISOString();
      
      const { error } = await supabase
        .from('pending_invitations')
        .update({
          expires_at: newExpiresAt,
          invited_at: newInvitedAt
        })
        .eq('id', invitationId);
        
      if (error) throw error;
      
      // Send the email
      const emailSent = await sendInvitationEmail(
        invitation.email,
        organizationData.name,
        invitation.token,
        inviterName
      );
      
      if (!emailSent) {
        throw new Error('Failed to send invitation email');
      }
      
      // Update local state
      setInvitations(prevInvitations => 
        prevInvitations.map(inv => 
          inv.id === invitationId 
            ? {
                ...inv,
                expires_at: newExpiresAt,
                invited_at: newInvitedAt
              } 
            : inv
        )
      );
      
      toast({
        title: "Invitation resent",
        description: "The invitation has been successfully resent.",
      });
    } catch (err: any) {
      console.error('Error resending invitation:', err);
      
      let errorTitle = "Error resending invitation";
      let errorDescription = "An error occurred while resending the invitation.";
      
      if (err.message?.includes('not found')) {
        errorTitle = "Invitation not found";
        errorDescription = "The invitation could not be found. It may have been deleted.";
      } else if (err.message?.includes('permission')) {
        errorTitle = "Permission denied";
        errorDescription = "You don't have permission to resend this invitation.";
      } else if (err.message?.includes('email')) {
        errorTitle = "Email delivery failed";
        errorDescription = "The invitation was updated but the email could not be sent. Please try again.";
      } else if (err.message) {
        errorDescription = err.message;
      }
      
      toast({
        title: errorTitle,
        description: errorDescription,
        variant: "destructive",
      });
    } finally {
      setIsResending(null);
    }
  };

  // Function to cancel invitation
  const handleCancelInvitation = async (invitationId: string) => {
    setIsCancelling(invitationId);
    
    try {
      // Get the invitation details first
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) {
        throw new Error('Invitation not found');
      }
      
      // Confirm the cancellation
      if (!window.confirm(`Are you sure you want to cancel the invitation for ${invitation.email}?`)) {
        setIsCancelling(null);
        return;
      }
      
      // Delete the invitation
      const { error } = await supabase
        .from('pending_invitations')
        .delete()
        .eq('id', invitationId);
        
      if (error) throw error;
      
      // Update local state
      setInvitations(prevInvitations => 
        prevInvitations.filter(invitation => invitation.id !== invitationId)
      );
      
      // Log security activity
      try {
        const { error: securityError } = await supabase
          .from('security_activity')
          .insert({
            user_id: (await supabase.auth.getUser()).data.user?.id,
            event_type: 'invitation_cancelled',
            description: `Cancelled invitation for ${invitation.email}`,
            ip_address: '127.0.0.1', // In a real app, you would get the actual IP
            organization_id: invitation.organization_id
          });
          
        if (securityError) {
          console.error('Error logging security activity:', securityError);
        }
      } catch (secError) {
        console.error('Error in security logging:', secError);
      }
      
      toast({
        title: "Invitation cancelled",
        description: "The invitation has been successfully cancelled.",
      });
    } catch (err: any) {
      console.error('Error cancelling invitation:', err);
      toast({
        title: "Error cancelling invitation",
        description: err.message || "An error occurred while cancelling the invitation.",
        variant: "destructive",
      });
    } finally {
      setIsCancelling(null);
    }
  };

  // Function to sort invitations
  const sortedInvitations = [...invitations]
    .sort((a, b) => {
      if (sortColumn === 'email') {
        const emailA = a.email.toLowerCase();
        const emailB = b.email.toLowerCase();
        return sortDirection === 'asc'
          ? emailA.localeCompare(emailB)
          : emailB.localeCompare(emailA);
      }
      
      if (sortColumn === 'date') {
        const dateA = new Date(a.invited_at).getTime();
        const dateB = new Date(b.invited_at).getTime();
        return sortDirection === 'asc'
          ? dateA - dateB
          : dateB - dateA;
      }
      
      return 0;
    });

  // Function to toggle sort direction
  const toggleSort = (column: 'email' | 'date') => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  // Helper function to render role badge
  const getRoleBadge = (role: OrganizationRole) => {
    switch (role) {
      case 'org_admin':
        return <Badge>Admin</Badge>;
      case 'org_member':
        return <Badge variant="secondary">Member</Badge>;
      case 'org_retoucher':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Retoucher</Badge>;
      case 'org_prompter':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Prompter</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Pending Invitations</h3>
        </div>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Sent</TableHead>
                <TableHead>Expires</TableHead>
                <TableHead>Invited By</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[1, 2, 3].map((_, index) => (
                <TableRow key={index} className="animate-pulse">
                  <TableCell><div className="h-4 w-40 bg-muted rounded"></div></TableCell>
                  <TableCell><div className="h-4 w-24 bg-muted rounded"></div></TableCell>
                  <TableCell><div className="h-4 w-24 bg-muted rounded"></div></TableCell>
                  <TableCell><div className="h-4 w-32 bg-muted rounded"></div></TableCell>
                  <TableCell className="text-right"><div className="h-8 w-8 bg-muted rounded ml-auto"></div></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="py-12 text-center">
        <p className="text-red-500">Error loading pending invitations: {error}</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-medium">Pending Invitations</h3>
          <Badge variant="outline" className="ml-2">{sortedInvitations.length}</Badge>
        </div>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('email')}
                >
                  Email
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead>
                <button 
                  className="flex items-center gap-1 hover:text-foreground"
                  onClick={() => toggleSort('date')}
                >
                  Sent
                  <ArrowUpDown size={14} className="ml-1" />
                </button>
              </TableHead>
              <TableHead>Expires</TableHead>
              <TableHead>Invited By</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedInvitations.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                  No pending invitations
                </TableCell>
              </TableRow>
            ) : (
              sortedInvitations.map(invitation => {
                const expired = isExpired(invitation.expires_at);
                const canModify = isPlatformUser || isBrandAdmin;
                
                return (
                  <TableRow key={invitation.id} className={expired ? "bg-muted/30" : ""}>
                    <TableCell>{invitation.email}</TableCell>
                    <TableCell>
                      <span className="text-sm text-muted-foreground">{formatDate(invitation.invited_at)}</span>
                    </TableCell>
                    <TableCell>
                      <span className={`text-sm flex items-center gap-1 ${expired ? "text-destructive" : "text-muted-foreground"}`}>
                        <Clock size={14} />
                        {expired ? 'Expired' : `Expires in ${getTimeUntilExpiration(invitation.expires_at)}`}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">{getInviterDisplayName(invitation)}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      {canModify && (
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => handleResendInvitation(invitation.id)}
                            disabled={!!isResending}
                            className="h-8 px-2"
                          >
                            <RefreshCw size={14} className={isResending === invitation.id ? "animate-spin" : ""} />
                            <span className="ml-1 sr-only md:not-sr-only">Resend</span>
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => handleCancelInvitation(invitation.id)}
                            disabled={!!isCancelling}
                            className="h-8 px-2 text-destructive hover:text-destructive"
                          >
                            <X size={14} />
                            <span className="ml-1 sr-only md:not-sr-only">Cancel</span>
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

export default PendingInvitationsTable; 