import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Package, CheckCircle, Calendar, Clock, BarChart, Plus } from 'lucide-react';
import { cn } from '../common/utils/utils';
import { useOrganizations } from '../common/hooks/useOrganizations';
import { useProducts } from '../common/hooks/useProducts';
import { useCollections } from '../common/hooks/useCollections';
import { Skeleton } from '../../components/ui/skeleton';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/use-toast';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { supabase, STORAGE_BUCKETS } from "../common/utils/supabase";

// Organization overview dashboard component

interface OrganizationOverviewProps {
  searchTerm?: string;
  sortOption?: string;
  viewMode?: 'grid' | 'list';
  allowCreate?: boolean;
}

export function OrganizationOverview({ 
  searchTerm = '', 
  sortOption = 'name',
  viewMode = 'grid',
  allowCreate = true
}: OrganizationOverviewProps) {
  const navigate = useNavigate();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [isAddOrganizationDialogOpen, setIsAddOrganizationDialogOpen] = useState(false);
  const [newOrganizationName, setNewOrganizationName] = useState('');
  const [newOrganizationLogo, setNewOrganizationLogo] = useState<File | null>(null);
  const [isCreatingOrganization, setIsCreatingOrganization] = useState(false);
  
  // Convert sort option to parameters for the hook
  const getSortParams = () => {
    switch (sortOption) {
      case 'name':
        return { sortBy: 'name' as const, sortOrder: 'asc' as const };
      case 'name_desc':
        return { sortBy: 'name' as const, sortOrder: 'desc' as const };
      case 'created_at':
        return { sortBy: 'created_at' as const, sortOrder: 'desc' as const };
      case 'created_at_asc':
        return { sortBy: 'created_at' as const, sortOrder: 'asc' as const };
      default:
        return { sortBy: 'name' as const, sortOrder: 'asc' as const };
    }
  };
  
  const { sortBy, sortOrder } = getSortParams();
  
  // Fetch organizations (brands) from Supabase
  const { 
    data: organizations = [], 
    isLoading, 
    isError,
    error
  } = useOrganizations({
    searchTerm,
    sortBy,
    sortOrder
  });

  // Fetch collections to count by organization
  const { data: collections = [], isLoading: isLoadingCollections } = useCollections();
  
  // Fetch products to count by client
  const { data: products = [], isLoading: isLoadingProducts } = useProducts();

  // Get collections count for an organization
  const getCollectionsCount = (organizationId: string) => {
    return collections.filter(collection => collection.organization_id === organizationId).length;
  };

  // Get products count for an organization
  const getProductsCount = (organizationId: string) => {
    // Get all collections for this organization
    const orgCollections = collections.filter(collection => collection.organization_id === organizationId);
    // Get all products in these collections
    const orgProducts = products.filter(product => 
      orgCollections.some(collection => collection.id === product.collection_id)
    );
    return orgProducts.length;
  };

  // Get active collections count for an organization
  const getActiveCollectionsCount = (organizationId: string) => {
    return collections.filter(
      collection => collection.organization_id === organizationId && collection.status === 'active'
    ).length;
  };

  // Get recently updated date
  const getLastUpdated = (organizationId: string) => {
    const orgCollections = collections.filter(collection => collection.organization_id === organizationId);
    if (orgCollections.length === 0) return null;
    
    // Find the most recently updated collection
    const mostRecentCollection = orgCollections.reduce((latest, current) => {
      return (latest.updated_at && current.updated_at && 
        new Date(current.updated_at) > new Date(latest.updated_at)) ? current : latest;
    }, orgCollections[0]);
    
    return mostRecentCollection?.updated_at || null;
  };

  // Format date to a readable format
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Unknown date';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Format relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      if (diffHours === 0) {
        const diffMinutes = Math.floor(diffTime / (1000 * 60));
        return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
      }
      return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    } else if (diffDays < 30) {
      const diffWeeks = Math.floor(diffDays / 7);
      return `${diffWeeks} week${diffWeeks !== 1 ? 's' : ''} ago`;
    } else {
      return formatDate(dateString);
    }
  };

  const handleOrganizationClick = (organizationId: string) => {
    navigate(`/organizations/${organizationId}`);
  };

  const handleCreateOrganization = () => {
    setIsAddOrganizationDialogOpen(true);
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewOrganizationLogo(e.target.files[0]);
    }
  };

  // Handle add organization form submit
  const handleAddOrganizationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isCreatingOrganization) return; // Prevent multiple submissions
    if (!newOrganizationName.trim()) {
      toast({
        title: "Error",
        description: "Organization name is required",
        variant: "destructive",
      });
      return;
    }
    
    setIsCreatingOrganization(true);
    
    try {
      let logoUrl: string | null = null;
      
      // Upload logo if provided
      if (newOrganizationLogo) {
        const fileExt = newOrganizationLogo.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `organization-logos/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .upload(filePath, newOrganizationLogo);
        
        if (uploadError) throw uploadError;
        
        // Get public URL
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .getPublicUrl(filePath);
        
        logoUrl = publicUrlData.publicUrl;
      }
      
      // Insert a new organization
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          name: newOrganizationName,
          logo_url: logoUrl
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Show success toast
      toast({
        title: "Brand created",
        description: "New brand has been created successfully.",
        variant: "default",
      });
      
      // Reset form and close dialog
      setNewOrganizationName('');
      setNewOrganizationLogo(null);
      setIsAddOrganizationDialogOpen(false);
      
      // Navigate to the new organization's page
      if (data) {
        navigate(`/organizations/${data.id}`);
      }
    } catch (error: any) {
      console.error('Error creating organization:', error);
      
      // Show error toast
      toast({
        title: "Error creating brand",
        description: error.message || "An error occurred while creating the brand.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingOrganization(false);
    }
  };

  const CreateOrganizationCard = () => (
    <Card 
      className={cn(
        "transition-all duration-200 hover:bg-[#E6F0FF] hover:shadow-md cursor-pointer",
        viewMode === 'grid' ? "h-[180px]" : "h-auto"
      )}
      onClick={handleCreateOrganization}
    >
      <CardContent className="p-4">
        <div className={cn(
          "flex",
          viewMode === 'grid' ? "h-full flex-col" : "flex-row items-center"
        )}>
          <div className={cn(
            "flex items-center gap-4",
            viewMode === 'grid' ? "mb-3" : "flex-1"
          )}>
            <div className="w-12 h-12 rounded-md bg-primary/10 flex items-center justify-center">
              <Plus size={24} className="text-primary" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-primary">Create New Brand</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Add a new brand to your workspace
              </p>
            </div>
          </div>

          {viewMode === 'grid' && (
            <div className="mt-auto">
              <Button 
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCreateOrganization();
                }}
              >
                <Plus size={16} className="mr-2" />
                New Brand
              </Button>
            </div>
          )}

          {viewMode === 'list' && (
            <Button 
              className="ml-4"
              onClick={(e) => {
                e.stopPropagation();
                handleCreateOrganization();
              }}
            >
              <Plus size={16} className="mr-2" />
              New Brand
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading || isLoadingCollections || isLoadingProducts) {
    return (
      <div className={cn(
        "animate-enter",
        viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "flex flex-col gap-3"
      )}>
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className={cn(
            viewMode === 'grid' ? "h-[180px]" : "h-auto"
          )}>
            <CardContent className="p-4">
              <div className={cn(
                "flex",
                viewMode === 'grid' ? "h-full flex-col" : "flex-row items-center"
              )}>
                <div className={cn(
                  "flex items-center gap-4",
                  viewMode === 'grid' ? "mb-3" : "flex-1"
                )}>
                  <Skeleton className="w-12 h-12 rounded-md" />
                  <div className="flex-1">
                    <Skeleton className="h-6 w-32 mb-2" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
                {viewMode === 'grid' && (
                  <div className="flex gap-4 mb-4">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                )}
                <div className={viewMode === 'grid' ? "mt-auto w-full" : "ml-4"}>
                  <Skeleton className={cn("h-9", viewMode === 'grid' ? "w-full" : "w-28")} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (isError) {
    return (
      <div className="py-12 text-center">
        <p className="text-red-500">Error loading brands: {error?.message}</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Try Again
        </Button>
      </div>
    );
  }

  // If there are no organizations (brands), show only the create client card
  if (organizations.length === 0) {
    if (!allowCreate) {
      return (
        <div className="py-12 text-center">
          <p className="text-muted-foreground">No brands available.</p>
        </div>
      );
    }
    
    return (
      <>
        <div className={cn(
          "animate-enter",
          viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "flex flex-col gap-3"
        )}>
          <CreateOrganizationCard />
        </div>
        
        {/* Add Client Dialog */}
        <Dialog open={isAddOrganizationDialogOpen} onOpenChange={setIsAddOrganizationDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Brand</DialogTitle>
              <DialogDescription>
                Create a new brand account. You can add more details after creation.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddOrganizationSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="name" className="required">Brand Name</Label>
                  <Input
                    id="name"
                    placeholder="Enter brand name"
                    value={newOrganizationName}
                    onChange={(e) => setNewOrganizationName(e.target.value)}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="logo">Brand Logo (Optional)</Label>
                  <Input
                    id="logo"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: Square image, at least 200x200px
                  </p>
                </div>
              </div>
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddOrganizationDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreatingOrganization}>
                  {isCreatingOrganization ? 'Creating...' : 'Create Brand'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  // If there are organizations, show them along with the create client card at the end
  return (
    <>
      <div className={cn(
        "animate-enter",
        viewMode === 'grid' ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" : "flex flex-col gap-3"
      )}>
        {organizations.map((organization) => {
          const collectionsCount = getCollectionsCount(organization.id);
          const productsCount = getProductsCount(organization.id);
          const activeCollectionsCount = getActiveCollectionsCount(organization.id);
          const lastUpdated = getLastUpdated(organization.id);
          
          return (
            <Card 
              key={organization.id} 
              className={cn(
                "transition-all duration-200 hover:bg-[#E6F0FF] hover:shadow-md cursor-pointer",
                viewMode === 'grid' ? "h-[180px]" : "h-auto"
              )}
              onClick={() => handleOrganizationClick(organization.id)}
            >
              <CardContent className="p-4">
                <div className={cn(
                  "flex",
                  viewMode === 'grid' ? "h-full flex-col" : "flex-row items-center"
                )}>
                  <div className={cn(
                    "flex items-center gap-4",
                    viewMode === 'grid' ? "mb-3" : "flex-1"
                  )}>
                    <div className="w-12 h-12 rounded-md bg-secondary flex items-center justify-center overflow-hidden">
                      <img 
                        src={organization.logo_url || '/placeholder.svg'} 
                        alt={organization.name} 
                        className="w-8 h-8 object-contain"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold truncate">{organization.name}</h3>
                        <Badge 
                          variant="default"
                          className="capitalize"
                        >
                          active
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                        <Calendar size={14} />
                        <span>Created {formatDate(organization.created_at)}</span>
                      </div>
                    </div>
                  </div>

                  {viewMode === 'grid' && (
                    <div className="grid grid-cols-2 gap-2 mb-4">
                      <div className="flex items-center gap-2">
                        <Package size={16} className="text-muted-foreground" />
                        <span className="text-sm">{productsCount} Products</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle size={16} className="text-muted-foreground" />
                        <span className="text-sm">{collectionsCount} Collections</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <BarChart size={16} className="text-muted-foreground" />
                        <span className="text-sm">{activeCollectionsCount} Active</span>
                      </div>
                      {lastUpdated && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center gap-2 cursor-help">
                                <Clock size={16} className="text-muted-foreground" />
                                <span className="text-sm truncate">Updated {formatRelativeTime(lastUpdated)}</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Last updated on {formatDate(lastUpdated)}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  )}

                  {viewMode === 'list' && (
                    <div className="flex items-center gap-6 mx-4">
                      <div className="flex items-center gap-2">
                        <Package size={16} className="text-muted-foreground" />
                        <span className="text-sm">{productsCount} Products</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle size={16} className="text-muted-foreground" />
                        <span className="text-sm">{collectionsCount} Collections</span>
                      </div>
                      {lastUpdated && (
                        <div className="flex items-center gap-2">
                          <Clock size={16} className="text-muted-foreground" />
                          <span className="text-sm">Updated {formatRelativeTime(lastUpdated)}</span>
                        </div>
                      )}
                    </div>
                  )}

                  <div className={cn(
                    viewMode === 'grid' ? "mt-auto" : "ml-4"
                  )}>
                    <Button 
                      variant="outline" 
                      className={viewMode === 'grid' ? "w-full" : ""}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleOrganizationClick(organization.id);
                      }}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
        {allowCreate && <CreateOrganizationCard />}
      </div>
      
      {/* Add Client Dialog */}
      <Dialog open={isAddOrganizationDialogOpen} onOpenChange={setIsAddOrganizationDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Brand</DialogTitle>
            <DialogDescription>
              Create a new brand account. You can add more details after creation.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddOrganizationSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name" className="required">Brand Name</Label>
                <Input
                  id="name"
                  placeholder="Enter brand name"
                  value={newOrganizationName}
                  onChange={(e) => setNewOrganizationName(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="logo">Brand Logo (Optional)</Label>
                <Input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-muted-foreground">
                  Recommended: Square image, at least 200x200px
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsAddOrganizationDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreatingOrganization}>
                {isCreatingOrganization ? 'Creating...' : 'Create Brand'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default OrganizationOverview;
