import React, { useState, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../ui/use-toast';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Checkbox } from "../ui/checkbox";
import { UserPlus, Loader2 } from 'lucide-react';
import type { Database } from '../common/types/database.types';

type UserRole = Database['public']['Enums']['user_role'];
type Organization = Database['public']['Tables']['organizations']['Row'];

export default function CreateUserDirectForm() {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form fields
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [role, setRole] = useState<UserRole>('brand_member');
  const [isFreelancer, setIsFreelancer] = useState(false);
  const [selectedOrganizations, setSelectedOrganizations] = useState<string[]>([]);
  
  // Organizations list
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoadingOrgs, setIsLoadingOrgs] = useState(true);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('name');

      if (error) throw error;
      setOrganizations(data || []);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast({
        title: "Error loading organizations",
        description: "Failed to load organization list",
        variant: "destructive",
      });
    } finally {
      setIsLoadingOrgs(false);
    }
  };

  const handleOrganizationToggle = (orgId: string) => {
    setSelectedOrganizations(prev => 
      prev.includes(orgId) 
        ? prev.filter(id => id !== orgId)
        : [...prev, orgId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!email || !firstName || !lastName) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (role !== 'platform_admin' && role !== 'platform_super' && selectedOrganizations.length === 0) {
      toast({
        title: "No organization selected",
        description: "Please select at least one organization for non-platform users",
        variant: "destructive",
      });
      return;
    }

    if (isFreelancer && role !== 'brand_admin') {
      toast({
        title: "Invalid configuration",
        description: "Freelancers must have brand_admin role",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Get auth token for the Edge Function
      const session = await supabase.auth.getSession();
      if (!session.data.session) {
        throw new Error('No active session');
      }

      // Call the Edge Function
      const response = await supabase.functions.invoke('create-user', {
        body: {
          email,
          firstName,
          lastName,
          role,
          organizationIds: selectedOrganizations,
          isFreelancer,
          sendPasswordReset: true
        }
      });

      if (response.error) {
        throw response.error;
      }

      const result = response.data;
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create user');
      }

      toast({
        title: "User created successfully",
        description: (
          <div>
            <p>Account created for {email}</p>
            <p className="text-sm mt-1">Password reset email has been sent</p>
          </div>
        ),
      });

      // Reset form
      setEmail('');
      setFirstName('');
      setLastName('');
      setRole('brand_member');
      setIsFreelancer(false);
      setSelectedOrganizations([]);

    } catch (error: any) {
      console.error('Error creating user:', error);
      
      let errorMessage = 'Failed to create user';
      if (error.message?.includes('already exists')) {
        errorMessage = 'A user with this email already exists';
      } else if (error.message?.includes('permission')) {
        errorMessage = 'You do not have permission to create users';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Error creating user",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="required">Email</Label>
          <Input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="role" className="required">Role</Label>
          <Select value={role} onValueChange={(value) => setRole(value as UserRole)}>
            <SelectTrigger id="role">
              <SelectValue placeholder="Select a role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="platform_admin">Platform Admin</SelectItem>
              <SelectItem value="brand_admin">Brand Admin</SelectItem>
              <SelectItem value="brand_member">Brand Member</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="required">First Name</Label>
          <Input
            id="firstName"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            placeholder="John"
            required
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="lastName" className="required">Last Name</Label>
          <Input
            id="lastName"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            placeholder="Doe"
            required
          />
        </div>
      </div>

      {role === 'brand_admin' && (
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="freelancer"
            checked={isFreelancer}
            onCheckedChange={(checked) => setIsFreelancer(checked as boolean)}
          />
          <Label 
            htmlFor="freelancer" 
            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            This is an external freelancer
          </Label>
        </div>
      )}

      {role !== 'platform_admin' && role !== 'platform_super' && (
        <div className="space-y-2">
          <Label className="required">Assign to Organizations</Label>
          <p className="text-xs text-muted-foreground mb-2">
            Users can belong to multiple organizations. Select all that apply.
          </p>
          {isLoadingOrgs ? (
            <div className="text-center py-4">
              <Loader2 className="h-4 w-4 animate-spin mx-auto" />
            </div>
          ) : organizations.length === 0 ? (
            <p className="text-sm text-muted-foreground">No organizations available</p>
          ) : (
            <div className="border rounded-md p-3 max-h-[200px] overflow-y-auto space-y-2">
              {organizations.map((org) => (
                <div key={org.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`org-${org.id}`}
                    checked={selectedOrganizations.includes(org.id)}
                    onCheckedChange={() => handleOrganizationToggle(org.id)}
                  />
                  <Label 
                    htmlFor={`org-${org.id}`}
                    className="text-sm font-normal cursor-pointer flex-1"
                  >
                    {org.name}
                  </Label>
                </div>
              ))}
            </div>
          )}
          {isFreelancer && selectedOrganizations.length > 0 && (
            <p className="text-xs text-muted-foreground">
              Freelancer will have brand admin access to {selectedOrganizations.length} organization{selectedOrganizations.length !== 1 ? 's' : ''}
            </p>
          )}
        </div>
      )}

      <div className="bg-muted/50 p-3 rounded-md">
        <p className="text-sm text-muted-foreground">
          <strong>Note:</strong> The user will receive a password reset email to set up their account access. Make sure the email address is correct.
        </p>
      </div>

      <Button 
        type="submit" 
        disabled={isSubmitting || (role !== 'platform_admin' && role !== 'platform_super' && selectedOrganizations.length === 0)}
        className="w-full"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Creating User...
          </>
        ) : (
          <>
            <UserPlus className="h-4 w-4 mr-2" />
            Create User Account
          </>
        )}
      </Button>
    </form>
  );
}