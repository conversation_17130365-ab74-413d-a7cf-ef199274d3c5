import React, { useState } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../ui/use-toast';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { UserPlus } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { v4 as uuidv4 } from 'uuid';
import { sendInvitationEmail, isLocalEnvironment, getMailServerUrl } from '../common/utils/emailUtils';

interface InviteMemberFormProps {
  organizationId: string;
  organizationName: string;
  onInviteSent?: () => void;
  triggerButton?: React.ReactNode;
}

export function InviteMemberForm({ 
  organizationId, 
  organizationName,
  onInviteSent,
  triggerButton
}: InviteMemberFormProps) {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [emails, setEmails] = useState('');
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    if (!emails.trim()) {
      toast({
        title: "Error",
        description: "Please enter at least one email address",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Parse multiple emails (comma or newline separated)
      const emailList = emails
        .split(/[,\n]/)
        .map(email => email.trim())
        .filter(email => email !== '');
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = emailList.filter(email => !emailRegex.test(email));
      
      if (invalidEmails.length > 0) {
        toast({
          title: "Invalid email format",
          description: `The following emails are invalid: ${invalidEmails.join(', ')}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }
      
      const user = await supabase.auth.getUser();
      const inviterId = user.data.user?.id;
      
      if (!inviterId) {
        throw new Error("Unable to identify current user");
      }
      
      // Get current user's name for the invitation
      const { data: inviterData } = await supabase
        .from('users')
        .select('first_name, last_name, email')
        .eq('id', inviterId)
        .single();
        
      const inviterName = inviterData ? 
        `${inviterData.first_name || ''} ${inviterData.last_name || ''}`.trim() || inviterData.email 
        : '';
      
      // First get all existing memberships for this organization
      const { data: existingMemberships, error: membershipError } = await supabase
        .from('organization_memberships')
        .select('user_id')
        .eq('organization_id', organizationId);
        
      if (membershipError) throw membershipError;
      
      // Then get details of those users
      const memberUserIds = existingMemberships?.map(m => m.user_id) || [];
      
      let alreadyMemberEmails: string[] = [];
      
      if (memberUserIds.length > 0) {
        const { data: memberUsers, error: memberUsersError } = await supabase
          .from('users')
          .select('email')
          .in('id', memberUserIds);
          
        if (memberUsersError) throw memberUsersError;
        
        alreadyMemberEmails = memberUsers?.map(u => u.email.toLowerCase()) || [];
      }
      
      // Filter out emails of users who are already members
      const emailsToInvite = emailList.filter(
        email => !alreadyMemberEmails.includes(email.toLowerCase())
      );
      
      // If some emails are already members, show a message
      const alreadyMembers = emailList.filter(
        email => alreadyMemberEmails.includes(email.toLowerCase())
      );
      
      if (alreadyMembers.length > 0) {
        toast({
          title: "Already members",
          description: `The following emails are already members: ${alreadyMembers.join(', ')}`,
          variant: "destructive",
        });
        
        if (alreadyMembers.length === emailList.length) {
          setIsSubmitting(false);
          return;
        }
      }
      
      // Create pending invitations for all users that aren't already members
      if (emailsToInvite.length > 0) {
        // Create invitations and send emails
        for (const email of emailsToInvite) {
          const invitationToken = uuidv4();
          
          // 1. Create the invitation record
          // Log debugging info about user and permission
          console.log(`Attempt to create invitation for org ${organizationId} by user ${inviterId}`);
          
          // Create invitation payload
          // Note: In the new role system, invited users will start as 'brand_member' by default
          // and can be promoted later by organization admins
          const invitationPayload = {
            email: email.toLowerCase(),
            organization_id: organizationId,
            invited_by: inviterId,
            token: invitationToken,
            // Only include message if it's not empty
            ...(message ? { message } : {})
          };
          
          console.log('Creating invitation with payload:', invitationPayload);
          
          // Attempt to insert the invitation with RLS debugging
          console.log('About to perform insert operation...');
          const { error: invitationError } = await supabase
            .from('pending_invitations')
            .insert(invitationPayload);
            
          if (invitationError) {
            console.error(`Error creating invitation for ${email}:`, invitationError);
            console.error('Full error details:', JSON.stringify(invitationError, null, 2));
            
            // Handle specific error cases
            if (invitationError.code === '23505' || invitationError.message?.includes('duplicate')) {
              toast({
                title: "Invitation already sent",
                description: `An invitation has already been sent to ${email}. They can use the existing invitation link.`,
                variant: "destructive",
              });
            } else if (invitationError.code === '42501' || invitationError.message?.includes('permission denied')) {
              toast({
                title: "Permission denied",
                description: "You don't have permission to invite members to this organization. Only administrators can send invitations.",
                variant: "destructive",
              });
            } else if (invitationError.message?.includes('expired')) {
              toast({
                title: "Previous invitation expired",
                description: `The previous invitation to ${email} has expired. A new invitation will be sent.`,
              });
            } else {
              toast({
                title: "Failed to create invitation",
                description: `Could not create invitation for ${email}. Please try again later.`,
                variant: "destructive",
              });
            }
            continue; // Skip to next email
          } else {
            // 2. Send invitation email
            console.log(`Sending invitation email to ${email}`);
            const emailSent = await sendInvitationEmail(
              email,
              organizationName,
              invitationToken,
              inviterName
            );
            
            if (emailSent) {
              console.info(`✅ Invitation created and email sent to ${email}`);
              console.info(`📧 Check mail server at ${getMailServerUrl()}`);
              
              // In local development, show toast with direct link for testing
              if (isLocalEnvironment()) {
                const inviteUrl = `${window.location.origin}/invite/${invitationToken}`;
                toast({
                  title: "Local testing: Use this direct URL",
                  description: (
                    <div>
                      <p>For testing the invitation flow, use this direct link instead of the magic link:</p>
                      <a 
                        href={inviteUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline break-all mt-2 inline-block"
                      >
                        {inviteUrl}
                      </a>
                    </div>
                  ),
                  duration: 10000, // 10 seconds
                });
              }
            } else {
              console.warn(`⚠️ Invitation created but email sending failed for ${email}`);
            }
          }
        }
        
        toast({
          title: "Invitations sent",
          description: `${emailsToInvite.length} users have been invited to the organization.`,
        });
      }
      
      // Reset form and close dialog
      setEmails('');
      setMessage('');
      setIsOpen(false);
      
      // Call the callback if provided
      if (onInviteSent) {
        onInviteSent();
      }
    } catch (err: any) {
      console.error('Error inviting members:', err);
      
      // Provide specific error messages
      let errorTitle = "Error inviting members";
      let errorDescription = "An error occurred while sending invitations.";
      
      if (err.message?.includes('auth')) {
        errorTitle = "Authentication error";
        errorDescription = "Please log in again and try sending the invitations.";
      } else if (err.message?.includes('network')) {
        errorTitle = "Network error";
        errorDescription = "Unable to connect to the server. Please check your internet connection.";
      } else if (err.message?.includes('rate limit')) {
        errorTitle = "Too many invitations";
        errorDescription = "You've sent too many invitations recently. Please wait a few minutes and try again.";
      } else if (err.message) {
        errorDescription = err.message;
      }
      
      toast({
        title: errorTitle,
        description: errorDescription,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {triggerButton || (
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Members
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invite People to {organizationName}</DialogTitle>
          <DialogDescription>
            Add team members by sending them an invitation email.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="emails" className="required">Email Addresses</Label>
              <Textarea
                id="emails"
                placeholder="Enter email addresses (one per line, or comma-separated)"
                value={emails}
                onChange={(e) => setEmails(e.target.value)}
                rows={4}
                required
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground">
                Enter multiple email addresses separated by commas or new lines
              </p>
            </div>
            <div className="bg-muted/50 p-3 rounded-md">
              <p className="text-sm text-muted-foreground">
                <strong>Note:</strong> Invited users will start as Brand Members and can be promoted to different roles later by organization administrators.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="message">Personal Message (optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a personal message to the invitation email"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Sending...' : 'Send Invitations'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default InviteMemberForm; 