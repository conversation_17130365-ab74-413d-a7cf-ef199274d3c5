# Organization Management Components

This folder contains components for **brand/organization management** - handling organizations (brands), their members, invitations, and overview dashboards.

## 📋 Component Overview

### `OrganizationOverview.tsx`
- **Purpose**: Main dashboard displaying all organizations with key metrics
- **Features**: Organization cards with statistics, creation dialog, grid/list views
- **Usage**: Used in `/organizations` page and platform admin dashboard
- **Renamed from**: `ClientsList.tsx` (cleaned up naming inconsistencies)

### `InviteMemberForm.tsx`
- **Purpose**: Form for inviting new members to organizations
- **Features**: Email invitation with role assignment, validation
- **Usage**: Modal/dialog for adding members to organizations

### `OrganizationMembersTable.tsx`
- **Purpose**: Table displaying all members of an organization
- **Features**: Member list with roles, management actions, sorting
- **Usage**: Organization members management page

### `PendingInvitationsTable.tsx`
- **Purpose**: Table showing pending/unaccepted invitations
- **Features**: Invitation status, resend/cancel actions, expiration tracking
- **Usage**: Organization members page, invitation management

## 🎨 Design Patterns

### Role-Based Features
Components respect the unified role system:

```typescript
const { isPlatformUser, isBrandAdmin } = useUserRole();

// Platform users can manage all organizations
const canManageAllOrgs = isPlatformUser;

// Brand admins can manage their own organization
const canManageOwnOrg = isBrandAdmin;
```

### Organization Data Flow
1. **Organization selection** via context or route parameters
2. **Membership checking** for access control
3. **CRUD operations** with proper permissions
4. **Real-time updates** for member changes

### Permission Patterns
```typescript
// Example: Organization creation
const canCreateOrganization = isPlatformUser;

// Example: Member management  
const canManageMembers = isPlatformUser;

// Example: Invitation management
const canInviteMembers = isPlatformUser;
```

## 🚀 Usage Examples

### Organization Overview Dashboard
```typescript
import OrganizationOverview from './OrganizationOverview';

function OrganizationsPage() {
  return (
    <div>
      <PageTitle title="Organizations" />
      <OrganizationOverview 
        searchTerm={searchTerm}
        sortOption={sortOption}
        viewMode="grid"
        allowCreate={isPlatformUser}
      />
    </div>
  );
}
```

### Member Management Interface
```typescript
import { OrganizationMembersTable, PendingInvitationsTable, InviteMemberForm } from './organizations';

function OrganizationMembersPage() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between">
        <h1>Organization Members</h1>
        <InviteMemberForm 
          organizationId={orgId}
          onInviteSent={refreshData}
        />
      </div>
      
      <OrganizationMembersTable 
        organizationId={orgId}
        allowManagement={canManageMembers}
      />
      
      <PendingInvitationsTable 
        organizationId={orgId}
      />
    </div>
  );
}
```

## 🔄 Component Relationships

### Data Dependencies
- **OrganizationOverview**: Fetches all organizations user has access to
- **OrganizationMembersTable**: Fetches members for specific organization
- **PendingInvitationsTable**: Fetches pending invitations for organization
- **InviteMemberForm**: Creates new invitations

### Navigation Flow
```
OrganizationOverview → Organization Detail Page
                    ↓
              OrganizationMembersTable + PendingInvitationsTable
                    ↓
              InviteMemberForm (modal/dialog)
```

## 🔧 Technical Implementation

### State Management
```typescript
// Organization context for current organization
const { currentOrganization, userMemberships } = useOrganizations();

// Role-based permissions
const { isPlatformUser } = useUserRole();

// Local component state for UI interactions
const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
```

### Data Fetching
- **useOrganizations**: Hook for fetching organizations
- **Direct Supabase queries**: For member/invitation data
- **Real-time subscriptions**: For live member updates (future enhancement)

### Permission Enforcement
```typescript
// RLS policies in database ensure data access control
// Frontend components provide UI-level permission checks
const canViewOrganization = isPlatformUser || userMemberships.some(m => m.organization_id === orgId);
```

## 🎯 Migration Notes

### Renamed Components
- **`ClientsList.tsx`** → **`OrganizationOverview.tsx`**
  - Fixed naming inconsistencies (client vs organization vs brand)
  - Cleaned up variable names and comments
  - Updated UI text for consistency

### Terminology Consistency
- **Database**: `organizations` table
- **Code**: `organization` variables and functions  
- **UI**: "Brand" for user-facing text
- **Routes**: `/organizations/:id`

## 🔮 Future Enhancements

### Planned Features
- **Real-time member updates** via Supabase realtime
- **Bulk member operations** (bulk invite, bulk role changes)
- **Organization settings management** integration
- **Advanced search and filtering** for large member lists

### Performance Optimizations
- **Virtualized member tables** for large organizations
- **Cached organization data** with smart invalidation
- **Optimistic updates** for better UX
- **Pagination** for member lists

## 🧪 Testing Considerations

### Unit Tests
- **Organization creation** flow
- **Member invitation** process
- **Permission-based** UI rendering
- **Form validation** and error handling

### Integration Tests
- **End-to-end invitation** workflow
- **Member management** operations
- **Organization switching** functionality
- **Permission enforcement** across components

### Performance Tests
- **Large organization** rendering (100+ members)
- **Search and filtering** performance
- **Concurrent member updates** handling