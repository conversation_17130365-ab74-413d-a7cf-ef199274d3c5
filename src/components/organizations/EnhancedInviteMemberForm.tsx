import React, { useState, useEffect } from 'react';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useUserRole } from '../../contexts/UserRoleContext';
import { useToast } from '../ui/use-toast';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../ui/dialog";
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { UserPlus, Plus, X } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Checkbox } from "../ui/checkbox";
import { v4 as uuidv4 } from 'uuid';
import { sendInvitationEmail, isLocalEnvironment, getMailServerUrl } from '../common/utils/emailUtils';
import { sendInvitationEmailViaAuth } from '../common/utils/localEmailService';
import type { Database } from '../common/types/database.types';

type UserRole = Database['public']['Enums']['user_role'];
type Organization = Database['public']['Tables']['organizations']['Row'];

interface EnhancedInviteMemberFormProps {
  organizationId?: string;
  organizationName?: string;
  onInviteSent?: () => void;
  triggerButton?: React.ReactNode;
}

export function EnhancedInviteMemberForm({ 
  organizationId, 
  organizationName,
  onInviteSent,
  triggerButton
}: EnhancedInviteMemberFormProps) {
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { isPlatformUser } = useUserRole();
  const [isOpen, setIsOpen] = useState(false);
  const [emails, setEmails] = useState('');
  const [message, setMessage] = useState('');
  const [selectedRole, setSelectedRole] = useState<UserRole>('brand_member');
  const [isFreelancer, setIsFreelancer] = useState(false);
  const [selectedOrganizations, setSelectedOrganizations] = useState<string[]>(
    organizationId ? [organizationId] : []
  );
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch all organizations for platform admins
  useEffect(() => {
    if (isPlatformUser && isOpen) {
      fetchOrganizations();
    }
  }, [isPlatformUser, isOpen]);

  const fetchOrganizations = async () => {
    const { data, error } = await supabase
      .from('organizations')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching organizations:', error);
      toast({
        title: "Error loading organizations",
        description: "Failed to load organization list",
        variant: "destructive",
      });
    } else {
      setOrganizations(data || []);
    }
  };

  const handleOrganizationToggle = (orgId: string) => {
    setSelectedOrganizations(prev => 
      prev.includes(orgId) 
        ? prev.filter(id => id !== orgId)
        : [...prev, orgId]
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    if (!emails.trim()) {
      toast({
        title: "Error",
        description: "Please enter at least one email address",
        variant: "destructive",
      });
      return;
    }

    if (selectedOrganizations.length === 0 && selectedRole !== 'platform_admin' && selectedRole !== 'platform_super') {
      toast({
        title: "Error",
        description: "Please select at least one organization",
        variant: "destructive",
      });
      return;
    }

    // Validate freelancer configuration
    if (isFreelancer && selectedRole !== 'brand_admin') {
      toast({
        title: "Error",
        description: "Freelancers must have brand_admin role",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Parse multiple emails
      const emailList = emails
        .split(/[,\n]/)
        .map(email => email.trim())
        .filter(email => email !== '');
      
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = emailList.filter(email => !emailRegex.test(email));
      
      if (invalidEmails.length > 0) {
        toast({
          title: "Invalid email format",
          description: `The following emails are invalid: ${invalidEmails.join(', ')}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }
      
      const user = await supabase.auth.getUser();
      const inviterId = user.data.user?.id;
      
      if (!inviterId) {
        throw new Error("Unable to identify current user");
      }
      
      // Get current user's name for the invitation
      const { data: inviterData } = await supabase
        .from('users')
        .select('first_name, last_name, email')
        .eq('id', inviterId)
        .single();
        
      const inviterName = inviterData ? 
        `${inviterData.first_name || ''} ${inviterData.last_name || ''}`.trim() || inviterData.email 
        : '';
      
      // Create invitations
      const primaryOrgId = selectedOrganizations[0] || organizationId;
      const additionalOrgIds = selectedOrganizations.slice(1);
      
      for (const email of emailList) {
        const invitationToken = uuidv4();
        
        // Create the invitation with role if platform admin
        const invitationPayload: any = {
          email: email.toLowerCase(),
          organization_id: primaryOrgId,
          invited_by: inviterId,
          token: invitationToken,
          ...(message ? { message } : {})
        };

        // Only platform admins can set role
        if (isPlatformUser) {
          invitationPayload.role = selectedRole;
        }
        
        const { data: invitation, error: invitationError } = await supabase
          .from('pending_invitations')
          .insert(invitationPayload)
          .select()
          .single();
          
        if (invitationError) {
          console.error(`Error creating invitation for ${email}:`, invitationError);
          
          if (invitationError.code === '23505' || invitationError.message?.includes('duplicate')) {
            toast({
              title: "Invitation already sent",
              description: `An invitation has already been sent to ${email}.`,
              variant: "destructive",
            });
          } else if (invitationError.code === '42501') {
            toast({
              title: "Permission denied",
              description: "You don't have permission to invite members.",
              variant: "destructive",
            });
            break;
          } else {
            toast({
              title: "Failed to create invitation",
              description: `Could not create invitation for ${email}.`,
              variant: "destructive",
            });
          }
          continue;
        }

        // Create additional organization associations for freelancers
        if (invitation && additionalOrgIds.length > 0 && isFreelancer) {
          const orgAssociations = additionalOrgIds.map(orgId => ({
            invitation_id: invitation.id,
            organization_id: orgId
          }));

          const { error: orgError } = await supabase
            .from('invitation_organizations')
            .insert(orgAssociations);

          if (orgError) {
            console.error('Error creating organization associations:', orgError);
          }
        }

        // Update auth metadata if creating a freelancer
        if (isFreelancer && invitation) {
          // Store freelancer metadata in the invitation for the handle_new_user function
          const { error: updateError } = await supabase.rpc('update_auth_user_metadata', {
            user_email: email,
            metadata: { is_freelancer: true }
          });

          if (updateError) {
            console.log('Note: User metadata will be set when user signs up');
          }
        }

        // Send invitation email
        const orgName = primaryOrgId && organizations.length > 0
          ? organizations.find(o => o.id === primaryOrgId)?.name || organizationName || 'the organization'
          : organizationName || 'the organization';

        let emailSent = false;
        
        // Use Auth-based email for local development (shows in Inbucket)
        if (isLocalEnvironment()) {
          emailSent = await sendInvitationEmailViaAuth(
            email,
            invitationToken,
            orgName
          );
        } else {
          // Use Edge Function for production
          emailSent = await sendInvitationEmail(
            email,
            orgName,
            invitationToken,
            inviterName
          );
        }
        
        if (emailSent) {
          console.info(`✅ Invitation created and email sent to ${email}`);
          
          // In local development, show toast with direct link
          if (isLocalEnvironment()) {
            const inviteUrl = `${window.location.origin}/invite/${invitationToken}`;
            toast({
              title: "Local testing: Use this direct URL",
              description: (
                <div>
                  <p>For testing the invitation flow, use this direct link:</p>
                  <a 
                    href={inviteUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-500 hover:underline break-all mt-2 inline-block"
                  >
                    {inviteUrl}
                  </a>
                </div>
              ),
              duration: 10000,
            });
          }
        }
      }
      
      toast({
        title: "Invitations sent",
        description: `${emailList.length} user(s) have been invited.`,
      });
      
      // Reset form and close dialog
      setEmails('');
      setMessage('');
      setSelectedRole('brand_member');
      setIsFreelancer(false);
      setSelectedOrganizations(organizationId ? [organizationId] : []);
      setIsOpen(false);
      
      if (onInviteSent) {
        onInviteSent();
      }
    } catch (err: any) {
      console.error('Error inviting members:', err);
      
      toast({
        title: "Error inviting members",
        description: err.message || "An error occurred while sending invitations.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {triggerButton || (
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Members
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isPlatformUser ? 'Create User Account' : `Invite People to ${organizationName}`}
          </DialogTitle>
          <DialogDescription>
            {isPlatformUser 
              ? 'Create new user accounts and assign them to organizations.'
              : 'Add team members by sending them an invitation email.'
            }
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="emails" className="required">Email Addresses</Label>
              <Textarea
                id="emails"
                placeholder="Enter email addresses (one per line, or comma-separated)"
                value={emails}
                onChange={(e) => setEmails(e.target.value)}
                rows={3}
                required
                className="min-h-[80px]"
              />
              <p className="text-xs text-muted-foreground">
                Enter multiple email addresses separated by commas or new lines
              </p>
            </div>

            {isPlatformUser && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="role">User Role</Label>
                  <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as UserRole)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="platform_admin">Platform Admin</SelectItem>
                      <SelectItem value="brand_admin">Brand Admin</SelectItem>
                      <SelectItem value="brand_member">Brand Member</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {selectedRole === 'brand_admin' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="freelancer"
                      checked={isFreelancer}
                      onCheckedChange={(checked) => setIsFreelancer(checked as boolean)}
                    />
                    <Label 
                      htmlFor="freelancer" 
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      This is an external freelancer
                    </Label>
                  </div>
                )}

                {selectedRole !== 'platform_admin' && selectedRole !== 'platform_super' && (
                  <div className="grid gap-2">
                    <Label>Assign to Organizations</Label>
                    <div className="border rounded-md p-3 max-h-[200px] overflow-y-auto space-y-2">
                      {organizations.map((org) => (
                        <div key={org.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={org.id}
                            checked={selectedOrganizations.includes(org.id)}
                            onCheckedChange={() => handleOrganizationToggle(org.id)}
                          />
                          <Label 
                            htmlFor={org.id}
                            className="text-sm font-normal cursor-pointer"
                          >
                            {org.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                    {isFreelancer && (
                      <p className="text-xs text-muted-foreground">
                        Freelancers can be assigned to multiple organizations
                      </p>
                    )}
                  </div>
                )}
              </>
            )}

            {!isPlatformUser && (
              <div className="bg-muted/50 p-3 rounded-md">
                <p className="text-sm text-muted-foreground">
                  <strong>Note:</strong> Invited users will start as Brand Members.
                </p>
              </div>
            )}

            <div className="grid gap-2">
              <Label htmlFor="message">Personal Message (optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a personal message to the invitation email"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Sending...' : 'Send Invitations'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

export default EnhancedInviteMemberForm;