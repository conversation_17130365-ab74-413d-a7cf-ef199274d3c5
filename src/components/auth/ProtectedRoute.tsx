import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useSupabase } from '../../contexts/SupabaseContext';

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, isLoadingUser } = useSupabase();
  const location = useLocation();

  // If authentication is still loading, show nothing or a loading indicator
  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If there are children, render them, otherwise render the outlet
  return <>{children || <Outlet />}</>;
};

export default ProtectedRoute; 