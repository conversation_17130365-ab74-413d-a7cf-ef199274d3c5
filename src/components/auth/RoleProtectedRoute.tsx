import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useUserRole, UserRole } from '../../contexts/UserRoleContext';

interface RoleProtectedRouteProps {
  allowedRoles: UserRole[];
  redirectPath?: string;
  children?: React.ReactNode;
}

// Helper function for common role patterns
export const createRoleGuard = {
  platformOnly: (): UserRole[] => ['platform_super', 'platform_admin'],
  adminOnly: (): UserRole[] => ['platform_super', 'platform_admin', 'brand_admin'],
  brandOnly: (): UserRole[] => ['brand_admin', 'brand_member'],
  externalOnly: (): UserRole[] => ['external_retoucher', 'external_prompter'],
  all: (): UserRole[] => ['platform_super', 'platform_admin', 'brand_admin', 'brand_member', 'external_retoucher', 'external_prompter']
};

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({ 
  allowedRoles, 
  redirectPath = '/dashboard', 
  children 
}) => {
  const { user, isLoadingUser } = useSupabase();
  const { userRole, isLoadingRole } = useUserRole();
  const location = useLocation();

  // Show loading state when checking authentication or role
  if (isLoadingUser || isLoadingRole) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If not in allowed roles, redirect to specified path
  if (!allowedRoles.includes(userRole as UserRole)) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  // If there are children, render them, otherwise render the outlet
  return <>{children || <Outlet />}</>;
};

export default RoleProtectedRoute; 