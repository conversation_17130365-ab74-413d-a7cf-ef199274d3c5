import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { FileText, Image as ImageIcon, Download, Eye, AlertCircle } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { supabase, STORAGE_BUCKETS } from '../common/utils/supabase';
import { useToast } from '../ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog';

// Define Collection interface based on the one from ClientCollectionDetail.tsx
interface ImageOutputTypes {
  fullHeightFront: boolean;
  fullHeightBack: boolean;
  fullHeightSide: boolean;
  halfBody: boolean;
  portrait: boolean;
}

interface ImageOutputSettings {
  types: ImageOutputTypes;
  deadline: string;
  pixelSize: {
    width: number;
    height: number;
  };
  format: 'WebP' | 'JPG' | 'TIFF' | 'PNG' | 'Other';
  customFormat?: string;
  maxResolution: number;
}

interface CollectionMetadata {
  creation_flow?: {
    collection_type?: string;
    model_choice?: string;
    product_count?: number;
    image_count?: string;
    products_per_image?: string;
    campaign_details?: {
      purpose?: string;
      lookAndFeel?: string;
      poses?: string;
      targetGroup?: string;
    };
    imageOutput?: ImageOutputSettings;
  };
  briefingFilePaths?: string[];
}

export interface Collection {
  id: string;
  name: string;
  description: string | null;
  status: string;
  created_at: string | null;
  updated_at: string | null;
  cover_image_url: string | null;
  organization_id: string | null;
  metadata?: CollectionMetadata;
}

interface CollectionBriefDisplayProps {
  collection: Collection | null;
}

export function CollectionBriefDisplay({ collection }: CollectionBriefDisplayProps) {
  const creationFlow = collection?.metadata?.creation_flow;
  const imageOutput = creationFlow?.imageOutput;
  const briefFiles = collection?.metadata?.briefingFilePaths; // Using correct field name
  const { toast } = useToast();
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(new Set());
  const [previewFile, setPreviewFile] = useState<{ name: string; url: string; type: string } | null>(null);

  if (!creationFlow) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          No collection brief details found in metadata.
        </CardContent>
      </Card>
    );
  }

  // Helper to format image type counts
  const renderImageTypeCounts = () => {
      if (!imageOutput?.types) return null;
      const typesWithCounts = Object.entries(imageOutput.types)
          .filter(([_, count]) => count > 0);

      if (typesWithCounts.length === 0) return <p className="text-sm text-muted-foreground">No specific image types requested.</p>;

      return (
          <ul className="list-disc list-inside text-sm space-y-1">
              {typesWithCounts.map(([type, count]) => (
                  <li key={type} className="capitalize">
                      {type.replace(/([A-Z])/g, ' $1').trim()}: {count}
                  </li>
              ))}
          </ul>
      );
  };

  // Helper to get download URL - simplified approach
  const getDownloadUrl = async (filePath: string): Promise<{ success: boolean; url?: string; error?: string }> => {
    try {
      // Try to get signed URL directly - if file doesn't exist, this will fail gracefully
      const { data: urlData, error: urlError } = await supabase.storage
        .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (urlError || !urlData) {
        console.error('Error creating signed URL:', urlError);
        // Fallback to public URL for local development
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .getPublicUrl(filePath);

        if (publicUrlData?.publicUrl) {
          return { success: true, url: publicUrlData.publicUrl };
        }

        return { success: false, error: 'Could not generate download URL' };
      }

      return { success: true, url: urlData.signedUrl };
    } catch (error) {
      console.error('Error getting download URL:', error);
      return { success: false, error: 'Error accessing file' };
    }
  };

  // Handle file download for brief files (uses general-uploads bucket, not affected by FAS-73)
  const handleDownload = async (filePath: string, fileName: string) => {
    setDownloadingFiles(prev => new Set(prev).add(filePath));

    try {
      const result = await getDownloadUrl(filePath);

      if (!result.success || !result.url) {
        toast({
          title: 'Download failed',
          description: result.error || 'File not found or inaccessible',
          variant: 'destructive',
        });
        return;
      }

      // Fetch the file and trigger download
      const response = await fetch(result.url);
      if (!response.ok) {
        throw new Error('Failed to fetch file');
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(downloadUrl);

      toast({
        title: 'Download started',
        description: `Downloading ${fileName}`,
      });

    } catch (error: any) {
      console.error('Download error:', error);
      toast({
        title: 'Download failed',
        description: error.message || 'An error occurred while downloading the file',
        variant: 'destructive',
      });
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(filePath);
        return newSet;
      });
    }
  };

  // Handle file preview
  const handlePreview = async (filePath: string, fileName: string) => {
    try {
      const result = await getDownloadUrl(filePath);

      if (!result.success || !result.url) {
        toast({
          title: 'Preview failed',
          description: result.error || 'File not found or inaccessible',
          variant: 'destructive',
        });
        return;
      }

      const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
      setPreviewFile({
        name: fileName,
        url: result.url,
        type: fileExtension
      });

    } catch (error: any) {
      console.error('Preview error:', error);
      toast({
        title: 'Preview failed',
        description: 'Could not load file preview',
        variant: 'destructive',
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Collection Brief & Settings</CardTitle>
        <CardDescription>Details provided during collection creation.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Display General Info */}
        <div className="space-y-2">
          <h4 className="font-medium text-muted-foreground">Collection Type</h4>
          <p>{creationFlow.collection_type === 'product' ? 'Product Images' : 'Campaign Images'}</p>
        </div>
        <div className="space-y-2">
          <h4 className="font-medium text-muted-foreground">Model Choice</h4>
          <p>{creationFlow.model_choice === 'custom' ? 'Custom Models' : 'Library Models'}</p>
        </div>

        {/* Display Product Specific Info */}
        {creationFlow.collection_type === 'product' && (
          <>
            {creationFlow.product_count && (
              <div className="space-y-2">
                <h4 className="font-medium text-muted-foreground">Product Count</h4>
                <p>{creationFlow.product_count}</p>
              </div>
            )}
            {creationFlow.products_per_image && (
              <div className="space-y-2">
                <h4 className="font-medium text-muted-foreground">Products Per Image</h4>
                <p>{creationFlow.products_per_image}</p>
              </div>
            )}
             <div className="space-y-2">
                <h4 className="font-medium text-muted-foreground">Requested Image Counts</h4>
                {renderImageTypeCounts()}
             </div>
          </>
        )}

        {/* Display Campaign Specific Info */}
        {creationFlow.collection_type === 'campaign' && creationFlow.campaign_details && (
             <div className="pt-4 border-t">
                 <h4 className="font-semibold text-lg mb-3">Campaign Details</h4>
                 <div className="space-y-4 text-sm">
                     {creationFlow.campaign_details.purpose && <div><span className="text-muted-foreground">Purpose:</span> {creationFlow.campaign_details.purpose}</div>}
                     {creationFlow.campaign_details.lookAndFeel && <div><span className="text-muted-foreground">Look & Feel:</span> {creationFlow.campaign_details.lookAndFeel}</div>}
                     {creationFlow.campaign_details.poses && <div><span className="text-muted-foreground">Poses:</span> {creationFlow.campaign_details.poses}</div>}
                     {creationFlow.campaign_details.targetGroup && <div><span className="text-muted-foreground">Target Group:</span> {creationFlow.campaign_details.targetGroup}</div>}
                 </div>
             </div>
         )}

        {/* Display Image Output Settings */}
        {imageOutput && (
          <div className="pt-4 border-t">
            <h4 className="font-semibold text-lg mb-3">Output Settings</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                {imageOutput.deadline && <div><span className="text-muted-foreground">Deadline:</span> {new Date(imageOutput.deadline).toLocaleDateString()}</div>}
                <div><span className="text-muted-foreground">Size:</span> {imageOutput.pixelSize?.width} x {imageOutput.pixelSize?.height}px</div>
                <div><span className="text-muted-foreground">Format:</span> {imageOutput.format === 'Other' ? imageOutput.customFormat : imageOutput.format}</div>
                <div><span className="text-muted-foreground">Max Resolution:</span> {imageOutput.maxResolution} MB</div>
            </div>
          </div>
        )}

        {/* Display Briefing Files (with Download Links and Preview) */}
        {briefFiles && briefFiles.length > 0 && (
          <div className="pt-4 border-t">
            <h4 className="font-semibold text-lg mb-3">Uploaded Briefing Files</h4>
            <div className="space-y-3">
              {briefFiles.map((filePath, index) => {
                const fileName = filePath.split('/').pop() || 'download';
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                const isDownloading = downloadingFiles.has(filePath);
                const isPdf = fileExtension === 'pdf';
                const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension);

                return (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg bg-muted/30">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {isPdf ? (
                          <FileText className="h-5 w-5 text-red-500" />
                        ) : isImage ? (
                          <ImageIcon className="h-5 w-5 text-blue-500" />
                        ) : (
                          <FileText className="h-5 w-5 text-gray-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {fileName}
                        </p>
                        <p className="text-xs text-gray-500 uppercase">
                          {fileExtension} file
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {(isPdf || isImage) && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(filePath, fileName)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownload(filePath, fileName)}
                        disabled={isDownloading}
                        className="h-8 w-8 p-0"
                      >
                        {isDownloading ? (
                          <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                        ) : (
                          <Download className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* File Preview Dialog */}
        <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>{previewFile?.name}</span>
              </DialogTitle>
            </DialogHeader>

            <div className="flex-1 overflow-auto">
              {previewFile && (
                <div className="w-full h-full">
                  {previewFile.type === 'pdf' ? (
                    <iframe
                      src={previewFile.url}
                      className="w-full h-[70vh] border-0"
                      title={`Preview of ${previewFile.name}`}
                    />
                  ) : ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(previewFile.type) ? (
                    <img
                      src={previewFile.url}
                      alt={`Preview of ${previewFile.name}`}
                      className="max-w-full h-auto mx-auto"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-64 text-gray-500">
                      <div className="text-center">
                        <AlertCircle className="h-12 w-12 mx-auto mb-4" />
                        <p>Preview not available for this file type</p>
                        <Button
                          onClick={() => handleDownload(previewFile.url, previewFile.name)}
                          className="mt-4"
                        >
                          Download to view
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}

export default CollectionBriefDisplay; 