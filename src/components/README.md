# Components Architecture

This directory contains all React components for the FashionLab application, organized by domain and functionality.

## 📁 Folder Structure

### **Domain-Specific Components**
These folders contain components specific to particular business domains:

#### `asset-detail/` - Individual Asset Management
Components for viewing and managing single assets:
- `AssetComments.tsx` - Comment system for assets
- `AssetDetailPage/` - Complete asset detail page with tabs
- `AssetDetailTabs.tsx` - Tab navigation for asset views
- `AssetMetadata.tsx` - Asset metadata display
- `AssetRelated.tsx` - Related assets display

#### `asset-management/` - Bulk Asset Operations  
Components for managing multiple assets at once:
- `AssetActions.tsx` - Action buttons for asset operations
- `AssetSelectionHeader.tsx` - Header for asset selection UI
- `AssetStageGroup.tsx` - Grouping assets by workflow stage
- `AssetTagsManager.tsx` - Managing tags on assets
- `BulkTagManager.tsx` - Bulk tagging operations
- `BulkWorkflowStageManager.tsx` - Bulk workflow management
- `EmptyAssetsState.tsx` - Empty state when no assets exist

#### `organizations/` - Brand/Organization Management
Components for managing organizations (brands):
- `InviteMemberForm.tsx` - Form for inviting new members
- `OrganizationMembersTable.tsx` - Table showing organization members
- `PendingInvitationsTable.tsx` - Table showing pending invitations
- `OrganizationOverview.tsx` - Dashboard showing all organizations with metrics

#### `collections/` - Campaign Management
Components for managing collections (campaigns):
- `CollectionBriefDisplay.tsx` - Display collection information

#### `products/` - Product Management
Components for managing products within collections:
- `EnhancedAssetGrid.tsx` - Grid view for product assets
- `ProductSelector.tsx` - Product selection interface

#### `dashboard/` - Dashboard & Analytics
Components for the main dashboard:
- `DashboardStats.tsx` - Statistics cards and metrics
- `RecentActivities.tsx` - Recent activity feed
- `StorageUsageDetails.tsx` - Storage usage information

#### `profile/` - User Profile Management
Components for user account management:
- `AccountSettings.tsx` - User account settings
- `ProfileSection.tsx` - Profile information display
- `SecuritySettings.tsx` - Security and password settings

### **Infrastructure Components**

#### `auth/` - Authentication & Authorization
Components for managing user authentication and permissions:
- `ProtectedRoute.tsx` - Route protection wrapper
- `RoleProtectedRoute.tsx` - Role-based route protection with helper functions

#### `layout/` - Layout & Navigation
Components for application layout and navigation:
- `FilterSidebar.tsx` - Sidebar with filtering options
- `Header.tsx` - Main application header
- `Layout.tsx` - Main layout wrapper
- `navigation-items.ts` - Navigation configuration

#### `ui/` - Shared UI Primitives
Low-level UI components based on shadcn/ui:
- Buttons, inputs, dialogs, tables, etc.
- These are design system components used throughout the app
- `PageTitle.tsx` - Standardized page title component

#### `common/` - Shared Utilities & Components
Centralized shared functionality used across the application:
- `hooks/` - Custom React hooks (useAssets, useOrganizations, etc.)
- `utils/` - Utility functions (cn, getAssetUrl, supabase client, etc.)
- `types/` - TypeScript type definitions (database types, asset types)
- `forms/` - Reusable form components (future expansion area)

## 🎯 Component Design Patterns

### **Role-Based Access Control**
All components use the unified role system with these patterns:

```typescript
// ✅ Correct - Use unified role system
const { isPlatformUser, isBrandAdmin } = useUserRole();

// ❌ Deprecated - Don't use dual role system
const { isAdmin, isSuperAdmin } = useUserRole(); // Old pattern
```

### **Import Patterns**
Use the centralized common folder for shared functionality:

```typescript
// ✅ Correct - Import from common folder
import { useAssets } from '../common/hooks/useAssets';
import { cn } from '../common/utils/utils';
import type { Asset } from '../common/types/database.types';

// ❌ Old pattern - Scattered imports (no longer available)
import { useAssets } from '../../hooks/useAssets'; // Old
import { cn } from '../../lib/utils'; // Old  
import type { Asset } from '../../types/database.types'; // Old
```

### **Permission Helpers**
Use the role guard helpers from `RoleProtectedRoute`:

```typescript
import { createRoleGuard } from '../auth/RoleProtectedRoute';

const { platformOnly, adminOnly, brandOnly } = createRoleGuard();

// Example usage
if (platformOnly()) {
  // Show platform admin features
}
```

### **Component Naming Conventions**
- **PascalCase** for component files (e.g., `AssetDetailTabs.tsx`)
- **Descriptive names** that indicate purpose (e.g., `BulkWorkflowStageManager`)
- **Domain prefixes** when needed (e.g., `AssetActions`, `OrganizationMembersTable`)

### **File Organization**
- **Single responsibility** - One main component per file
- **Complex components** - Use subfolder with `index.tsx` (e.g., `AssetDetailPage/`)
- **Related utilities** - Keep helper functions close to components

## 🔄 Migration & Cleanup Status

### ✅ Recently Completed
- **Role system refactoring** - All components now use unified role system
- **Unused component removal** - Removed 8 unused components (3000+ lines)
- **Permission standardization** - Consistent permission checking patterns
- **Option A restructuring** - Renamed folders for clarity (asset-detail/, asset-management/)
- **Option C implementation** - Consolidated shared utilities into common/ folder

### ✅ Current Structure Benefits
- **Centralized utilities** - All hooks, utils, and types in organized common/ folder
- **Clear domain separation** - Business domains clearly separated from infrastructure
- **Improved import paths** - Consistent, logical import patterns
- **Better scalability** - Easy to add new shared functionality

### 🎯 Future Improvements
Consider these enhancements as the codebase grows:

1. **Form component extraction**:
   - Extract reusable form components to `common/forms/`
   - Standardize form validation patterns

2. **Performance optimizations**:
   - Implement tree shaking for utilities
   - Add dynamic imports for large components

3. **Additional common utilities**:
   - API clients for external services
   - Shared constants and configuration
   - Common validation schemas

## 🚀 Usage Guidelines

### **Adding New Components**
1. **Choose the right domain folder** based on primary functionality
2. **Follow naming conventions** and existing patterns
3. **Use role-based permissions** with the unified system
4. **Add TypeScript types** and proper prop interfaces
5. **Update this README** if adding new categories

### **Refactoring Components**
1. **Check dependencies** before moving or renaming
2. **Update imports** across the codebase
3. **Test role-based functionality** after changes
4. **Follow existing patterns** for consistency

### **Component Dependencies**
Components should follow these dependency rules:
- **UI components** can be used by any domain component
- **Domain components** should not depend on other domain components
- **Layout components** can be used by pages and other components
- **Auth components** can be used anywhere permissions are needed

## 📚 Related Documentation
- `/src/contexts/README.md` - React Context patterns
- `/src/hooks/README.md` - Custom hooks documentation  
- `/docs/architecture/` - Overall architecture documentation
- `CLAUDE.md` - Development setup and patterns