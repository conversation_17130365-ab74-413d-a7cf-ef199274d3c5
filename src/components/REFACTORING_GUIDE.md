# Component Refactoring Guide

This guide provides a systematic approach to refactoring components in the FashionLab codebase based on our established patterns and lessons learned.

## 🎯 Refactoring Principles

1. **Single Responsibility**: Each component should have one clear purpose
2. **Proper State Management**: Use the right tool for the right job (React Query, Context, local state)
3. **Type Safety**: Leverage TypeScript fully - no `any` types
4. **Performance**: Use React optimization patterns appropriately
5. **Accessibility**: Ensure all interactive elements are accessible
6. **Consistency**: Follow established patterns across the codebase

## 📋 Pre-Refactoring Checklist

Before starting any refactoring:

- [ ] Understand the component's current functionality
- [ ] Check for existing tests (run if available)
- [ ] Identify dependencies and usage locations
- [ ] Review related components for patterns
- [ ] Create a feature branch: `git checkout -b refactor/component-name`

## 🔍 Component Analysis Checklist

### 1. **Imports & Dependencies**
- [ ] Remove unused imports
- [ ] Consolidate duplicate imports (e.g., multiple supabase imports)
- [ ] Check for correct import paths
- [ ] Ensure all used dependencies are imported

### 2. **TypeScript & Type Safety**
- [ ] Replace all `any` types with proper types
- [ ] Define interfaces for props and state
- [ ] Use type imports where appropriate: `import type { ... }`
- [ ] Leverage database types from `database.types.ts`

### 3. **State Management**
- [ ] Identify what should be local state vs. context vs. React Query
- [ ] Check for state that should be derived (use `useMemo`)
- [ ] Remove redundant state variables
- [ ] Ensure state updates are immutable

### 4. **Data Fetching**
- [ ] Use custom hooks (`useAssets`, `useCollections`, etc.) instead of direct Supabase calls
- [ ] Implement proper loading states with Skeleton components
- [ ] Handle errors gracefully with user-friendly messages
- [ ] Add proper error boundaries where needed

### 5. **Performance Optimizations**
- [ ] Use `useMemo` for expensive calculations
- [ ] Use `useCallback` for functions passed as props
- [ ] Implement proper React.memo where beneficial
- [ ] Check for unnecessary re-renders

### 6. **Form Handling**
- [ ] For complex forms: Use `react-hook-form` with `zod` validation
- [ ] For simple forms: Basic controlled inputs are acceptable
- [ ] Ensure proper validation and error messages
- [ ] Handle form submission states (loading, success, error)

### 7. **Component Structure**
- [ ] Extract reusable logic to custom hooks
- [ ] Break down large components into smaller ones
- [ ] Keep render logic clean and readable
- [ ] Organize code: types → hooks → helpers → component → exports

### 8. **UI/UX Consistency**
- [ ] Use shadcn/ui components consistently
- [ ] Follow the terminology guide (Brand/Campaign in UI)
- [ ] Implement proper loading skeletons
- [ ] Add appropriate animations/transitions
- [ ] Ensure mobile responsiveness

### 9. **Security & Permissions**
- [ ] Use proper role checks (`isPlatformUser`, `isBrandAdmin`, etc.)
- [ ] Don't expose sensitive operations to unauthorized users
- [ ] Validate permissions both in UI and before operations
- [ ] Use the single role system correctly

### 10. **Error Handling**
- [ ] Wrap async operations in try-catch blocks
- [ ] Show user-friendly error messages via toast
- [ ] Log errors appropriately for debugging
- [ ] Handle edge cases gracefully

## 🛠️ Common Refactoring Patterns

### Pattern 1: Removing Duplicate Functionality
```typescript
// ❌ Bad: Component handles too much
function CollectionList() {
  // Handles listing AND complex editing including image upload
}

// ✅ Good: Separate concerns
function CollectionList() {
  // Only handles listing and basic inline editing
}
function CollectionDetail() {
  // Handles comprehensive editing including images
}
```

### Pattern 2: Using Custom Hooks
```typescript
// ❌ Bad: Direct Supabase queries in component
const { data } = await supabase.from('collections').select('*');

// ✅ Good: Use custom hooks
const { data: collections, isLoading } = useCollections({ organizationId });
```

### Pattern 3: Role-Based Access
```typescript
// ❌ Bad: Checking membership.role (old dual-role system)
const canEdit = membership?.role === 'org_admin';

// ✅ Good: Use UserRoleContext (single role system)
const { isBrandAdmin, isPlatformUser } = useUserRole();
const canEdit = isPlatformUser || (isBrandAdmin && hasMembership);
```

### Pattern 4: Type Safety
```typescript
// ❌ Bad: Using any
} catch (error: any) {
  console.error(error.message);
}

// ✅ Good: Proper typing
} catch (error) {
  const message = error instanceof Error ? error.message : 'An error occurred';
  console.error(message);
}
```

### Pattern 5: Loading States
```typescript
// ❌ Bad: No loading state
if (!data) return null;

// ✅ Good: Proper loading state
if (isLoading) {
  return <Skeleton className="h-32 w-full" />;
}
```

## 📝 Refactoring Process

1. **Analyze** the component using the checklist above
2. **Plan** the changes needed
3. **Refactor** incrementally, testing as you go
4. **Test** thoroughly - both manual and automated tests
5. **Review** the changes for consistency
6. **Commit** with clear message: `refactor: improve CollectionList component structure and type safety`

## 🚀 Component Priority List

Based on complexity and impact, refactor in this order:

### High Priority (Complex/High Impact)
1. `CollectionAssets.tsx` - Complex state management, bulk operations
2. `AssetDetail.tsx` - Large component, complex interactions
3. `OrganizationDetail.tsx` - Multiple concerns, needs splitting
4. `BulkUploadWizard.tsx` - Complex flow, state management

### Medium Priority (Moderate Complexity)
5. `ProductList.tsx` - Data fetching patterns
6. `OrganizationMembers.tsx` - Role management
7. `AssetUpload.tsx` - Form handling, file processing
8. `Dashboard.tsx` - Data aggregation, performance

### Low Priority (Simple/Low Impact)
9. `Profile.tsx` - Simple forms
10. `Organizations.tsx` - Mostly display logic
11. Other utility components

## 🔄 Post-Refactoring

After refactoring each component:

1. **Run linter**: `npm run lint`
2. **Run tests**: `npm test` and `npm run test:e2e`
3. **Manual testing**: Test all functionality
4. **Update tests**: Add/update tests as needed
5. **Update documentation**: If behavior changed
6. **Commit changes**: Follow commit message conventions
7. **Create PR**: If working on feature branch

## 📊 Success Metrics

A successfully refactored component should:
- ✅ Have no TypeScript errors or `any` types
- ✅ Pass all existing tests
- ✅ Follow established patterns consistently
- ✅ Be more maintainable and readable
- ✅ Have better performance (if applicable)
- ✅ Handle errors gracefully
- ✅ Be properly typed and documented

## 🎯 Example: CollectionList Refactoring

Here's what we did with CollectionList as a reference:

1. **Removed duplicate functionality**: Cover image upload moved to CollectionDetail
2. **Cleaned up state**: Removed unused `editCollectionCoverImage` state
3. **Improved UX**: Added helpful text about where to manage cover images
4. **Fixed imports**: Removed unused imports
5. **Maintained simplicity**: Kept basic form validation for simple use case

This resulted in a cleaner, more focused component that follows the single responsibility principle.

## 🎯 Example: OrganizationCollectionCreation Refactoring (FAS-60)

For the large OrganizationCollectionCreation component (~2000 lines), we applied these patterns:

### 1. **Custom Hooks for State Management**
```typescript
// ❌ Bad: All state management in component
const [formData, setFormData] = useState<CollectionFormData>(getInitialFormData());
const handleEnhancedBriefUpload = (section, field, files) => {
  setFormData(prev => ({
    ...prev,
    enhancedBrief: {
      ...prev.enhancedBrief,
      [section]: { ...prev.enhancedBrief[section], [field]: files }
    }
  }));
};

// ✅ Good: Extract to custom hooks
const { formData, updateFormData, updateEnhancedBrief } = useCollectionForm();
const { persistFormData, clearStoredData } = useFormPersistence(formData);
```

### 2. **Component Decomposition**
```typescript
// ❌ Bad: Single massive component
function OrganizationCollectionCreation() {
  // 2000+ lines of mixed concerns
}

// ✅ Good: Split into focused components
function OrganizationCollectionCreation() {
  return (
    <CollectionFormWizard>
      <BasicInfoStep />
      <CollectionTypeStep />
      <ModelsReferencesStep />
      <CollectionDetailsStep />
      <ReviewStep />
    </CollectionFormWizard>
  );
}
```

### 3. **Type-Safe State Updates**
```typescript
// ❌ Bad: Manual nested updates prone to errors
setFormData(prev => ({
  ...prev,
  enhancedBrief: {
    ...prev.enhancedBrief,
    [section]: { ...prev.enhancedBrief[section], [field]: value }
  }
}));

// ✅ Good: Type-safe update utilities
const updateNestedField = <T>(
  path: string[],
  value: T,
  updater: (prev: CollectionFormData) => CollectionFormData
) => {
  // Type-safe nested update logic
};
```

## 📋 Refactoring Implementation for OrganizationCollectionCreation

### **Problem Analysis**
The original component (~2000 lines) suffered from:
- **Multiple responsibilities**: Form state, validation, persistence, navigation, UI rendering
- **Complex nested state**: Deep object updates prone to errors
- **Repetitive patterns**: Similar handlers duplicated across sections
- **Poor maintainability**: Single massive file difficult to test and modify

### **Solution Architecture**

#### **1. Custom Hooks Created**
- `useCollectionForm.ts` - Centralized form state management with type-safe updates
- `useFormPersistence.ts` - localStorage persistence with serialization handling
- `useStepNavigation.ts` - Step validation and navigation logic

#### **2. Component Decomposition**
- `CollectionFormStep.tsx` - Base step wrapper component
- `BasicInfoStep.tsx` - Step 1: Name, description, cover image
- `CollectionTypeStep.tsx` - Step 2: Product vs Campaign selection
- `RefactoredCollectionCreation.tsx` - Main orchestrator component

#### **3. Key Improvements**

**State Management:**
```typescript
// Before: Manual nested updates
const handleEnhancedBriefUpload = (section, field, files) => {
  setFormData(prev => ({
    ...prev,
    enhancedBrief: {
      ...prev.enhancedBrief,
      [section]: { ...prev.enhancedBrief[section], [field]: files }
    }
  }));
};

// After: Type-safe hook methods
const { updateEnhancedBrief } = useCollectionForm();
updateEnhancedBrief('targetGarmentImages', 'files', files);
```

**Persistence:**
```typescript
// Before: Manual localStorage handling scattered throughout
useEffect(() => {
  const formDataToSave = { ...formData };
  if (formDataToSave.coverImage) formDataToSave.coverImage = null;
  localStorage.setItem('collectionFormData', JSON.stringify(formDataToSave));
}, [formData]);

// After: Centralized persistence hook
const { clearStoredData } = useFormPersistence(formData, currentStep, completedSteps);
```

**Navigation:**
```typescript
// Before: Manual step validation and navigation
const nextStep = () => {
  if (validateCurrentStep()) {
    if (!completedSteps.includes(currentStep)) {
      setCompletedSteps(prev => [...prev, currentStep]);
    }
    setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  }
};

// After: Declarative step navigation
const { nextStep } = useStepNavigation({
  totalSteps: 5,
  validationRules: [...],
});
```

### **4. Benefits Achieved**
- ✅ **Reduced complexity**: Main component from 2000+ to ~300 lines
- ✅ **Improved testability**: Each hook and component can be tested independently
- ✅ **Better type safety**: Centralized type-safe update methods
- ✅ **Enhanced reusability**: Hooks can be used in other form components
- ✅ **Easier maintenance**: Clear separation of concerns
- ✅ **Consistent patterns**: Standardized approach to form state management

### **5. Migration Strategy**
1. **Phase 1**: Create custom hooks alongside existing component
2. **Phase 2**: Create step components for each form section
3. **Phase 3**: Build refactored main component using hooks and step components
4. **Phase 4**: Gradually migrate remaining step components
5. **Phase 5**: Replace original component and clean up

### **6. Files Created**
- `src/hooks/useCollectionForm.ts` - Form state management
- `src/hooks/useFormPersistence.ts` - localStorage persistence
- `src/hooks/useStepNavigation.ts` - Step navigation and validation
- `src/components/collection-creation/CollectionFormStep.tsx` - Base step component
- `src/components/collection-creation/BasicInfoStep.tsx` - Step 1 component
- `src/components/collection-creation/CollectionTypeStep.tsx` - Step 2 component
- `src/components/collection-creation/RefactoredCollectionCreation.tsx` - Main refactored component