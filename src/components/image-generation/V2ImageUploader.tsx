import React, { useCallback } from 'react';
import { Upload, X } from 'lucide-react';
import { Label } from '../ui/label';
import { cn } from '../common/utils/utils';

export interface V2ImageData {
  file: File;
  preview: string;
  base64?: string;
}

export interface V2Images {
  face?: V2ImageData;
  image_2?: V2ImageData;
  image_3?: V2ImageData;
  image_4?: V2ImageData;
}

interface V2ImageUploaderProps {
  images: V2Images;
  onImagesChange: (images: V2Images) => void;
  className?: string;
}

export function V2ImageUploader({ images, onImagesChange, className }: V2ImageUploaderProps) {
  const handleImageUpload = useCallback(async (
    type: 'face' | 'image_2' | 'image_3' | 'image_4',
    file: File
  ) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type. Please upload an image.');
      return;
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('File too large. Maximum size is 10MB.');
      return;
    }

    // Create preview URL
    const preview = URL.createObjectURL(file);
    
    // Convert to base64
    const reader = new FileReader();
    reader.onloadend = () => {
      const base64 = reader.result as string;
      onImagesChange({
        ...images,
        [type]: { file, preview, base64 }
      });
    };
    reader.readAsDataURL(file);
  }, [images, onImagesChange]);

  const removeImage = useCallback((type: 'face' | 'image_2' | 'image_3' | 'image_4') => {
    if (images[type]?.preview) {
      URL.revokeObjectURL(images[type]!.preview);
    }
    const newImages = { ...images };
    delete newImages[type];
    onImagesChange(newImages);
  }, [images, onImagesChange]);

  const renderImageSlot = (
    type: 'face' | 'image_2' | 'image_3' | 'image_4',
    label: string
  ) => {
    const image = images[type];
    
    return (
      <div className="space-y-1">
        <Label className="text-xs font-medium">{label}</Label>
        <div className="relative">
          {image ? (
            <div className="relative group">
              <img 
                src={image.preview} 
                alt={`${label} preview`}
                className="w-full h-20 object-cover rounded border"
              />
              <button
                onClick={() => removeImage(type)}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                aria-label={`Remove ${label}`}
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ) : (
            <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleImageUpload(type, file);
                }}
                className="hidden"
              />
              <div className="text-center">
                <Upload className="w-5 h-5 text-gray-400 mx-auto mb-1" />
                <p className="text-xs text-gray-500">{label}</p>
              </div>
            </label>
          )}
        </div>
      </div>
    );
  };

  const isComplete = images.face && images.image_2 && images.image_3 && images.image_4;

  return (
    <div className={cn("space-y-3", className)}>
      <div>
        <Label className="text-sm font-medium mb-2 block">Fashion Lab V2 API Images</Label>
        <p className="text-xs text-muted-foreground mb-3">
          Upload 4 images: face photo + 3 reference images
        </p>
      </div>
      
      <div className="grid grid-cols-2 gap-2">
        {renderImageSlot('face', 'Face')}
        {renderImageSlot('image_2', 'Garment 1')}
        {renderImageSlot('image_3', 'Garment 2')}
        {renderImageSlot('image_4', 'Garment 3')}
      </div>
      
      {isComplete && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
          <p className="text-xs text-green-700">✓ V2 API ready - all 4 images uploaded</p>
        </div>
      )}
    </div>
  );
}