import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Check, Edit, ZoomIn, Save, Copy } from 'lucide-react';
import { GeneratedImage } from './GeneratedImageGrid';

interface ImageDetailModalProps {
  image: GeneratedImage;
  onClose: () => void;
}

export function ImageDetailModal({ image, onClose }: ImageDetailModalProps) {
  const imageUrl = image.storage_path 
    ? `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/ai-generated/${image.storage_path}`
    : image.url;

  const handleCopySeed = () => {
    if (image.seed) {
      navigator.clipboard.writeText(image.seed.toString());
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl p-0">
        <div className="flex">
          {/* Image Preview */}
          <div className="flex-1 bg-gray-100 p-8 flex items-center justify-center">
            <img 
              src={imageUrl} 
              alt="Full view"
              className="max-w-full max-h-[80vh] object-contain"
            />
          </div>
          
          {/* Metadata Panel */}
          <div className="w-80 bg-white p-6 border-l">
            <DialogHeader>
              <DialogTitle>Image Details</DialogTitle>
            </DialogHeader>
            
            <div className="mt-6 space-y-4">
              {/* Image Info */}
              <div className="space-y-2">
                <Badge variant="outline">{image.modelName}</Badge>
                <Badge variant="secondary">{image.angleName}</Badge>
                {image.selected && (
                  <Badge className="bg-green-500">Selected</Badge>
                )}
              </div>
              
              {/* Actions */}
              <div className="grid grid-cols-2 gap-2">
                <Button className="text-xs" size="sm">
                  <Check className="w-3 h-3 mr-1" />
                  Approve
                </Button>
                <Button variant="outline" className="text-xs" size="sm">
                  <Edit className="w-3 h-3 mr-1" />
                  Refine
                </Button>
                <Button variant="outline" className="text-xs" size="sm">
                  <ZoomIn className="w-3 h-3 mr-1" />
                  Upscale
                </Button>
                <Button variant="outline" className="text-xs" size="sm">
                  <Save className="w-3 h-3 mr-1" />
                  Save
                </Button>
              </div>
              
              {/* Metadata */}
              <div className="space-y-3 text-sm">
                <div>
                  <p className="font-medium text-muted-foreground mb-1">Prompt</p>
                  <p className="text-xs bg-gray-50 p-2 rounded overflow-y-auto max-h-24">
                    {image.prompt}
                  </p>
                </div>
                
                {image.seed !== undefined && (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <p className="font-medium text-muted-foreground mb-1">Seed</p>
                      <div className="flex items-center gap-1">
                        <p className="text-xs font-mono">{image.seed}</p>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-5 w-5"
                          onClick={handleCopySeed}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    {image.cfg !== undefined && (
                      <div>
                        <p className="font-medium text-muted-foreground mb-1">CFG Scale</p>
                        <p className="text-xs">{image.cfg}</p>
                      </div>
                    )}
                  </div>
                )}
                
                <div>
                  <p className="font-medium text-muted-foreground mb-1">Generated</p>
                  <p className="text-xs">
                    {new Date(image.timestamp || image.created_at || '').toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}