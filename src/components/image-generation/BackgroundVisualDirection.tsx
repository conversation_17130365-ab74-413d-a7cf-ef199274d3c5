import React, { useCallback, useState } from 'react';
import { Upload, X } from 'lucide-react';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { cn } from '../common/utils/utils';

export interface BackgroundImage {
  id: string;
  file: File;
  preview: string;
  base64?: string;
  name: string;
}

interface BackgroundVisualDirectionProps {
  backgroundImages: BackgroundImage[];
  visualDirection: string;
  onBackgroundImagesChange: (images: BackgroundImage[]) => void;
  onVisualDirectionChange: (direction: string) => void;
  className?: string;
}

export function BackgroundVisualDirection({
  backgroundImages,
  visualDirection,
  onBackgroundImagesChange,
  onVisualDirectionChange,
  className
}: BackgroundVisualDirectionProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleImageUpload = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      console.error('Invalid file type. Please upload an image.');
      return;
    }

    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('File too large. Maximum size is 10MB.');
      return;
    }

    setIsUploading(true);
    
    try {
      const preview = URL.createObjectURL(file);
      
      // Convert to base64
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        const newImage: BackgroundImage = {
          id: `bg-${Date.now()}`,
          file,
          preview,
          base64,
          name: file.name
        };
        
        onBackgroundImagesChange([...backgroundImages, newImage]);
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error uploading image:', error);
      setIsUploading(false);
    }
  }, [backgroundImages, onBackgroundImagesChange]);

  const removeImage = useCallback((id: string) => {
    const imageToRemove = backgroundImages.find(img => img.id === id);
    if (imageToRemove?.preview) {
      URL.revokeObjectURL(imageToRemove.preview);
    }
    onBackgroundImagesChange(backgroundImages.filter(img => img.id !== id));
  }, [backgroundImages, onBackgroundImagesChange]);

  return (
    <div className={cn("space-y-4", className)}>
      <div>
        <Label className="text-sm font-medium mb-2 block">Background & Visual Direction</Label>
        <p className="text-xs text-muted-foreground mb-3">
          Upload background images and describe the visual style you want
        </p>
      </div>

      {/* Background Images */}
      <div>
        <Label className="text-xs font-medium mb-2 block">Background Images</Label>
        <div className="grid grid-cols-3 gap-2">
          {backgroundImages.map((image) => (
            <div
              key={image.id}
              className="border rounded-lg p-2 relative overflow-hidden bg-gray-50"
            >
              <div className="w-full h-16 rounded mb-1 overflow-hidden bg-gray-100">
                <img 
                  src={image.preview} 
                  alt={image.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <p className="text-xs font-medium line-clamp-1 mb-1">{image.name}</p>
              <button
                onClick={() => removeImage(image.id)}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 hover:opacity-100 transition-opacity"
                aria-label={`Remove ${image.name}`}
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ))}
          
          {/* Upload slot */}
          <label className="border border-dashed border-gray-300 rounded-lg p-2 cursor-pointer hover:border-gray-400 transition-colors">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleImageUpload(file);
              }}
              className="hidden"
              disabled={isUploading}
            />
            <div className="w-full h-16 flex items-center justify-center">
              {isUploading ? (
                <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Upload className="w-5 h-5 text-gray-400" />
              )}
            </div>
            <p className="text-xs text-gray-400 text-center">
              {isUploading ? 'Uploading...' : 'Upload'}
            </p>
          </label>
        </div>
      </div>

      {/* Visual Direction */}
      <div>
        <Label className="text-xs font-medium mb-2 block">Visual Direction</Label>
        <Textarea
          value={visualDirection}
          onChange={(e) => onVisualDirectionChange(e.target.value)}
          placeholder="Describe the visual style, mood, lighting, and atmosphere you want for the images..."
          rows={3}
          className="text-sm"
        />
        <p className="text-xs text-muted-foreground mt-1">
          Example: "Bright studio lighting, minimalist aesthetic, clean white background, professional fashion photography style"
        </p>
      </div>
    </div>
  );
}
