import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { CollectionFormStep } from './CollectionFormStep';
import {
  FileText,
  Image as ImageIcon,
  Users,
  Calculator,
  Settings,
  CheckCircle,
  AlertCircle,
  Camera,
  Shirt,
  Eye,
  Palette
} from 'lucide-react';
import type { CollectionFormData } from '../../hooks/useCollectionForm';

interface ReviewStepProps {
  formData: CollectionFormData;
  organizationName?: string;
}

export const ReviewStep: React.FC<ReviewStepProps> = ({
  formData,
  organizationName
}) => {
  const getCollectionTypeName = () => {
    if (formData.collectionType === 'product') return 'Project';
    if (formData.collectionType === 'campaign') return 'Campaign';
    return 'Collection';
  };

  const getModelChoiceName = () => {
    if (formData.modelChoice === 'custom') return 'Custom Models';
    if (formData.modelChoice === 'library') return 'Library Models';
    return 'Not Selected';
  };

  // Enhanced validation - make target garments optional for now
  const targetGarmentCount = formData.enhancedBrief?.targetGarmentImages?.files?.length || 0;
  const hasMinimumTargetGarments = targetGarmentCount >= 5;

  const isComplete = formData.name &&
                    formData.description &&
                    formData.collectionType &&
                    formData.modelChoice;
                    // Removed hasMinimumTargetGarments requirement

  // File preview component
  const FilePreviewGrid: React.FC<{ files: File[], title: string, maxPreview?: number }> = ({
    files,
    title,
    maxPreview = 6
  }) => {
    if (!files || files.length === 0) return null;

    return (
      <div className="space-y-2">
        <p className="text-sm font-medium text-muted-foreground">{title}</p>
        <div className="grid grid-cols-3 sm:grid-cols-6 gap-2">
          {files.slice(0, maxPreview).map((file, index) => (
            <div key={index} className="relative aspect-square">
              {file.type?.startsWith('image/') ? (
                <img
                  src={URL.createObjectURL(file)}
                  alt={file.name}
                  className="w-full h-full object-cover rounded border"
                />
              ) : (
                <div className="w-full h-full bg-gray-100 rounded border flex items-center justify-center">
                  <FileText className="h-4 w-4 text-gray-400" />
                </div>
              )}
            </div>
          ))}
          {files.length > maxPreview && (
            <div className="aspect-square bg-gray-100 rounded border flex items-center justify-center">
              <span className="text-xs text-gray-500">+{files.length - maxPreview}</span>
            </div>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{files.length} file{files.length !== 1 ? 's' : ''} uploaded</p>
      </div>
    );
  };

  return (
    <CollectionFormStep
      title={`Review Your ${getCollectionTypeName()}`}
      description={`Review all the details before creating your ${formData.collectionType || 'collection'}`}
    >
      <div className="space-y-6">
        {/* Completion Status */}
        <Card className={isComplete ? 'border-green-200 bg-green-50' : 'border-orange-200 bg-orange-50'}>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              {isComplete ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertCircle className="h-5 w-5 text-orange-600" />
              )}
              <div>
                <p className={`font-medium ${isComplete ? 'text-green-900' : 'text-orange-900'}`}>
                  {isComplete ? 'Ready to Create' : 'Missing Required Information'}
                </p>
                <p className={`text-sm ${isComplete ? 'text-green-700' : 'text-orange-700'}`}>
                  {isComplete 
                    ? `Your ${formData.collectionType || 'collection'} is ready to be created.`
                    : 'Please complete all required fields before proceeding.'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Basic Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Name</p>
                <p className="text-base">{formData.name || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Type</p>
                <Badge variant={formData.collectionType ? 'default' : 'secondary'}>
                  {getCollectionTypeName()}
                </Badge>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-muted-foreground">Description</p>
              <p className="text-base">{formData.description || 'Not provided'}</p>
            </div>

            {organizationName && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Organization</p>
                <p className="text-base">{organizationName}</p>
              </div>
            )}

            {formData.coverImage && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Cover Image</p>
                <p className="text-base">{formData.coverImage.name}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Model Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Model Selection</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Model Choice</p>
                <Badge variant={formData.modelChoice ? 'default' : 'secondary'}>
                  {getModelChoiceName()}
                </Badge>
              </div>

              {formData.briefFiles && formData.briefFiles.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Brief Files</p>
                  <div className="space-y-1">
                    {formData.briefFiles.map((file, index) => (
                      <p key={index} className="text-sm text-muted-foreground">
                        • {file.name}
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* AI Images Calculation */}
        {(formData.productCount || formData.aiImagesPerProduct || formData.totalAiImages) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>AI Image Calculation</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {formData.productCount && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Products</p>
                    <p className="text-2xl font-bold text-primary">{formData.productCount}</p>
                  </div>
                )}
                
                {formData.aiImagesPerProduct && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">AI Images per Product</p>
                    <p className="text-2xl font-bold text-primary">{formData.aiImagesPerProduct}</p>
                  </div>
                )}
                
                {formData.totalAiImages && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total AI Images</p>
                    <p className="text-2xl font-bold text-green-600">{formData.totalAiImages}</p>
                  </div>
                )}
              </div>

              {formData.totalAiImages && (
                <div className="mt-4 p-3 bg-primary/10 rounded-lg">
                  <p className="text-sm text-primary font-medium">
                    Calculation: {formData.productCount} × {formData.aiImagesPerProduct} = {formData.totalAiImages} total AI images
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Campaign Details */}
        {formData.collectionType === 'campaign' && formData.campaignDetails && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>Campaign Details</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {formData.campaignDetails.purpose && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Purpose</p>
                    <p className="text-sm">{formData.campaignDetails.purpose}</p>
                  </div>
                )}
                
                {formData.campaignDetails.lookAndFeel && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Look & Feel</p>
                    <p className="text-sm">{formData.campaignDetails.lookAndFeel}</p>
                  </div>
                )}
                
                {formData.campaignDetails.poses && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Poses</p>
                    <p className="text-sm">{formData.campaignDetails.poses}</p>
                  </div>
                )}
                
                {formData.campaignDetails.targetGroup && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Target Group</p>
                    <p className="text-sm">{formData.campaignDetails.targetGroup}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Image Output Settings */}
        {formData.imageOutput && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Image Output Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {formData.imageOutput.deadline && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Deadline</p>
                    <p className="text-base">{formData.imageOutput.deadline}</p>
                  </div>
                )}
                
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Format</p>
                  <p className="text-base">{formData.imageOutput.format}</p>
                </div>
                
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Max Resolution</p>
                  <p className="text-base">{formData.imageOutput.maxResolution} MP</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Brief Visual Review */}
        {formData.enhancedBrief && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-5 w-5" />
              <span>Enhanced Brief Visual Review</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">

              {/* Target Garment Images - Required Section */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Shirt className="h-5 w-5 text-primary" />
                  <h4 className="font-medium">Target Garment Images</h4>
                  {targetGarmentCount > 0 && (
                    <Badge variant="default">
                      {targetGarmentCount} uploaded
                    </Badge>
                  )}
                </div>
                {formData.enhancedBrief?.targetGarmentImages?.files?.length ? (
                  <FilePreviewGrid
                    files={formData.enhancedBrief.targetGarmentImages.files}
                    title="Main product images for AI generation"
                    maxPreview={8}
                  />
                ) : (
                  <div className="p-4 border-2 border-dashed border-red-300 rounded-lg text-center">
                    <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                    <p className="text-red-600 font-medium">No target garment images uploaded</p>
                    <p className="text-sm text-red-500">Minimum 5 images required to proceed</p>
                  </div>
                )}
              </div>

              {/* Secondary Elements */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Secondary Garment */}
                {formData.enhancedBrief?.secondaryGarment?.files?.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Shirt className="h-4 w-4 text-muted-foreground" />
                      <h5 className="font-medium">Secondary Garment</h5>
                      <Badge variant="outline">{formData.enhancedBrief.secondaryGarment.files.length} files</Badge>
                    </div>
                    <FilePreviewGrid
                      files={formData.enhancedBrief.secondaryGarment.files}
                      title="Complementary pieces"
                      maxPreview={4}
                    />
                    {formData.enhancedBrief.secondaryGarment.description && (
                      <p className="text-sm text-muted-foreground italic">
                        "{formData.enhancedBrief.secondaryGarment.description}"
                      </p>
                    )}
                  </div>
                )}

                {/* Shoes */}
                {formData.enhancedBrief?.shoes?.files?.length > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <ImageIcon className="h-4 w-4 text-muted-foreground" />
                      <h5 className="font-medium">Shoes</h5>
                      <Badge variant="outline">{formData.enhancedBrief.shoes.files.length} files</Badge>
                    </div>
                    <FilePreviewGrid
                      files={formData.enhancedBrief.shoes.files}
                      title="Footwear styling"
                      maxPreview={4}
                    />
                  </div>
                )}
              </div>

              {/* Styling & Creative Direction */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center space-x-2">
                  <Palette className="h-5 w-5 text-primary" />
                  <span>Creative Direction</span>
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Styling Details */}
                  {formData.enhancedBrief?.stylingDetails?.files?.length > 0 && (
                    <div className="space-y-3">
                      <h5 className="font-medium">Styling Details</h5>
                      <FilePreviewGrid
                        files={formData.enhancedBrief.stylingDetails.files}
                        title="Styling references"
                        maxPreview={4}
                      />
                      {formData.enhancedBrief.stylingDetails.notes && (
                        <p className="text-sm text-muted-foreground italic">
                          "{formData.enhancedBrief.stylingDetails.notes}"
                        </p>
                      )}
                    </div>
                  )}

                  {/* Photoshoot Style */}
                  {formData.enhancedBrief?.photoshootStyle?.files?.length > 0 && (
                    <div className="space-y-3">
                      <h5 className="font-medium">Photoshoot Style</h5>
                      <FilePreviewGrid
                        files={formData.enhancedBrief.photoshootStyle.files}
                        title="Style references"
                        maxPreview={4}
                      />
                      <div className="text-sm space-y-1">
                        {formData.enhancedBrief.photoshootStyle.background && (
                          <p><strong>Background:</strong> {formData.enhancedBrief.photoshootStyle.background}</p>
                        )}
                        {formData.enhancedBrief.photoshootStyle.lighting && (
                          <p><strong>Lighting:</strong> {formData.enhancedBrief.photoshootStyle.lighting}</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Model Profile */}
              {(formData.enhancedBrief?.modelProfile?.faceExamples?.length > 0 ||
                formData.enhancedBrief?.modelProfile?.bodyExamples?.length > 0) && (
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center space-x-2">
                    <Users className="h-5 w-5 text-primary" />
                    <span>Model Profile</span>
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {formData.enhancedBrief.modelProfile.faceExamples?.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium">Face Examples</h5>
                        <FilePreviewGrid
                          files={formData.enhancedBrief.modelProfile.faceExamples}
                          title="Facial features reference"
                          maxPreview={4}
                        />
                      </div>
                    )}

                    {formData.enhancedBrief.modelProfile.bodyExamples?.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium">Body Examples</h5>
                        <FilePreviewGrid
                          files={formData.enhancedBrief.modelProfile.bodyExamples}
                          title="Body type reference"
                          maxPreview={4}
                        />
                      </div>
                    )}
                  </div>

                  {(formData.enhancedBrief.modelProfile.age || formData.enhancedBrief.modelProfile.appearance) && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <h6 className="font-medium mb-2">Model Description</h6>
                      {formData.enhancedBrief.modelProfile.age && (
                        <p className="text-sm"><strong>Age:</strong> {formData.enhancedBrief.modelProfile.age}</p>
                      )}
                      {formData.enhancedBrief.modelProfile.appearance && (
                        <p className="text-sm"><strong>Appearance:</strong> {formData.enhancedBrief.modelProfile.appearance}</p>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Technical Specifications */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-primary" />
                  <span>Technical Specifications</span>
                </h4>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Required Angles */}
                  {formData.enhancedBrief?.requiredAngles?.selectedAngles?.length > 0 && (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h6 className="font-medium mb-2">Required Angles</h6>
                      <div className="flex flex-wrap gap-1">
                        {formData.enhancedBrief.requiredAngles.selectedAngles.map((angle, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {angle}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Format Selection */}
                  {(formData.enhancedBrief?.formatSelection?.portrait ||
                    formData.enhancedBrief?.formatSelection?.landscape) && (
                    <div className="p-3 bg-green-50 rounded-lg">
                      <h6 className="font-medium mb-2">Format Selection</h6>
                      <div className="space-y-1">
                        {formData.enhancedBrief.formatSelection.portrait && (
                          <Badge variant="outline">Portrait</Badge>
                        )}
                        {formData.enhancedBrief.formatSelection.landscape && (
                          <Badge variant="outline">Landscape</Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* AI Images Calculation */}
                  {formData.totalAiImages && (
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <h6 className="font-medium mb-2">AI Generation</h6>
                      <p className="text-2xl font-bold text-purple-600">{formData.totalAiImages}</p>
                      <p className="text-xs text-purple-700">Total AI Images</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Campaign-Specific Visual Elements */}
              {formData.collectionType === 'campaign' && (
                <div className="space-y-4">
                  <h4 className="font-medium flex items-center space-x-2">
                    <Camera className="h-5 w-5 text-primary" />
                    <span>Campaign-Specific Elements</span>
                    <Badge variant="outline">Campaign Only</Badge>
                  </h4>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Setting References */}
                    {formData.enhancedBrief?.settingReferences?.files?.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium">Setting References</h5>
                        <FilePreviewGrid
                          files={formData.enhancedBrief.settingReferences.files}
                          title="Background environments"
                          maxPreview={4}
                        />
                      </div>
                    )}

                    {/* Photoshoot Inspiration */}
                    {formData.enhancedBrief?.photoshootInspiration?.files?.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium">Photoshoot Inspiration</h5>
                        <FilePreviewGrid
                          files={formData.enhancedBrief.photoshootInspiration.files}
                          title="Mood and style inspiration"
                          maxPreview={4}
                        />
                      </div>
                    )}

                    {/* Video References */}
                    {formData.enhancedBrief?.videoUploads?.length > 0 && (
                      <div className="space-y-3">
                        <h5 className="font-medium">Video References</h5>
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-muted-foreground">Movement and pose videos</p>
                          <div className="grid grid-cols-2 gap-2">
                            {formData.enhancedBrief.videoUploads.slice(0, 4).map((file, index) => (
                              <div key={index} className="aspect-video bg-gray-100 rounded border flex items-center justify-center">
                                <div className="text-center">
                                  <Camera className="h-6 w-6 text-gray-400 mx-auto mb-1" />
                                  <p className="text-xs text-gray-500 truncate px-1">{file.name}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                          <p className="text-xs text-muted-foreground">{formData.enhancedBrief.videoUploads.length} video{formData.enhancedBrief.videoUploads.length !== 1 ? 's' : ''} uploaded</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Campaign Requirements Notice */}
                  <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h6 className="font-medium text-blue-900 mb-2">Campaign Image Requirements</h6>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p><strong>Minimum 10 images per "look":</strong></p>
                      <ul className="list-disc list-inside ml-4 space-y-0.5">
                        <li>Full-height: front, back, right profile, left profile</li>
                        <li>Half-body: front, back, right side, left side</li>
                        <li>2-4 close-ups (textures, zippers, buttons, logos)</li>
                      </ul>
                      <p className="mt-2"><strong>Quality:</strong> Garments must be on body or mannequin (iPhone or professional shots)</p>
                    </div>
                  </div>
                </div>
              )}

            </div>
          </CardContent>
        </Card>
        )}

        <Separator />

        {/* Final Summary */}
        <div className="text-center space-y-2">
          <p className="text-lg font-medium">
            Ready to create your {formData.collectionType || 'collection'}?
          </p>
          <p className="text-sm text-muted-foreground">
            Click "Create {getCollectionTypeName()}" to proceed with the setup.
          </p>
        </div>
      </div>
    </CollectionFormStep>
  );
};
