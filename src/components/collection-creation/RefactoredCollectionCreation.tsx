import React, { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, Check } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { StepIndicator } from '../ui/step-indicator';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { useOrganizations } from '../../contexts/OrganizationContext';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../ui/use-toast';
import { STORAGE_BUCKETS, uploadFile, sanitizeFileName } from '../common/utils/supabase';
import { supabase } from '../../integrations/supabase/client';

// Import our custom hooks
import { useCollectionForm } from '../../hooks/useCollectionForm';
import { useFormPersistence } from '../../hooks/useFormPersistence';
import { useStepNavigation } from '../../hooks/useStepNavigation';

// Import step components
import { BasicInfoStep } from './BasicInfoStep';
import { CollectionTypeStep } from './CollectionTypeStep';
import { ModelsReferencesStep } from './ModelsReferencesStep';
import { CollectionDetailsStep } from './CollectionDetailsStep';
import { ReviewStep } from './ReviewStep';

// Import types
import type { CollectionFormData } from '../../hooks/useCollectionForm';

interface RefactoredCollectionCreationProps {
  className?: string;
}

export const RefactoredCollectionCreation: React.FC<RefactoredCollectionCreationProps> = ({
  className = ''
}) => {
  const { orgId, clientId } = useParams<{ orgId?: string; clientId?: string }>();
  const { currentOrganization, switchOrganization } = useOrganizations();
  const { user } = useSupabase();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Initialize form state with persistence
  const persistence = useFormPersistence(
    {} as CollectionFormData, // Will be initialized properly
    1,
    [],
    { enabled: true }
  );

  // Load initial data from localStorage
  const initialFormData = persistence.loadFormData();
  const initialStep = persistence.loadStep() || 1;
  const initialCompletedSteps = persistence.loadCompletedSteps();

  // Initialize form management
  const {
    formData,
    updateFormData,
    updateEnhancedBrief,
    updateImageOutput,
    updateCampaignDetails,
    updateReferences,
    resetForm
  } = useCollectionForm(initialFormData || undefined);

  // Initialize step navigation
  const {
    currentStep,
    completedSteps,
    nextStep,
    prevStep,
    goToStep,
    isFirstStep,
    isLastStep,
    progress
  } = useStepNavigation({
    totalSteps: 5,
    initialStep,
    initialCompletedSteps
  });

  // Initialize persistence with actual data
  const {
    clearAll: clearStoredData
  } = useFormPersistence(formData, currentStep, completedSteps);

  // Step configuration
  const steps = [
    { title: 'Basic Info' },
    { title: 'Collection Type' },
    { title: 'Models & References' },
    { title: 'Details' },
    { title: 'Review' }
  ];

  // Navigation state
  const [showExitDialog, setShowExitDialog] = React.useState(false);
  const [exitPath, setExitPath] = React.useState("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Upload progress state
  const [uploadProgress, setUploadProgress] = React.useState<{[key: string]: number}>({});
  const [isUploading, setIsUploading] = React.useState(false);

  // Sync organization context with URL
  useEffect(() => {
    if (orgId && (!currentOrganization || currentOrganization.id !== orgId)) {
      console.log(`RefactoredCollectionCreation: Setting context to match URL orgId ${orgId}`);
      switchOrganization(orgId);
    }
  }, [orgId, currentOrganization, switchOrganization]);

  // Step content renderer
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <BasicInfoStep
            formData={formData}
            onUpdate={updateFormData}
            organizationName={currentOrganization?.name}
          />
        );
      case 2:
        return (
          <CollectionTypeStep
            formData={formData}
            onUpdate={updateFormData}
          />
        );
      case 3:
        return (
          <ModelsReferencesStep
            formData={formData}
            onUpdate={updateFormData}
          />
        );
      case 4:
        return (
          <CollectionDetailsStep
            formData={formData}
            onUpdate={updateFormData}
            onUpdateEnhancedBrief={(section: any, field: string, value: any) => updateEnhancedBrief(section, field, value)}
            onUpdateImageOutput={(field: any, value: any, subField?: any) => updateImageOutput(field, value, subField)}
            onUpdateCampaignDetails={(field: any, value: string) => updateCampaignDetails(field, value)}
          />
        );
      case 5:
        return (
          <ReviewStep
            formData={formData}
            organizationName={currentOrganization?.name}
          />
        );
      default:
        return null;
    }
  };

  // Get step title and description
  const getStepTitle = () => {
    const type = formData.collectionType;
    const baseTitle = steps[currentStep - 1]?.title || 'Step';
    
    if (currentStep === 1) {
      if (type === 'product') return 'Project Information';
      if (type === 'campaign') return 'Campaign Information';
      return 'Collection Information';
    }
    
    return baseTitle;
  };

  const getStepDescription = () => {
    const type = formData.collectionType;
    const typeName = type === 'product' ? 'project' : type === 'campaign' ? 'campaign' : 'collection';
    
    switch (currentStep) {
      case 1:
        return `Enter the basic information for your ${typeName}`;
      case 2:
        return 'Choose the type of collection you want to create';
      case 3:
        return 'Select your model preferences and upload reference materials';
      case 4:
        return 'Configure the details and requirements for your collection';
      case 5:
        return 'Review your collection settings before creating';
      default:
        return '';
    }
  };

  // Handle navigation
  const handleNext = () => {
    nextStep(formData);
  };

  const handlePrevious = () => {
    prevStep();
  };

  const handleStepClick = (stepIndex: number) => {
    goToStep(stepIndex, formData);
  };

  // Handle back button
  const handleBack = () => {
    const isFormEmpty = formData.name === '' && 
                       formData.description === '' && 
                       formData.collectionType === '';
    
    if (currentStep > 1 && !isFormEmpty) {
      setShowExitDialog(true);
      setExitPath(`/organization/${orgId}/collections`);
    } else {
      if (orgId) {
        navigate(`/organizations/${orgId}/collections`);
      } else if (clientId) {
        navigate(`/organizations/${clientId}`);
      }
    }
  };

  // Confirm exit
  const confirmExit = () => {
    clearStoredData();
    navigate(exitPath);
  };

  // Upload files helper function
  const uploadFiles = async (files: File[], section: string, bucket: string, collectionId?: string) => {
    if (!user) throw new Error('User not authenticated');

    const uploadedFiles = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const progressKey = `${section}-${i}`;

      try {
        // Generate unique file path
        const timestamp = Date.now();
        const sanitizedName = sanitizeFileName(file.name);
        
        // Use collection ID if provided, otherwise use organization ID
        const pathPrefix = collectionId 
          ? `collections/${collectionId}/${section}`
          : `organizations/${orgId || clientId}/${section}`;
        
        const fileName = `${pathPrefix}/${timestamp}-${sanitizedName}`;

        // Upload file with progress tracking
        const result = await uploadFile(
          bucket,
          fileName,
          file,
          (progress) => {
            setUploadProgress(prev => ({
              ...prev,
              [progressKey]: progress
            }));
          }
        );

        if (result) {
          uploadedFiles.push({
            originalName: file.name,
            fileName: result.sanitizedPath,
            publicUrl: result.publicUrl,
            size: file.size,
            type: file.type
          });
        }
      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error);
        throw new Error(`Failed to upload ${file.name}`);
      }
    }

    return uploadedFiles;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!user) return;

    try {
      setIsSubmitting(true);
      
      // Ensure we have an organization ID
      const organizationId = orgId || clientId;
      if (!organizationId) {
        throw new Error('No organization ID found. Please ensure you are accessing this page from a valid organization context.');
      }

      // First, create the collection in database
      let coverImageUrl = null;

      // Upload cover image to general uploads if selected
      if (formData.coverImage) {
        const filePath = `collections/covers/${Date.now()}-${sanitizeFileName(formData.coverImage.name)}`;

        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .upload(filePath, formData.coverImage);

        if (uploadError) throw uploadError;

        const { data: urlData } = supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .getPublicUrl(filePath);

        coverImageUrl = urlData.publicUrl;
      }

      // Create collection in database with metadata
      const collectionData = {
        name: formData.name,
        description: formData.description,
        cover_image_url: coverImageUrl,
        status: 'active',
        organization_id: organizationId,
        metadata: {
          creation_flow: {
            collection_type: formData.collectionType,
            model_choice: formData.modelChoice,
            product_count: formData.productCount,
            ai_images_per_product: formData.aiImagesPerProduct,
            total_ai_images: formData.totalAiImages,
            campaign_details: formData.campaignDetails,
            imageOutput: formData.imageOutput,
          }
        }
      };

      const { data: collection, error } = await supabase
        .from('collections')
        .insert(collectionData as any)
        .select('id')
        .single();

      if (error) throw error;
      if (!collection) throw new Error('Failed to create collection');

      // Now upload enhanced brief files with real collection ID
      setIsUploading(true);
      setUploadProgress({});

      const uploadedData: any = {};

      // Upload enhanced brief files
      if (formData.enhancedBrief) {
        const brief = formData.enhancedBrief;

        // Target Garment Images (required)
        if (brief.targetGarmentImages?.files?.length) {
          uploadedData.targetGarmentImages = await uploadFiles(
            brief.targetGarmentImages.files,
            'target-garment',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Secondary Garment
        if (brief.secondaryGarment?.files?.length) {
          uploadedData.secondaryGarment = await uploadFiles(
            brief.secondaryGarment.files,
            'secondary-garment',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Shoes
        if (brief.shoes?.files?.length) {
          uploadedData.shoes = await uploadFiles(
            brief.shoes.files,
            'shoes',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Styling Details
        if (brief.stylingDetails?.files?.length) {
          uploadedData.stylingDetails = await uploadFiles(
            brief.stylingDetails.files,
            'styling-details',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Photoshoot Style
        if (brief.photoshootStyle?.files?.length) {
          uploadedData.photoshootStyle = await uploadFiles(
            brief.photoshootStyle.files,
            'photoshoot-style',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Model Profile
        if (brief.modelProfile?.faceExamples?.length) {
          uploadedData.modelProfileFace = await uploadFiles(
            brief.modelProfile.faceExamples,
            'model-face',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        if (brief.modelProfile?.bodyExamples?.length) {
          uploadedData.modelProfileBody = await uploadFiles(
            brief.modelProfile.bodyExamples,
            'model-body',
            STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
            collection.id
          );
        }

        // Campaign-specific uploads
        if (formData.collectionType === 'campaign') {
          if (brief.settingReferences?.files?.length) {
            uploadedData.settingReferences = await uploadFiles(
              brief.settingReferences.files,
              'setting-references',
              STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
              collection.id
            );
          }

          if (brief.photoshootInspiration?.files?.length) {
            uploadedData.photoshootInspiration = await uploadFiles(
              brief.photoshootInspiration.files,
              'photoshoot-inspiration',
              STORAGE_BUCKETS.MEDIA_ORIGINALS.name,
              collection.id
            );
          }

          if (brief.videoUploads?.length) {
            uploadedData.videoUploads = await uploadFiles(
              brief.videoUploads,
              'video-uploads',
              STORAGE_BUCKETS.GENERAL_UPLOADS.name,
              collection.id
            );
          }
        }
      }

      // Upload brief files if present
      if (formData.briefFiles?.length) {
        uploadedData.briefFiles = await uploadFiles(
          formData.briefFiles,
          'brief-files',
          STORAGE_BUCKETS.GENERAL_UPLOADS.name,
          collection.id
        );
      }

      // Update collection metadata with uploaded file URLs
      if (Object.keys(uploadedData).length > 0) {
        const { error: updateError } = await supabase
          .from('collections')
          .update({ 
            metadata: { 
              ...collectionData.metadata,
              uploadedFiles: uploadedData 
            } 
          } as any)
          .eq('id', collection.id);
          
        if (updateError) console.error('Error updating collection with uploaded files:', updateError);
      }

      toast({
        title: "Success",
        description: `${formData.collectionType === 'product' ? 'Project' : 'Campaign'} created successfully!`,
      });

      clearStoredData();

      // Navigate to the new collection
      if (orgId) {
        navigate(`/organizations/${orgId}/collections/${collection.id}`);
      } else if (clientId) {
        navigate(`/organizations/${clientId}/collections/${collection.id}`);
      }

    } catch (error) {
      console.error('Submission error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create collection. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
      setIsUploading(false);
      setUploadProgress({});
    }
  };

  return (
    <div className={`container mx-auto py-6 space-y-6 ${className}`}>
      {/* Back button */}
      <Button variant="ghost" className="p-0" onClick={handleBack}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to {formData.collectionType === 'product' ? 'Projects' : formData.collectionType === 'campaign' ? 'Campaigns' : 'Collections'}
      </Button>

      {/* Upload Progress Indicator */}
      {isUploading && (
        <Card className="w-full">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Uploading files...</span>
                <span className="text-sm text-muted-foreground">
                  {Object.keys(uploadProgress).length} files in progress
                </span>
              </div>
              <Progress value={
                Object.keys(uploadProgress).length > 0
                  ? Object.values(uploadProgress).reduce((sum, progress) => sum + progress, 0) / Object.keys(uploadProgress).length
                  : 0
              } className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{getStepTitle()}</CardTitle>
              <CardDescription className="mt-1">
                {getStepDescription()}
                {currentOrganization && (
                  <span className="block mt-1">
                    Creating {formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'} for {currentOrganization.name}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="text-sm font-medium">
              Step {currentStep} of {steps.length}
            </div>
          </div>
          <StepIndicator 
            steps={steps} 
            currentStep={currentStep} 
            className="mt-6 mb-2"
            onStepClick={handleStepClick}
          />
          <Progress value={progress} className="h-2 mt-4" />
        </CardHeader>
        
        <CardContent>
          {renderStepContent()}
        </CardContent>
        
        <CardFooter className="flex flex-col sm:flex-row sm:justify-between gap-4 pt-2">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={isFirstStep || isSubmitting}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            Previous
          </Button>
          
          {!isLastStep ? (
            <Button 
              onClick={handleNext}
              disabled={isSubmitting}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {isUploading ? 'Uploading files...' : isSubmitting ? 'Creating...' : `Create ${formData.collectionType === 'product' ? 'Project' : formData.collectionType === 'campaign' ? 'Campaign' : 'Collection'}`}
              <Check className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
      
      {/* Navigation confirmation dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to leave?</AlertDialogTitle>
            <AlertDialogDescription>
              Your {formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'} information hasn't been saved. If you leave now, your progress will be lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Stay on Page</AlertDialogCancel>
            <AlertDialogAction onClick={confirmExit}>Leave Anyway</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};
