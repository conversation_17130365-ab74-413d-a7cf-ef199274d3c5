import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Input } from '../ui/input';
import { CollectionFormStep } from './CollectionFormStep';
import { Users, Library, Info } from 'lucide-react';
import type { CollectionFormData, ModelChoice } from '../../hooks/useCollectionForm';

interface ModelsReferencesStepProps {
  formData: CollectionFormData;
  onUpdate: (updates: Partial<CollectionFormData>) => void;
}

export const ModelsReferencesStep: React.FC<ModelsReferencesStepProps> = ({
  formData,
  onUpdate
}) => {
  const handleModelChoiceChange = (value: ModelChoice) => {
    onUpdate({ modelChoice: value });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      onUpdate({ briefFiles: files });
    }
  };

  const handleModelCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const count = parseInt(e.target.value) || 0;
    onUpdate({ 
      modelCount: count,
      modelNames: Array(count).fill('') 
    });
  };

  const handleModelNameChange = (index: number, name: string) => {
    const newModelNames = [...(formData.modelNames || [])];
    newModelNames[index] = name;
    onUpdate({ modelNames: newModelNames });
  };

  return (
    <CollectionFormStep
      title="Models & References"
      description="Choose your model preferences and upload reference materials"
    >
      <div className="space-y-6">
        {/* Model Choice Section */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Model Selection</h3>
          <RadioGroup
            value={formData.modelChoice}
            onValueChange={handleModelChoiceChange}
            className="grid gap-4"
          >
            <Card className={`cursor-pointer transition-colors ${
              formData.modelChoice === 'custom' 
                ? 'ring-2 ring-primary bg-primary/5' 
                : 'hover:bg-muted/50'
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <RadioGroupItem value="custom" id="custom" className="mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Users className="h-5 w-5 text-primary" />
                      <Label htmlFor="custom" className="text-base font-medium cursor-pointer">
                        Create Custom Models
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Define specific model characteristics and upload reference images
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={`cursor-pointer transition-colors ${
              formData.modelChoice === 'library' 
                ? 'ring-2 ring-primary bg-primary/5' 
                : 'hover:bg-muted/50'
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <RadioGroupItem value="library" id="library" className="mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Library className="h-5 w-5 text-primary" />
                      <Label htmlFor="library" className="text-base font-medium cursor-pointer">
                        Choose Models from Library
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Library is added to menu when onboarding is finished
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </RadioGroup>
        </div>

        {/* Custom Model Details - Show only if custom is selected */}
        {formData.modelChoice === 'custom' && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">Custom Model Creation</h3>
              <p className="text-sm text-muted-foreground">
                Specify how many custom models you need and provide names for each
              </p>
            </div>
            
            {/* Model Count */}
            <div className="space-y-2">
              <Label htmlFor="modelCount">How many custom models are needed?</Label>
              <Input
                id="modelCount"
                type="number"
                min="0"
                max="10"
                value={formData.modelCount || ''}
                onChange={handleModelCountChange}
                placeholder="Enter number of models"
                className="max-w-xs"
              />
            </div>

            {/* Dynamic Model Name Fields */}
            {formData.modelCount && formData.modelCount > 0 && (
              <div className="space-y-4">
                <div className="space-y-3">
                  {Array.from({ length: formData.modelCount }, (_, index) => (
                    <div key={index} className="space-y-2">
                      <Label htmlFor={`model-${index}`}>
                        Model {index + 1}
                      </Label>
                      <Input
                        id={`model-${index}`}
                        type="text"
                        value={formData.modelNames?.[index] || ''}
                        onChange={(e) => handleModelNameChange(index, e.target.value)}
                        placeholder="Enter model name or description"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Help text */}
            <Card className="p-4 bg-blue-50 border-blue-200">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm text-blue-700">
                    Here you can upload references for models – you can also add it later after you have finished the brief
                  </p>
                </div>
              </div>
            </Card>

            {/* Model Profile Examples will be shown in the enhanced brief section */}
            <div className="p-4 bg-muted/50 rounded-lg">
              <p className="text-sm text-muted-foreground">
                Model profile details including face examples, body examples, age range, and appearance description will be available in the next step.
              </p>
            </div>
          </div>
        )}

        {/* Library Model Info - Show only if library is selected */}
        {formData.modelChoice === 'library' && (
          <div className="space-y-4">
            <Card className="p-4 bg-blue-50 border-blue-200">
              <div className="flex items-start space-x-3">
                <Library className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Model Library Access</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    You'll be able to browse and select from our model library after completing the initial setup. 
                    The library will be available in your dashboard menu.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Brief Files Section */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Brief Files</h3>
            <p className="text-sm text-muted-foreground">
              Upload any additional reference materials or brief documents
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="briefFiles">Upload Brief Files (Optional)</Label>
            <Input
              id="briefFiles"
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.webp"
              onChange={handleFileUpload}
            />
            {formData.briefFiles && formData.briefFiles.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Selected {formData.briefFiles.length} file(s):
                <ul className="list-disc list-inside mt-1">
                  {formData.briefFiles.map((file, index) => (
                    <li key={index}>{file.name}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </CollectionFormStep>
  );
};
