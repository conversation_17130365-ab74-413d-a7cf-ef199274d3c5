import React from 'react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { CollectionFormStep } from './CollectionFormStep';
import type { CollectionFormData } from '../../hooks/useCollectionForm';

interface BasicInfoStepProps {
  formData: CollectionFormData;
  onUpdate: (updates: Partial<CollectionFormData>) => void;
  organizationName?: string;
}

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  formData,
  onUpdate,
  organizationName
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    onUpdate({ [name]: value });
  };

  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      onUpdate({ coverImage: e.target.files[0] });
    }
  };

  const getTitle = () => {
    const type = formData.collectionType;
    if (type === 'product') return 'Project Information';
    if (type === 'campaign') return 'Campaign Information';
    return 'Collection Information';
  };

  const getDescription = () => {
    const type = formData.collectionType;
    const typeName = type === 'product' ? 'project' : type === 'campaign' ? 'campaign' : 'collection';
    
    return `Enter the basic information for your ${typeName}${organizationName ? ` for ${organizationName}` : ''}`;
  };

  const getNamePlaceholder = () => {
    const type = formData.collectionType;
    if (type === 'product') return 'Enter project name';
    if (type === 'campaign') return 'Enter campaign name';
    return 'Enter collection name';
  };

  const getNameLabel = () => {
    const type = formData.collectionType;
    if (type === 'product') return 'Project Name';
    if (type === 'campaign') return 'Campaign Name';
    return 'Collection Name';
  };

  return (
    <CollectionFormStep
      title={getTitle()}
      description={getDescription()}
    >
      <div className="grid gap-6">
        <div className="space-y-2">
          <Label htmlFor="name">{getNameLabel()}</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder={getNamePlaceholder()}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Describe your collection..."
            rows={4}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="coverImage">Cover Image (Optional)</Label>
          <Input
            id="coverImage"
            type="file"
            accept="image/*"
            onChange={handleCoverImageUpload}
          />
          {formData.coverImage && (
            <p className="text-sm text-muted-foreground">
              Selected: {formData.coverImage.name}
            </p>
          )}
        </div>
      </div>
    </CollectionFormStep>
  );
};
