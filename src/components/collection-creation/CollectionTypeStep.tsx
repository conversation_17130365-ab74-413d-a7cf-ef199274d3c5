import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Label } from '../ui/label';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Checkbox } from '../ui/checkbox';
import { CollectionFormStep } from './CollectionFormStep';
import { Package, Camera, Shirt, User2, Footprints } from 'lucide-react';
import type { CollectionFormData, CollectionType } from '../../hooks/useCollectionForm';

interface CollectionTypeStepProps {
  formData: CollectionFormData;
  onUpdate: (updates: Partial<CollectionFormData>) => void;
}

export const CollectionTypeStep: React.FC<CollectionTypeStepProps> = ({
  formData,
  onUpdate
}) => {
  const handleTypeChange = (value: CollectionType) => {
    onUpdate({ collectionType: value });
  };

  const handleMainProductChange = (value: 'top' | 'bottoms' | 'shoes') => {
    onUpdate({ mainProduct: value });
  };

  const handleSecondaryProductToggle = (product: 'top' | 'bottoms' | 'shoes', checked: boolean) => {
    const currentSecondary = formData.secondaryProducts || [];
    let newSecondary: ('top' | 'bottoms' | 'shoes')[];

    if (checked) {
      newSecondary = [...currentSecondary, product];
    } else {
      newSecondary = currentSecondary.filter(p => p !== product);
    }

    onUpdate({ secondaryProducts: newSecondary });
  };

  const isProductType = formData.collectionType === 'product';

  return (
    <CollectionFormStep
      title="Collection Type"
      description="Choose the type of collection and products"
    >
      <div className="space-y-8">
        {/* Collection Type Selection */}
        <div>
          <h3 className="text-lg font-medium mb-4">What type of images do you need?</h3>
          <RadioGroup
            value={formData.collectionType}
            onValueChange={handleTypeChange}
            className="grid gap-4"
          >
            <Card className={`cursor-pointer transition-colors ${
              formData.collectionType === 'product' 
                ? 'ring-2 ring-primary bg-primary/5' 
                : 'hover:bg-muted/50'
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <RadioGroupItem value="product" id="product" className="mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Package className="h-5 w-5 text-primary" />
                      <Label htmlFor="product" className="text-base font-medium cursor-pointer">
                        Product images for e-commerce
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Create professional product images for your online store
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className={`cursor-pointer transition-colors ${
              formData.collectionType === 'campaign' 
                ? 'ring-2 ring-primary bg-primary/5' 
                : 'hover:bg-muted/50'
            }`}>
              <CardContent className="p-6">
                <div className="flex items-start space-x-4">
                  <RadioGroupItem value="campaign" id="campaign" className="mt-1" />
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-5 w-5 text-primary" />
                      <Label htmlFor="campaign" className="text-base font-medium cursor-pointer">
                        Campaign images for marketing
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Create lifestyle and marketing images for campaigns
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </RadioGroup>
        </div>

        {/* Product Selection - Show only when collection type is selected */}
        {(isProductType || formData.collectionType === 'campaign') && (
          <>
            {/* Main Product Selection */}
            <div>
              <h3 className="text-lg font-medium mb-4">Main Product Selection</h3>
              <RadioGroup
                value={formData.mainProduct || ''}
                onValueChange={handleMainProductChange}
                className="grid gap-4 md:grid-cols-3"
              >
                <Card className={`cursor-pointer transition-colors ${
                  formData.mainProduct === 'top' 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <RadioGroupItem value="top" id="mainTop" className="sr-only" />
                      <Shirt className="h-8 w-8 text-primary" />
                      <Label htmlFor="mainTop" className="text-sm font-medium cursor-pointer">
                        Top
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-colors ${
                  formData.mainProduct === 'bottoms' 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <RadioGroupItem value="bottoms" id="mainBottoms" className="sr-only" />
                      <User2 className="h-8 w-8 text-primary" />
                      <Label htmlFor="mainBottoms" className="text-sm font-medium cursor-pointer">
                        Bottoms
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-colors ${
                  formData.mainProduct === 'shoes' 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <RadioGroupItem value="shoes" id="mainShoes" className="sr-only" />
                      <Footprints className="h-8 w-8 text-primary" />
                      <Label htmlFor="mainShoes" className="text-sm font-medium cursor-pointer">
                        Shoes
                      </Label>
                    </div>
                  </CardContent>
                </Card>
              </RadioGroup>
            </div>

            {/* Secondary Products for Styling */}
            <div>
              <h3 className="text-lg font-medium mb-4">Secondary Products for Styling</h3>
              <div className="grid gap-4 md:grid-cols-3">
                <Card className={`cursor-pointer transition-colors ${
                  formData.secondaryProducts?.includes('top') 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <Checkbox
                        id="secondaryTop"
                        checked={formData.secondaryProducts?.includes('top') || false}
                        onCheckedChange={(checked) => handleSecondaryProductToggle('top', checked as boolean)}
                        disabled={formData.mainProduct === 'top'}
                      />
                      <Shirt className={`h-8 w-8 ${formData.mainProduct === 'top' ? 'text-muted' : 'text-primary'}`} />
                      <Label 
                        htmlFor="secondaryTop" 
                        className={`text-sm font-medium cursor-pointer ${formData.mainProduct === 'top' ? 'text-muted' : ''}`}
                      >
                        Top
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-colors ${
                  formData.secondaryProducts?.includes('bottoms') 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <Checkbox
                        id="secondaryBottoms"
                        checked={formData.secondaryProducts?.includes('bottoms') || false}
                        onCheckedChange={(checked) => handleSecondaryProductToggle('bottoms', checked as boolean)}
                        disabled={formData.mainProduct === 'bottoms'}
                      />
                      <User2 className={`h-8 w-8 ${formData.mainProduct === 'bottoms' ? 'text-muted' : 'text-primary'}`} />
                      <Label 
                        htmlFor="secondaryBottoms" 
                        className={`text-sm font-medium cursor-pointer ${formData.mainProduct === 'bottoms' ? 'text-muted' : ''}`}
                      >
                        Bottoms
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                <Card className={`cursor-pointer transition-colors ${
                  formData.secondaryProducts?.includes('shoes') 
                    ? 'ring-2 ring-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}>
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center space-y-2">
                      <Checkbox
                        id="secondaryShoes"
                        checked={formData.secondaryProducts?.includes('shoes') || false}
                        onCheckedChange={(checked) => handleSecondaryProductToggle('shoes', checked as boolean)}
                        disabled={formData.mainProduct === 'shoes'}
                      />
                      <Footprints className={`h-8 w-8 ${formData.mainProduct === 'shoes' ? 'text-muted' : 'text-primary'}`} />
                      <Label 
                        htmlFor="secondaryShoes" 
                        className={`text-sm font-medium cursor-pointer ${formData.mainProduct === 'shoes' ? 'text-muted' : ''}`}
                      >
                        Shoes
                      </Label>
                    </div>
                  </CardContent>
                </Card>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Select additional products to style with your main product
              </p>
            </div>
          </>
        )}
      </div>
    </CollectionFormStep>
  );
};
