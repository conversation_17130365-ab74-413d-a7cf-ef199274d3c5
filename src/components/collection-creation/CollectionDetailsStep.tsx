import React from 'react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Checkbox } from '../ui/checkbox';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { EnhancedDropzone } from '../ui/EnhancedDropzone';
import { AngleSelector } from '../ui/AngleSelector';
import { Calculator, Image as ImageIcon, Palette, Camera, User } from 'lucide-react';
import type {
  CollectionFormData,
  EnhancedBriefSections,
  ImageOutputSettings,
  ImageOutputTypes,
  CampaignDetails
} from '../../hooks/useCollectionForm';

interface CollectionDetailsStepProps {
  formData: CollectionFormData;
  onUpdate: (updates: Partial<CollectionFormData>) => void;
  onUpdateEnhancedBrief: (section: keyof EnhancedBriefSections, field: string, value: any) => void;
  onUpdateImageOutput: (field: keyof ImageOutputSettings, value: any, subField?: keyof ImageOutputTypes | 'width' | 'height') => void;
  onUpdateCampaignDetails: (field: keyof CampaignDetails, value: string) => void;
}

export const CollectionDetailsStep: React.FC<CollectionDetailsStepProps> = ({
  formData,
  onUpdate,
  onUpdateEnhancedBrief,
  onUpdateImageOutput,
  onUpdateCampaignDetails
}) => {
  const isProductType = formData.collectionType === 'product';
  const isCampaignType = formData.collectionType === 'campaign';

  const handleProductCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const count = parseInt(e.target.value) || undefined;
    onUpdate({ 
      productCount: count,
      totalAiImages: count && formData.aiImagesPerProduct 
        ? count * formData.aiImagesPerProduct 
        : undefined
    });
  };

  const handleAiImagesPerProductChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const count = parseInt(e.target.value) || undefined;
    onUpdate({
      aiImagesPerProduct: count,
      totalAiImages: count && formData.productCount
        ? count * formData.productCount
        : undefined
    });
  };

  // Enhanced Brief Handlers
  const handleTargetGarmentUpload = (files: File[]) => {
    onUpdateEnhancedBrief('targetGarmentImages', 'files', files);
  };

  const handleSecondaryGarmentUpload = (files: File[]) => {
    onUpdateEnhancedBrief('secondaryGarment', 'files', files);
  };

  const handleShoesUpload = (files: File[]) => {
    onUpdateEnhancedBrief('shoes', 'files', files);
  };

  const handleStylingDetailsUpload = (files: File[]) => {
    onUpdateEnhancedBrief('stylingDetails', 'files', files);
  };

  const handleFormatChange = (format: 'portrait' | 'landscape', checked: boolean) => {
    onUpdateEnhancedBrief('formatSelection', format, checked);
  };

  const handlePhotoshootStyleUpload = (files: File[]) => {
    onUpdateEnhancedBrief('photoshootStyle', 'files', files);
  };

  const handleModelFaceUpload = (files: File[]) => {
    onUpdateEnhancedBrief('modelProfile', 'faceExamples', files);
  };

  const handleModelBodyUpload = (files: File[]) => {
    onUpdateEnhancedBrief('modelProfile', 'bodyExamples', files);
  };

  const handleAnglesChange = (angles: string[]) => {
    onUpdateEnhancedBrief('requiredAngles', 'selectedAngles', angles);
  };

  // Campaign-specific handlers
  const handleSettingReferencesUpload = (files: File[]) => {
    onUpdateEnhancedBrief('settingReferences', 'files', files);
  };

  const handlePhotoshootInspirationUpload = (files: File[]) => {
    onUpdateEnhancedBrief('photoshootInspiration', 'files', files);
  };

  const handleVideoUploadsChange = (files: File[]) => {
    onUpdateEnhancedBrief('videoUploads', '', files);
  };

  const getTitle = () => {
    if (isProductType) return 'Project Details & Enhanced Brief';
    if (isCampaignType) return 'Campaign Details & Enhanced Brief';
    return 'Collection Details & Enhanced Brief';
  };

  const getDescription = () => {
    if (isProductType) return 'Configure your project requirements and upload detailed reference materials';
    if (isCampaignType) return 'Configure your campaign requirements and upload detailed reference materials';
    return 'Configure your collection requirements and upload detailed reference materials';
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h2 className="text-2xl font-semibold tracking-tight">{getTitle()}</h2>
        <p className="text-muted-foreground">{getDescription()}</p>
      </div>
      
      <div className="space-y-8">
        {/* AI Images Calculation Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calculator className="h-5 w-5" />
              <span>AI Image Calculation</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Primary Question - How many AI images per product */}
            <div className="p-4 bg-primary/5 rounded-lg border border-primary/20">
              <Label htmlFor="aiImagesPerProduct" className="text-base font-medium">
                How many AI images do you want per product?
              </Label>
              <Input
                id="aiImagesPerProduct"
                type="number"
                min="1"
                value={formData.aiImagesPerProduct || ''}
                onChange={handleAiImagesPerProductChange}
                placeholder="Enter number"
                className="mt-2 max-w-xs"
              />
            </div>

            {(isProductType || isCampaignType) && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="productCount">
                    {isCampaignType ? 'How many products in this campaign?' : 'Number of products'}
                  </Label>
                  <Input
                    id="productCount"
                    type="number"
                    min="1"
                    value={formData.productCount || ''}
                    onChange={handleProductCountChange}
                    placeholder="Enter number"
                  />
                </div>

                <div className="space-y-2">
                  <Label>How many products per image?</Label>
                  <RadioGroup
                    value={formData.productsPerImage?.toString() || '1'}
                    onValueChange={(value) => onUpdate({ productsPerImage: parseInt(value) as 1 | 2 | 3 })}
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="1" id="products-1" />
                      <Label htmlFor="products-1" className="cursor-pointer">1 product</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="2" id="products-2" />
                      <Label htmlFor="products-2" className="cursor-pointer">2 products</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="3" id="products-3" />
                      <Label htmlFor="products-3" className="cursor-pointer">3 products</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            )}

            {formData.totalAiImages && (
              <div className="p-4 bg-primary/10 rounded-lg">
                <p className="font-medium text-primary text-lg">
                  Total AI Images: {formData.totalAiImages}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  {formData.productCount} products × {formData.aiImagesPerProduct} AI images = {formData.totalAiImages} total
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Brief Sections */}
        <div className="space-y-6">
          <div className="text-center">
            <h3 className="text-xl font-semibold">Enhanced Brief</h3>
            <p className="text-muted-foreground mt-1">
              Upload detailed reference materials to ensure the best AI-generated results
            </p>
          </div>

          {/* Target Garment Images - Required */}
          <Card className="border-l-4 border-l-primary">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5 text-primary" />
                <span>Target Garment Images</span>
                <span className="text-sm bg-primary text-primary-foreground px-2 py-1 rounded">Required</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <EnhancedDropzone
                title="Upload Target Garment Images"
                description="Upload clear, high-quality images of the main garment. Minimum 5 images required."
                onFilesChange={handleTargetGarmentUpload}
                currentFiles={formData.enhancedBrief?.targetGarmentImages.files || []}
                maxFiles={20}
                minFiles={5}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                maxFileSize={10 * 1024 * 1024}
              />
            </CardContent>
          </Card>

          {/* Secondary Garment */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>Secondary Garment</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <EnhancedDropzone
                title="Upload Secondary Garment Images"
                description="Upload images of additional garments to be worn with the main piece."
                onFilesChange={handleSecondaryGarmentUpload}
                currentFiles={formData.enhancedBrief?.secondaryGarment.files || []}
                maxFiles={10}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                maxFileSize={10 * 1024 * 1024}
              />
              <div className="space-y-2">
                <Label htmlFor="secondaryDescription">Description (Optional)</Label>
                <Textarea
                  id="secondaryDescription"
                  value={formData.enhancedBrief?.secondaryGarment.description || ''}
                  onChange={(e) => onUpdateEnhancedBrief('secondaryGarment', 'description', e.target.value)}
                  placeholder="Describe how this garment should be styled with the main piece..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Shoes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>Shoes</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <EnhancedDropzone
                title="Upload Shoe Images"
                description="Upload images of shoes to complete the outfit."
                onFilesChange={handleShoesUpload}
                currentFiles={formData.enhancedBrief?.shoes.files || []}
                maxFiles={5}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                maxFileSize={10 * 1024 * 1024}
              />
            </CardContent>
          </Card>

          {/* Styling Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="h-5 w-5" />
                <span>Styling Details</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <EnhancedDropzone
                title="Upload Styling Reference Images"
                description="Upload images showing desired styling, accessories, or overall look."
                onFilesChange={handleStylingDetailsUpload}
                currentFiles={formData.enhancedBrief?.stylingDetails.files || []}
                maxFiles={10}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                maxFileSize={10 * 1024 * 1024}
              />
              <div className="space-y-2">
                <Label htmlFor="stylingNotes">Styling Notes</Label>
                <Textarea
                  id="stylingNotes"
                  value={formData.enhancedBrief?.stylingDetails.notes || ''}
                  onChange={(e) => onUpdateEnhancedBrief('stylingDetails', 'notes', e.target.value)}
                  placeholder="Describe specific styling requirements, accessories, or details..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Photoshoot Style */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>Photoshoot Style</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <EnhancedDropzone
                title="Upload Photoshoot Style References"
                description="Upload images showing desired photography style, lighting, or mood."
                onFilesChange={handlePhotoshootStyleUpload}
                currentFiles={formData.enhancedBrief?.photoshootStyle.files || []}
                maxFiles={10}
                acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                maxFileSize={10 * 1024 * 1024}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="backgroundColor">Background Color</Label>
                  <Input
                    id="backgroundColor"
                    value={formData.enhancedBrief?.photoshootStyle.backgroundColor || ''}
                    onChange={(e) => onUpdateEnhancedBrief('photoshootStyle', 'backgroundColor', e.target.value)}
                    placeholder="e.g., White, Black, Gray"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="dropShadow">Drop Shadow</Label>
                  <Input
                    id="dropShadow"
                    value={formData.enhancedBrief?.photoshootStyle.dropShadow || ''}
                    onChange={(e) => onUpdateEnhancedBrief('photoshootStyle', 'dropShadow', e.target.value)}
                    placeholder="e.g., Soft, Hard, None"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lightDirection">Light Direction</Label>
                  <Input
                    id="lightDirection"
                    value={formData.enhancedBrief?.photoshootStyle.lightDirection || ''}
                    onChange={(e) => onUpdateEnhancedBrief('photoshootStyle', 'lightDirection', e.target.value)}
                    placeholder="e.g., Front, Side, Top"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Model Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Model Profile</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Face Examples</h4>
                  <EnhancedDropzone
                    title="Upload Face Reference Images"
                    description="Upload images showing desired facial features or expressions."
                    onFilesChange={handleModelFaceUpload}
                    currentFiles={formData.enhancedBrief?.modelProfile.faceExamples || []}
                    maxFiles={5}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    maxFileSize={10 * 1024 * 1024}
                  />
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Body Examples</h4>
                  <EnhancedDropzone
                    title="Upload Body Reference Images"
                    description="Upload images showing desired body type or poses."
                    onFilesChange={handleModelBodyUpload}
                    currentFiles={formData.enhancedBrief?.modelProfile.bodyExamples || []}
                    maxFiles={5}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    maxFileSize={10 * 1024 * 1024}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ageRange">Age Range</Label>
                  <Input
                    id="ageRange"
                    value={formData.enhancedBrief?.modelProfile.ageRange || ''}
                    onChange={(e) => onUpdateEnhancedBrief('modelProfile', 'ageRange', e.target.value)}
                    placeholder="e.g., 20-30, 25-35"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="appearance">Appearance Description</Label>
                  <Input
                    id="appearance"
                    value={formData.enhancedBrief?.modelProfile.appearance || ''}
                    onChange={(e) => onUpdateEnhancedBrief('modelProfile', 'appearance', e.target.value)}
                    placeholder="e.g., Athletic, Slim, Curvy"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Required Angles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>Required Angles</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AngleSelector
                selectedAngles={formData.enhancedBrief?.requiredAngles.selectedAngles || []}
                onAnglesChange={handleAnglesChange}
                requiredCount={formData.aiImagesPerProduct}
              />
            </CardContent>
          </Card>

          {/* Format Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ImageIcon className="h-5 w-5" />
                <span>Format Selection</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Image Size */}
              <div className="space-y-2">
                <Label htmlFor="imageSize">Image Size (in pixels)</Label>
                <Input
                  id="imageSize"
                  placeholder="e.g., 1920x1080, 4K, 3000x2000"
                  value={formData.imageOutput?.imageSize || ''}
                  onChange={(e) => onUpdateImageOutput('imageSize', e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Specify the desired image dimensions
                </p>
              </div>

              {/* File Type */}
              <div className="space-y-2">
                <Label>File Type</Label>
                <RadioGroup
                  value={formData.imageOutput?.format || 'JPG'}
                  onValueChange={(value) => onUpdateImageOutput('format', value)}
                  className="grid grid-cols-2 md:grid-cols-3 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="JPG" id="format-jpg" />
                    <Label htmlFor="format-jpg" className="cursor-pointer">JPG</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="TIFF" id="format-tiff" />
                    <Label htmlFor="format-tiff" className="cursor-pointer">TIFF</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="PNG" id="format-png" />
                    <Label htmlFor="format-png" className="cursor-pointer">PNG</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="WebP" id="format-webp" />
                    <Label htmlFor="format-webp" className="cursor-pointer">WebP</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Other" id="format-other" />
                    <Label htmlFor="format-other" className="cursor-pointer">Other</Label>
                  </div>
                </RadioGroup>
                {formData.imageOutput?.format === 'Other' && (
                  <Input
                    placeholder="Specify format"
                    value={formData.imageOutput?.customFormat || ''}
                    onChange={(e) => onUpdateImageOutput('customFormat', e.target.value)}
                    className="mt-2"
                  />
                )}
              </div>

              {/* Max File Size */}
              <div className="space-y-2">
                <Label htmlFor="maxFileSize">Max File Size per {formData.imageOutput?.format || 'JPG'}</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="maxFileSize"
                    type="number"
                    placeholder="10"
                    value={formData.imageOutput?.maxFileSize || ''}
                    onChange={(e) => onUpdateImageOutput('maxFileSize', parseInt(e.target.value) || undefined)}
                    className="max-w-[100px]"
                  />
                  <span className="text-sm text-muted-foreground">MB</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Maximum file size allowed per image
                </p>
              </div>

              {/* Orientation Selection */}
              <div className="space-y-2">
                <Label>Orientation Preference</Label>
                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="portrait"
                      checked={formData.enhancedBrief?.formatSelection.portrait || false}
                      onCheckedChange={(checked) => handleFormatChange('portrait', checked as boolean)}
                    />
                    <Label htmlFor="portrait">Portrait (Vertical)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="landscape"
                      checked={formData.enhancedBrief?.formatSelection.landscape || false}
                      onCheckedChange={(checked) => handleFormatChange('landscape', checked as boolean)}
                    />
                    <Label htmlFor="landscape">Landscape (Horizontal)</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Videos Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5" />
                <span>Videos</span>
                <span className="text-sm bg-muted text-muted-foreground px-2 py-1 rounded">Optional</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Please select which angles you want small videos created from
                </p>
                <p className="text-sm text-muted-foreground italic">
                  Images are replaced by small videos of same model posing
                </p>
              </div>
              
              {/* Videos Counter */}
              {formData.aiImagesPerProduct && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm">
                    <span className="font-medium">AI videos per product:</span> {formData.aiImagesPerProduct}
                  </p>
                </div>
              )}

              {/* Video Angle Selection - reuse the AngleSelector component */}
              <div className="pt-2">
                <AngleSelector
                  selectedAngles={formData.enhancedBrief?.videoAngles || []}
                  onAnglesChange={(angles) => onUpdateEnhancedBrief('videoAngles', '', angles)}
                  requiredCount={formData.aiImagesPerProduct}
                />
              </div>
            </CardContent>
          </Card>

          {/* Deadline Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calculator className="h-5 w-5" />
                <span>Deadline</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="deadline">Project Deadline</Label>
                <Input
                  id="deadline"
                  type="date"
                  value={formData.imageOutput?.deadline || ''}
                  onChange={(e) => onUpdateImageOutput('deadline', e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  When do you need the final images delivered?
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Campaign-Specific Sections */}
          {isCampaignType && (
            <>
              {/* Setting References */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <ImageIcon className="h-5 w-5" />
                    <span>Setting References</span>
                    <span className="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded">Campaign Only</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EnhancedDropzone
                    title="Upload Setting Reference Images"
                    description="Upload images of every background or environment to be recreated in the campaign."
                    onFilesChange={handleSettingReferencesUpload}
                    currentFiles={formData.enhancedBrief?.settingReferences?.files || []}
                    maxFiles={15}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    maxFileSize={10 * 1024 * 1024}
                  />
                </CardContent>
              </Card>

              {/* Photoshoot Inspiration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Camera className="h-5 w-5" />
                    <span>Photoshoot Inspiration</span>
                    <span className="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded">Campaign Only</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EnhancedDropzone
                    title="Upload Photoshoot Inspiration Images"
                    description="Upload example images showing desired mood, lighting, or filter directions for the campaign."
                    onFilesChange={handlePhotoshootInspirationUpload}
                    currentFiles={formData.enhancedBrief?.photoshootInspiration?.files || []}
                    maxFiles={15}
                    acceptedFileTypes={['image/jpeg', 'image/png', 'image/webp']}
                    maxFileSize={10 * 1024 * 1024}
                  />
                </CardContent>
              </Card>

              {/* Video References */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Camera className="h-5 w-5" />
                    <span>Video References</span>
                    <span className="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded">Campaign Only</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EnhancedDropzone
                    title="Upload Video References"
                    description="Upload videos showing desired movement, poses, or overall campaign feel."
                    onFilesChange={handleVideoUploadsChange}
                    currentFiles={formData.enhancedBrief?.videoUploads || []}
                    maxFiles={5}
                    acceptedFileTypes={['video/mp4', 'video/mov', 'video/avi', 'video/webm']}
                    maxFileSize={50 * 1024 * 1024}
                  />
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* Campaign Details */}
        {isCampaignType && (
          <Card>
            <CardHeader>
              <CardTitle>Campaign Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purpose">Purpose</Label>
                  <Textarea
                    id="purpose"
                    value={formData.campaignDetails?.purpose || ''}
                    onChange={(e) => onUpdateCampaignDetails('purpose', e.target.value)}
                    placeholder="Describe the campaign purpose..."
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="lookAndFeel">Look & Feel</Label>
                  <Textarea
                    id="lookAndFeel"
                    value={formData.campaignDetails?.lookAndFeel || ''}
                    onChange={(e) => onUpdateCampaignDetails('lookAndFeel', e.target.value)}
                    placeholder="Describe the desired look and feel..."
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="poses">Poses & Movement</Label>
                  <Textarea
                    id="poses"
                    value={formData.campaignDetails?.poses || ''}
                    onChange={(e) => onUpdateCampaignDetails('poses', e.target.value)}
                    placeholder="Describe desired poses and movements..."
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetGroup">Target Group</Label>
                  <Textarea
                    id="targetGroup"
                    value={formData.campaignDetails?.targetGroup || ''}
                    onChange={(e) => onUpdateCampaignDetails('targetGroup', e.target.value)}
                    placeholder="Describe the target audience..."
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
