import { useState } from 'react';
import { Bell, Menu, LogOut, Users, Check, User, Package } from 'lucide-react';
import { Button } from '../ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Link, NavLink, useNavigate } from 'react-router-dom';
import { cn } from '../common/utils/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel, 
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal
} from '../ui/dropdown-menu';
import { navigationItems } from './navigation-items';
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../common/hooks/use-toast';
import { useOrganizations } from '../../contexts/OrganizationContext';
import { useUserRole } from '../../contexts/UserRoleContext';
import { useProfile } from '../../contexts/ProfileContext';

export function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, signOut } = useSupabase();
  const { profile } = useProfile();
  const { userMemberships, currentOrganization, switchOrganization, isLoading: isLoadingOrgs } = useOrganizations();
  const { userRole, isPlatformUser, isBrandAdmin } = useUserRole();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSignOut = async () => {
    try {
      await signOut();
      toast({
        title: "Success",
        description: "You have been logged out",
      });
      navigate('/login');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to log out",
        variant: "destructive",
      });
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    // Use profile data if available
    if (profile?.first_name && profile?.last_name) {
      return `${profile.first_name[0]}${profile.last_name[0]}`.toUpperCase();
    } else if (profile?.first_name) {
      return profile.first_name.slice(0, 2).toUpperCase();
    } else if (profile?.last_name) {
      return profile.last_name.slice(0, 2).toUpperCase();
    }
    
    // Fallback to email
    if (!user?.email) return 'U';
    
    const emailName = user.email.split('@')[0];
    const nameParts = emailName.split(/[._-]/);
    
    if (nameParts.length > 1) {
      return (nameParts[0][0] + nameParts[1][0]).toUpperCase();
    }
    
    return emailName.slice(0, 2).toUpperCase();
  };

  return (
    <header className="h-16 border-b border-border flex items-center justify-between px-4 sm:px-6 bg-background/90 backdrop-blur-md">
      {/* Left side: Logo and navigation items */}
      <div className="flex items-center space-x-4 sm:space-x-8">
        <Link to="/" className="flex-shrink-0">
          <img 
            src="/fashionlab-logo.svg" 
            alt="FashionLab Logo" 
            className="h-6" 
          />
        </Link>
        
        {/* Desktop Navigation - Hidden on small screens */}
        <nav className="hidden sm:flex items-center space-x-6">
          {navigationItems
            .filter(item => !item.requiresRole || item.requiresRole.includes(userRole || ''))
            .map((item) => (
              <NavLink
                key={item.to}
                to={item.to}
                className={({ isActive }) => cn(
                  "flex items-center gap-2 text-sm font-medium transition-colors",
                  isActive 
                    ? "text-primary" 
                    : "text-muted-foreground hover:text-foreground"
                )}
              >
                <item.icon size={18} />
                <span>{item.label}</span>
              </NavLink>
            ))}
        </nav>
        
        {/* Mobile Navigation Dropdown */}
        <div className="relative sm:hidden">
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-expanded={mobileMenuOpen}
          >
            <Menu size={20} />
            <span className="sr-only">Open menu</span>
          </Button>
          
          {mobileMenuOpen && (
            <div className="absolute top-10 left-0 w-56 bg-background rounded-md shadow-lg border border-border z-50">
              <nav className="flex flex-col py-2">
                {navigationItems
                  .filter(item => !item.requiresRole || item.requiresRole.includes(userRole || ''))
                  .map((item) => (
                    <NavLink
                      key={item.to}
                      to={item.to}
                      className={({ isActive }) => cn(
                        "flex items-center gap-3 px-4 py-2 text-sm font-medium transition-colors",
                        isActive 
                          ? "bg-secondary text-primary" 
                          : "text-muted-foreground hover:text-foreground hover:bg-secondary/50"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      <item.icon size={18} />
                      <span>{item.label}</span>
                    </NavLink>
                  ))}
              </nav>
            </div>
          )}
        </div>
      </div>

      {/* Right side: Notifications and profile */}
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon" className="relative">
          <Bell size={18} />
          <span className="absolute top-1.5 right-1.5 w-2 h-2 bg-primary rounded-full"></span>
        </Button>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-9 w-9 rounded-full">
              <Avatar className="h-9 w-9">
                <AvatarImage src={profile?.avatar_url || user?.user_metadata?.avatar_url || ""} alt={user?.email || "User"} />
                <AvatarFallback>{getUserInitials()}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {(profile?.first_name || profile?.last_name) 
                    ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
                    : user?.email}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link to="/profile">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </Link>
            </DropdownMenuItem>
            {isPlatformUser && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link to="/admin/users">
                    <Users className="mr-2 h-4 w-4" />
                    <span>User Management</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link to="/admin/model-library">
                    <Package className="mr-2 h-4 w-4" />
                    <span>Model Library</span>
                  </Link>
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuSub>
              <DropdownMenuSubTrigger>
                <Users className="mr-2 h-4 w-4" />
                <span>Switch Brand</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <DropdownMenuLabel>Brands</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {isLoadingOrgs ? (
                    <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
                  ) : userMemberships.length === 0 ? (
                     <DropdownMenuItem disabled>No brands found</DropdownMenuItem>
                  ) : (
                    userMemberships.map((membership) => (
                      membership.organizations && ( // Ensure organization data exists
                        <DropdownMenuItem
                          key={membership.organization_id}
                          onClick={() => {
                            const orgId = membership.organization_id;
                            switchOrganization(orgId); // Update context first
                            // Navigate to appropriate page based on role
                            if (isPlatformUser || isBrandAdmin) {
                              navigate(`/organizations/${orgId}`); // Admin view of organization
                            } else {
                              navigate(`/organizations/${orgId}/collections`); // Regular user view
                            }
                          }}
                          className="cursor-pointer"
                        >
                          {currentOrganization?.id === membership.organization_id && (
                            <Check className="mr-2 h-4 w-4" />
                          )}
                          <span>{membership.organizations.name}</span>
                        </DropdownMenuItem>
                      )
                    ))
                  )}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut}>
              <LogOut className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}

export default Header;
