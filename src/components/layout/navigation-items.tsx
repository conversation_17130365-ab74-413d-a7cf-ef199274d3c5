import { LayoutDashboard, Users, Package } from 'lucide-react';

export interface NavigationItem {
  icon: any;
  label: string;
  to: string;
  requiresRole?: string[];
}

export const navigationItems: NavigationItem[] = [
  { 
    icon: LayoutDashboard, 
    label: 'Dashboard', 
    to: '/dashboard' 
  },
  {
    icon: Users,
    label: 'User Management',
    to: '/admin/users',
    requiresRole: ['platform_super', 'platform_admin']
  },
  {
    icon: Package,
    label: 'Model Library',
    to: '/admin/model-library',
    requiresRole: ['platform_super', 'platform_admin']
  }
];