import { useState, useMemo } from 'react';
import { cn } from '../common/utils/utils';
import {
  Filter, X, CalendarClock,
  ImageIcon, Pencil, Zap, Check, ChevronsUpDown, Package, Tag, Plus,
  PanelLeftClose, PanelLeftOpen, WandSparkles, CheckCircle, Sparkles, Search, ChevronDown, ChevronUp,
  Ruler
} from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Checkbox } from '../../components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../../components/ui/collapsible";
import { Input } from "../../components/ui/input";
import { useFilters, WorkflowStage, TimeFilter } from '../../contexts/FilterContext';
import { useCollectionTags } from '../common/hooks/useTags';
import { useUserRole } from '../../contexts/UserRoleContext';
import { getWorkflowStageConfigs } from '../common/utils/workflowStageUtils';
import { useIsFreelancer } from '../common/hooks/useIsFreelancer';
import { Skeleton } from '../../components/ui/skeleton';
import { supabase } from '../common/utils/supabase';
import { useToast } from '../common/hooks/use-toast';
import { TagCategory, RetouchSubstage } from '../common/types/database.types';
import { useProducts } from '../common/hooks/useProducts';
import { useAssetCounts } from '../common/hooks/useAssetCounts';
import { useQueryClient } from '@tanstack/react-query';
import { getRetouchSubstageConfigs, getRetouchSubstageBadgeClass, getAllowedRetouchSubstages, getRetouchSubstageLabel } from '../common/utils/retouchSubstageUtils';
import { Eye, Edit, Edit2, MoreHorizontal } from 'lucide-react';

interface FilterSidebarProps {
  collectionId?: string;
  className?: string;
  isMobile?: boolean;
}

export function FilterSidebar({ collectionId, className, isMobile }: FilterSidebarProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [newTagName, setNewTagName] = useState('');
  const [isCreatingTag, setIsCreatingTag] = useState(false);
  const [newProductName, setNewProductName] = useState('');
  const [newProductSku, setNewProductSku] = useState('');
  const [isCreatingProduct, setIsCreatingProduct] = useState(false);
  const [showEmptyProducts, setShowEmptyProducts] = useState(true);
  const [isOpen, setIsOpen] = useState(!isMobile);
  const [tagSearchQuery, setTagSearchQuery] = useState('');
  const [showAllTags, setShowAllTags] = useState(false);
  const [showAllProducts, setShowAllProducts] = useState(false);
  const [isRetouchExpanded, setIsRetouchExpanded] = useState(true);
  const [expandedTagCategories, setExpandedTagCategories] = useState<Record<string, boolean>>({
    global: true,
    angles: true,
    styling: true,
    collection: true
  });
  
  const { 
    filters, 
    setWorkflowStageFilter,
    setTagFilter,
    setTimeFilter,
    setProductFilter,
    setSizeFilter,
    setRetouchSubstageFilter,
    clearFilters
  } = useFilters();
  
  // Fetch tags for this collection
  const { 
    data: collectionTags, 
    isLoading: isLoadingTags, 
    isError: isTagsError,
    refetch: refetchTags
  } = useCollectionTags(collectionId);
  
  // Fetch products for this collection
  const {
    data: collectionProducts,
    isLoading: isLoadingProducts,
    isError: isProductsError
  } = useProducts({
    collectionId,
    enabled: !!collectionId
  });
  
  // Use the hook for filtered counts for workflow stages
  const {
    data: filteredAssetCounts,
    isLoading: isLoadingFilteredCounts
  } = useAssetCounts(collectionId, { 
    filters: {
      productIds: filters.productIds,
      tagIds: filters.tagIds,
      timeUpdated: filters.timeUpdated,
      sizes: filters.sizes
    }
  });
  
  // Use the hook for product counts (exclude product filter itself)
  const {
    data: productFilteredCounts,
    isLoading: isLoadingProductCounts
  } = useAssetCounts(collectionId, { 
    filters: {
      // Exclude productIds from filters to show counts for each product
      tagIds: filters.tagIds,
      timeUpdated: filters.timeUpdated,
      sizes: filters.sizes,
      workflowStages: filters.workflowStages
    }
  });
  
  // Use the hook for tag counts (exclude tag filter itself)
  const {
    data: tagFilteredCounts,
    isLoading: isLoadingTagCounts
  } = useAssetCounts(collectionId, { 
    filters: {
      // Exclude tagIds from filters to show counts for each tag
      productIds: filters.productIds,
      timeUpdated: filters.timeUpdated,
      sizes: filters.sizes,
      workflowStages: filters.workflowStages
    }
  });
  
  // Use filtered counts for all sections
  const workflowStageCounts = filteredAssetCounts?.workflowStages || {};
  const retouchSubstageCounts = filteredAssetCounts?.retouchSubstages || {};
  const productAssetCounts = productFilteredCounts?.products || {};
  const tagAssetCounts = tagFilteredCounts?.tags || {};
  const isLoadingCounts = isLoadingFilteredCounts || isLoadingProductCounts || isLoadingTagCounts;
  
  // Fixed set of standard sizes
  const availableSizes = ['XS', 'S', 'M', 'L', 'XL'];
  
  const handleWorkflowStageChange = (stage: WorkflowStage, checked: boolean) => {
    if (checked) {
      setWorkflowStageFilter([...filters.workflowStages, stage]);
    } else {
      setWorkflowStageFilter(filters.workflowStages.filter(s => s !== stage));
      // Clear substages when deselecting retouch
      if (stage === 'retouch') {
        setRetouchSubstageFilter([]);
      }
    }
  };
  
  const handleTagChange = (tagId: string, checked: boolean) => {
    if (checked) {
      setTagFilter([...filters.tagIds, tagId]);
    } else {
      setTagFilter(filters.tagIds.filter(id => id !== tagId));
    }
  };
  
  const handleProductChange = (productId: string, checked: boolean) => {
    const currentProductIds = [...filters.productIds];
    
    if (checked) {
      if (!currentProductIds.includes(productId)) {
        setProductFilter([...currentProductIds, productId]);
      }
    } else {
      setProductFilter(currentProductIds.filter(id => id !== productId));
    }
  };
  
  // Create a new tag
  const handleCreateTag = async () => {
    if (!newTagName.trim() || !collectionId) return;
    
    setIsCreatingTag(true);
    
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert({
          name: newTagName.trim(),
          category: 'collection',  // User-created tags are always collection-specific
          collection_id: collectionId,
          color: '#9333EA'  // Default purple color for collection tags
        })
        .select()
        .single();
      
      if (error) throw error;
      
      toast({
        title: 'Tag Created',
        description: `Successfully created tag "${newTagName}"`,
      });
      
      setNewTagName('');
      
      // Invalidate tags query to refetch
      queryClient.invalidateQueries({ queryKey: ['tags', collectionId] });
      queryClient.invalidateQueries({ queryKey: ['collection-tags', collectionId] });
      refetchTags();
      
    } catch (error) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: `Failed to create tag: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive'
      });
    } finally {
      setIsCreatingTag(false);
    }
  };
  
  // Create a new product
  const handleCreateProduct = async () => {
    if (!newProductName.trim() || !collectionId) return;
    
    setIsCreatingProduct(true);
    
    try {
      const { data, error } = await supabase
        .from('products')
        .insert({
          name: newProductName.trim(),
          sku: newProductSku.trim() || null,
          collection_id: collectionId
        })
        .select()
        .single();
      
      if (error) throw error;
      
      toast({
        title: 'Product Created',
        description: `Successfully created product "${newProductName}"`,
      });
      
      setNewProductName('');
      setNewProductSku('');
      
      // Invalidate products query to refetch
      queryClient.invalidateQueries({ queryKey: ['products', collectionId] });
      
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        title: 'Error',
        description: `Failed to create product: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive'
      });
    } finally {
      setIsCreatingProduct(false);
    }
  };
  
  const getTagColor = (category: string) => {
    switch (category) {
      case 'global':
        return 'text-green-500';
      case 'angles':
        return 'text-blue-500';
      case 'styling':
        return 'text-pink-500';
      case 'collection':
        return 'text-purple-500';
      default:
        return 'text-gray-500';
    }
  };
  
  const toggleSidebar = () => {
    setIsOpen(prev => !prev);
  };

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stage configurations, considering freelancer status
  const workflowStageConfigs = getWorkflowStageConfigs(userRole, isFreelancer);

  // Map configurations to include icon components
  const workflowStages = workflowStageConfigs.map(config => ({
    id: config.id,
    label: config.label,
    icon: config.icon === 'ImageIcon' ? ImageIcon :
          config.icon === 'Pencil' ? Pencil :
          config.icon === 'CheckCircle' ? CheckCircle :
          config.icon === 'Sparkles' ? Sparkles :
          config.icon === 'Zap' ? Zap :
          config.icon === 'WandSparkles' ? WandSparkles :
          config.icon === 'Check' ? Check : ImageIcon,
    color: config.color
  }));
  
  // Filter tags based on search query
  const filteredTags = collectionTags?.filter(tag =>
    tag.name.toLowerCase().includes(tagSearchQuery.toLowerCase())
  ) || [];
  
  // Group tags by category for better organization
  const tagsByCategory = filteredTags.reduce((acc, tag) => {
    const category = tag.category || 'collection';
    if (!acc[category]) acc[category] = [];
    acc[category].push(tag);
    return acc;
  }, {} as Record<string, typeof filteredTags>);
  
  // Display logic - show first 8 tags unless "show all" is enabled
  const INITIAL_TAG_DISPLAY_LIMIT = 8;
  const tagsToDisplay = showAllTags ? filteredTags : filteredTags.slice(0, INITIAL_TAG_DISPLAY_LIMIT);
  const hasMoreTags = filteredTags.length > INITIAL_TAG_DISPLAY_LIMIT;
  
  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'global': return 'Global Tags';
      case 'angles': return 'Angles';
      case 'styling': return 'Styling';
      case 'collection': return 'Collection Tags';
      default: return 'Other';
    }
  };
  
  return (
    <>
      {/* Mobile toggle button */}
      {isMobile && (
        <Button
          variant="outline"
          size="sm"
          onClick={toggleSidebar}
          className="fixed bottom-4 left-4 z-40 rounded-full shadow-md h-12 w-12 flex items-center justify-center lg:hidden"
        >
          {isOpen ? <PanelLeftClose size={20} /> : <PanelLeftOpen size={20} />}
          <span className="sr-only">Toggle filters</span>
        </Button>
      )}
      
      {/* Sidebar container */}
      <div 
        className={cn(
          "border-r bg-background h-full overflow-y-auto flex flex-col transition-all duration-300",
          "lg:w-72 lg:static",
          isMobile ? (
            isOpen 
              ? "fixed inset-y-0 left-0 z-30 w-3/4 max-w-xs shadow-xl" 
              : "fixed inset-y-0 -left-full z-30 w-3/4 max-w-xs"
          ) : (
            "w-72"
          ),
          className
        )}
      >
        {/* Close button for mobile */}
        {isMobile && isOpen && (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleSidebar}
            className="absolute top-3 right-3 h-8 w-8 lg:hidden"
          >
            <X size={18} />
            <span className="sr-only">Close filters</span>
          </Button>
        )}
        
        <div className="p-6 border-b bg-gray-50/50 flex justify-between items-center">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg">
              <Filter className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">Filters</h2>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8 px-3 text-xs font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            Clear All
          </Button>
        </div>
        
        <div className="p-6 space-y-6 flex-1">
          {/* Workflow Stage Filter */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger className="flex w-full items-center justify-between px-3 py-3 text-sm font-semibold rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-6 h-6 bg-amber-100 rounded-md">
                  <Zap size={14} className="text-amber-600" />
                </div>
                Workflow Stage
              </div>
              <ChevronsUpDown size={14} className="text-gray-400" />
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-3 px-3">
              {workflowStages.map(stage => {
                const Icon = stage.icon;
                const count = workflowStageCounts[stage.id] || 0;
                const isRetouch = stage.id === 'retouch';
                const allowedSubstages = isRetouch ? getAllowedRetouchSubstages(userRole, isFreelancer) : [];
                
                return (
                  <div key={stage.id}>
                    <div className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="flex items-center space-x-3 flex-1">
                        <Checkbox
                          id={stage.id}
                          checked={filters.workflowStages.includes(stage.id as WorkflowStage)}
                          onCheckedChange={(checked) => handleWorkflowStageChange(stage.id as WorkflowStage, checked as boolean)}
                          className={cn(
                            "data-[state=checked]:bg-primary data-[state=checked]:border-primary",
                            stage.color === 'blue' && "data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600",
                            stage.color === 'indigo' && "data-[state=checked]:bg-indigo-600 data-[state=checked]:border-indigo-600",
                            stage.color === 'green' && "data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600",
                            stage.color === 'teal' && "data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600",
                            stage.color === 'amber' && "data-[state=checked]:bg-amber-600 data-[state=checked]:border-amber-600",
                            stage.color === 'emerald' && "data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600",
                            stage.color === 'purple' && "data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                          )}
                        />
                        <label
                          htmlFor={stage.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Icon size={14} className={`text-${stage.color}-500`} />
                              {stage.label}
                            </div>
                            {count > 0 && (
                              <span className={`text-xs text-${stage.color}-700 bg-${stage.color}-100 px-2 py-0.5 rounded-full font-medium`}>
                                {isLoadingCounts ? '...' : count}
                              </span>
                            )}
                          </div>
                        </label>
                      </div>
                      {isRetouch && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsRetouchExpanded(!isRetouchExpanded);
                          }}
                        >
                          {isRetouchExpanded ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                        </Button>
                      )}
                    </div>
                    
                    {/* Retouch substages */}
                    {isRetouch && isRetouchExpanded && filters.workflowStages.includes('retouch') && (
                      <div className="ml-9 mt-1 space-y-1">
                        {allowedSubstages.map(substage => {
                          const config = getRetouchSubstageConfigs().find(c => c.id === substage);
                          if (!config) return null;
                          
                          return (
                            <div key={substage} className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                              <Checkbox
                                id={`retouch-${substage}`}
                                checked={filters.retouchSubstages.includes(substage)}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    setRetouchSubstageFilter([...filters.retouchSubstages, substage]);
                                    // Ensure retouch stage is selected when selecting a substage
                                    if (!filters.workflowStages.includes('retouch')) {
                                      setWorkflowStageFilter([...filters.workflowStages, 'retouch']);
                                    }
                                  } else {
                                    setRetouchSubstageFilter(filters.retouchSubstages.filter(s => s !== substage));
                                  }
                                }}
                                className="data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600 h-4 w-4"
                              />
                              <label
                                htmlFor={`retouch-${substage}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    {config.icon === 'Eye' && <Eye size={12} className="text-gray-500" />}
                                    {config.icon === 'Edit' && <Edit size={12} className="text-blue-500" />}
                                    {config.icon === 'Edit2' && <Edit2 size={12} className="text-purple-500" />}
                                    {config.icon === 'MoreHorizontal' && <MoreHorizontal size={12} className="text-amber-500" />}
                                    <span className="text-xs">{getRetouchSubstageLabel(substage as RetouchSubstage)}</span>
                                  </div>
                                  {retouchSubstageCounts[substage] > 0 && (
                                    <span className="text-xs text-emerald-700 bg-emerald-100 px-1.5 py-0.5 rounded-full font-medium">
                                      {isLoadingCounts ? '...' : retouchSubstageCounts[substage]}
                                    </span>
                                  )}
                                </div>
                              </label>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })}
            </CollapsibleContent>
          </Collapsible>
          
          {/* Products Filter */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger className="flex w-full items-center justify-between px-3 py-3 text-sm font-semibold rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-6 h-6 bg-green-100 rounded-md">
                  <Package size={14} className="text-green-600" />
                </div>
                Products
                {collectionProducts && (() => {
                  const visibleProducts = collectionProducts.filter(p => showEmptyProducts || (productAssetCounts[p.id] || 0) > 0).length;
                  if (visibleProducts === 0) return null;
                  
                  return (
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                      {filters.productIds.length}/{visibleProducts}
                    </span>
                  );
                })()}
              </div>
              <ChevronsUpDown size={14} className="text-gray-400" />
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-3 px-3">
              {/* Add new product input */}
              <div className="px-2 space-y-2">
                <div className="flex gap-2">
                  <Input
                    placeholder="Product name..."
                    value={newProductName}
                    onChange={(e) => setNewProductName(e.target.value)}
                    className="h-8 text-sm bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !newProductSku) {
                        handleCreateProduct();
                      }
                    }}
                  />
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="h-8 px-2 bg-white hover:bg-gray-50 border-gray-200"
                    onClick={handleCreateProduct}
                    disabled={!newProductName.trim() || isCreatingProduct}
                  >
                    <Plus size={14} />
                  </Button>
                </div>
                {newProductName && (
                  <Input
                    placeholder="SKU (optional)..."
                    value={newProductSku}
                    onChange={(e) => setNewProductSku(e.target.value)}
                    className="h-8 text-sm bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateProduct();
                      }
                    }}
                  />
                )}
              </div>
              
              {/* Toggle for showing empty products */}
              {collectionProducts && collectionProducts.length > 0 && (() => {
                const productsWithAssets = collectionProducts.filter(p => (productAssetCounts[p.id] || 0) > 0).length;
                const productsWithoutAssets = collectionProducts.length - productsWithAssets;
                
                // Only show the toggle if there are products without assets
                if (productsWithoutAssets === 0) return null;
                
                return (
                  <div className="px-2 space-y-1">
                    <label className="text-xs text-muted-foreground flex items-center gap-2 cursor-pointer">
                      <Checkbox
                        checked={!showEmptyProducts}
                        onCheckedChange={(checked) => setShowEmptyProducts(!checked)}
                      />
                      Hide empty products
                    </label>
                    <div className="text-xs text-muted-foreground pl-6">
                      {showEmptyProducts ? (
                        `Showing all ${collectionProducts.length} products`
                      ) : (
                        `Showing ${productsWithAssets} of ${collectionProducts.length} products`
                      )}
                    </div>
                  </div>
                );
              })()}
              
              {/* Product list */}
              {isLoadingProducts ? (
                <div className="space-y-2 px-2">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ) : isProductsError ? (
                <p className="px-2 text-sm text-muted-foreground">Failed to load products</p>
              ) : collectionProducts && collectionProducts.length > 0 ? (
                <div className="space-y-2">
                  {(() => {
                    const filteredProducts = collectionProducts.filter(
                      product => showEmptyProducts || (productAssetCounts[product.id] || 0) > 0
                    );
                    const INITIAL_PRODUCT_DISPLAY_LIMIT = 8;
                    const productsToDisplay = showAllProducts 
                      ? filteredProducts 
                      : filteredProducts.slice(0, INITIAL_PRODUCT_DISPLAY_LIMIT);
                    const hasMoreProducts = filteredProducts.length > INITIAL_PRODUCT_DISPLAY_LIMIT;

                    return (
                      <>
                        {productsToDisplay.map(product => {
                          const count = productAssetCounts[product.id] || 0;
                          
                          return (
                            <div key={product.id} className="flex items-center px-2 py-1 hover:bg-gray-50 rounded transition-colors">
                              <Checkbox 
                                id={`product-${product.id}`}
                                checked={filters.productIds.includes(product.id)}
                                onCheckedChange={(checked) => 
                                  handleProductChange(product.id, checked === true)
                                }
                              />
                              <label 
                                htmlFor={`product-${product.id}`}
                                className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer min-w-0"
                              >
                                <div className="flex items-center justify-between gap-2">
                                  <div className="flex items-center gap-2 min-w-0 flex-1">
                                    <Package size={14} className="text-muted-foreground flex-shrink-0" />
                                    <div className="truncate" title={`${product.name}${product.sku ? ` (${product.sku})` : ''}`}>
                                      <span className="truncate">{product.name}</span>
                                      {product.sku && <span className="text-xs text-muted-foreground ml-1">({product.sku})</span>}
                                    </div>
                                  </div>
                                  {count > 0 && (
                                    <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full ml-2 flex-shrink-0">
                                      {isLoadingCounts ? '...' : count}
                                    </span>
                                  )}
                                </div>
                              </label>
                            </div>
                          );
                        })}
                        
                        {/* Show more/less button */}
                        {hasMoreProducts && (
                          <div className="px-2 pt-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowAllProducts(!showAllProducts)}
                              className="w-full h-8 text-xs text-muted-foreground hover:text-foreground"
                            >
                              {showAllProducts ? (
                                <>
                                  <ChevronUp size={14} className="mr-1" />
                                  Show Less ({filteredProducts.length - INITIAL_PRODUCT_DISPLAY_LIMIT} hidden)
                                </>
                              ) : (
                                <>
                                  <ChevronDown size={14} className="mr-1" />
                                  Show {filteredProducts.length - INITIAL_PRODUCT_DISPLAY_LIMIT} More Products
                                </>
                              )}
                            </Button>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              ) : (
                <p className="px-2 text-sm text-muted-foreground">No products found</p>
              )}
            </CollapsibleContent>
          </Collapsible>
          
          {/* Size Filter */}
          {availableSizes.length > 0 && (
            <Collapsible defaultOpen>
              <CollapsibleTrigger className="flex w-full items-center justify-between px-3 py-3 text-sm font-semibold rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-6 h-6 bg-blue-100 rounded-md">
                    <Ruler size={14} className="text-blue-600" />
                  </div>
                  Filter by Size
                  {filters.sizes.length > 0 && (
                    <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                      {filters.sizes.length}
                    </span>
                  )}
                </div>
                <ChevronsUpDown size={14} className="text-gray-400" />
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-3 space-y-3 px-3">
                <div className="flex justify-between items-center px-2">
                  <span className="text-xs text-muted-foreground">
                    {filters.sizes.length} of {availableSizes.length} sizes selected
                  </span>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-xs h-6 px-2"
                    onClick={() => {
                      if (filters.sizes.length === availableSizes.length) {
                        setSizeFilter([]);
                      } else {
                        setSizeFilter(availableSizes);
                      }
                    }}
                  >
                    {filters.sizes.length === availableSizes.length ? 'Clear All' : 'Select All'}
                  </Button>
                </div>
                
                <div className="flex flex-wrap gap-2 px-2">
                  {availableSizes.map((size) => {
                    const isSelected = filters.sizes.includes(size);
                    return (
                      <Button
                        key={size}
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          if (isSelected) {
                            setSizeFilter(filters.sizes.filter(s => s !== size));
                          } else {
                            setSizeFilter([...filters.sizes, size]);
                          }
                        }}
                        className={`h-8 px-3 ${
                          isSelected 
                            ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                            : 'hover:bg-gray-100'
                        }`}
                      >
                        {size}
                      </Button>
                    );
                  })}
                </div>
                
                {filters.sizes.length > 0 && (
                  <div className="text-xs text-gray-500 px-2">
                    Showing assets for: {filters.sizes.join(', ')}
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>
          )}
          
          {/* Tags Filter */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger className="flex w-full items-center justify-between px-3 py-3 text-sm font-semibold rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-6 h-6 bg-purple-100 rounded-md">
                  <Tag size={14} className="text-purple-600" />
                </div>
                Tags
                {filteredTags.length > 0 && (
                  <span className="text-xs bg-purple-100 text-purple-700 px-2 py-0.5 rounded-full">
                    {filters.tagIds.length}/{filteredTags.length}
                  </span>
                )}
              </div>
              <ChevronsUpDown size={14} className="text-gray-400" />
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-3 px-3">
              {/* Add new tag input */}
              <div className="px-2 space-y-2">
                <p className="text-xs text-muted-foreground">
                  Create custom tags for this collection
                </p>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add collection tag..."
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                    className="h-8 text-sm bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleCreateTag();
                      }
                    }}
                  />
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="h-8 px-2 bg-white hover:bg-gray-50 border-gray-200"
                    onClick={handleCreateTag}
                    disabled={!newTagName.trim() || isCreatingTag}
                  >
                    <Plus size={14} />
                  </Button>
                </div>
              </div>
              
              {/* Search tags input */}
              {collectionTags && collectionTags.length > 6 && (
                <div className="px-2">
                  <div className="relative">
                    <Search size={14} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Input
                      placeholder="Search tags..."
                      value={tagSearchQuery}
                      onChange={(e) => setTagSearchQuery(e.target.value)}
                      className="h-8 text-sm bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 pl-9"
                    />
                  </div>
                </div>
              )}
              
              {isLoadingTags ? (
                <div className="space-y-2 px-2">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-5 w-full" />
                </div>
              ) : isTagsError ? (
                <div className="px-2 text-sm text-muted-foreground">
                  Error loading tags
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Display tags grouped by category */}
                  {(['global', 'angles', 'styling', 'collection'] as const).map((category) => {
                    const categoryTags = tagsByCategory[category] || [];
                    const isExpanded = expandedTagCategories[category];
                    
                    return (
                      <div key={category} className="space-y-2">
                        <button
                          onClick={() => setExpandedTagCategories(prev => ({
                            ...prev,
                            [category]: !prev[category]
                          }))}
                          className="flex items-center justify-between w-full px-2 py-1 text-xs font-medium text-muted-foreground hover:text-foreground transition-colors"
                        >
                          <span>{getCategoryLabel(category)}</span>
                          {isExpanded ? (
                            <ChevronUp size={12} />
                          ) : (
                            <ChevronDown size={12} />
                          )}
                        </button>
                        
                        {isExpanded && (
                          <div className="space-y-1">
                            {categoryTags.length > 0 ? (
                              categoryTags.map((tag) => (
                                <div key={tag.id} className="flex items-center px-2 py-1 hover:bg-gray-50 rounded transition-colors">
                                  <Checkbox
                                    id={`tag-${tag.id}`}
                                    checked={filters.tagIds.includes(tag.id)}
                                    onCheckedChange={(checked) => handleTagChange(tag.id, checked as boolean)}
                                  />
                                  <label
                                    htmlFor={`tag-${tag.id}`}
                                    className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer"
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <Tag size={14} className={getTagColor(tag.category)} />
                                        <span className="truncate" title={tag.name}>
                                          {tag.name}
                                        </span>
                                      </div>
                                      {(tagAssetCounts[tag.id] || 0) > 0 && (
                                        <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full ml-2 flex-shrink-0">
                                          {isLoadingCounts ? '...' : (tagAssetCounts[tag.id] || 0)}
                                        </span>
                                      )}
                                    </div>
                                  </label>
                                </div>
                              ))
                            ) : (
                              <div className="px-2 py-1 text-xs text-muted-foreground italic">
                                No {getCategoryLabel(category).toLowerCase()} available
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    );
                  })}
                  
                  {filteredTags.length === 0 && tagSearchQuery && (
                    <div className="px-2 text-sm text-muted-foreground">
                      No tags found matching "{tagSearchQuery}"
                    </div>
                  )}
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
          
          {/* Time Updated Filter */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger className="flex w-full items-center justify-between px-3 py-3 text-sm font-semibold rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-md">
                  <CalendarClock size={14} className="text-gray-600" />
                </div>
                Last Updated
              </div>
              <ChevronsUpDown size={14} className="text-gray-400" />
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 px-3">
              <Select 
                value={filters.timeUpdated} 
                onValueChange={(value) => setTimeFilter(value as TimeFilter)}
              >
                <SelectTrigger className="w-full bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500/20">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="quarter">This Quarter</SelectItem>
                </SelectContent>
              </Select>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </div>
      
      {/* Mobile backdrop */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black/20 z-20 lg:hidden"
          onClick={toggleSidebar}
        />
      )}
    </>
  );
}

export default FilterSidebar;