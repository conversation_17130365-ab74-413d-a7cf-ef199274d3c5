import { Outlet, useLocation } from 'react-router-dom';
import Header from './Header';

export function Layout() {
  return (
    <div className="min-h-screen flex flex-col bg-background text-foreground w-full">
      {/* Header is always shown and full-width */}
      <Header />
      
      {/* Content area that takes remaining height */}
      <div className="flex-1 flex">
        {/* All pages now use the same layout without sidebar */}
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default Layout;
