export interface GenerationSettings {
  prompt: string;
  negativePrompt: string;
  width: number;
  height: number;
  steps: number;
  cfg: number;
  seed: number | null;
  sampler: string;
  clipSkip: number;
  loraWeight: number;
  fluxGuidance: number;
  numImages: number;
  ratio: string;
  imageFormat: string;
}

export interface Model {
  id: string;
  code?: string; // Model code (S, M, L, XL)
  name: string;
  shortName: string;
  description: string;
  promptText: string;
  lora: string;
  image: string;
}

export interface Angle {
  id: number;
  name: string;
  promptText: string;
}

export interface GeneratedImage {
  id: string;
  url: string;
  thumbnail?: string;
  prompt: string;
  model: string;
  modelId: string;
  angle: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
  isSelected?: boolean;
  asset_id?: string;
  isFromDatabase?: boolean;
}

export interface V2ImageData {
  file?: File;
  base64?: string;
  preview?: string;
  fromAsset?: boolean;
  assetId?: string;
}

export interface V2Images {
  face: V2ImageData | null;
  image_2: V2ImageData | null;
  image_3: V2ImageData | null;
  image_4: V2ImageData | null;
}

export interface ImageGenerationParameters {
  prompt: string;
  settings: GenerationSettings;
  v2Images?: V2Images;
  selectedModels: string[];
  selectedAngles: number[];
  activeBlocks?: Array<{
    type: string;
    name: string;
    text: string;
    subType?: string;
  }>;
}