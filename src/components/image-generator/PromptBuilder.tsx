import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '../../components/ui/card';
import { Textarea } from '../../components/ui/textarea';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { X, History, Info } from 'lucide-react';
import { cn } from '../../components/common/utils/utils';
import { BLOCK_COLORS } from '../../config/imageGeneration.config';
import { debounce } from 'lodash';

export interface ActiveBlock {
  type: 'model' | 'angle' | 'garment' | 'background' | 'artDirection';
  name: string;
  text: string;
  subType?: 'product' | 'additional'; // For garments to distinguish between product and additional
}

interface PromptBuilderProps {
  prompt: string;
  onPromptChange: (prompt: string) => void;
  activeBlocks: ActiveBlock[];
  onActiveBlocksChange: (blocks: ActiveBlock[]) => void;
  onShowHistory?: () => void;
}

interface PromptSegment {
  text: string;
  type: string | null;
  blockData?: ActiveBlock;
}

export function PromptBuilder({
  prompt,
  onPromptChange,
  activeBlocks,
  onActiveBlocksChange,
  onShowHistory
}: PromptBuilderProps) {
  const [localPrompt, setLocalPrompt] = useState(prompt);
  const [isEditing, setIsEditing] = useState(false);
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  // Debounced prompt change to prevent too many re-renders
  const debouncedPromptChange = useMemo(
    () => debounce((value: string) => {
      onPromptChange(value);
    }, 300),
    [onPromptChange]
  );

  // Auto-resize textarea based on content
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';
      // Set height to scrollHeight to fit content
      const newHeight = Math.max(120, textarea.scrollHeight);
      textarea.style.height = `${newHeight}px`;
    }
  }, []);

  // Handle prompt change
  const handlePromptChange = useCallback((value: string) => {
    setLocalPrompt(value);
    debouncedPromptChange(value);
    // Adjust height after content changes
    setTimeout(adjustTextareaHeight, 0);
  }, [debouncedPromptChange, adjustTextareaHeight]);

  // Update local prompt when prop changes
  React.useEffect(() => {
    setLocalPrompt(prompt);
    adjustTextareaHeight();
  }, [prompt, adjustTextareaHeight]);

  // Initial height adjustment
  React.useEffect(() => {
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);


  // Parse prompt into segments with highlighting information
  const parsePromptSegments = (promptText: string): PromptSegment[] => {
    const segments: PromptSegment[] = [];
    
    // Sort blocks by position in prompt
    const blocksWithPositions = activeBlocks
      .map(block => ({
        ...block,
        index: promptText.indexOf(block.text)
      }))
      .filter(block => block.index !== -1)
      .sort((a, b) => a.index - b.index);
    
    let lastIndex = 0;
    
    blocksWithPositions.forEach(block => {
      const index = promptText.indexOf(block.text, lastIndex);
      if (index !== -1) {
        // Add text before block
        if (index > lastIndex) {
          segments.push({ 
            text: promptText.substring(lastIndex, index), 
            type: null 
          });
        }
        // Add block
        segments.push({ 
          text: block.text, 
          type: block.type,
          blockData: block
        });
        lastIndex = index + block.text.length;
      }
    });
    
    // Add remaining text
    if (lastIndex < promptText.length) {
      segments.push({ 
        text: promptText.substring(lastIndex), 
        type: null 
      });
    }
    
    // If no segments were created, return the whole prompt as a single segment
    if (segments.length === 0 && promptText.length > 0) {
      segments.push({ text: promptText, type: null });
    }
    
    return segments;
  };

  // Get background color for highlighting
  const getHighlightColor = (type: string | null, blockData?: ActiveBlock) => {
    if (!type) return 'transparent';
    
    // Special case for main garments
    if (type === 'garment' && blockData?.subType === 'product') {
      return 'rgba(99, 102, 241, 0.2)'; // indigo for main garment
    }
    
    switch (type) {
      case 'model':
        return 'rgba(59, 130, 246, 0.2)'; // blue
      case 'angle':
        return 'rgba(16, 185, 129, 0.2)'; // green
      case 'garment':
        return 'rgba(139, 92, 246, 0.2)'; // purple
      case 'background':
        return 'rgba(251, 146, 60, 0.2)'; // orange
      case 'artDirection':
        return 'rgba(236, 72, 153, 0.2)'; // pink
      default:
        return 'transparent';
    }
  };

  // Create rich text content
  const renderHighlightedText = () => {
    if (!localPrompt) return null;
    
    return parsePromptSegments(localPrompt).map((segment, index) => (
      <span
        key={index}
        className={cn(
          segment.type && "px-0.5 rounded-sm"
        )}
        style={{
          backgroundColor: getHighlightColor(segment.type, segment.blockData),
        }}
      >
        {segment.text}
      </span>
    ));
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium">Prompt Builder</h3>
            <button
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="Edit the prompt below. Colored highlights show prompt blocks."
            >
              <Info className="h-3 w-3" />
            </button>
          </div>
          <div className="flex items-center gap-1">
            {activeBlocks.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onActiveBlocksChange([])}
                className="h-7 px-2 text-xs"
              >
                Clear Blocks
              </Button>
            )}
            {onShowHistory && (
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7"
                onClick={onShowHistory}
                title="Prompt History"
              >
                <History className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Single Editor with Inline Highlighting */}
        <div className="relative">
          {/* Background layer with highlighted text */}
          <div 
            className="absolute inset-0 p-3 pointer-events-none text-sm"
            style={{ 
              minHeight: '120px',
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              lineHeight: '1.5rem',
              overflow: 'hidden'
            }}
          >
            {renderHighlightedText()}
          </div>

          {/* Foreground textarea with transparent text */}
          <Textarea
            ref={textareaRef}
            value={localPrompt}
            onChange={(e) => handlePromptChange(e.target.value)}
            onFocus={() => setIsEditing(true)}
            onBlur={() => setIsEditing(false)}
            placeholder="Describe the image you want to generate..."
            className={cn(
              "text-sm transition-all relative resize-none",
              "bg-transparent",
              isEditing && "ring-2 ring-blue-500 border-blue-500"
            )}
            style={{
              color: 'transparent',
              caretColor: 'black',
              WebkitTextFillColor: 'transparent',
              lineHeight: '1.5rem',
              minHeight: '120px',
              overflow: 'hidden'
            }}
          />
        </div>

        {/* Info bar */}
        <div className="flex items-center justify-between text-xs text-gray-500 px-1">
          <span>Type to edit prompt • Colored blocks show selections</span>
          <span>{localPrompt.length} chars</span>
        </div>

        {/* Block Categories */}
        <div className="flex flex-wrap gap-1.5 pt-2">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'model') && "bg-blue-50 text-blue-700"
            )}
            disabled
          >
            Model
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'angle') && "bg-green-50 text-green-700"
            )}
            disabled
          >
            Angle
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'garment' && b.subType === 'product') && "bg-indigo-50 text-indigo-700"
            )}
            disabled
          >
            Product
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'garment' && b.subType !== 'product') && "bg-purple-50 text-purple-700"
            )}
            disabled
          >
            Garment
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'background') && "bg-orange-50 text-orange-700"
            )}
            disabled
          >
            Background
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              "h-7 px-2 text-xs font-normal",
              activeBlocks.some(b => b.type === 'artDirection') && "bg-pink-50 text-pink-700"
            )}
            disabled
          >
            Art Direction
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}