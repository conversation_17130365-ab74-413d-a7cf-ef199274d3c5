import React, { useState } from 'react';
import { Upload, X, FolderOpen } from 'lucide-react';
import { Label } from '../../../components/ui/label';
import { Button } from '../../../components/ui/button';
import { cn } from '../../../components/common/utils/utils';
import { BriefAssetSelectorDialog } from '../dialogs/BriefAssetSelectorDialog';
import type { V2Images } from '../types';
import type { Asset } from '../../../types/database.types';

interface V2ImageUploaderProps {
  images: V2Images;
  onImageUpload: (file: File, slot: keyof V2Images) => void;
  onImageRemove: (slot: keyof V2Images) => void;
  onImageFromAsset?: (asset: Asset, slot: keyof V2Images) => void;
  collectionId?: string;
  className?: string;
}

export function V2ImageUploader({ 
  images, 
  onImageUpload, 
  onImageRemove,
  onImageFromAsset,
  collectionId,
  className 
}: V2ImageUploaderProps) {
  const [assetDialogOpen, setAssetDialogOpen] = useState(false);
  const [currentSlot, setCurrentSlot] = useState<keyof V2Images | null>(null);

  const handleSelectFromBrief = (slot: keyof V2Images) => {
    if (!collectionId || !onImageFromAsset) return;
    setCurrentSlot(slot);
    setAssetDialogOpen(true);
  };

  const handleAssetSelected = (asset: Asset) => {
    if (currentSlot && onImageFromAsset) {
      onImageFromAsset(asset, currentSlot);
    }
    setAssetDialogOpen(false);
    setCurrentSlot(null);
  };

  const renderImageSlot = (
    slot: keyof V2Images,
    label: string,
    assetType?: 'garment' | 'additional'
  ) => {
    const image = images[slot];
    const canSelectFromBrief = collectionId && onImageFromAsset;
    
    return (
      <div className="space-y-1">
        <Label className="text-xs font-medium">{label}</Label>
        <div className="relative">
          {image ? (
            <div className="relative group">
              <img 
                src={image.preview} 
                alt={`${label} preview`}
                className="w-full h-20 object-cover rounded border"
              />
              <button
                onClick={() => onImageRemove(slot)}
                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                aria-label={`Remove ${label}`}
              >
                <X className="w-3 h-3" />
              </button>
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-[10px] p-1 truncate">
                {image.file?.name || image.fromAsset ? 'From brief' : 'Uploaded image'}
              </div>
            </div>
          ) : (
            <div className="space-y-1">
              <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) onImageUpload(file, slot);
                  }}
                  className="hidden"
                />
                <div className="text-center">
                  <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                  <p className="text-[10px] text-gray-500">Upload</p>
                </div>
              </label>
              {canSelectFromBrief && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleSelectFromBrief(slot)}
                  className="w-full h-7 text-xs"
                >
                  <FolderOpen className="w-3 h-3 mr-1" />
                  From Brief
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const isComplete = !!(images.face && images.image_2 && images.image_3 && images.image_4);

  return (
    <>
      <div className={cn("space-y-3", className)}>
        <div>
          <Label className="text-sm font-medium mb-2 block">Fashion Lab V2 API Images</Label>
          <p className="text-xs text-muted-foreground mb-3">
            Upload 4 images: face photo + 3 reference images
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          {renderImageSlot('face', 'Face')}
          {renderImageSlot('image_2', 'Garment 1', 'garment')}
          {renderImageSlot('image_3', 'Garment 2', 'garment')}
          {renderImageSlot('image_4', 'Garment 3', 'additional')}
        </div>
        
        {isComplete && (
          <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded">
            <p className="text-xs text-green-700">✓ V2 API ready - all 4 images uploaded</p>
          </div>
        )}
      </div>

      {/* Asset selector dialog */}
      {collectionId && currentSlot && (
        <BriefAssetSelectorDialog
          open={assetDialogOpen}
          onOpenChange={setAssetDialogOpen}
          collectionId={collectionId}
          onSelectAsset={handleAssetSelected}
          assetType={currentSlot === 'image_4' ? 'additional' : 'garment'}
        />
      )}
    </>
  );
}