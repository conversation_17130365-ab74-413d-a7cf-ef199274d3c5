import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { cn } from '../../../components/common/utils/utils';
import { SETTING_BLOCKS, BLOCK_COLORS } from '../../../config/imageGeneration.config';
import type { ActiveBlock } from '../PromptBuilder';

interface PromptBlocksSelectorProps {
  activeBlocks: ActiveBlock[];
  onBlockToggle: (block: ActiveBlock) => void;
  className?: string;
}

export function PromptBlocksSelector({
  activeBlocks,
  onBlockToggle,
  className
}: PromptBlocksSelectorProps) {
  const isBlockActive = (blockText: string) => {
    return activeBlocks.some(block => block.text === blockText);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {SETTING_BLOCKS.map((settingBlock) => {
        const colors = BLOCK_COLORS[settingBlock.id as keyof typeof BLOCK_COLORS] || BLOCK_COLORS.background;
        
        return (
          <Card key={settingBlock.id} className="overflow-hidden">
            <CardHeader className="pb-3 bg-gray-50">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <span>{settingBlock.icon}</span>
                <span>{settingBlock.name}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-3">
              <div className="grid grid-cols-1 gap-2">
                {settingBlock.options.map((option) => {
                  const isActive = isBlockActive(option.promptText);
                  
                  return (
                    <Button
                      key={option.id}
                      variant={isActive ? "default" : "outline"}
                      size="sm"
                      className={cn(
                        "justify-start text-left h-auto py-2 px-3 transition-all",
                        isActive && "ring-2 ring-offset-1",
                        isActive && colors.bg,
                        isActive && colors.text,
                        isActive && colors.border
                      )}
                      onClick={() => {
                        onBlockToggle({
                          type: settingBlock.id,
                          name: option.name,
                          text: option.promptText
                        });
                      }}
                    >
                      <span className="text-xs">{option.name}</span>
                      {isActive && (
                        <Badge 
                          variant="secondary" 
                          className={cn(
                            "ml-auto text-[10px]",
                            colors.bg,
                            colors.text,
                            colors.border
                          )}
                        >
                          Active
                        </Badge>
                      )}
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}