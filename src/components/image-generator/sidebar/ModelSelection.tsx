import React from 'react';
import { Card, CardContent } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Badge } from '../../../components/ui/badge';
import { cn, getAssetUrl } from '../../../components/common/utils/utils';
import type { Model } from '../types';

interface ModelSelectionProps {
  models: Model[];
  selectedModels: string[];
  onToggleModel: (modelId: string) => void;
  onSelectAll: () => void;
  onClearAll: () => void;
}

export function ModelSelection({
  models,
  selectedModels,
  onToggleModel,
  onSelectAll,
  onClearAll,
}: ModelSelectionProps) {
  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Select Models</h3>
        {models.length > 0 && (
          <div className="flex gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 text-xs"
              onClick={onSelectAll}
            >
              Select All
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-7 text-xs"
              onClick={onClearAll}
            >
              Clear
            </Button>
          </div>
        )}
      </div>
      
      {models.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p className="text-sm">No models available</p>
          <p className="text-xs mt-1">Please contact an admin to set up models</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-2">
          {models.map((model) => (
          <Card 
            key={model.id}
            className={cn(
              "cursor-pointer transition-all duration-200 hover:shadow-md",
              selectedModels.includes(model.id) && 
              "ring-2 ring-blue-500 shadow-lg transform scale-[1.02]"
            )}
            onClick={() => onToggleModel(model.id)}
          >
            <CardContent className="p-2">
              <div className="aspect-[3/4] rounded overflow-hidden mb-2 bg-gray-100">
                {model.image && (
                  <img 
                    src={model.image} 
                    alt={model.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/300x400/f5f5f5/666666?text=${encodeURIComponent(model.shortName)}`;
                    }}
                  />
                )}
              </div>
              <div className="space-y-1">
                <h4 className="text-xs font-medium text-center line-clamp-1">
                  {model.shortName}
                </h4>
                {selectedModels.includes(model.id) && (
                  <div className="flex justify-center">
                    <Badge className="text-[10px] h-4">Selected</Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          ))}
        </div>
      )}
      
      {models.length > 0 && (
        <div className="text-xs text-gray-500 text-center">
          {selectedModels.length} model{selectedModels.length !== 1 ? 's' : ''} selected
        </div>
      )}
    </div>
  );
}