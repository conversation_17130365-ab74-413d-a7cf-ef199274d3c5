import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Slider } from '../../../components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { Input } from '../../../components/ui/input';
import { Button } from '../../../components/ui/button';
import { ChevronDown, ChevronUp, RotateCcw } from 'lucide-react';
import { settingBlocks } from '../constants';
import type { GenerationSettings } from '../types';

interface GenerationSettingsProps {
  settings: GenerationSettings;
  isAdvancedMode: boolean;
  onUpdateSetting: <K extends keyof GenerationSettings>(key: K, value: GenerationSettings[K]) => void;
  onToggleAdvancedMode: () => void;
  onResetSettings: () => void;
}

export function GenerationSettingsComponent({
  settings,
  isAdvancedMode,
  onUpdateSetting,
  onToggleAdvancedMode,
  onResetSettings,
}: GenerationSettingsProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Generation Settings</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            className="h-7 text-xs"
            onClick={onResetSettings}
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Main Prompt */}
        <div className="space-y-2">
          <Label htmlFor="prompt" className="text-xs">
            Additional Prompt Details
          </Label>
          <Textarea
            id="prompt"
            placeholder="Add specific details like clothing, pose, expression..."
            value={settings.prompt}
            onChange={(e) => onUpdateSetting('prompt', e.target.value)}
            className="min-h-[80px] text-sm"
          />
        </div>

        {/* Negative Prompt */}
        <div className="space-y-2">
          <Label htmlFor="negative-prompt" className="text-xs">
            Negative Prompt
          </Label>
          <Textarea
            id="negative-prompt"
            placeholder="Things to avoid..."
            value={settings.negativePrompt}
            onChange={(e) => onUpdateSetting('negativePrompt', e.target.value)}
            className="min-h-[60px] text-sm"
          />
        </div>

        {/* Advanced Settings Toggle */}
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-between"
          onClick={onToggleAdvancedMode}
        >
          <span className="text-xs">Advanced Settings</span>
          {isAdvancedMode ? (
            <ChevronUp className="h-3 w-3" />
          ) : (
            <ChevronDown className="h-3 w-3" />
          )}
        </Button>

        {/* Advanced Settings */}
        {isAdvancedMode && (
          <div className="space-y-4 pt-2 border-t">
            {/* Dimensions */}
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="width" className="text-xs">Width</Label>
                <Input
                  id="width"
                  type="number"
                  value={settings.width}
                  onChange={(e) => onUpdateSetting('width', parseInt(e.target.value) || 768)}
                  min="256"
                  max="2048"
                  step="64"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="height" className="text-xs">Height</Label>
                <Input
                  id="height"
                  type="number"
                  value={settings.height}
                  onChange={(e) => onUpdateSetting('height', parseInt(e.target.value) || 1024)}
                  min="256"
                  max="2048"
                  step="64"
                />
              </div>
            </div>

            {/* Technical Settings */}
            {settingBlocks.map((setting) => {
              if (setting.id === 'sampler') {
                return (
                  <div key={setting.id} className="space-y-2">
                    <Label htmlFor={setting.id} className="text-xs">
                      {setting.label}
                    </Label>
                    <Select
                      value={settings[setting.id as keyof GenerationSettings]?.toString()}
                      onValueChange={(value) => onUpdateSetting(setting.id as keyof GenerationSettings, value as any)}
                    >
                      <SelectTrigger id={setting.id} className="text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {setting.options?.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                );
              }

              if (setting.id === 'seed') {
                return (
                  <div key={setting.id} className="space-y-2">
                    <Label htmlFor={setting.id} className="text-xs">
                      {setting.label} (use -1 for random)
                    </Label>
                    <Input
                      id={setting.id}
                      type="number"
                      value={settings[setting.id as keyof GenerationSettings]}
                      onChange={(e) => onUpdateSetting(
                        setting.id as keyof GenerationSettings, 
                        parseInt(e.target.value) || -1
                      )}
                      min={setting.min}
                      max={setting.max}
                    />
                  </div>
                );
              }

              return (
                <div key={setting.id} className="space-y-2">
                  <div className="flex justify-between">
                    <Label htmlFor={setting.id} className="text-xs">
                      {setting.label}
                    </Label>
                    <span className="text-xs text-gray-500">
                      {settings[setting.id as keyof GenerationSettings]}
                    </span>
                  </div>
                  <Slider
                    id={setting.id}
                    min={setting.min}
                    max={setting.max}
                    step={1}
                    value={[settings[setting.id as keyof GenerationSettings] as number]}
                    onValueChange={([value]) => onUpdateSetting(
                      setting.id as keyof GenerationSettings,
                      value as any
                    )}
                  />
                </div>
              );
            })}

            {/* Additional Advanced Settings */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="clip-skip" className="text-xs">
                  CLIP Skip
                </Label>
                <span className="text-xs text-gray-500">
                  {settings.clipSkip}
                </span>
              </div>
              <Slider
                id="clip-skip"
                min={1}
                max={2}
                step={1}
                value={[settings.clipSkip]}
                onValueChange={([value]) => onUpdateSetting('clipSkip', value)}
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="lora-weight" className="text-xs">
                  LoRA Weight
                </Label>
                <span className="text-xs text-gray-500">
                  {settings.loraWeight.toFixed(1)}
                </span>
              </div>
              <Slider
                id="lora-weight"
                min={0}
                max={2}
                step={0.1}
                value={[settings.loraWeight]}
                onValueChange={([value]) => onUpdateSetting('loraWeight', value)}
              />
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}