import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { cn } from '../../../components/common/utils/utils';
import { styleSettingBlocks } from '../constants';
import type { ActiveBlock } from '../PromptBuilder';

interface SettingsBlocksProps {
  selectedSettings: { [key: string]: number };
  onSettingToggle: (settingId: string, optionId: number) => void;
  onBlockToggle?: (block: ActiveBlock) => void;
}

export function SettingsBlocks({
  selectedSettings,
  onSettingToggle,
  onBlockToggle,
}: SettingsBlocksProps) {
  const handleOptionClick = (settingId: string, option: any) => {
    // Toggle the setting
    if (selectedSettings[settingId] === option.id) {
      onSettingToggle(settingId, 0); // Deselect
      
      // Remove from active blocks if callback provided
      if (onBlockToggle) {
        onBlockToggle({
          type: settingId as any,
          name: option.name,
          text: option.promptText,
        });
      }
    } else {
      onSettingToggle(settingId, option.id); // Select
      
      // Add to active blocks if callback provided
      if (onBlockToggle) {
        onBlockToggle({
          type: settingId as any,
          name: option.name,
          text: option.promptText,
        });
      }
    }
  };

  return (
    <div className="space-y-4">
      {styleSettingBlocks.map((settingBlock) => (
        <Card key={settingBlock.id}>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <span>{settingBlock.icon}</span>
              <span>{settingBlock.name}</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 gap-2">
              {settingBlock.options.map((option) => {
                const isSelected = selectedSettings[settingBlock.id] === option.id;
                return (
                  <div
                    key={option.id}
                    className={cn(
                      "p-3 rounded-lg border cursor-pointer transition-all hover:shadow-sm",
                      isSelected 
                        ? "border-primary bg-primary/5" 
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={() => handleOptionClick(settingBlock.id, option)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{option.name}</span>
                      {isSelected && (
                        <Badge variant="secondary" className="text-xs">
                          Selected
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {option.promptText}
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}