import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { cn } from '../common/utils/utils';
import { 
  NUM_IMAGES_OPTIONS,
  DEFAULT_GENERATION_SETTINGS 
} from '../../config/imageGeneration.config';

interface TechnicalSettingsProps {
  settings: typeof DEFAULT_GENERATION_SETTINGS;
  onSettingsChange: (settings: Partial<typeof DEFAULT_GENERATION_SETTINGS>) => void;
  totalImages: number;
  className?: string;
}

export function TechnicalSettings({
  settings,
  onSettingsChange,
  totalImages,
  className
}: TechnicalSettingsProps) {
  const handleSettingChange = <K extends keyof typeof DEFAULT_GENERATION_SETTINGS>(
    key: K,
    value: typeof DEFAULT_GENERATION_SETTINGS[K]
  ) => {
    onSettingsChange({ [key]: value });
  };

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium mb-2 block">Technical Settings</Label>
      
      {/* Number of Images */}
      <div>
        <Label className="text-xs text-muted-foreground">Number of Images</Label>
        <Select
          value={settings.numImages.toString()}
          onValueChange={(v) => handleSettingChange('numImages', parseInt(v) as typeof settings.numImages)}
        >
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {NUM_IMAGES_OPTIONS.map(num => (
              <SelectItem key={num} value={num.toString()}>
                {num} {num === 1 ? 'image' : 'images'} per combination
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {/* Seed */}
      <div>
        <Label className="text-xs text-muted-foreground">Seed (leave empty for random)</Label>
        <Input
          type="number"
          placeholder="Random"
          className="h-8 text-sm"
          value={settings.seed || ''}
          onChange={(e) => {
            const seed = e.target.value ? parseInt(e.target.value) : null;
            handleSettingChange('seed', seed);
          }}
        />
      </div>
      
      <div className="pt-2 border-t flex items-center justify-between text-xs text-muted-foreground">
        <span>Total to generate:</span>
        <span className="font-medium">{totalImages} images</span>
      </div>
    </div>
  );
}