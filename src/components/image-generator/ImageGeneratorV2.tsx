import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Progress } from '../../components/ui/progress';
import { useToast } from '../../components/ui/use-toast';
import { Badge } from '../../components/ui/badge';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { 
  ArrowLeft, Sparkles, History, Save, Upload, 
  X, Image as ImageIcon, CheckCircle, Loader2
} from 'lucide-react';
import { cn } from '../../components/common/utils/utils';

// Import hooks
import { useModelManagement } from './hooks/useModelManagement';
import { useGenerationSettings } from './hooks/useGenerationSettings';
import { useV2ImageManagement } from './hooks/useV2ImageManagement';
import { useGeneratedImages } from './hooks/useGeneratedImages';
import { useImageSelection } from './hooks/useImageSelection';
import { usePromptBuilder } from './hooks/usePromptBuilder';
import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { useCollection } from '../../components/common/hooks/useCollections';
import { useOrganization } from '../../components/common/hooks/useOrganizations';
import { useProducts } from '../../components/common/hooks/useProducts';
import { useAssets } from '../../components/common/hooks/useAssets';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';
import { openAIService } from '../../services/openaiService';
import { getModelImageAsBase64 } from '../../hooks/useModelLibrary';
import { getAssetUrl } from '../../components/common/utils/utils';

// Import components
import { ModelSelection } from './sidebar/ModelSelection';
import { AngleSelection } from './sidebar/AngleSelection';
import { GenerationSettingsComponent } from './sidebar/GenerationSettings';
import { GeneratedImagesGrid } from './main-content/GeneratedImagesGrid';
import { SelectionToolbar } from './main-content/SelectionToolbar';
import { ImageDetailDialog } from './dialogs/ImageDetailDialog';
import { ProductSelectorDialog } from './dialogs/ProductSelectorDialog';
import { ProductImageSelectorDialog } from './dialogs/ProductImageSelectorDialog';
import { PromptBuilder } from './PromptBuilder';
import { PromptBlocksSelector } from './sidebar/PromptBlocksSelector';
import { FlexibleInputBox, FlexibleInput } from '../image-generator/FlexibleInputBox';
import { PromptHistoryDialog } from './dialogs/PromptHistoryDialog';
import { TechnicalSettings } from './TechnicalSettings';

// Import constants and types
import { angleBlocks } from './constants';
import type { GeneratedImage } from './types';

// Angle preview component
const AnglePreview = ({ angle, selectedModel }: { angle: any; selectedModel: any }) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  
  useEffect(() => {
    const loadAngleImage = async () => {
      if (selectedModel?.code) {
        try {
          // Map angle names to database angle types
          const angleTypeMap: Record<string, string> = {
            'Half-body Front': 'half-body-front',
            'Half-body Back': 'half-body-back',
            'Half-body 3/4 Left': 'half-body-34-left',
            'Half-body 3/4 Right': 'half-body-34-right',
            'Full-height Front': 'full-body-front',
            'Full-height Back': 'full-body-back',
            'Full-height Side Left': 'full-body-side-left',
            'Full-height Side Right': 'full-body-side-right',
            'Full-height 3/4 Left': 'full-body-34-left',
            'Full-height 3/4 Right': 'full-body-34-right'
          };
          
          const dbAngleType = angleTypeMap[angle.name] || angle.name.toLowerCase().replace(/\s+/g, '-');
          const base64 = await getModelImageAsBase64(selectedModel.code, dbAngleType);
          
          if (base64) {
            setImageUrl(base64);
          } else {
            setImageUrl(null);
          }
        } catch (error) {
          console.error(`Error loading angle preview for ${selectedModel.code} - ${angle.name}:`, error);
          setImageUrl(null);
        }
      } else {
        setImageUrl(null);
      }
    };
    
    loadAngleImage();
  }, [selectedModel, angle]);
  
  if (imageUrl) {
    return (
      <div className="h-16 rounded mb-1 overflow-hidden bg-gray-100">
        <img 
          src={imageUrl} 
          alt={`${selectedModel?.name || 'Model'} - ${angle.name}`}
          className="w-full h-full object-cover"
        />
      </div>
    );
  }
  
  // Fallback to generic preview or empty placeholder
  return <div className="h-16 bg-gray-100 rounded mb-1 flex items-center justify-center">
    <span className="text-xs text-gray-400">Select model</span>
  </div>;
};

export function ImageGeneratorV2() {
  const navigate = useNavigate();
  const { orgId, collectionId } = useParams();
  const { toast } = useToast();

  // Local state - declare these first before they're used in hooks
  const [activeTab, setActiveTab] = useState<'models' | 'v2'>('v2');
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [selectedProductAsset, setSelectedProductAsset] = useState<any>(null);
  const [flexibleInputs, setFlexibleInputs] = useState<FlexibleInput[]>([]);
  const [viewingImage, setViewingImage] = useState<GeneratedImage | null>(null);
  const [isImageDetailOpen, setIsImageDetailOpen] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false);
  const [isProductImageSelectorOpen, setIsProductImageSelectorOpen] = useState(false);
  const [pendingProduct, setPendingProduct] = useState<any>(null);
  const [showAllModels, setShowAllModels] = useState(false);
  const [showAllAngles, setShowAllAngles] = useState(false);
  const [isPromptHistoryOpen, setIsPromptHistoryOpen] = useState(false);

  // Additional V2 image slots for three-column layout
  const [additionalGarmentImage, setAdditionalGarmentImage] = useState<any>(null);

  // Hooks
  const {
    campaignModels,
    selectedModels,
    selectedAngles,
    modelImages,
    isLoadingModels,
    toggleModel,
    toggleAngle,
    selectAllModels,
    selectAllAngles,
    clearAllModels,
    clearAllAngles,
  } = useModelManagement();

  const {
    settings,
    isAdvancedMode,
    updateSetting,
    resetSettings,
    toggleAdvancedMode,
  } = useGenerationSettings();

  const {
    v2Images,
    isProcessingImages,
    handleImageUpload,
    removeImage,
    clearAllImages,
    hasAllRequiredImages,
  } = useV2ImageManagement();

  const {
    generatedImages,
    selectedImages,
    isLoadingImages,
    addGeneratedImage,
    toggleImageSelection,
    selectAllImages,
    clearSelection,
    deleteSelectedImages,
    refreshImages,
  } = useGeneratedImages(collectionId);

  const {
    moveImagesToCollection,
    isMovingImages,
  } = useImageSelection(collectionId);

  const {
    prompt,
    activeBlocks,
    isAnalyzingGarment,
    handleBlockToggle,
    addModelBlock,
    addAngleBlock,
    analyzeGarment,
    clearBlocks,
    updatePrompt,
    setActiveBlocks,
    promptHistory,
    loadModelWithCustomPrompts,
    loadAngleWithCustomPrompts,
    loadCustomAnglePrompt,
  } = usePromptBuilder({
    selectedModels,
    selectedAngles,
    v2Images,
    flexibleInputs,
    selectedProduct,
  });

  const {
    generate: generateV2,
    isGenerating: isGeneratingV2,
    progress: progressV2,
  } = useFashionLabImages({
    collectionId: collectionId || '',
    onComplete: (images) => {
      refreshImages();
    }
  });

  const { data: collection } = useCollection(collectionId!);
  const { data: organization } = useOrganization(orgId!);
  const { data: products = [] } = useProducts({ collectionId: collectionId! });
  const { data: assets = [] } = useAssets(collectionId!);

  // Handle product selection
  const handleProductSelect = useCallback(async (product: any) => {
    console.log('Product selected:', product);
    
    if (product) {
      // Store the product temporarily and open the image selector dialog
      setPendingProduct(product);
      setIsProductImageSelectorOpen(true);
    } else {
      // Clear selection
      setSelectedProduct(null);
      setSelectedProductAsset(null);
    }
  }, []);

  // Handle asset selection from the product
  const handleAssetSelect = useCallback(async (asset: any) => {
    console.log('Asset selected:', asset);
    
    // Set the selected product and asset now that we have chosen an asset
    setSelectedProduct(pendingProduct);
    setSelectedProductAsset(asset);
    setPendingProduct(null);
    
    if (asset) {
      // Ensure asset has collection_id from the product if not present
      const assetWithCollection = {
        ...asset,
        collection_id: asset.collection_id || pendingProduct?.collection_id || collectionId
      };
      const imageUrl = getAssetUrl(assetWithCollection, false); // Get full resolution
      
      console.log('Loading asset image from:', imageUrl);
      
      try {
        // Fetch the image and convert to base64
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status}`);
        }
        
        const blob = await response.blob();
        const reader = new FileReader();
        
        reader.onloadend = async () => {
          const base64 = reader.result as string;
          // Create file from blob
          const file = new File([blob], asset.file_name, { type: blob.type });
          
          // Update the product image slot (image_3)
          handleImageUpload(file, 'image_3');
          
          // Analyze the garment with OpenAI to create a garment block
          await analyzeGarment(file, `Product: ${pendingProduct?.name || 'Main Garment'}`);
          
          toast({
            title: "Product Image Loaded",
            description: `Successfully loaded and analyzed ${asset.file_name}`,
          });
        };
        
        reader.onerror = () => {
          console.error('FileReader error:', reader.error);
          toast({
            title: "Failed to process image",
            description: "Could not convert image to base64",
            variant: "destructive",
          });
        };
        
        reader.readAsDataURL(blob);
      } catch (error) {
        console.error('Failed to load asset image:', error);
        toast({
          title: "Failed to load image",
          description: error instanceof Error ? error.message : "Unknown error occurred",
          variant: "destructive",
        });
      }
    }
  }, [handleImageUpload, toast, pendingProduct]);

  // Load model face image when model is selected
  useEffect(() => {
    const loadModelFaceImage = async () => {
      if (selectedModels.length > 0 && activeTab === 'v2') {
        const modelId = selectedModels[0];
        const model = campaignModels.find(m => m.id === modelId);
        
        if (model) {
          try {
            // Try to load face-ai image first (white background for AI generation)
            // If not available, fall back to regular face image
            let faceImageBase64 = await getModelImageAsBase64(model.code, 'face-ai');
            
            if (!faceImageBase64) {
              console.log('No face-ai image found, trying regular face image');
              faceImageBase64 = await getModelImageAsBase64(model.code, 'face');
            }
            
            if (faceImageBase64) {
              console.log('Loading face image for model:', model.name);
              
              // Convert base64 to File object
              const response = await fetch(faceImageBase64);
              const blob = await response.blob();
              const file = new File([blob], `${model.code}_face.jpg`, { type: blob.type || 'image/jpeg' });
              
              // Update the face slot in V2 images
              await handleImageUpload(file, 'face');
              
              // Load the face angle's custom prompt if it exists
              const facePrompt = await loadCustomAnglePrompt(model.code, 'face');
              console.log('Face angle custom prompt:', facePrompt);
              
              // Use face angle prompt if available, otherwise fall back to model's prompt_text
              const promptToUse = facePrompt || model.promptText;
              await loadModelWithCustomPrompts(model.code, model.name, promptToUse);
              
              console.log('Face image loaded successfully, using prompt:', promptToUse);
            } else {
              console.log('No face image found for model:', model.name);
            }
          } catch (error) {
            console.error('Error loading model face image:', error);
          }
        }
      } else if (selectedModels.length === 0 && activeTab === 'v2') {
        // Clear face image when no model is selected
        removeImage('face');
      }
    };
    
    loadModelFaceImage();
  }, [selectedModels, activeTab, campaignModels, handleImageUpload, loadModelWithCustomPrompts, loadCustomAnglePrompt, removeImage]);

  // Load model angle image when angle is selected
  useEffect(() => {
    const loadModelAngleImage = async () => {
      if (selectedModels.length > 0 && selectedAngles.length > 0 && activeTab === 'v2') {
        const modelId = selectedModels[0];
        const angleName = selectedAngles[0];
        const model = campaignModels.find(m => m.id === modelId);
        
        if (model) {
          try {
            // Map angle names to database angle types
            const angleTypeMap: Record<string, string> = {
              'Half-body Front': 'half-body-front',
              'Half-body Back': 'half-body-back',
              'Half-body 3/4 Left': 'half-body-34-left',
              'Half-body 3/4 Right': 'half-body-34-right',
              'Full-height Front': 'full-body-front',
              'Full-height Back': 'full-body-back',
              'Full-height Side Left': 'full-body-side-left',
              'Full-height Side Right': 'full-body-side-right',
              'Full-height 3/4 Left': 'full-body-34-left',
              'Full-height 3/4 Right': 'full-body-34-right'
            };
            
            const dbAngleType = angleTypeMap[angleName] || angleName.toLowerCase().replace(/\s+/g, '-');
            console.log('Fetching angle image - Model code:', model.code, 'DB angle type:', dbAngleType, 'Original angle:', angleName);
            
            const angleImageBase64 = await getModelImageAsBase64(model.code, dbAngleType);
            
            if (angleImageBase64) {
              console.log('Loading angle image for model:', model.name, 'angle:', angleName);
              console.log('Base64 length:', angleImageBase64.length);
              
              // Convert base64 to File object
              const response = await fetch(angleImageBase64);
              const blob = await response.blob();
              console.log('Blob size:', blob.size, 'type:', blob.type);
              
              const file = new File([blob], `${model.code}_${dbAngleType}.jpg`, { type: blob.type || 'image/jpeg' });
              console.log('File created:', file.name, 'size:', file.size);
              
              // Update the image_2 slot (Model Body Shot) in V2 images
              console.log('Calling handleImageUpload for image_2 slot...');
              await handleImageUpload(file, 'image_2');
              console.log('After handleImageUpload - checking v2Images.image_2:', v2Images.image_2);
              
              // Add angle block to prompt
              const angleBlock = angleBlocks.find(a => a.name === angleName);
              if (angleBlock) {
                addAngleBlock({
                  name: angleBlock.name,
                  promptText: angleBlock.promptText
                });
              }
              
              console.log('Angle image loaded successfully into image_2 slot');
            } else {
              console.log('No angle image found for model:', model.name, 'angle:', angleName, 'dbAngleType:', dbAngleType);
            }
          } catch (error) {
            console.error('Error loading model angle image:', error);
          }
        }
      } else if (selectedAngles.length === 0 && activeTab === 'v2') {
        // Clear angle image when no angle is selected
        removeImage('image_2');
      }
    };
    
    loadModelAngleImage();
  }, [selectedModels, selectedAngles, activeTab, campaignModels, handleImageUpload, addAngleBlock, removeImage]);

  // Generate batch function
  const generateBatch = async () => {
    if (activeTab === 'v2') {
      if (!hasAllRequiredImages()) {
        toast({
          title: "Missing Images",
          description: "Please upload all 4 required images",
          variant: "destructive",
        });
        return;
      }

      try {
        // Extract model and angle info from selected models/angles or v2Images metadata
        const modelName = selectedModels.length > 0 
          ? campaignModels.find(m => m.id === selectedModels[0])?.name || 'V2 Model'
          : 'V2 Model';
        const angleName = selectedAngles.length > 0 
          ? selectedAngles[0] 
          : 'front';

        await generateV2({
          prompt: prompt || 'fashion photography',
          faceImage: v2Images.face!.base64!,
          image2: v2Images.image_2!.base64!,
          image3: v2Images.image_3!.base64!,
          image4: v2Images.image_4!.base64!,
          seed1: settings.seed || undefined,
          seed2: settings.seed || undefined,
          seed3: settings.seed || undefined,
          seed4: settings.seed || undefined,
          numImages: settings.numImages,
          metadata: {
            source: 'image-generator-v2',
            modelName,
            angle: angleName,
            activeBlocks,
            flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
          }
        });
      } catch (error) {
        console.error('V2 generation error:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate images with V2 API",
          variant: "destructive",
        });
      }
    } else {
      // Model-based generation logic (same as before)
      if (selectedModels.length === 0 || selectedAngles.length === 0) {
        toast({
          title: "Selection Required",
          description: "Please select at least one model and one angle",
          variant: "destructive",
        });
        return;
      }

      let targetCollectionId = collectionId;
      if (!targetCollectionId) {
        targetCollectionId = await getOrCreateDemoCollection();
        if (!targetCollectionId) {
          toast({
            title: "Error",
            description: "Failed to create demo collection",
            variant: "destructive",
          });
          return;
        }
      }

      const combinations = selectedModels.flatMap(modelId => 
        selectedAngles.map(angle => ({ modelId, angle }))
      );

      try {
        for (const { modelId, angle } of combinations) {
          const model = campaignModels.find(m => m.id === modelId);
          if (!model) continue;

          const angleData = angleBlocks.find(a => a.name === angle);
          if (!angleData) continue;

          const modelImageKey = `${modelId}-${angle}`;
          const modelImageBase64 = modelImages[modelImageKey];

          if (!modelImageBase64) {
            console.warn(`No image found for ${modelId} - ${angle}`);
            continue;
          }

          // Use the built prompt from prompt builder or fall back to basic prompt
          const finalPrompt = prompt || `${model.promptText}, ${angleData.promptText}`;

          await generateV2({
            prompt: finalPrompt,
            faceImage: modelImageBase64,
            // Use actual uploaded images if available, otherwise use model image
            image2: v2Images.image_2?.base64 || modelImageBase64,
            image3: v2Images.image_3?.base64 || modelImageBase64,
            image4: v2Images.image_4?.base64 || modelImageBase64,
            seed1: settings.seed || undefined,
            seed2: settings.seed || undefined,
            seed3: settings.seed || undefined,
            seed4: settings.seed || undefined,
            numImages: settings.numImages,
            metadata: {
              source: 'image-generator',
              modelId,
              modelName: model.name,
              angle: angle,
              loraName: model.lora,
              selectedProduct: selectedProduct?.id,
              selectedModels,
              selectedAngles,
              flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
            }
          });
        }
      } catch (error) {
        console.error('Batch generation error:', error);
        toast({
          title: "Generation Failed",
          description: "An error occurred while generating images",
          variant: "destructive",
        });
      }
    }
  };

  const handleMoveToCollection = async () => {
    const selectedImageIds = Array.from(selectedImages);
    
    if (selectedImageIds.length === 0) return;

    const success = await moveImagesToCollection(selectedImageIds);
    
    if (success) {
      clearSelection();
      await refreshImages();
    }
  };

  const handleDownloadImage = async (image: GeneratedImage) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${image.model}_${image.angle}_${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the image",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate(`/organizations/${orgId}/collections/${collectionId}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Collection
              </Button>
              <div>
                <h1 className="text-xl font-semibold">FashionLab Image Generator</h1>
                <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                  <span>Campaign: {collection?.name || 'Loading...'}</span>
                  <span>•</span>
                  <span>Products: {products.length}/5</span>
                  <span>•</span>
                  <span>Resolution: 1920x1080 (9:16)</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>{campaignModels.length} Models</span>
              <span>×</span>
              <span>{angleBlocks.length} Angles</span>
              <span>=</span>
              <span>{campaignModels.length * angleBlocks.length} images/product</span>
            </div>
          </div>
        </div>
      </div>

      {/* Three Column Layout */}
      <div className="flex h-[calc(100vh-73px)]">
        {/* Left Column - Selection */}
        <div className="w-80 bg-white border-r flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-medium">Selection</h2>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* Target Garment */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Target Garment</Label>
              <Card className={cn(
                "cursor-pointer transition-all hover:shadow-md",
                selectedProduct && "ring-2 ring-blue-500"
              )}>
                <CardContent className="p-4">
                  {selectedProduct ? (
                    <div className="space-y-2">
                      {selectedProductAsset ? (
                        <img 
                          src={getAssetUrl({
                            ...selectedProductAsset,
                            collection_id: selectedProductAsset.collection_id || selectedProduct?.collection_id || collectionId
                          }, true)} 
                          alt={selectedProduct.name}
                          className="w-full h-32 object-cover rounded"
                        />
                      ) : selectedProduct.assets && selectedProduct.assets[0] ? (
                        <img 
                          src={getAssetUrl({
                            ...selectedProduct.assets[0],
                            collection_id: selectedProduct.assets[0].collection_id || selectedProduct?.collection_id || collectionId
                          }, true)} 
                          alt={selectedProduct.name}
                          className="w-full h-32 object-cover rounded"
                        />
                      ) : (
                        <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-gray-400 text-sm">No image</span>
                        </div>
                      )}
                      <p className="text-sm font-medium">{selectedProduct.name}</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => setIsProductSelectorOpen(true)}
                      >
                        Change Product
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p className="text-sm">No product selected</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2"
                        onClick={() => setIsProductSelectorOpen(true)}
                      >
                        Select Product
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Additional Garments */}
            <div>
              <Label className="text-sm font-medium mb-2 block">Additional Garments</Label>
              <label className="block">
                <input
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={async (e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      // Store the image as image_4 for the API
                      await handleImageUpload(file, 'image_4');
                      // Analyze the garment for prompt description
                      await analyzeGarment(file, 'Additional Garment');
                    }
                  }}
                  disabled={isAnalyzingGarment}
                />
                <div className="border-2 border-dashed rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors">
                  {isAnalyzingGarment ? (
                    <>
                      <Loader2 className="h-8 w-8 mx-auto text-gray-400 mb-2 animate-spin" />
                      <p className="text-sm text-gray-500">Analyzing garment...</p>
                    </>
                  ) : (
                    <>
                      <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500">Upload & Analyze with AI</p>
                    </>
                  )}
                </div>
              </label>
            </div>

            {/* Models */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Models</Label>
                {campaignModels.length > 4 && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="h-auto p-0 text-xs"
                    onClick={() => setShowAllModels(!showAllModels)}
                  >
                    {showAllModels ? 'Show less' : `Show all (${campaignModels.length})`}
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                {isLoadingModels ? (
                  <div className="col-span-2 text-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  </div>
                ) : campaignModels.length === 0 ? (
                  <div className="col-span-2 text-center py-4 text-gray-500">
                    <p className="text-sm">No models available</p>
                  </div>
                ) : (
                  (showAllModels ? campaignModels : campaignModels.slice(0, 4)).map((model) => (
                    <Card 
                      key={model.id}
                      className={cn(
                        "cursor-pointer transition-all hover:shadow-md",
                        selectedModels.includes(model.id) && "ring-2 ring-blue-500"
                      )}
                      onClick={async () => {
                        toggleModel(model.id);
                        if (model.code) {
                          await loadModelWithCustomPrompts(model.code, model.name, model.promptText);
                        } else {
                          addModelBlock(model);
                        }
                      }}
                    >
                      <CardContent className="p-2">
                        {model.image ? (
                          <img 
                            src={model.image} 
                            alt={model.name}
                            className="w-full h-24 object-cover rounded mb-1"
                          />
                        ) : (
                          <div className="w-full h-24 bg-gray-100 rounded mb-1 flex items-center justify-center">
                            <span className="text-gray-400 text-xs">No image</span>
                          </div>
                        )}
                        <p className="text-xs text-center">{model.shortName}</p>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>

            {/* Angles */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label className="text-sm font-medium">Angles</Label>
                {angleBlocks.length > 4 && (
                  <Button 
                    variant="link" 
                    size="sm" 
                    className="h-auto p-0 text-xs"
                    onClick={() => setShowAllAngles(!showAllAngles)}
                  >
                    {showAllAngles ? 'Show less' : `Show all (${angleBlocks.length})`}
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                {(showAllAngles ? angleBlocks : angleBlocks.slice(0, 4)).map((angle) => (
                  <Card 
                    key={angle.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      selectedAngles.includes(angle.name) && "ring-2 ring-green-500"
                    )}
                    onClick={async () => {
                      toggleAngle(angle.name);
                      if (selectedModels.length > 0) {
                        const model = campaignModels.find(m => m.id === selectedModels[0]);
                        if (model?.code) {
                          await loadAngleWithCustomPrompts(model.code, angle.name, angle.promptText);
                        } else {
                          addAngleBlock(angle);
                        }
                      } else {
                        addAngleBlock(angle);
                      }
                    }}
                  >
                    <CardContent className="p-2">
                      <AnglePreview
                        angle={angle}
                        selectedModel={selectedModels.length > 0 ? campaignModels.find(m => m.id === selectedModels[0]) : undefined}
                      />
                      <p className="text-xs text-center line-clamp-2">{angle.name}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Middle Column - Generated Images */}
        <div className="flex-1 flex flex-col">
          <div className="p-4 bg-white border-b">
            <div className="flex items-center justify-between">
              <h2 className="font-medium">V2 API Generated Images</h2>
              <SelectionToolbar
                selectedCount={selectedImages.size}
                totalCount={generatedImages.length}
                onSelectAll={selectAllImages}
                onClearSelection={clearSelection}
                onDeleteSelected={deleteSelectedImages}
                onMoveToCollection={handleMoveToCollection}
                showMoveToCollection={!!collectionId}
                isMovingImages={isMovingImages}
              />
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-6">
            <GeneratedImagesGrid
              images={generatedImages}
              selectedImages={selectedImages}
              onToggleSelection={toggleImageSelection}
              onViewImage={(image) => {
                setViewingImage(image);
                setIsImageDetailOpen(true);
              }}
              onDownloadImage={handleDownloadImage}
              isLoading={isLoadingImages}
            />
          </div>
        </div>

        {/* Right Column - Prompt Builder */}
        <div className="w-96 bg-white border-l flex flex-col">
          <div className="p-4 border-b flex items-center justify-between">
            <h2 className="font-medium">Prompt Builder</h2>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => setShowHistory(!showHistory)}
              >
                <History className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon">
                <Save className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {/* Model Photos Upload */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Model Photos</CardTitle>
                <p className="text-xs text-gray-500">Upload photos to customize your model generation</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  {/* Model Face Photo */}
                  <div>
                    <Label className="text-xs mb-1 block">Model Face Photo</Label>
                    {v2Images.face ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.face.preview} 
                          alt="Face"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('face')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'face');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Upload Face Photo</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Model Body Shot */}
                  <div>
                    <Label className="text-xs mb-1 block">Model Body Shot</Label>
                    {v2Images.image_2 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_2.preview} 
                          alt="Body"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_2')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_2');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Model Body Shot</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Product Image */}
                  <div>
                    <Label className="text-xs mb-1 block">Product Image</Label>
                    {v2Images.image_3 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_3.preview} 
                          alt="Product"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_3')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_3');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Product Image</p>
                        </div>
                      </label>
                    )}
                  </div>

                  {/* Additional Item */}
                  <div>
                    <Label className="text-xs mb-1 block">Additional Item</Label>
                    {v2Images.image_4 ? (
                      <div className="relative group">
                        <img 
                          src={v2Images.image_4.preview} 
                          alt="Additional"
                          className="w-full h-20 object-cover rounded border"
                        />
                        <button
                          onClick={() => removeImage('image_4')}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </div>
                    ) : (
                      <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded cursor-pointer hover:border-gray-400">
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(file, 'image_4');
                          }}
                          className="hidden"
                        />
                        <div className="text-center">
                          <Upload className="w-4 h-4 text-gray-400 mx-auto mb-1" />
                          <p className="text-xs text-gray-500">Additional Item</p>
                        </div>
                      </label>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Prompt Builder */}
            <PromptBuilder
              prompt={prompt}
              onPromptChange={updatePrompt}
              activeBlocks={activeBlocks}
              onActiveBlocksChange={setActiveBlocks}
              onShowHistory={() => setIsPromptHistoryOpen(true)}
            />

            {/* Technical Settings */}
            <TechnicalSettings
              settings={settings}
              onSettingsChange={(newSettings) => {
                Object.entries(newSettings).forEach(([key, value]) => {
                  updateSetting(key as any, value);
                });
              }}
              totalImages={settings.numImages * (selectedModels.length || 1) * (selectedAngles.length || 1)}
              className="mt-4"
            />

            {/* Extra Elements */}
            <FlexibleInputBox
              inputs={flexibleInputs}
              onChange={setFlexibleInputs}
            />

            {/* Generate Button */}
            <Button
              className="w-full"
              size="lg"
              onClick={generateBatch}
              disabled={
                isGeneratingV2 || isMovingImages ||
                (activeTab === 'models' && (selectedModels.length === 0 || selectedAngles.length === 0)) ||
                (activeTab === 'v2' && !hasAllRequiredImages())
              }
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isGeneratingV2 ? 'Generating...' : 'Generate V2 Images'}
            </Button>

            {/* Progress */}
            {isGeneratingV2 && (
              <div className="space-y-1">
                <Progress value={progressV2} className="h-2" />
                <p className="text-xs text-center text-gray-600">
                  Processing API request...
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Image Detail Dialog */}
      <ImageDetailDialog
        image={viewingImage}
        isOpen={isImageDetailOpen}
        onClose={() => {
          setIsImageDetailOpen(false);
          setViewingImage(null);
        }}
      />

      {/* Product Selector Dialog */}
      <ProductSelectorDialog
        isOpen={isProductSelectorOpen}
        onClose={() => setIsProductSelectorOpen(false)}
        collectionId={collectionId || ''}
        selectedProductId={selectedProduct?.id}
        onProductSelect={handleProductSelect}
      />

      {/* Product Image Selector Dialog */}
      <ProductImageSelectorDialog
        isOpen={isProductImageSelectorOpen}
        onClose={() => {
          setIsProductImageSelectorOpen(false);
          setPendingProduct(null);
        }}
        product={pendingProduct}
        onImageSelect={handleAssetSelect}
      />

      {/* Prompt History Dialog */}
      <PromptHistoryDialog
        isOpen={isPromptHistoryOpen}
        onClose={() => setIsPromptHistoryOpen(false)}
        promptHistory={promptHistory.map((p, i) => ({ prompt: p, timestamp: new Date(Date.now() - i * 3600000) }))}
        onSelectPrompt={(prompt) => {
          updatePrompt(prompt);
        }}
      />
    </div>
  );
}