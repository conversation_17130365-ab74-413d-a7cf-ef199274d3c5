import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Card, CardContent } from '../../../components/ui/card';
import { Search, Check } from 'lucide-react';
import { useProducts } from '../../../components/common/hooks/useProducts';
import { getAssetUrl } from '../../../components/common/utils/utils';
import { cn } from '../../../components/common/utils/utils';

interface ProductSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  collectionId: string;
  selectedProductId?: string;
  onProductSelect: (product: any) => void;
}

export function ProductSelectorDialog({
  isOpen,
  onClose,
  collectionId,
  selectedProductId,
  onProductSelect,
}: ProductSelectorDialogProps) {
  const [searchTerm, setSearchTerm] = useState('');
  
  const { data: products = [], isLoading } = useProducts(collectionId);
  
  const filteredProducts = products.filter(product =>
    product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.sku?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (product: any) => {
    onProductSelect(product);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Select Target Garment</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-3 gap-3 max-h-[50vh] overflow-y-auto">
            {isLoading ? (
              <div className="col-span-3 text-center py-8 text-gray-500">
                Loading products...
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="col-span-3 text-center py-8 text-gray-500">
                No products found
              </div>
            ) : (
              filteredProducts.map((product) => {
                const primaryAsset = product.assets?.[0];
                const imageUrl = primaryAsset ? getAssetUrl(primaryAsset, true) : null;
                
                return (
                  <Card
                    key={product.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      selectedProductId === product.id && "ring-2 ring-blue-500"
                    )}
                    onClick={() => handleSelect(product)}
                  >
                    <CardContent className="p-3">
                      {imageUrl ? (
                        <img
                          src={imageUrl}
                          alt={product.name}
                          className="w-full h-32 object-cover rounded mb-2"
                        />
                      ) : (
                        <div className="w-full h-32 bg-gray-100 rounded mb-2 flex items-center justify-center">
                          <span className="text-gray-400 text-sm">No image</span>
                        </div>
                      )}
                      <h4 className="text-sm font-medium line-clamp-1">{product.name}</h4>
                      {product.sku && (
                        <p className="text-xs text-gray-500">{product.sku}</p>
                      )}
                      {selectedProductId === product.id && (
                        <div className="mt-1 flex items-center text-blue-600">
                          <Check className="h-3 w-3 mr-1" />
                          <span className="text-xs">Selected</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>

          <div className="flex justify-end gap-2 pt-2 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {selectedProductId && (
              <Button variant="outline" onClick={() => handleSelect(null)}>
                Clear Selection
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}