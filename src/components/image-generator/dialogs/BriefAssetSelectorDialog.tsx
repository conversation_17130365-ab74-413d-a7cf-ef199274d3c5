import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../ui/dialog';
import { Button } from '../../ui/button';
import { Input } from '../../ui/input';
import { Badge } from '../../ui/badge';
import { ScrollArea } from '../../ui/scroll-area';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { Search, Check, X, Loader2, Package } from 'lucide-react';
import { useAssets } from '../../../contexts/hooks/queries/useAssets';
import { cn } from '../../common/utils/utils';
import type { Asset } from '../../../types/database.types';

interface BriefAssetSelectorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  collectionId: string;
  onSelectAsset: (asset: Asset) => void;
  selectedAssetId?: string;
  assetType?: 'garment' | 'additional' | 'all';
}

export function BriefAssetSelectorDialog({
  open,
  onOpenChange,
  collectionId,
  onSelectAsset,
  selectedAssetId,
  assetType = 'all',
}: BriefAssetSelectorDialogProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState<'all' | 'garments' | 'additional'>('all');
  
  const { data: assets, isLoading } = useAssets(collectionId);

  // Filter assets based on type and search
  const filteredAssets = React.useMemo(() => {
    if (!assets) return [];
    
    let filtered = assets;
    
    // Filter by tab selection
    if (selectedTab === 'garments') {
      filtered = filtered.filter(asset => 
        asset.tags?.includes('garment') || 
        asset.tags?.includes('product') ||
        asset.metadata?.type === 'garment'
      );
    } else if (selectedTab === 'additional') {
      filtered = filtered.filter(asset => 
        asset.tags?.includes('additional') || 
        asset.tags?.includes('accessory') ||
        asset.metadata?.type === 'additional'
      );
    }
    
    // Filter by asset type prop if specified
    if (assetType === 'garment') {
      filtered = filtered.filter(asset => 
        asset.tags?.includes('garment') || 
        asset.tags?.includes('product') ||
        asset.metadata?.type === 'garment'
      );
    } else if (assetType === 'additional') {
      filtered = filtered.filter(asset => 
        asset.tags?.includes('additional') || 
        asset.tags?.includes('accessory') ||
        asset.metadata?.type === 'additional'
      );
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(asset => 
        asset.tags?.some(tag => tag.toLowerCase().includes(query)) ||
        asset.metadata?.description?.toLowerCase().includes(query) ||
        asset.product?.name?.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  }, [assets, selectedTab, assetType, searchQuery]);

  const handleSelectAsset = (asset: Asset) => {
    onSelectAsset(asset);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Select Asset from Brief</DialogTitle>
          <DialogDescription>
            Choose an asset from the campaign brief to use in your image generation
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col gap-4 overflow-hidden">
          {/* Search bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search assets by tag or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Tabs for filtering */}
          {assetType === 'all' && (
            <Tabs value={selectedTab} onValueChange={(v) => setSelectedTab(v as 'all' | 'garments' | 'additional')}>
              <TabsList className="grid grid-cols-3 w-full">
                <TabsTrigger value="all">All Assets</TabsTrigger>
                <TabsTrigger value="garments">Garments</TabsTrigger>
                <TabsTrigger value="additional">Additional</TabsTrigger>
              </TabsList>
            </Tabs>
          )}

          {/* Asset grid */}
          <ScrollArea className="flex-1">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              </div>
            ) : filteredAssets.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <Package className="h-12 w-12 text-gray-300 mb-3" />
                <p className="text-gray-500">No assets found</p>
                <p className="text-sm text-gray-400 mt-1">
                  {searchQuery ? 'Try adjusting your search' : 'Upload assets to the brief first'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-3 md:grid-cols-4 gap-4 p-1">
                {filteredAssets.map((asset) => (
                  <button
                    key={asset.id}
                    onClick={() => handleSelectAsset(asset)}
                    className={cn(
                      "relative group overflow-hidden rounded-lg border-2 transition-all",
                      selectedAssetId === asset.id
                        ? "border-blue-500 shadow-lg"
                        : "border-gray-200 hover:border-gray-400"
                    )}
                  >
                    {/* Asset thumbnail */}
                    <div className="aspect-square bg-gray-100">
                      {asset.thumbnail_url ? (
                        <img
                          src={asset.thumbnail_url}
                          alt={asset.product?.name || 'Asset'}
                          className="w-full h-full object-cover"
                        />
                      ) : asset.storage_path ? (
                        <img
                          src={`${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/assets/${asset.storage_path}`}
                          alt={asset.product?.name || 'Asset'}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="h-8 w-8 text-gray-300" />
                        </div>
                      )}
                    </div>

                    {/* Selection indicator */}
                    {selectedAssetId === asset.id && (
                      <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                        <Check className="h-3 w-3" />
                      </div>
                    )}

                    {/* Asset info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
                      <p className="text-white text-xs truncate">
                        {asset.product?.name || 'Untitled'}
                      </p>
                      {asset.tags && asset.tags.length > 0 && (
                        <div className="flex gap-1 mt-1 flex-wrap">
                          {asset.tags.slice(0, 2).map((tag, idx) => (
                            <Badge 
                              key={idx} 
                              variant="secondary" 
                              className="text-[10px] px-1 py-0 bg-white/20 text-white border-0"
                            >
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <span className="text-white text-sm font-medium">Select</span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}