import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { ScrollArea } from '../../../components/ui/scroll-area';
import { Card, CardContent } from '../../../components/ui/card';
import { Copy, CheckCircle } from 'lucide-react';
// import { formatDistanceToNow } from 'date-fns';

interface PromptHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  promptHistory: { prompt: string; timestamp: Date }[];
  onSelectPrompt: (prompt: string) => void;
}

export function PromptHistoryDialog({
  isOpen,
  onClose,
  promptHistory,
  onSelectPrompt,
}: PromptHistoryDialogProps) {
  const [copiedIndex, setCopiedIndex] = React.useState<number | null>(null);

  const handleCopy = async (prompt: string, index: number) => {
    try {
      await navigator.clipboard.writeText(prompt);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      console.error('Failed to copy prompt:', error);
    }
  };

  const handleSelect = (prompt: string) => {
    onSelectPrompt(prompt);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Prompt History</DialogTitle>
          <DialogDescription>
            Select a previous prompt to reuse it or copy it to clipboard
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="h-[500px] pr-4">
          <div className="space-y-3">
            {promptHistory.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">
                No prompt history yet. Start generating images to build your history.
              </p>
            ) : (
              promptHistory.map((item, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">
                        {new Date(item.timestamp).toLocaleString()}
                      </p>
                      <p className="text-sm leading-relaxed">{item.prompt}</p>
                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSelect(item.prompt)}
                        >
                          Use This Prompt
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleCopy(item.prompt, index)}
                        >
                          {copiedIndex === index ? (
                            <>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-3 w-3 mr-1" />
                              Copy
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}