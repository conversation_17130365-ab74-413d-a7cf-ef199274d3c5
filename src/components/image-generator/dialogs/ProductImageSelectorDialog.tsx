import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogTitle } from '../../../components/ui/dialog';
import { Button } from '../../../components/ui/button';
import { Card, CardContent } from '../../../components/ui/card';
import { useAssets } from '../../../components/common/hooks/useAssets';
import { getAssetUrl } from '../../../components/common/utils/utils';
import { cn } from '../../../components/common/utils/utils';
import { Loader2 } from 'lucide-react';

interface ProductImageSelectorDialogProps {
  isOpen: boolean;
  onClose: () => void;
  product: any;
  onImageSelect: (asset: any) => void;
}

export function ProductImageSelectorDialog({
  isOpen,
  onClose,
  product,
  onImageSelect,
}: ProductImageSelectorDialogProps) {
  // Fetch assets for the collection and filter by product
  const { data: allAssets = [], isLoading } = useAssets({
    collectionId: product?.collection_id,
    enabled: !!product?.collection_id
  });
  
  // Filter assets that belong to this product
  const productAssets = allAssets.filter(asset => asset.product_id === product?.id);
  
  const handleSelectImage = (asset: any) => {
    onImageSelect(asset);
    onClose();
  };

  if (!product) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Select Product Image</DialogTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Choose which image of {product.name} to use as the main garment
          </p>
        </DialogHeader>
        
        <div className="mt-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading images...</span>
            </div>
          ) : productAssets.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <p>No images found for this product</p>
              <p className="text-sm mt-2">Please upload images to the product first</p>
            </div>
          ) : (
            <div className="grid grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
              {productAssets.map((asset) => {
                const imageUrl = getAssetUrl(asset, false); // Get full resolution
                const thumbnailUrl = getAssetUrl(asset, true); // Get thumbnail for preview
                
                return (
                  <Card
                    key={asset.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-lg hover:ring-2 hover:ring-blue-500",
                      "overflow-hidden"
                    )}
                    onClick={() => handleSelectImage(asset)}
                  >
                    <CardContent className="p-0">
                      <div className="aspect-square relative">
                        <img
                          src={thumbnailUrl}
                          alt={asset.file_name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="p-3">
                        <p className="text-sm font-medium truncate">{asset.file_name}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {asset.metadata?.description || 'No description available'}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
          
          <div className="flex justify-end gap-2 mt-6 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}