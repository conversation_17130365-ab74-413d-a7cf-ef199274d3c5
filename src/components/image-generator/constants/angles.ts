// Angles with hidden prompt text
export const angleBlocks = [
  // Half-body angles
  { id: 1, name: 'Half-body Front', promptText: 'half body front facing shot' },
  { id: 2, name: 'Half-body Back', promptText: 'half body shot from behind' },
  { id: 3, name: 'Half-body 3/4 Left', promptText: 'half body three quarter angle left' },
  { id: 4, name: 'Half-body 3/4 Right', promptText: 'half body three quarter angle right' },
  // Full-height angles
  { id: 5, name: 'Full-height Front', promptText: 'full height front facing shot' },
  { id: 6, name: 'Full-height Back', promptText: 'full height shot from behind' },
  { id: 7, name: 'Full-height Side Left', promptText: 'full height left side profile' },
  { id: 8, name: 'Full-height Side Right', promptText: 'full height right side profile' },
  { id: 9, name: 'Full-height 3/4 Left', promptText: 'full height three quarter angle left' },
  { id: 10, name: 'Full-height 3/4 Right', promptText: 'full height three quarter angle right' }
];

// Angle preview image mapping (for UI display only)
export const anglePreviewMapping: Record<string, string> = {
  'Half-body Front': 'images/angles/halfbody_front.jpg',
  'Half-body Back': 'images/angles/halfbody_back.jpg',
  'Half-body 3/4 Left': 'images/angles/halfbody_3:4_left.jpg',
  'Half-body 3/4 Right': 'images/angles/halfbody_3:4_right.jpg',
  'Full-height Front': 'images/angles/fullheight_front.jpg',
  'Full-height Back': 'images/angles/fullheight_back.jpg',
  'Full-height Side Left': 'images/angles/fullheight_side_left.jpg',
  'Full-height Side Right': 'images/angles/fullheight_side_right.jpg',
  'Full-height 3/4 Left': 'images/angles/fullheight_3:4_left.jpg',
  'Full-height 3/4 Right': 'images/angles/fullheight_3:4_right.jpg'
};