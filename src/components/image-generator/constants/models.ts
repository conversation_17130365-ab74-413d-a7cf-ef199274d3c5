// Static fallback models (used if dynamic model library is empty)
export const staticCampaignModels = [
  { 
    id: 'S', 
    name: 'Model S - Scandinavian',
    shortName: 'Model S',
    description: 'Blond scandinavian young woman in her twenties, thin and petite, not too tall',
    promptText: 'this scandinavian fashion model with blonde hair, young woman in her twenties, thin and petite build',
    lora: 'bubbleroom_model_s_v1',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2UzZjJmZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiMxOTc2ZDIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIFM8L3RleHQ+PC9zdmc+',
  },
  { 
    id: 'M', 
    name: 'Model M - European',
    shortName: 'Model M',
    description: 'European looking white woman with mid length dark brown hair and brown eyes, casual and confident',
    promptText: 'this european fashion model with mid-length dark brown hair, brown eyes, casual and confident demeanor',
    lora: 'bubbleroom_model_m_v1',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2ZjZTRlYyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNjMjE4NWIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIE08L3RleHQ+PC9zdmc+',
  },
  { 
    id: 'L', 
    name: 'Model L - African American',
    shortName: 'Model L',
    description: 'African American woman in her thirties, wide hips and tall, long black hair, cheerful and casual',
    promptText: 'this african american fashion model, woman in her thirties, tall with wide hips, long black hair, cheerful expression',
    lora: 'bubbleroom_model_l_v1',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2U4ZjVlOSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiMzODhlM2MiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIEw8L3RleHQ+PC9zdmc+',
  },
  { 
    id: 'XL', 
    name: 'Model XL - Latino',
    shortName: 'Model XL',
    description: 'Latino woman in her forties, wide hips and sand watch figure, feminine attitude, long black hair',
    promptText: 'this latino fashion model, woman in her forties, hourglass figure with wide hips, long black hair, feminine and graceful',
    lora: 'bubbleroom_model_xl_v1',
    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2ZmZjNlMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNlNjUxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIFhMPC90ZXh0Pjwvc3ZnPg==',
  }
];

// Static model image file mapping (fallback when dynamic library is not available)
export const staticModelImageMapping: Record<string, Record<string, string>> = {
  'S': {
    // Face image
    face: 'Library Models/Small/S_face.webp',
    // Half-body angles
    'Half-body Front': 'Library Models/Small/S_half body face on.jpg',
    'Half-body Back': 'Library Models/Small/S_hlafbody_backside.jpg', // Note: typo in filename
    'Half-body 3/4 Left': 'Library Models/Small/S_half body_3_4angle_left.jpg',
    'Half-body 3/4 Right': 'Library Models/Small/S_half body_3_4angle right.jpg',
    // Full-height angles
    'Full-height Front': 'Library Models/Small/S_fullbody_faceonjpg.jpg', // Note: missing dot
    'Full-height Back': 'Library Models/Small/S_fullbody_backside.jpg',
    'Full-height Side Left': 'Library Models/Small/S_fullbody_side_left.jpg',
    'Full-height Side Right': 'Library Models/Small/S_fullbody_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/Small/S_full body_3_4angle_left.jpg',
    'Full-height 3/4 Right': 'Library Models/Small/S_fullbody_3_4angle-right.jpg'
  },
  // Placeholder mappings for other models - these would need actual file paths
  'M': {
    face: 'Library Models/m_face.jpg', // Placeholder - update with actual paths
    'Half-body Front': 'Library Models/m_half_front.jpg',
    'Half-body Back': 'Library Models/m_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/m_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/m_half_34_right.jpg',
    'Full-height Front': 'Library Models/m_full_front.jpg',
    'Full-height Back': 'Library Models/m_full_back.jpg',
    'Full-height Side Left': 'Library Models/m_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/m_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/m_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/m_full_34_right.jpg'
  },
  'L': {
    face: 'Library Models/l_face.jpg',
    'Half-body Front': 'Library Models/l_half_front.jpg',
    'Half-body Back': 'Library Models/l_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/l_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/l_half_34_right.jpg',
    'Full-height Front': 'Library Models/l_full_front.jpg',
    'Full-height Back': 'Library Models/l_full_back.jpg',
    'Full-height Side Left': 'Library Models/l_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/l_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/l_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/l_full_34_right.jpg'
  },
  'XL': {
    face: 'Library Models/xl_face.jpg',
    'Half-body Front': 'Library Models/xl_half_front.jpg',
    'Half-body Back': 'Library Models/xl_half_back.jpg',
    'Half-body 3/4 Left': 'Library Models/xl_half_34_left.jpg',
    'Half-body 3/4 Right': 'Library Models/xl_half_34_right.jpg',
    'Full-height Front': 'Library Models/xl_full_front.jpg',
    'Full-height Back': 'Library Models/xl_full_back.jpg',
    'Full-height Side Left': 'Library Models/xl_full_side_left.jpg',
    'Full-height Side Right': 'Library Models/xl_full_side_right.jpg',
    'Full-height 3/4 Left': 'Library Models/xl_full_34_left.jpg',
    'Full-height 3/4 Right': 'Library Models/xl_full_34_right.jpg'
  }
};