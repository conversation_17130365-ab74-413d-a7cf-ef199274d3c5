import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '../../components/ui/button';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../components/ui/tabs';
import { Progress } from '../../components/ui/progress';
import { useToast } from '../../components/ui/use-toast';
import { <PERSON>Lef<PERSON>, Sparkles, Save } from 'lucide-react';

// Import hooks
import { useModelManagement } from './hooks/useModelManagement';
import { useGenerationSettings } from './hooks/useGenerationSettings';
import { useV2ImageManagement } from './hooks/useV2ImageManagement';
import { useGeneratedImages } from './hooks/useGeneratedImages';
import { useImageSelection } from './hooks/useImageSelection';
import { useFashionLabImages } from '../../hooks/useFashionLabImages';
import { useCollection } from '../../components/common/hooks/useCollections';
import { useOrganization } from '../../components/common/hooks/useOrganizations';
import { getOrCreateDemoCollection } from '../../utils/demoHelpers';
import { openAIService } from '../../services/openaiService';
import { getModelImageAsBase64 } from '../../hooks/useModelLibrary';
import { supabase } from '../../components/common/utils/supabase';

// Import components
import { ModelSelection } from './sidebar/ModelSelection';
import { AngleSelection } from './sidebar/AngleSelection';
import { GenerationSettingsComponent } from './sidebar/GenerationSettings';
import { V2ImageUploader } from './sidebar/V2ImageUploader';
import { GeneratedImagesGrid } from './main-content/GeneratedImagesGrid';
import { SelectionToolbar } from './main-content/SelectionToolbar';
import { ImageDetailDialog } from './dialogs/ImageDetailDialog';
import { FlexibleInputBox, FlexibleInput } from '../image-generator/FlexibleInputBox';

// Import constants and types
import { angleBlocks } from './constants';
import type { GeneratedImage } from './types';

export function ImageGenerator() {
  const navigate = useNavigate();
  const { orgId, collectionId } = useParams();
  const { toast } = useToast();

  // Hooks
  const {
    campaignModels,
    selectedModels,
    selectedAngles,
    modelImages,
    isLoadingModels,
    toggleModel,
    toggleAngle,
    selectAllModels,
    selectAllAngles,
    clearAllModels,
    clearAllAngles,
  } = useModelManagement();

  const {
    settings,
    isAdvancedMode,
    updateSetting,
    resetSettings,
    toggleAdvancedMode,
  } = useGenerationSettings();

  const {
    v2Images,
    isProcessingImages,
    handleImageUpload,
    removeImage,
    clearAllImages,
    hasAllRequiredImages,
  } = useV2ImageManagement();

  const {
    generatedImages,
    selectedImages,
    isLoadingImages,
    addGeneratedImage,
    toggleImageSelection,
    selectAllImages,
    clearSelection,
    deleteSelectedImages,
    refreshImages,
  } = useGeneratedImages(collectionId);

  const {
    moveImagesToCollection,
    isMovingImages,
  } = useImageSelection(collectionId);

  const {
    generateImages: generateV2,
    isGenerating: isGeneratingV2,
    progress: progressV2,
    error: errorV2,
  } = useFashionLabImages({
    collectionId: collectionId || '',
    onComplete: (images) => {
      // Refresh the generated images list after new images are created
      refreshImages();
    }
  });

  const { data: collection } = useCollection(collectionId!);
  const { data: organization } = useOrganization(orgId!);

  // Local state
  const [activeTab, setActiveTab] = useState<'models' | 'v2'>('models');
  const [flexibleInputs, setFlexibleInputs] = useState<FlexibleInput[]>([]);
  const [viewingImage, setViewingImage] = useState<GeneratedImage | null>(null);
  const [isImageDetailOpen, setIsImageDetailOpen] = useState(false);

  // Generate batch function
  const generateBatch = async () => {
    if (activeTab === 'v2') {
      // V2 API generation
      if (!hasAllRequiredImages()) {
        toast({
          title: "Missing Images",
          description: "Please upload all 4 required images: face photo, body shot, product image, and additional item.",
          variant: "destructive",
        });
        return;
      }

      try {
        await generateV2({
          prompt: settings.prompt,
          faceImage: v2Images.face!.base64!,
          image2: v2Images.image_2!.base64!,
          image3: v2Images.image_3!.base64!,
          image4: v2Images.image_4!.base64!,
          metadata: {
            source: 'image-generator',
            flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
          }
        });
      } catch (error) {
        console.error('V2 generation error:', error);
        toast({
          title: "Generation Failed",
          description: "Failed to generate images with V2 API",
          variant: "destructive",
        });
      }
    } else {
      // Model-based generation
      if (selectedModels.length === 0 || selectedAngles.length === 0) {
        toast({
          title: "Selection Required",
          description: "Please select at least one model and one angle",
          variant: "destructive",
        });
        return;
      }

      let targetCollectionId = collectionId;
      if (!targetCollectionId) {
        targetCollectionId = await getOrCreateDemoCollection();
        if (!targetCollectionId) {
          toast({
            title: "Error",
            description: "Failed to create demo collection",
            variant: "destructive",
          });
          return;
        }
      }

      // Generate images for each model/angle combination
      const combinations = selectedModels.flatMap(modelId => 
        selectedAngles.map(angle => ({ modelId, angle }))
      );

      try {
        for (const { modelId, angle } of combinations) {
          const model = campaignModels.find(m => m.id === modelId);
          if (!model) continue;

          const angleData = angleBlocks.find(a => a.name === angle);
          if (!angleData) continue;

          // Get model image for this angle
          const modelImageKey = `${modelId}-${angle}`;
          const modelImageBase64 = modelImages[modelImageKey];

          if (!modelImageBase64) {
            console.warn(`No image found for ${modelId} - ${angle}`);
            continue;
          }

          // Build the full prompt
          const fullPrompt = `${model.promptText}, ${angleData.promptText}${settings.prompt ? `, ${settings.prompt}` : ''}`;

          // Generate using V2 API with model image
          await generateV2({
            prompt: fullPrompt,
            faceImage: modelImageBase64,
            image2: v2Images.image_2?.base64 || modelImageBase64,
            image3: v2Images.image_3?.base64 || modelImageBase64,
            image4: v2Images.image_4?.base64 || modelImageBase64,
            metadata: {
              source: 'image-generator',
              modelId,
              modelName: model.name,
              angle: angle,
              loraName: model.lora,
              selectedModels,
              selectedAngles,
              flexibleInputs: flexibleInputs.length > 0 ? flexibleInputs : undefined
            }
          });
        }
      } catch (error) {
        console.error('Batch generation error:', error);
        toast({
          title: "Generation Failed",
          description: "An error occurred while generating images",
          variant: "destructive",
        });
      }
    }
  };

  // Handle moving images to collection
  const handleMoveToCollection = async () => {
    const selectedImageIds = Array.from(selectedImages);
    
    if (selectedImageIds.length === 0) return;

    const success = await moveImagesToCollection(selectedImageIds);
    
    if (success) {
      // Clear selection and refresh the images list
      clearSelection();
      await refreshImages();
    }
  };

  // Handle image download
  const handleDownloadImage = async (image: GeneratedImage) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${image.model}_${image.angle}_${image.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      toast({
        title: "Download failed",
        description: "Failed to download the image",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-96 bg-white border-r flex flex-col">
        {/* Header */}
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(`/organizations/${orgId}/collections/${collectionId}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Collection
            </Button>
          </div>
          
          <h1 className="text-xl font-semibold">AI Image Generator</h1>
          {collection && (
            <p className="text-sm text-gray-600 mt-1">{collection.name}</p>
          )}
        </div>

        {/* Sidebar Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'models' | 'v2')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="models">Model Library</TabsTrigger>
              <TabsTrigger value="v2">Custom Images</TabsTrigger>
            </TabsList>

            <TabsContent value="models" className="space-y-4">
              {isLoadingModels ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading models...</p>
                </div>
              ) : (
                <ModelSelection
                  models={campaignModels}
                  selectedModels={selectedModels}
                  onToggleModel={toggleModel}
                  onSelectAll={selectAllModels}
                  onClearAll={clearAllModels}
                />
              )}
              
              <AngleSelection
                selectedAngles={selectedAngles}
                onToggleAngle={toggleAngle}
                onSelectAll={selectAllAngles}
                onClearAll={clearAllAngles}
              />
            </TabsContent>

            <TabsContent value="v2">
              <V2ImageUploader
                images={v2Images}
                onImageUpload={handleImageUpload}
                onImageRemove={removeImage}
              />
            </TabsContent>
          </Tabs>

          {/* Flexible Inputs */}
          <div className="mt-4">
            <FlexibleInputBox
              inputs={flexibleInputs}
              onChange={setFlexibleInputs}
            />
          </div>

          {/* Generation Settings */}
          <div className="mt-4">
            <GenerationSettingsComponent
              settings={settings}
              isAdvancedMode={isAdvancedMode}
              onUpdateSetting={updateSetting}
              onToggleAdvancedMode={toggleAdvancedMode}
              onResetSettings={resetSettings}
            />
          </div>
        </div>

        {/* Generate Button */}
        <div className="p-4 border-t">
          <Button
            className="w-full"
            size="lg"
            onClick={generateBatch}
            disabled={
              isGeneratingV2 || isMovingImages ||
              (activeTab === 'models' && (selectedModels.length === 0 || selectedAngles.length === 0)) ||
              (activeTab === 'v2' && !hasAllRequiredImages())
            }
          >
            <Sparkles className="h-4 w-4 mr-2" />
            {isGeneratingV2 ? 'Generating...' : 'Generate Images'}
          </Button>

          {/* Progress */}
          {isGeneratingV2 && (
            <div className="mt-2 space-y-1">
              <Progress value={progressV2} className="h-2" />
              <p className="text-xs text-center text-gray-600">
                Processing API request...
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Selection Toolbar */}
        <div className="p-4 bg-white border-b">
          <SelectionToolbar
            selectedCount={selectedImages.size}
            totalCount={generatedImages.length}
            onSelectAll={selectAllImages}
            onClearSelection={clearSelection}
            onDeleteSelected={deleteSelectedImages}
            onMoveToCollection={handleMoveToCollection}
            showMoveToCollection={!!collectionId}
            isMovingImages={isMovingImages}
          />
        </div>

        {/* Generated Images Grid */}
        <div className="flex-1 overflow-y-auto p-4">
          <GeneratedImagesGrid
            images={generatedImages}
            selectedImages={selectedImages}
            onToggleSelection={toggleImageSelection}
            onViewImage={(image) => {
              setViewingImage(image);
              setIsImageDetailOpen(true);
            }}
            onDownloadImage={handleDownloadImage}
            isLoading={isLoadingImages}
          />
        </div>
      </div>

      {/* Image Detail Dialog */}
      <ImageDetailDialog
        image={viewingImage}
        isOpen={isImageDetailOpen}
        onClose={() => {
          setIsImageDetailOpen(false);
          setViewingImage(null);
        }}
      />
    </div>
  );
}