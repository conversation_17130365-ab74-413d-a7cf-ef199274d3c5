import type { ImageGenerationParameters } from '../types';

const STORAGE_KEY = 'fashionlab_image_gen_params';
const STORAGE_VERSION = '1.0';

interface StoredParameters {
  version: string;
  timestamp: string;
  params: ImageGenerationParameters;
}

export function saveParameters(params: ImageGenerationParameters): void {
  try {
    const data: StoredParameters = {
      version: STORAGE_VERSION,
      timestamp: new Date().toISOString(),
      params,
    };
    
    // Don't store file objects or base64 data to avoid storage limits
    if (data.params.v2Images) {
      const cleanedImages = { ...data.params.v2Images };
      Object.keys(cleanedImages).forEach(key => {
        const imageKey = key as keyof typeof cleanedImages;
        if (cleanedImages[imageKey]) {
          cleanedImages[imageKey] = {
            ...cleanedImages[imageKey],
            file: undefined,
            base64: undefined,
            // Keep preview URL and asset info
          };
        }
      });
      data.params.v2Images = cleanedImages;
    }
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
  } catch (error) {
    console.error('[FashionLab] Failed to save parameters:', error);
  }
}

export function loadParameters(): ImageGenerationParameters | null {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return null;
    
    const data: StoredParameters = JSON.parse(stored);
    
    // Check version compatibility
    if (data.version !== STORAGE_VERSION) {
      console.warn('[FashionLab] Stored parameters version mismatch, clearing storage');
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }
    
    // Check if data is not too old (e.g., 7 days)
    const storedDate = new Date(data.timestamp);
    const daysSinceStored = (Date.now() - storedDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceStored > 7) {
      console.warn('[FashionLab] Stored parameters are too old, clearing storage');
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }
    
    return data.params;
  } catch (error) {
    console.error('[FashionLab] Failed to load parameters:', error);
    localStorage.removeItem(STORAGE_KEY);
    return null;
  }
}

export function clearParameters(): void {
  localStorage.removeItem(STORAGE_KEY);
}