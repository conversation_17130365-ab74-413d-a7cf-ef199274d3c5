import { useState, useCallback } from 'react';
import { compressImage, needsCompression } from '../../../utils/imageOptimization';
import type { V2Images, V2ImageData } from '../types';

export function useV2ImageManagement() {
  const [v2Images, setV2Images] = useState<V2Images>({
    face: null,
    image_2: null,
    image_3: null,
    image_4: null,
  });
  const [isProcessingImages, setIsProcessingImages] = useState(false);

  const handleImageUpload = useCallback(async (
    file: File, 
    slot: keyof V2Images
  ): Promise<void> => {
    setIsProcessingImages(true);
    try {
      // Validate that we have a valid file
      if (!file || !(file instanceof Blob)) {
        console.error(`Invalid file for ${slot} slot:`, file);
        throw new Error(`Invalid file provided for ${slot} slot`);
      }
      
      let processedFile = file;
      
      // Compress if needed
      if (needsCompression(file)) {
        console.log(`Compressing image for ${slot} slot...`);
        const compressed = await compressImage(file);
        // Make sure compression returned a valid file
        if (compressed && compressed instanceof Blob) {
          processedFile = compressed;
        } else {
          console.warn('Compression failed, using original file');
          processedFile = file;
        }
      }
      
      // Double-check we have a valid file before reading
      if (!processedFile || !(processedFile instanceof Blob)) {
        console.error(`ProcessedFile is not a valid Blob for ${slot}:`, processedFile);
        throw new Error(`Failed to process file for ${slot} slot`);
      }
      
      // Convert to base64 and wait for it to complete
      await new Promise<void>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64 = reader.result as string;
          setV2Images(prev => ({
            ...prev,
            [slot]: {
              file: processedFile,
              base64: base64.split(',')[1], // Remove data URL prefix
              preview: base64,
            },
          }));
          console.log(`Image uploaded to ${slot} slot successfully`);
          resolve();
        };
        reader.onerror = () => {
          console.error(`Failed to read file for ${slot} slot:`, reader.error);
          reject(reader.error);
        };
        reader.readAsDataURL(processedFile);
      });
    } catch (error) {
      console.error('Error processing image:', error);
      throw error;
    } finally {
      setIsProcessingImages(false);
    }
  }, []);

  const removeImage = useCallback((slot: keyof V2Images) => {
    setV2Images(prev => ({
      ...prev,
      [slot]: null,
    }));
  }, []);

  const clearAllImages = useCallback(() => {
    setV2Images({
      face: null,
      image_2: null,
      image_3: null,
      image_4: null,
    });
  }, []);

  const hasAllRequiredImages = useCallback(() => {
    return !!(
      v2Images.face?.base64 &&
      v2Images.image_2?.base64 &&
      v2Images.image_3?.base64 &&
      v2Images.image_4?.base64
    );
  }, [v2Images]);

  return {
    v2Images,
    isProcessingImages,
    handleImageUpload,
    removeImage,
    clearAllImages,
    hasAllRequiredImages,
  };
}