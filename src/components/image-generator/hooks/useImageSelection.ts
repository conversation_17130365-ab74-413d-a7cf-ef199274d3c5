import { useState } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import { FashionLabImageService } from '../../../services/fashionLabImageService';

const fashionLabImageService = new FashionLabImageService();

export function useImageSelection(collectionId?: string) {
  const [isMovingImages, setIsMovingImages] = useState(false);
  const { toast } = useToast();

  const moveImagesToCollection = async (imageIds: string[]) => {
    if (!collectionId || imageIds.length === 0) {
      toast({
        title: "Invalid selection",
        description: "Please select images and ensure you're in a collection",
        variant: "destructive",
      });
      return false;
    }

    setIsMovingImages(true);
    try {
      // Call the fashion lab service to select images
      const result = await fashionLabImageService.selectGeneratedImages(
        imageIds,
        collectionId
      );

      if (result.success) {
        toast({
          title: "Images moved successfully",
          description: `${result.selectedCount} image(s) have been moved to the collection as raw AI images`,
        });
        return true;
      } else {
        throw new Error(result.error || 'Failed to move images');
      }
    } catch (error) {
      console.error('Error moving images:', error);
      toast({
        title: "Error moving images",
        description: error instanceof Error ? error.message : "Failed to move images to collection",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsMovingImages(false);
    }
  };

  return {
    moveImagesToCollection,
    isMovingImages,
  };
}