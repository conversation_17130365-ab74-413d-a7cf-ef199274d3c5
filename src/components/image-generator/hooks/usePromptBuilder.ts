import { useState, useCallback, useEffect } from 'react';
import { useToast } from '../../../components/ui/use-toast';
import { openAIService } from '../../../services/openaiService';
import { supabase } from '../../../components/common/utils/supabase';
import type { ActiveBlock } from '../PromptBuilder';
import type { FlexibleInput } from '../../image-generator/FlexibleInputBox';

interface UsePromptBuilderProps {
  selectedModels?: string[];
  selectedAngles?: string[];
  selectedSettings?: { [key: string]: number };
  v2Images?: any;
  flexibleInputs?: FlexibleInput[];
  selectedProduct?: any;
  uploadedGarments?: any[];
  selectedGarments?: string[];
}

export function usePromptBuilder(props: UsePromptBuilderProps = {}) {
  const [prompt, setPrompt] = useState('');
  const [activeBlocks, setActiveBlocks] = useState<ActiveBlock[]>([]);
  const [isAnalyzingGarment, setIsAnalyzingGarment] = useState(false);
  const [manuallyEdited, setManuallyEdited] = useState(false);
  const [customAnglePrompts, setCustomAnglePrompts] = useState<Record<string, string>>({});
  const [promptHistory, setPromptHistory] = useState<string[]>([]);
  const { toast } = useToast();

  // Load custom angle prompt from database
  const loadCustomAnglePrompt = useCallback(async (modelCode: string, angleName: string): Promise<string | null> => {
    try {
      const angleTypeMap: Record<string, string> = {
        'Half-body Front': 'half-body-front',
        'Half-body Back': 'half-body-back',
        'Half-body 3/4 Left': 'half-body-34-left',
        'Half-body 3/4 Right': 'half-body-34-right',
        'Full-height Front': 'full-body-front',
        'Full-height Back': 'full-body-back',
        'Full-height Side Left': 'full-body-side-left',
        'Full-height Side Right': 'full-body-side-right',
        'Full-height 3/4 Left': 'full-body-34-left',
        'Full-height 3/4 Right': 'full-body-34-right',
        'face': 'face'
      };
      
      const dbAngleType = angleTypeMap[angleName] || angleName;
      
      // First get the model UUID from the code
      const { data: modelData } = await supabase
        .from('model_library')
        .select('id')
        .eq('code', modelCode)
        .eq('is_active', true)
        .maybeSingle();
        
      if (!modelData) return null;
      
      // Query the database for this specific model and angle
      const { data } = await supabase
        .from('model_images')
        .select('angle_prompt_text')
        .eq('model_id', modelData.id)
        .eq('angle_type', dbAngleType)
        .maybeSingle();
      
      if (data?.angle_prompt_text) {
        const key = `${modelCode}_${angleName}`;
        setCustomAnglePrompts(prev => ({
          ...prev,
          [key]: data.angle_prompt_text as string
        }));
        return data.angle_prompt_text;
      }
    } catch (error) {
      console.error('Error loading custom angle prompt:', error);
    }
    return null;
  }, []);

  // Build prompt in FLUX.1 Kontext style
  const buildFluxStylePrompt = useCallback(() => {
    const { 
      selectedModels = [], 
      selectedAngles = [], 
      selectedSettings = {},
      flexibleInputs = [],
      selectedProduct,
      uploadedGarments = [],
      selectedGarments = []
    } = props;

    const promptParts = [];
    
    // Start with "Place"
    promptParts.push('Place');
    
    // 1. Model description (face and body) from active blocks or custom prompts
    const modelBlock = activeBlocks.find(b => b.type === 'model');
    if (modelBlock) {
      // Check if we have a custom face prompt
      const modelCode = selectedModels[0];
      const customFacePromptKey = `${modelCode}_face`;
      const customFacePrompt = customAnglePrompts[customFacePromptKey];
      
      // Add "this" before model description
      promptParts.push('this');
      // Use custom face prompt if available, otherwise use block text
      promptParts.push(customFacePrompt || modelBlock.text);
    }
    
    // 2. Main garment from product selection is now handled via garment blocks
    // The product garment will be added as a garment block through analyzeGarment
    
    // 3. All garments from active blocks (product garments first, then additional)
    const garmentBlocks = activeBlocks.filter(b => b.type === 'garment');
    if (garmentBlocks.length > 0) {
      // Sort garments so product garments come first
      const sortedGarments = [...garmentBlocks].sort((a, b) => {
        if (a.subType === 'product' && b.subType !== 'product') return -1;
        if (a.subType !== 'product' && b.subType === 'product') return 1;
        return 0;
      });
      
      const garmentDescriptions = sortedGarments.map((garment, index) => {
        // First garment (should be the product if there is one) uses "wearing this"
        if (index === 0) {
          return `wearing this ${garment.text}`;
        } else if (index === 1) {
          return `styled with this ${garment.text}`;
        } else {
          return `and this ${garment.text}`;
        }
      });
      promptParts.push(...garmentDescriptions);
    }
    
    // 4. Setting/Background from active blocks
    const backgroundBlock = activeBlocks.find(b => b.type === 'background');
    if (backgroundBlock) {
      promptParts.push(backgroundBlock.text);
    }
    
    // 5. Angle from active blocks or custom prompts
    const angleBlock = activeBlocks.find(b => b.type === 'angle');
    if (angleBlock && selectedModels.length > 0) {
      // Check if we have a custom angle prompt
      const customPromptKey = `${selectedModels[0]}_${angleBlock.name}`;
      const customPrompt = customAnglePrompts[customPromptKey];
      
      // Use custom prompt if available, otherwise use block text
      promptParts.push(customPrompt || angleBlock.text);
    } else if (angleBlock) {
      promptParts.push(angleBlock.text);
    }
    
    // 6. Art direction (camera angle) from active blocks
    const artDirectionBlock = activeBlocks.find(b => b.type === 'artDirection');
    if (artDirectionBlock) {
      promptParts.push(artDirectionBlock.text);
    }
    
    // 7. Flexible inputs
    const textInputs = flexibleInputs.filter(input => input.type === 'text' && input.value);
    if (textInputs.length > 0) {
      textInputs.forEach(input => {
        promptParts.push(`with ${input.label.toLowerCase()}: ${input.value}`);
      });
    }
    
    const colorInputs = flexibleInputs.filter(input => input.type === 'color' && input.value);
    if (colorInputs.length > 0) {
      colorInputs.forEach(input => {
        promptParts.push(`using ${input.label.toLowerCase()} color ${input.value}`);
      });
    }
    
    return promptParts.filter(p => p).join(', ');
  }, [activeBlocks, customAnglePrompts, props]);

  // Build prompt from active blocks (legacy method)
  const buildPromptFromBlocks = useCallback(() => {
    // If we have props, use FLUX style
    if (props.selectedModels || props.selectedAngles) {
      return buildFluxStylePrompt();
    }

    // Otherwise use legacy method
    const modelBlocks = activeBlocks.filter(b => b.type === 'model');
    const angleBlocks = activeBlocks.filter(b => b.type === 'angle');
    const garmentBlocks = activeBlocks.filter(b => b.type === 'garment');
    const backgroundBlocks = activeBlocks.filter(b => b.type === 'background');
    const artDirectionBlocks = activeBlocks.filter(b => b.type === 'artDirection');

    const promptParts = [];

    // Add model description
    if (modelBlocks.length > 0) {
      promptParts.push(modelBlocks[0].text);
    }

    // Add garments
    if (garmentBlocks.length > 0) {
      promptParts.push(`wearing ${garmentBlocks.map(b => b.text).join(' with ')}`);
    }

    // Add background
    if (backgroundBlocks.length > 0) {
      promptParts.push(backgroundBlocks[0].text);
    }

    // Add angle
    if (angleBlocks.length > 0) {
      promptParts.push(angleBlocks[0].text);
    }

    // Add art direction
    if (artDirectionBlocks.length > 0) {
      promptParts.push(artDirectionBlocks[0].text);
    }

    return promptParts.join(', ');
  }, [activeBlocks, buildFluxStylePrompt, props]);

  // Update prompt when blocks change (unless manually edited)
  useEffect(() => {
    if (!manuallyEdited) {
      const newPrompt = buildPromptFromBlocks();
      setPrompt(newPrompt);
    }
  }, [activeBlocks, buildPromptFromBlocks, manuallyEdited]);

  // Handle block toggle
  const handleBlockToggle = useCallback((block: ActiveBlock) => {
    setActiveBlocks(prev => {
      const existingIndex = prev.findIndex(b => b.text === block.text);
      
      if (existingIndex >= 0) {
        // Remove block
        return prev.filter((_, index) => index !== existingIndex);
      } else {
        // Add block, but replace if same type
        const filtered = prev.filter(b => b.type !== block.type);
        return [...filtered, block];
      }
    });
  }, []);

  // Add model block
  const addModelBlock = useCallback((model: { name: string; promptText: string }) => {
    handleBlockToggle({
      type: 'model',
      name: model.name,
      text: model.promptText
    });
  }, [handleBlockToggle]);

  // Add angle block
  const addAngleBlock = useCallback((angle: { name: string; promptText: string }) => {
    handleBlockToggle({
      type: 'angle',
      name: angle.name,
      text: angle.promptText
    });
  }, [handleBlockToggle]);

  // Analyze garment with OpenAI
  const analyzeGarment = useCallback(async (imageFile: File, garmentType: string = 'garment') => {
    setIsAnalyzingGarment(true);
    try {
      // Convert file to base64
      const reader = new FileReader();
      const base64 = await new Promise<string>((resolve) => {
        reader.onloadend = () => resolve(reader.result as string);
        reader.readAsDataURL(imageFile);
      });
      
      const result = await openAIService.analyzeGarmentImage(base64);
      
      if (result && result.description) {
        const isProductGarment = garmentType.toLowerCase().includes('product');
        
        const garmentBlock: ActiveBlock = {
          type: 'garment',
          name: isProductGarment ? `Main: ${result.colors?.[0] || 'Garment'}` : `${garmentType}: ${result.colors?.[0] || 'Garment'}`,
          text: result.description,
          subType: isProductGarment ? 'product' : 'additional'
        };
        
        // Add garment block
        setActiveBlocks(prev => [...prev, garmentBlock]);
        
        toast({
          title: "Garment analyzed",
          description: "AI description added to prompt",
        });
        
        return result;
      } else {
        throw new Error('Failed to analyze garment');
      }
    } catch (error) {
      console.error('Error analyzing garment:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      toast({
        title: "Garment Analysis Failed",
        description: errorMessage,
        variant: "destructive",
      });
      
      // If it's an OpenAI vision failure, provide more context
      if (errorMessage.includes('OpenAI vision analysis failed')) {
        toast({
          title: "Image Analysis Issue",
          description: "Try uploading a clearer image or a different format (JPEG/PNG recommended)",
          variant: "destructive",
        });
      }
      
      return null;
    } finally {
      setIsAnalyzingGarment(false);
    }
  }, [toast]);

  // Clear all blocks
  const clearBlocks = useCallback(() => {
    setActiveBlocks([]);
    setPrompt('');
  }, []);

  // Manual prompt update
  const updatePrompt = useCallback((newPrompt: string) => {
    setPrompt(newPrompt);
    setManuallyEdited(true);
    
    // Add to history if different from last entry
    if (promptHistory.length === 0 || promptHistory[promptHistory.length - 1] !== newPrompt) {
      setPromptHistory(prev => [...prev.slice(-9), newPrompt]); // Keep last 10 entries
    }
  }, [promptHistory]);

  // Load model with custom prompts
  const loadModelWithCustomPrompts = useCallback(async (modelCode: string, modelName: string, modelPromptText?: string) => {
    // Clear existing model blocks
    setActiveBlocks(prev => prev.filter(b => b.type !== 'model'));
    
    // Use the custom prompt text if provided, otherwise use model name
    const promptText = modelPromptText || `${modelName} fashion model`;
    
    // Add model block with custom prompt
    const modelBlock: ActiveBlock = {
      type: 'model',
      name: modelName,
      text: promptText
    };
    
    // Add the new model block
    setActiveBlocks(prev => [...prev.filter(b => b.type !== 'model'), modelBlock]);
    
    // Clear manually edited flag to allow prompt to rebuild
    setManuallyEdited(false);
    
    return promptText;
  }, []);

  // Load angle with custom prompts
  const loadAngleWithCustomPrompts = useCallback(async (modelCode: string, angleName: string, anglePromptText: string) => {
    const customPrompt = await loadCustomAnglePrompt(modelCode, angleName);
    
    // Add angle block with custom prompt if available
    const angleBlock: ActiveBlock = {
      type: 'angle',
      name: angleName,
      text: customPrompt || anglePromptText
    };
    
    // Replace existing angle block
    setActiveBlocks(prev => [...prev.filter(b => b.type !== 'angle'), angleBlock]);
    
    return customPrompt;
  }, [loadCustomAnglePrompt]);

  return {
    prompt,
    activeBlocks,
    isAnalyzingGarment,
    handleBlockToggle,
    addModelBlock,
    addAngleBlock,
    analyzeGarment,
    clearBlocks,
    updatePrompt,
    setActiveBlocks,
    manuallyEdited,
    setManuallyEdited,
    promptHistory,
    setPromptHistory,
    customAnglePrompts,
    loadCustomAnglePrompt,
    loadModelWithCustomPrompts,
    loadAngleWithCustomPrompts,
  };
}