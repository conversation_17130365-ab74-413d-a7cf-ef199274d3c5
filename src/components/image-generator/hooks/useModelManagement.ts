import { useState, useEffect, useMemo, useCallback } from 'react';
import { useActiveModels } from '../../../hooks/useActiveModels';
import { getModelImageAsBase64 } from '../../../hooks/useModelLibrary';
import { angleBlocks, staticCampaignModels, staticModelImageMapping } from '../constants';
import { getModelPreviewImage, modelHasFaceImage } from '../../../utils/modelImageUtils';

export function useModelManagement() {
  const [selectedModels, setSelectedModels] = useState<string[]>([]);
  const [selectedAngles, setSelectedAngles] = useState<string[]>([]);
  const [modelImages, setModelImages] = useState<Record<string, string>>({});
  
  const { 
    models: activeModels = [], 
    isLoading: isLoadingModels,
    modelImageMapping: dynamicModelImageMapping 
  } = useActiveModels();

  // Use dynamic models from database or fall back to static models
  const campaignModels = useMemo(() => {
    if (activeModels.length > 0) {
      return activeModels.map((model, index) => {
        // Get a preview image from the model's images
        const previewImage = getModelPreviewImage(model.imageMapping || {}, model.code);
        
        return {
          id: model.id || `${model.code}-${index}`, // Use unique database ID first
          code: model.code, // Keep code separate for lookups
          name: model.name,
          shortName: model.model_persona_name || model.name,
          description: model.description || '',
          promptText: model.prompt_text || `${model.name} fashion model`,
          lora: `bubbleroom_model_${model.code.toLowerCase()}_v1`,
          image: previewImage,
        };
      });
    } else {
      // Fall back to static models with unique IDs
      return staticCampaignModels.map((model, index) => ({
        ...model,
        id: `static-${model.id}-${index}`, // Ensure unique IDs
        code: model.id // Add code field for compatibility
      }));
    }
  }, [activeModels]);

  // Use dynamic model image mapping if available, otherwise use static
  const modelImageMapping = useMemo(() => {
    return Object.keys(dynamicModelImageMapping || {}).length > 0 
      ? dynamicModelImageMapping 
      : staticModelImageMapping;
  }, [dynamicModelImageMapping]);

  // Fetch model images
  useEffect(() => {
    const fetchModelImages = async () => {
      const images: Record<string, string> = {};
      
      for (const model of selectedModels) {
        for (const angle of selectedAngles) {
          const key = `${model}-${angle}`;
          
          // Find the model data
          const modelData = activeModels.find(m => m.code === model || m.id === model);
          if (modelData && modelData.imageMapping && modelData.imageMapping[angle]) {
            images[key] = modelData.imageMapping[angle];
          } else {
            // Try to fetch from model library
            try {
              const base64 = await getModelImageAsBase64(model, angle);
              if (base64) {
                images[key] = base64;
              }
            } catch (error) {
              console.error(`Failed to load image for ${model} - ${angle}:`, error);
            }
          }
        }
      }
      
      setModelImages(images);
    };

    if (selectedModels.length > 0 && selectedAngles.length > 0) {
      fetchModelImages();
    }
  }, [selectedModels, selectedAngles, activeModels]);

  const toggleModel = (modelId: string) => {
    setSelectedModels(prev => {
      // If clicking the same model, deselect it
      if (prev.includes(modelId)) {
        return [];
      }
      // Otherwise, select only this model (single selection)
      return [modelId];
    });
  };

  const toggleAngle = (angleName: string) => {
    setSelectedAngles(prev => {
      // If clicking the same angle, deselect it
      if (prev.includes(angleName)) {
        return [];
      }
      // Otherwise, select only this angle (single selection)
      return [angleName];
    });
  };

  const selectAllModels = () => {
    setSelectedModels(campaignModels.map(m => m.id));
  };

  const selectAllAngles = () => {
    setSelectedAngles(angleBlocks.map(a => a.name));
  };

  const clearAllModels = () => {
    setSelectedModels([]);
  };

  const clearAllAngles = () => {
    setSelectedAngles([]);
  };

  // Check if a model has the required images
  const checkModelImages = useCallback((modelId: string): { hasFace: boolean; hasAngle: boolean; angleName?: string } => {
    // Find model to get code
    const model = campaignModels.find(m => m.id === modelId);
    const modelCode = model?.code || modelId;
    
    const hasFace = modelHasFaceImage(modelImageMapping[modelCode] || {});
    let hasAngle = true;
    let angleName = '';

    if (selectedAngles.length > 0) {
      const angle = selectedAngles[0];
      angleName = angle;
      hasAngle = !!(modelImageMapping[modelCode]?.[angle]);
    }

    return { hasFace, hasAngle, angleName };
  }, [modelImageMapping, selectedAngles, campaignModels]);

  // Validate generation requirements
  const validateGenerationRequirements = useCallback((): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    // Check if model is selected
    if (selectedModels.length === 0) {
      errors.push('Please select a model');
    } else {
      // Check each selected model for required images
      selectedModels.forEach(modelId => {
        const model = campaignModels.find(m => m.id === modelId);
        const modelCheck = checkModelImages(modelId);
        
        if (!modelCheck.hasFace) {
          errors.push(`${model?.name || 'Selected model'} needs a face photo. Please upload it in the Model Library.`);
        }
        
        if (selectedAngles.length > 0 && !modelCheck.hasAngle) {
          errors.push(`${model?.name || 'Selected model'} needs a photo for the ${modelCheck.angleName} angle.`);
        }
      });
    }
    
    // Check if angle is selected
    if (selectedAngles.length === 0) {
      errors.push('Please select an angle');
    }
    
    return { isValid: errors.length === 0, errors };
  }, [selectedModels, selectedAngles, campaignModels, checkModelImages]);

  // Load model image (face or angle)
  const loadModelImage = useCallback(async (modelId: string, imageType: 'face' | string): Promise<string | null> => {
    try {
      // Find model to get code
      const model = campaignModels.find(m => m.id === modelId);
      const modelCode = model?.code || modelId;
      
      // First try dynamic mapping
      if (modelImageMapping[modelCode]?.[imageType]) {
        return modelImageMapping[modelCode][imageType];
      }
      
      // Then try to fetch from database
      const angleTypeMap: Record<string, string> = {
        'face': 'face',
        'Half-body Front': 'half-body-front',
        'Half-body Back': 'half-body-back',
        'Half-body 3/4 Left': 'half-body-34-left',
        'Half-body 3/4 Right': 'half-body-34-right',
        'Full-height Front': 'full-body-front',
        'Full-height Back': 'full-body-back',
        'Full-height Side Left': 'full-body-side-left',
        'Full-height Side Right': 'full-body-side-right',
        'Full-height 3/4 Left': 'full-body-34-left',
        'Full-height 3/4 Right': 'full-body-34-right'
      };
      
      const dbAngleType = angleTypeMap[imageType] || imageType;
      const base64 = await getModelImageAsBase64(modelCode, dbAngleType);
      
      return base64;
    } catch (error) {
      console.error(`Error loading model image for ${modelId} - ${imageType}:`, error);
      return null;
    }
  }, [modelImageMapping, campaignModels]);

  return {
    campaignModels,
    selectedModels,
    selectedAngles,
    modelImages,
    isLoadingModels,
    toggleModel,
    toggleAngle,
    selectAllModels,
    selectAllAngles,
    clearAllModels,
    clearAllAngles,
    checkModelImages,
    validateGenerationRequirements,
    loadModelImage,
    modelImageMapping,
  };
}