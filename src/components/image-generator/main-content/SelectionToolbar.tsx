import React, { useState } from 'react';
import { Button } from '../../../components/ui/button';
import { 
  CheckSquare, Square, Trash2, Save, 
  Download, ArrowRight, X, Loader2, AlertTriangle 
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../../components/ui/alert-dialog';

interface SelectionToolbarProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onDeleteSelected: () => void;
  onMoveToCollection: () => void;
  showMoveToCollection?: boolean;
  isMovingImages?: boolean;
}

export function SelectionToolbar({
  selectedCount,
  totalCount,
  onSelectAll,
  onClearSelection,
  onDeleteSelected,
  onMoveToCollection,
  showMoveToCollection = true,
  isMovingImages = false,
}: SelectionToolbarProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  if (totalCount === 0) return null;

  return (
    <div className="flex items-center justify-between p-4 bg-gray-50 border rounded-lg">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={selectedCount === totalCount ? onClearSelection : onSelectAll}
          >
            {selectedCount === totalCount ? (
              <>
                <Square className="h-3 w-3 mr-1" />
                Deselect All
              </>
            ) : (
              <>
                <CheckSquare className="h-3 w-3 mr-1" />
                Select All
              </>
            )}
          </Button>
          
          {selectedCount > 0 && (
            <span className="text-sm text-gray-600">
              {selectedCount} of {totalCount} selected
            </span>
          )}
        </div>
      </div>

      {selectedCount > 0 && (
        <div className="flex items-center gap-2">
          {showMoveToCollection && (
            <Button
              size="sm"
              onClick={onMoveToCollection}
              disabled={isMovingImages}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isMovingImages ? (
                <>
                  <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                  Moving...
                </>
              ) : (
                <>
                  <ArrowRight className="h-3 w-3 mr-1" />
                  Move to Collection ({selectedCount})
                </>
              )}
            </Button>
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDeleteConfirm(true)}
            className="text-red-600 hover:bg-red-50"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Delete
          </Button>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Delete Selected Images
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCount} selected image{selectedCount !== 1 ? 's' : ''}? 
              This action cannot be undone and will permanently remove the images from your collection.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                onDeleteSelected();
                setShowDeleteConfirm(false);
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete {selectedCount} Image{selectedCount !== 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}