import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MoreH<PERSON>zontal, Edit, Trash2, ToggleLeft, ToggleRight, Globe, Building, Folder } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { Badge } from '../ui/badge';
import { Skeleton } from '../ui/skeleton';

export interface Model {
  id: string;
  name: string;
  code: string;
  description?: string;
  is_active: boolean;
  display_order: number;
  created_at: string;
  scope_type?: 'global' | 'organization' | 'collection';
  organization_id?: string;
  collection_id?: string;
  organization_name?: string;
  collection_name?: string;
}

interface ModelLibraryTableProps {
  models: Model[];
  isLoading: boolean;
  onEdit: (model: Model) => void;
  onDelete: (model: Model) => void;
  onToggleActive: (model: Model) => void;
}

export function ModelLibraryTable({
  models,
  isLoading,
  onEdit,
  onDelete,
  onToggleActive,
}: ModelLibraryTableProps) {
  const navigate = useNavigate();
  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    );
  }

  if (models.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <p className="text-muted-foreground">No models found</p>
        <p className="text-sm text-muted-foreground mt-1">
          Create your first model to get started
        </p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Code</TableHead>
          <TableHead>Description</TableHead>
          <TableHead>Scope</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {models.map((model) => (
          <TableRow 
            key={model.id}
            className="cursor-pointer hover:bg-muted/50"
            onClick={() => navigate(`/admin/model-library/${model.id}`)}
          >
            <TableCell className="font-medium">{model.name}</TableCell>
            <TableCell>
              <Badge variant="outline">{model.code}</Badge>
            </TableCell>
            <TableCell className="max-w-md truncate">
              {model.description || '-'}
            </TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                {(!model.scope_type || model.scope_type === 'global') && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    <Globe className="w-3 h-3 mr-1" />
                    Global
                  </Badge>
                )}
                {model.scope_type === 'organization' && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Building className="w-3 h-3 mr-1" />
                    {model.organization_name || 'Organization'}
                  </Badge>
                )}
                {model.scope_type === 'collection' && (
                  <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                    <Folder className="w-3 h-3 mr-1" />
                    {model.collection_name || 'Collection'}
                  </Badge>
                )}
              </div>
            </TableCell>
            <TableCell>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleActive(model);
                }}
                className="gap-2"
              >
                {model.is_active ? (
                  <>
                    <ToggleRight className="h-4 w-4 text-green-600" />
                    <span className="text-green-600">Active</span>
                  </>
                ) : (
                  <>
                    <ToggleLeft className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">Inactive</span>
                  </>
                )}
              </Button>
            </TableCell>
            <TableCell className="text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="ghost" 
                    className="h-8 w-8 p-0"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    onEdit(model);
                  }}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Model
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(model);
                    }}
                    className="text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Model
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}