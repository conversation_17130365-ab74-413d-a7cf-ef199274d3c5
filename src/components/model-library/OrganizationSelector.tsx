import React from 'react';
import { Building } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useOrganizations } from '../../hooks/useOrganizations';
import { Skeleton } from '../ui/skeleton';

interface OrganizationSelectorProps {
  value?: string;
  onChange: (value: string) => void;
  allowAll?: boolean;
  placeholder?: string;
}

export function OrganizationSelector({ 
  value, 
  onChange, 
  allowAll = false,
  placeholder = "Select organization"
}: OrganizationSelectorProps) {
  const { data: organizations, isLoading } = useOrganizations();

  if (isLoading) {
    return <Skeleton className="h-10 w-full" />;
  }

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {allowAll && (
          <SelectItem value="all">
            <div className="flex items-center">
              <Building className="w-4 h-4 mr-2" />
              All Organizations
            </div>
          </SelectItem>
        )}
        {organizations?.map((org) => (
          <SelectItem key={org.id} value={org.id}>
            <div className="flex items-center">
              <Building className="w-4 h-4 mr-2" />
              {org.name}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
