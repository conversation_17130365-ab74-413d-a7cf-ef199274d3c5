import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useToast } from '../ui/use-toast';
import { Model, updateModel } from '../../hooks/useModelLibrary';
import { OrganizationSelector } from './OrganizationSelector';
import { CollectionSelector } from './CollectionSelector';

interface EditModelDialogProps {
  model: Model;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditModelDialog({ model, isOpen, onClose, onSuccess }: EditModelDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    display_order: 0,
    is_active: true,
    prompt_text: '',
    model_persona_name: '',
    model_backstory: '',
    scope_type: 'global' as 'global' | 'organization' | 'collection',
    organization_id: '',
    collection_id: ''
  });

  useEffect(() => {
    if (model) {
      setFormData({
        name: model.name,
        code: model.code,
        description: model.description || '',
        display_order: model.display_order,
        is_active: model.is_active,
        prompt_text: model.prompt_text || '',
        model_persona_name: model.model_persona_name || '',
        model_backstory: model.model_backstory || '',
        scope_type: model.scope_type || 'global',
        organization_id: model.organization_id || '',
        collection_id: model.collection_id || ''
      });
    }
  }, [model]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.code) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await updateModel(model.id, {
        name: formData.name,
        code: formData.code.toUpperCase(),
        description: formData.description || null,
        display_order: formData.display_order,
        is_active: formData.is_active,
        prompt_text: formData.prompt_text || null,
        model_persona_name: formData.model_persona_name || null,
        model_backstory: formData.model_backstory || null,
        scope_type: formData.scope_type,
        organization_id: formData.scope_type === 'organization' ? formData.organization_id : null,
        collection_id: formData.scope_type === 'collection' ? formData.collection_id : null
      });

      toast({
        title: 'Model updated',
        description: `${formData.name} has been updated successfully.`
      });

      onSuccess();
    } catch (error: any) {
      console.error('Error updating model:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to update model. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Model</DialogTitle>
            <DialogDescription>
              Update the model details. Changes will be applied immediately.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto pr-2">
            <div className="grid gap-2">
              <Label htmlFor="name">
                Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Small Model"
                disabled={isSubmitting}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="code">
                Code <span className="text-destructive">*</span>
              </Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                placeholder="e.g., S, M, L, XL"
                className="uppercase"
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                A unique identifier for the model (will be converted to uppercase)
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description of the model"
                rows={3}
                disabled={isSubmitting}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="display_order">Display Order</Label>
              <Input
                id="display_order"
                type="number"
                value={formData.display_order}
                onChange={(e) => setFormData({ ...formData, display_order: parseInt(e.target.value) || 0 })}
                placeholder="0"
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                Lower numbers appear first in the list
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="scope">Model Scope</Label>
              <Select
                value={formData.scope_type}
                onValueChange={(value: 'global' | 'organization' | 'collection') =>
                  setFormData({
                    ...formData,
                    scope_type: value,
                    organization_id: value !== 'organization' ? '' : formData.organization_id,
                    collection_id: value !== 'collection' ? '' : formData.collection_id
                  })
                }
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select scope" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="global">Global (All Organizations)</SelectItem>
                  <SelectItem value="organization">Organization Only</SelectItem>
                  <SelectItem value="collection">Collection Only</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Global models are visible to all users. Organization/Collection models are only visible to members.
              </p>
            </div>

            {formData.scope_type === 'organization' && (
              <div className="grid gap-2">
                <Label htmlFor="organization">Organization</Label>
                <OrganizationSelector
                  value={formData.organization_id}
                  onChange={(id) => setFormData({ ...formData, organization_id: id })}
                  placeholder="Select organization"
                />
              </div>
            )}

            {formData.scope_type === 'collection' && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="organization">Organization</Label>
                  <OrganizationSelector
                    value={formData.organization_id}
                    onChange={(id) => setFormData({ ...formData, organization_id: id, collection_id: '' })}
                    placeholder="Select organization first"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="collection">Collection</Label>
                  <CollectionSelector
                    organizationId={formData.organization_id}
                    value={formData.collection_id}
                    onChange={(id) => setFormData({ ...formData, collection_id: id })}
                    placeholder="Select collection"
                  />
                </div>
              </>
            )}

            <div className="grid gap-2">
              <Label htmlFor="model_persona_name">Model Persona Name</Label>
              <Input
                id="model_persona_name"
                value={formData.model_persona_name}
                onChange={(e) => setFormData({ ...formData, model_persona_name: e.target.value })}
                placeholder="e.g., Emma, Sofia"
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                Display name for storytelling purposes
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="model_backstory">Model Backstory</Label>
              <Textarea
                id="model_backstory"
                value={formData.model_backstory}
                onChange={(e) => setFormData({ ...formData, model_backstory: e.target.value })}
                placeholder="Brief backstory or description for the model"
                rows={2}
                disabled={isSubmitting}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="prompt_text">Custom Prompt Text</Label>
              <Textarea
                id="prompt_text"
                value={formData.prompt_text}
                onChange={(e) => setFormData({ ...formData, prompt_text: e.target.value })}
                placeholder="Custom prompt text for this model (provided by Fashion Lab team)"
                rows={3}
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                This text will be used in AI generation prompts
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="is_active">Active Status</Label>
                <p className="text-sm text-muted-foreground">
                  Inactive models won't be shown in the image generator
                </p>
              </div>
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Updating...' : 'Update Model'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}