import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { useToast } from '../ui/use-toast';
import { supabase } from '../common/utils/supabase';
import { ModelImage } from '../../hooks/useModelLibrary';

interface EditAnglePromptDialogProps {
  modelImage: ModelImage | null;
  modelName: string;
  angleName: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function EditAnglePromptDialog({ 
  modelImage, 
  modelName,
  angleName,
  isOpen, 
  onClose, 
  onSuccess 
}: EditAnglePromptDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [promptText, setPromptText] = useState('');

  useEffect(() => {
    if (modelImage) {
      setPromptText(modelImage.angle_prompt_text || '');
    }
  }, [modelImage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!modelImage) return;

    try {
      setIsSubmitting(true);
      
      const { error } = await supabase
        .from('model_images')
        .update({
          angle_prompt_text: promptText || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', modelImage.id);

      if (error) throw error;

      toast({
        title: 'Angle prompt updated',
        description: `Custom prompt for ${angleName} has been saved.`
      });

      onSuccess();
    } catch (error) {
      console.error('Error updating angle prompt:', error);
      toast({
        title: 'Error',
        description: (error as Error).message || 'Failed to update angle prompt. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Angle Prompt</DialogTitle>
            <DialogDescription>
              Set a custom prompt for {modelName} - {angleName}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="prompt">Custom Angle Prompt</Label>
              <Textarea
                id="prompt"
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                placeholder={`Enter custom prompt text for ${angleName} angle...`}
                rows={4}
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                This prompt will be added when this specific angle is selected. 
                Example: "shot from low angle with dramatic lighting"
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save Prompt'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}