import React from 'react';
import { Folder } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useCollections } from '../../hooks/useOrganizations';
import { Skeleton } from '../ui/skeleton';

interface CollectionSelectorProps {
  organizationId?: string;
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export function CollectionSelector({ 
  organizationId,
  value, 
  onChange,
  placeholder = "Select collection"
}: CollectionSelectorProps) {
  const { data: collections, isLoading } = useCollections(organizationId);

  if (isLoading) {
    return <Skeleton className="h-10 w-full" />;
  }

  if (!organizationId) {
    return (
      <Select disabled>
        <SelectTrigger>
          <SelectValue placeholder="Select organization first" />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {collections?.map((collection) => (
          <SelectItem key={collection.id} value={collection.id}>
            <div className="flex items-center">
              <Folder className="w-4 h-4 mr-2" />
              {collection.name}
            </div>
          </SelectItem>
        ))}
        {collections?.length === 0 && (
          <SelectItem value="" disabled>
            No collections available
          </SelectItem>
        )}
      </SelectContent>
    </Select>
  );
}
