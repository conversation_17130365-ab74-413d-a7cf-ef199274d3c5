import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Model, ANGLE_TYPES, getModelImageUrl } from '../../hooks/useModelLibrary';
import { ModelImageUploader } from './ModelImageUploader';
import { ScrollArea } from '../ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { CheckCircle2, XCircle } from 'lucide-react';

interface ModelImagesDialogProps {
  model: Model;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

export function ModelImagesDialog({ model, isOpen, onClose, onUpdate }: ModelImagesDialogProps) {
  const [activeTab, setActiveTab] = useState('half-body');

  const halfBodyAngles = ANGLE_TYPES.filter(angle => angle.includes('Half-body') || angle === 'face');
  const fullHeightAngles = ANGLE_TYPES.filter(angle => angle.includes('Full-height'));

  const getImageForAngle = (angleType: string) => {
    return model.images?.find(img => img.angle_type === angleType);
  };

  const renderAngleSection = (angles: readonly string[]) => {
    return (
      <div className="grid gap-4">
        {angles.map((angle) => {
          const image = getImageForAngle(angle);
          const hasImage = !!image;

          return (
            <div key={angle} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{angle}</h4>
                  {hasImage ? (
                    <Badge variant="outline" className="gap-1">
                      <CheckCircle2 className="h-3 w-3" />
                      Uploaded
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="gap-1 text-muted-foreground">
                      <XCircle className="h-3 w-3" />
                      Missing
                    </Badge>
                  )}
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                {image && (
                  <div className="aspect-[3/4] relative rounded-md overflow-hidden bg-muted">
                    <img
                      src={getModelImageUrl(image.storage_path)}
                      alt={`${model.name} - ${angle}`}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                      <p className="text-xs text-white">
                        {(image.file_size / 1024).toFixed(1)} KB
                      </p>
                    </div>
                  </div>
                )}
                
                <ModelImageUploader
                  modelId={model.id}
                  angleType={angle}
                  currentImage={image}
                  onUploadSuccess={onUpdate}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Manage Images - {model.name}</DialogTitle>
          <DialogDescription>
            Upload images for each angle. Images should be high quality and consistent in style.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="half-body">
              Half-body & Face ({halfBodyAngles.filter(a => getImageForAngle(a)).length}/{halfBodyAngles.length})
            </TabsTrigger>
            <TabsTrigger value="full-height">
              Full-height ({fullHeightAngles.filter(a => getImageForAngle(a)).length}/{fullHeightAngles.length})
            </TabsTrigger>
          </TabsList>

          <ScrollArea className="h-[calc(90vh-200px)] mt-4">
            <TabsContent value="half-body" className="mt-0">
              {renderAngleSection(halfBodyAngles)}
            </TabsContent>

            <TabsContent value="full-height" className="mt-0">
              {renderAngleSection(fullHeightAngles)}
            </TabsContent>
          </ScrollArea>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}