import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { useToast } from '../ui/use-toast';
import { createModel } from '../../hooks/useModelLibrary';
import { OrganizationSelector } from './OrganizationSelector';
import { CollectionSelector } from './CollectionSelector';

interface CreateModelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function CreateModelDialog({ isOpen, onClose, onSuccess }: CreateModelDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    display_order: 0,
    prompt_text: '',
    model_persona_name: '',
    model_backstory: '',
    scope_type: 'global' as 'global' | 'organization' | 'collection',
    organization_id: '',
    collection_id: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.code) {
      toast({
        title: 'Validation error',
        description: 'Please fill in all required fields.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await createModel({
        name: formData.name,
        code: formData.code.toUpperCase(),
        description: formData.description || undefined,
        display_order: formData.display_order,
        scope_type: formData.scope_type,
        organization_id: formData.scope_type === 'organization' ? formData.organization_id : undefined,
        collection_id: formData.scope_type === 'collection' ? formData.collection_id : undefined
      });

      toast({
        title: 'Model created',
        description: `${formData.name} has been created successfully.`
      });

      // Reset form
      setFormData({
        name: '',
        code: '',
        description: '',
        display_order: 0,
        prompt_text: '',
        model_persona_name: '',
        model_backstory: '',
        scope_type: 'global',
        organization_id: '',
        collection_id: ''
      });

      onSuccess();
    } catch (error: any) {
      console.error('Error creating model:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create model. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Model</DialogTitle>
            <DialogDescription>
              Add a new model to the library. You can upload images after creation.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto pr-2">
            <div className="grid gap-2">
              <Label htmlFor="name">
                Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Small Model"
                disabled={isSubmitting}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="code">
                Code <span className="text-destructive">*</span>
              </Label>
              <Input
                id="code"
                value={formData.code}
                onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                placeholder="e.g., S, M, L, XL"
                className="uppercase"
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                A unique identifier for the model (will be converted to uppercase)
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description of the model"
                rows={3}
                disabled={isSubmitting}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="scope">Model Scope</Label>
              <Select
                value={formData.scope_type}
                onValueChange={(value: 'global' | 'organization' | 'collection') =>
                  setFormData({
                    ...formData,
                    scope_type: value,
                    organization_id: value !== 'organization' ? '' : formData.organization_id,
                    collection_id: value !== 'collection' ? '' : formData.collection_id
                  })
                }
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select scope" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="global">Global (All Organizations)</SelectItem>
                  <SelectItem value="organization">Organization Only</SelectItem>
                  <SelectItem value="collection">Collection Only</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Global models are visible to all users. Organization/Collection models are only visible to members.
              </p>
            </div>

            {formData.scope_type === 'organization' && (
              <div className="grid gap-2">
                <Label htmlFor="organization">Organization</Label>
                <OrganizationSelector
                  value={formData.organization_id}
                  onChange={(id) => setFormData({ ...formData, organization_id: id })}
                  placeholder="Select organization"
                />
              </div>
            )}

            {formData.scope_type === 'collection' && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="organization">Organization</Label>
                  <OrganizationSelector
                    value={formData.organization_id}
                    onChange={(id) => setFormData({ ...formData, organization_id: id, collection_id: '' })}
                    placeholder="Select organization first"
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="collection">Collection</Label>
                  <CollectionSelector
                    organizationId={formData.organization_id}
                    value={formData.collection_id}
                    onChange={(id) => setFormData({ ...formData, collection_id: id })}
                    placeholder="Select collection"
                  />
                </div>
              </>
            )}

            <div className="grid gap-2">
              <Label htmlFor="display_order">Display Order</Label>
              <Input
                id="display_order"
                type="number"
                value={formData.display_order}
                onChange={(e) => setFormData({ ...formData, display_order: parseInt(e.target.value) || 0 })}
                placeholder="0"
                disabled={isSubmitting}
              />
              <p className="text-sm text-muted-foreground">
                Lower numbers appear first in the list
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Model'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}