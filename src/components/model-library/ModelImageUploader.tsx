import React, { useState, useCallback } from 'react';
import { Upload, Image as ImageIcon } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { useToast } from '../ui/use-toast';
import { ModelImage, uploadModelImage } from '../../hooks/useModelLibrary';
import { cn } from '../common/utils/utils';

interface ModelImageUploaderProps {
  modelId: string;
  angleType: string;
  currentImage?: ModelImage;
  onUploadSuccess: () => void;
}

export function ModelImageUploader({
  modelId,
  angleType,
  currentImage,
  onUploadSuccess
}: ModelImageUploaderProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Invalid file type',
        description: 'Please upload an image file.',
        variant: 'destructive'
      });
      return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please upload an image smaller than 10MB.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(20);

      await uploadModelImage(
        modelId,
        angleType as any,
        file,
        queryClient,
        (progress) => setUploadProgress(20 + (progress * 0.8))
      );

      toast({
        title: 'Image uploaded',
        description: `${angleType} image has been uploaded successfully.`
      });

      // No need for setTimeout - cache invalidation handles refresh
      onUploadSuccess();
    } catch (error: any) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload failed',
        description: error.message || 'Failed to upload image. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  return (
    <div className="w-full h-full flex items-center justify-center p-4">
      <div
        className={cn(
          "relative w-full max-w-sm",
          isUploading && "pointer-events-none"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          id={`file-${modelId}-${angleType}`}
          className="sr-only"
          accept="image/*"
          onChange={handleFileSelect}
          disabled={isUploading}
        />

        <div className="flex flex-col items-center justify-center text-center">
          {isUploading ? (
            <div className="space-y-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white" />
              <p className="text-sm font-medium text-white">Uploading...</p>
              <Progress value={uploadProgress} className="w-32" />
            </div>
          ) : (
            <>
              <Upload className="h-10 w-10 text-white mb-3" />
              <p className="text-base font-medium text-white mb-2">
                {currentImage ? 'Replace image' : 'Upload image'}
              </p>
              <p className="text-sm text-white/80 mb-4">
                Drag & drop or click to browse
              </p>
              <label
                htmlFor={`file-${modelId}-${angleType}`}
                className="cursor-pointer"
              >
                <Button 
                  type="button" 
                  variant="secondary" 
                  size="sm" 
                  className="bg-white text-black hover:bg-gray-100"
                  asChild
                >
                  <span>
                    <ImageIcon className="h-4 w-4 mr-2" />
                    Choose file
                  </span>
                </Button>
              </label>
              <div className="mt-4 text-xs text-white/60">
                <p>JPG or PNG • Max 10MB</p>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}