import React, { useState } from 'react';
import { ImageOff, Expand, Edit2 } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Skeleton } from '../ui/skeleton';
import { ModelImage } from '../../hooks/useModelLibrary';
import { ModelImageUploader } from './ModelImageUploader';
import { EditAnglePromptDialog } from './EditAnglePromptDialog';
import { supabase } from '../common/utils/supabase';
import { FullScreenImageViewer } from '../common/FullScreenImageViewer';
import { useFullScreenImageViewer } from '../../hooks/useFullScreenImageViewer';

interface ModelPreviewGridProps {
  modelId: string;
  modelCode: string;
  modelName?: string;
  images: ModelImage[];
  isLoading: boolean;
  canEdit: boolean;
  onImageUpdate: () => void;
  showCategories?: boolean; // Whether to show category grouping (default: true)
  angleTypes?: string[]; // Specific angle types to display (if not provided, shows all)
  gridCols?: string; // Custom grid columns class (e.g., "grid-cols-1 md:grid-cols-2")
}

const angleCategories = {
  face: {
    label: 'Face',
    angles: ['face', 'face-ai']
  },
  'half-body': {
    label: 'Half Body',
    angles: ['half-body-front', 'half-body-back', 'half-body-34-left', 'half-body-34-right']
  },
  'full-height': {
    label: 'Full Height',
    angles: [
      'full-body-front',
      'full-body-back',
      'full-body-side-left',
      'full-body-side-right',
      'full-body-34-left',
      'full-body-34-right'
    ]
  }
};

const angleLabels: Record<string, string> = {
  'face': 'Display Face',
  'face-ai': 'AI Generation',
  'half-body-front': 'Front',
  'half-body-back': 'Back',
  'half-body-34-left': '3/4 Left',
  'half-body-34-right': '3/4 Right',
  'full-body-front': 'Front',
  'full-body-back': 'Back',
  'full-body-side-left': 'Side Left',
  'full-body-side-right': 'Side Right',
  'full-body-34-left': '3/4 Left',
  'full-body-34-right': '3/4 Right'
};

export function ModelPreviewGrid({
  modelId,
  modelCode,
  modelName = '',
  images,
  isLoading,
  canEdit,
  onImageUpdate,
  showCategories = true,
  angleTypes,
  gridCols
}: ModelPreviewGridProps) {
  const [editingAnglePrompt, setEditingAnglePrompt] = useState<{
    image: ModelImage;
    angleName: string;
  } | null>(null);
  const getImageUrl = (storagePath: string | null) => {
    if (!storagePath) return null;
    // Remove the 'model-library/' prefix if it exists
    const cleanPath = storagePath.replace(/^model-library\//, '');
    const { data } = supabase.storage.from('model-library').getPublicUrl(cleanPath);
    return data?.publicUrl;
  };

  const getImageForAngle = (angleType: string) => {
    return images.find(img => img.angle_type === angleType);
  };

  // Helper function to check if image is actually uploaded (not a placeholder)
  const isImageUploaded = (storagePath: string | null) => {
    return storagePath && !storagePath.includes('.placeholder');
  };

  // Prepare images for full-screen viewer
  const viewerImages = React.useMemo(() => {
    return images
      .filter(img => isImageUploaded(img.storage_path))
      .map(img => ({
        id: img.id,
        url: getImageUrl(img.storage_path!) || '',
        title: `${modelCode} - ${angleLabels[img.angle_type] || img.angle_type}`,
        description: img.angle_type
      }));
  }, [images, modelCode]);

  const { isOpen, currentIndex, openViewer, closeViewer, navigateToImage } = useFullScreenImageViewer(viewerImages);

  // Determine which angles to display
  const anglesToDisplay = React.useMemo(() => {
    if (angleTypes) {
      // If specific angle types are provided, use only those
      return angleTypes;
    }
    // Otherwise, show all angles from all categories
    return Object.values(angleCategories).flatMap(cat => cat.angles);
  }, [angleTypes]);

  // Determine grid columns class
  const gridClassName = gridCols || "grid-cols-2 md:grid-cols-3 lg:grid-cols-4";

  // Helper to render a single angle card
  const renderAngleCard = (angleType: string) => {
    const image = getImageForAngle(angleType);
    const imageUrl = image?.storage_path && isImageUploaded(image.storage_path) ? getImageUrl(image.storage_path) : null;
    
    return (
      <Card key={angleType} className="overflow-hidden">
        <CardContent className="p-0">
          <div className="aspect-[3/4] relative bg-muted group">
            {imageUrl ? (
              <>
                <img
                  src={imageUrl}
                  alt={`${modelCode} - ${angleLabels[angleType]}`}
                  className="w-full h-full object-cover cursor-pointer"
                  onClick={() => {
                    const imageIndex = viewerImages.findIndex(img => img.id === image.id);
                    if (imageIndex !== -1) {
                      openViewer(imageIndex);
                    }
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
                {/* Expand icon on hover */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                  <div className="bg-black/50 rounded-full p-1.5">
                    <Expand className="h-4 w-4 text-white" />
                  </div>
                </div>
              </>
            ) : null}
            
            <div className={`absolute inset-0 flex flex-col items-center justify-center p-4 ${imageUrl ? 'hidden' : ''}`}>
              <ImageOff className="h-12 w-12 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground text-center">
                No image uploaded
              </p>
            </div>

            {canEdit && imageUrl && (
              <div className="absolute inset-0 bg-black/60 opacity-0 hover:opacity-100 transition-opacity">
                <ModelImageUploader
                  modelId={modelId}
                  angleType={angleType}
                  currentImage={image}
                  onUploadSuccess={onImageUpdate}
                />
              </div>
            )}
            
            {canEdit && !imageUrl && (
              <ModelImageUploader
                modelId={modelId}
                angleType={angleType}
                currentImage={image}
                onUploadSuccess={onImageUpdate}
              />
            )}
          </div>
          
          <div className="p-3 space-y-2">
            <Badge variant="secondary" className="w-full justify-center">
              {angleLabels[angleType] || angleType}
            </Badge>
            {canEdit && (
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs"
                onClick={() => {
                  if (image?.id) {
                    setEditingAnglePrompt({
                      image: image,
                      angleName: angleLabels[angleType] || angleType
                    });
                  } else {
                    // If no image record exists yet, we need to create one first
                    console.warn(`No image record exists for ${angleType} angle. Please upload an image first.`);
                  }
                }}
                disabled={!image?.id}
                title={!image?.id ? "Upload an image first to add custom prompt" : "Edit angle prompt"}
              >
                <Edit2 className="w-3 h-3 mr-1" />
                Edit Prompt
              </Button>
            )}
            {image?.angle_prompt_text && (
              <p className="text-xs text-muted-foreground text-center truncate" title={image.angle_prompt_text}>
                Has custom prompt
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-8">
        {showCategories ? (
          Object.entries(angleCategories).map(([category, { label }]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold mb-4">{label}</h3>
              <div className={`grid ${gridClassName} gap-4`}>
                {[...Array(4)].map((_, i) => (
                  <Skeleton key={i} className="aspect-[3/4] rounded-lg" />
                ))}
              </div>
            </div>
          ))
        ) : (
          <div className={`grid ${gridClassName} gap-4`}>
            {[...Array(anglesToDisplay.length || 4)].map((_, i) => (
              <Skeleton key={i} className="aspect-[3/4] rounded-lg" />
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      <div className={showCategories ? "space-y-8" : ""}>
        {showCategories ? (
          // Render with category grouping
          Object.entries(angleCategories).map(([category, { label, angles }]) => {
            // Filter angles if angleTypes is provided
            const filteredAngles = angleTypes 
              ? angles.filter(angle => angleTypes.includes(angle))
              : angles;
            
            // Skip category if no angles to show
            if (filteredAngles.length === 0) return null;
            
            return (
              <div key={category}>
                <h3 className="text-lg font-semibold mb-4">{label}</h3>
                <div className={`grid ${gridClassName} gap-4`}>
                  {filteredAngles.map(angleType => renderAngleCard(angleType))}
                </div>
              </div>
            );
          })
        ) : (
          // Render without category grouping
          <div className={`grid ${gridClassName} gap-4`}>
            {anglesToDisplay.map(angleType => renderAngleCard(angleType))}
          </div>
        )}
      </div>

      {/* Full-screen image viewer */}
      <FullScreenImageViewer
        images={viewerImages}
        currentIndex={currentIndex}
        isOpen={isOpen}
        onClose={closeViewer}
        onNavigate={navigateToImage}
      />

      {/* Edit angle prompt dialog */}
      {editingAnglePrompt && (
        <EditAnglePromptDialog
          modelImage={editingAnglePrompt.image}
          modelName={modelName || modelCode}
          angleName={editingAnglePrompt.angleName}
          isOpen={!!editingAnglePrompt}
          onClose={() => setEditingAnglePrompt(null)}
          onSuccess={() => {
            setEditingAnglePrompt(null);
            onImageUpdate();
          }}
        />
      )}
    </>
  );
}