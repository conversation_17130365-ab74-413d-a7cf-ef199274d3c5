import { useEffect } from 'react';
import { useStorageUsage, formatBytes } from '../common/hooks/useStorageUsage';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Progress } from '../ui/progress';
import { Skeleton } from '../ui/skeleton';
import { Database, File, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';

export function StorageUsageDetails() {
  const { data: storageUsage, isLoading, error, refetch } = useStorageUsage();

  // Log data for debugging
  useEffect(() => {
    if (storageUsage) {
      console.log('Storage usage data:', storageUsage);
    }
    if (error) {
      console.error('Storage usage error:', error);
    }
  }, [storageUsage, error]);

  // If there's an error, show error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Storage Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error loading storage data</AlertTitle>
            <AlertDescription>
              {error.message}
              <div className="mt-4">
                <button 
                  onClick={() => refetch()} 
                  className="block text-xs underline"
                >
                  Try again
                </button>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // If still loading, show skeleton
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Storage Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-16 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no data, show empty state
  if (!storageUsage || storageUsage.buckets.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Storage Usage
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
            <File className="h-12 w-12 mb-4 opacity-50" />
            <p>No storage data available</p>
            <p className="text-sm">Storage buckets may not be configured yet</p>
            <div className="mt-4">
              <button 
                onClick={() => refetch()} 
                className="block text-xs underline"
              >
                Refresh data
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort buckets by size (largest first)
  const sortedBuckets = [...storageUsage.buckets].sort(
    (a, b) => b.total_size_bytes - a.total_size_bytes
  );

  // Calculate percentages for the progress bars
  const calculatePercentage = (bucketSize: number) => {
    return (bucketSize / storageUsage.totalSizeBytes) * 100;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Database className="mr-2 h-5 w-5" />
          Storage Usage
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Total Storage</span>
              <span className="text-sm font-medium">{formatBytes(storageUsage.totalSizeBytes)}</span>
            </div>
            <div className="flex justify-between items-center text-xs text-muted-foreground">
              <span>Total Files: {storageUsage.totalFiles}</span>
              <span>Buckets: {storageUsage.buckets.length}</span>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="text-sm font-medium">Storage by Bucket</h4>
            {sortedBuckets.map((bucket, index) => (
              <div key={index} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">{bucket.bucket_name}</span>
                  <span className="text-sm">{formatBytes(bucket.total_size_bytes)}</span>
                </div>
                <Progress value={calculatePercentage(bucket.total_size_bytes)} className="h-2" />
                <div className="flex justify-between items-center text-xs text-muted-foreground">
                  <span>{bucket.total_files} files</span>
                  <span>{Math.round(calculatePercentage(bucket.total_size_bytes))}% of total</span>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-right">
            <button 
              onClick={() => refetch()} 
              className="text-xs text-muted-foreground hover:text-foreground"
            >
              Refresh
            </button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default StorageUsageDetails; 