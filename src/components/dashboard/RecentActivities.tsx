import { Users, Image, Paintbrush, CheckCircle2, Package, FolderPlus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../common/utils/utils';
import { useOrganizations } from '../common/hooks/useOrganizations';
import { useCollections } from '../common/hooks/useCollections';
import { useProducts } from '../common/hooks/useProducts';
import { useAssets } from '../common/hooks/useAssets';
import { Skeleton } from '../ui/skeleton';
import type { LucideIcon } from 'lucide-react';
import { useSupabase } from '../../contexts/SupabaseContext';

// Define the activity type
interface Activity {
  id: string;
  description: string;
  time: string;
  icon: LucideIcon;
  color: string;
  user: string;
  date: Date;
}

// Helper function to format relative time
const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return `${diffInSeconds} seconds ago`;
  }
  
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  }
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  }
  
  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
};

export function RecentActivities() {
  // Fetch data from Supabase
  const { data: organizations = [], isLoading: isLoadingOrganizations } = useOrganizations();
  const { data: collections = [], isLoading: isLoadingCollections } = useCollections();
  const { data: products = [], isLoading: isLoadingProducts } = useProducts();
  const { data: assets = [], isLoading: isLoadingAssets } = useAssets();
  const { user } = useSupabase();
  
  const isLoading = isLoadingOrganizations || isLoadingCollections || isLoadingProducts || isLoadingAssets;
  
  // Generate activities based on recent changes
  const generateActivities = (): Activity[] => {
    const activities: Activity[] = [];
    const username = user?.email || 'Admin';
    
    // Add organization activities
    organizations.forEach(organization => {
      activities.push({
        id: `organization-${organization.id}`,
        description: `Organization "${organization.name}" created`,
        time: formatRelativeTime(new Date(organization.created_at || Date.now())),
        icon: Users,
        color: 'text-blue-500 bg-blue-100 dark:bg-blue-950',
        user: username,
        date: new Date(organization.created_at || Date.now())
      });
    });
    
    // Add collection activities
    collections.forEach(collection => {
      const organization = organizations.find(o => o.id === collection.organization_id);
      activities.push({
        id: `collection-${collection.id}`,
        description: `Collection "${collection.name}" created for ${organization?.name || 'an organization'}`,
        time: formatRelativeTime(new Date(collection.created_at || Date.now())),
        icon: FolderPlus,
        color: 'text-indigo-500 bg-indigo-100 dark:bg-indigo-950',
        user: username,
        date: new Date(collection.created_at || Date.now())
      });
    });
    
    // Add product activities
    products.forEach(product => {
      activities.push({
        id: `product-${product.id}`,
        description: `Product "${product.name}" added`,
        time: formatRelativeTime(new Date(product.created_at || Date.now())),
        icon: Package,
        color: 'text-purple-500 bg-purple-100 dark:bg-purple-950',
        user: username,
        date: new Date(product.created_at || Date.now())
      });
    });
    
    // Add asset activities
    assets.forEach(asset => {
      let description = '';
      let icon = Image;
      let color = 'text-orange-500 bg-orange-100 dark:bg-orange-950';
      
      switch (asset.workflow_stage) {
        case 'upload':
          description = `Asset "${asset.file_name}" uploaded`;
          break;
        case 'draft':
          description = `Draft created for "${asset.file_name}"`;
          break;
        case 'upscale':
          description = `"${asset.file_name}" sent for upscaling`;
          icon = Paintbrush;
          break;
        case 'retouch':
          description = `"${asset.file_name}" sent for retouching`;
          icon = Paintbrush;
          color = 'text-purple-500 bg-purple-100 dark:bg-purple-950';
          break;
        case 'final':
          description = `"${asset.file_name}" finalized`;
          icon = CheckCircle2;
          color = 'text-green-500 bg-green-100 dark:bg-green-950';
          break;
      }
      
      activities.push({
        id: `asset-${asset.id}`,
        description,
        time: formatRelativeTime(new Date(asset.updated_at || Date.now())),
        icon,
        color,
        user: 'System',
        date: new Date(asset.updated_at || Date.now())
      });
    });
    
    // Sort by date (newest first) and take the first 5
    return activities
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, 5);
  };
  
  const activities = isLoading ? [] : generateActivities();

  return (
    <Card className="animate-enter">
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-start gap-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-24" />
                </div>
              </div>
            ))}
          </div>
        ) : activities.length > 0 ? (
          <div className="space-y-4">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-4">
                <div className={cn("rounded-full p-2 mt-0.5", activity.color)}>
                  <activity.icon size={14} />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium">{activity.description}</p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <span>{activity.user}</span>
                    <span>•</span>
                    <span>{activity.time}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            <p>No recent activities found</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default RecentActivities;
