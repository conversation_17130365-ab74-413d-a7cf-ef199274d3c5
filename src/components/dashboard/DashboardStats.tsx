import { Users, PackageOpen, Image, Zap, Database } from 'lucide-react';
import { Card, CardContent } from '../ui/card';
import { cn } from '../common/utils/utils';
import { useOrganizations } from '../common/hooks/useOrganizations';
import { useCollections } from '../common/hooks/useCollections';
import { useProducts } from '../common/hooks/useProducts';
import { useAssets } from '../common/hooks/useAssets';
import { useStorageUsage, formatBytes } from '../common/hooks/useStorageUsage';
import { Skeleton } from '../ui/skeleton';

export function DashboardStats() {
  // Fetch data from Supabase
  const { data: organizations = [], isLoading: isLoadingOrganizations } = useOrganizations();
  const { data: collections = [], isLoading: isLoadingCollections } = useCollections();
  const { data: products = [], isLoading: isLoadingProducts } = useProducts();
  const { data: assets = [], isLoading: isLoadingAssets } = useAssets();
  const { data: storageUsage, isLoading: isLoadingStorage } = useStorageUsage();

  // Calculate statistics
  const activeOrganizations = organizations.length;
  const totalProducts = products.length;
  const totalAssets = assets.length;
  
  // Calculate assets with workflow_stage = 'upscale' for active generations
  // Using 'upscale' as a proxy for "in progress" assets
  const activeGenerations = assets.filter(asset => 
    asset.workflow_stage === 'upscale'
  ).length;
  
  // Calculate assets added this week for "Images Generated" change stat
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  const assetsThisWeek = assets.filter(asset => 
    asset.created_at && new Date(asset.created_at) > oneWeekAgo
  ).length;
  
  // Calculate products added this week for "Products Processed" change stat
  const productsThisWeek = products.filter(product => 
    product.created_at && new Date(product.created_at) > oneWeekAgo
  ).length;
  
  // Calculate organizations added this month for "Active Organizations" change stat
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const organizationsThisMonth = organizations.filter(organization => 
    organization.created_at && new Date(organization.created_at) > oneMonthAgo
  ).length;
  
  // Calculate queued assets for "Active Generations" change stat
  // Using 'draft' as a proxy for "queued" assets
  const queuedAssets = assets.filter(asset => 
    asset.workflow_stage === 'draft'
  ).length;

  // Format storage usage data
  const totalStorageUsed = storageUsage ? formatBytes(storageUsage.totalSizeBytes) : '0 Bytes';
  const totalFiles = storageUsage ? storageUsage.totalFiles : 0;

  const isLoading = isLoadingOrganizations || isLoadingCollections || 
                    isLoadingProducts || isLoadingAssets || isLoadingStorage;

  // Define stat cards with real data
  const statCards = [
    {
      title: 'Active Organizations',
      value: activeOrganizations.toString(),
      change: `+${organizationsThisMonth} this month`,
      icon: Users,
      color: 'bg-blue-100 text-blue-600 dark:bg-blue-950 dark:text-blue-300',
      isLoading
    },
    {
      title: 'Products Processed',
      value: totalProducts.toString(),
      change: `+${productsThisWeek} this week`,
      icon: PackageOpen,
      color: 'bg-indigo-100 text-indigo-600 dark:bg-indigo-950 dark:text-indigo-300',
      isLoading
    },
    {
      title: 'Images Generated',
      value: totalAssets.toString(),
      change: `+${assetsThisWeek} this week`,
      icon: Image,
      color: 'bg-purple-100 text-purple-600 dark:bg-purple-950 dark:text-purple-300',
      isLoading
    },
    {
      title: 'Active Generations',
      value: activeGenerations.toString(),
      change: `${queuedAssets} queued`,
      icon: Zap,
      color: 'bg-orange-100 text-orange-600 dark:bg-orange-950 dark:text-orange-300',
      isLoading
    },
    {
      title: 'Storage Usage',
      value: totalStorageUsed,
      change: `${totalFiles} files`,
      icon: Database,
      color: 'bg-green-100 text-green-600 dark:bg-green-950 dark:text-green-300',
      isLoading
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 xl:gap-4 animate-enter">
      {statCards.map((stat, i) => (
        <Card key={i} className="hover-scale">
          <CardContent className="p-4 sm:p-5 lg:p-6">
            <div className="flex justify-between items-start">
              <div className="flex-1 min-w-0">
                <p className="text-xs sm:text-sm font-medium text-muted-foreground truncate">{stat.title}</p>
                {stat.isLoading ? (
                  <>
                    <Skeleton className="h-7 w-16 mt-1" />
                    <Skeleton className="h-4 w-20 mt-1" />
                  </>
                ) : (
                  <>
                    <h3 className="text-xl sm:text-2xl font-bold mt-1 truncate">{stat.value}</h3>
                    <p className="text-xs text-muted-foreground mt-1 truncate">{stat.change}</p>
                  </>
                )}
              </div>
              <div className={cn("p-2 rounded-full flex-shrink-0", stat.color)}>
                <stat.icon size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

export default DashboardStats;
