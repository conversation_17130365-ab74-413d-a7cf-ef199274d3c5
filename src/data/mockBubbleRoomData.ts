import { AssetItem } from './productAssets';

// Mock data based on the Bubbleroom folder structure
export const bubbleRoomCollections = [
  { id: 1, name: 'Summer Collection 2023', organizationId: 1 },
  { id: 2, name: 'Fall Collection 2023', organizationId: 1 },
  { id: 3, name: 'Winter Collection 2023', organizationId: 1 }
];

export const bubbleRoomProducts = [
  { id: 1, name: '<PERSON><PERSON> Dress', collectionId: 1 },
  { id: 2, name: 'Denim Jacket', collectionId: 1 },
  { id: 3, name: 'Leather Boots', collectionId: 2 },
  { id: 4, name: 'Wool Sweater', collectionId: 3 }
];

// Generate mock assets based on real structure
export function getBubbleRoomAssets(): AssetItem[] {
  const assets: AssetItem[] = [];
  
  // For each product, create assets in different stages
  bubbleRoomProducts.forEach(product => {
    // Input images
    assets.push({
      id: `br-${product.id}-images-1`,
      name: `${product.name} Front.jpg`,
      preview: `https://placehold.co/600x800?text=${product.name}+Front`,
      type: 'product-front',
      stage: 'images',
      size: `${Math.round(2 * 100) / 100} MB`, // Format as string with MB
      dateCreated: new Date(2023, 5, 15).toISOString(),
      dateUpdated: new Date(2023, 5, 15).toISOString(),
      tags: ['bubbleroom', product.name, 'front'],
      productId: product.id
    });
    
    assets.push({
      id: `br-${product.id}-images-2`,
      name: `${product.name} Back.jpg`,
      preview: `https://placehold.co/600x800?text=${product.name}+Back`,
      type: 'product-back',
      stage: 'images',
      size: `${Math.round(1.8 * 100) / 100} MB`,
      dateCreated: new Date(2023, 5, 15).toISOString(),
      dateUpdated: new Date(2023, 5, 15).toISOString(),
      tags: ['bubbleroom', product.name, 'back'],
      productId: product.id
    });
    
    // Raw AI images (only for some products)
    if (product.id % 2 === 0) {
      assets.push({
        id: `br-${product.id}-raw-ai-1`,
        name: `${product.name} Raw AI.jpg`,
        preview: `https://placehold.co/600x800?text=${product.name}+Raw+AI`,
        type: 'product-front',
        stage: 'raw_ai_images',
        size: `${Math.round(3 * 100) / 100} MB`,
        dateCreated: new Date(2023, 6, 1).toISOString(),
        dateUpdated: new Date(2023, 6, 1).toISOString(),
        tags: ['bubbleroom', product.name, 'raw_ai_images'],
        productId: product.id
      });
    }
    
    // Upscaled images (only for some products)
    if (product.id % 3 === 0) {
      assets.push({
        id: `br-${product.id}-upscaled-1`,
        name: `${product.name} Upscaled.jpg`,
        preview: `https://placehold.co/600x800?text=${product.name}+Upscaled`,
        type: 'product-front',
        stage: 'upscaled',
        size: `${Math.round(5 * 100) / 100} MB`,
        dateCreated: new Date(2023, 6, 15).toISOString(),
        dateUpdated: new Date(2023, 6, 15).toISOString(),
        tags: ['bubbleroom', product.name, 'upscaled'],
        productId: product.id
      });
    }
    
    // Retouched images (only for some products)
    if (product.id % 4 === 0) {
      assets.push({
        id: `br-${product.id}-retouched-1`,
        name: `${product.name} Retouched.jpg`,
        preview: `https://placehold.co/600x800?text=${product.name}+Retouched`,
        type: 'product-front',
        stage: 'retouched',
        size: `${Math.round(4 * 100) / 100} MB`,
        dateCreated: new Date(2023, 7, 1).toISOString(),
        dateUpdated: new Date(2023, 7, 1).toISOString(),
        tags: ['bubbleroom', product.name, 'retouched'],
        productId: product.id
      });
    }
  });
  
  return assets;
}

// Function to get assets for a specific collection
export function getBubbleRoomCollectionAssets(collectionId: number): AssetItem[] {
  // Map collection ID to product IDs
  const productIds = bubbleRoomProducts
    .filter(product => product.collectionId === collectionId)
    .map(product => product.id);
  
  // Filter assets by product ID
  return getBubbleRoomAssets().filter(asset => 
    productIds.includes(asset.productId)
  );
} 