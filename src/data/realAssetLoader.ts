import { AssetItem } from './productAssets';
import path from 'path';
import fs from 'fs';

// Base path to client assets
const ASSETS_BASE_PATH = '/Users/<USER>/Documents/projects/fashionlab/fashionlab-v1/Assets/Clients';

// Function to load assets for a specific client
export async function loadClientAssets(clientName: string): Promise<AssetItem[]> {
  const clientPath = path.join(ASSETS_BASE_PATH, clientName);
  
  try {
    // Check if client directory exists
    if (!fs.existsSync(clientPath)) {
      console.error(`Client directory not found: ${clientPath}`);
      return [];
    }
    
    // Get all collections (subdirectories) for this client
    const collections = fs.readdirSync(clientPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
    
    let allAssets: AssetItem[] = [];
    
    // Process each collection
    for (const collection of collections) {
      const collectionPath = path.join(clientPath, collection);
      const collectionAssets = await processCollection(collectionPath, collection, clientName);
      allAssets = [...allAssets, ...collectionAssets];
    }
    
    return allAssets;
  } catch (error) {
    console.error(`Error loading assets for client ${clientName}:`, error);
    return [];
  }
}

// Process a collection directory and extract assets
async function processCollection(
  collectionPath: string, 
  collectionName: string, 
  clientName: string
): Promise<AssetItem[]> {
  try {
    // Check for workflow stage directories
    const stages = ['images', 'raw_ai_images', 'upscaled', 'retouched'];
    let collectionAssets: AssetItem[] = [];
    
    for (const stage of stages) {
      const stagePath = path.join(collectionPath, stage);
      
      if (fs.existsSync(stagePath)) {
        const stageAssets = await processStageDirectory(
          stagePath, 
          stage, 
          collectionName, 
          clientName
        );
        collectionAssets = [...collectionAssets, ...stageAssets];
      }
    }
    
    return collectionAssets;
  } catch (error) {
    console.error(`Error processing collection ${collectionName}:`, error);
    return [];
  }
}

// Process a stage directory and extract assets
async function processStageDirectory(
  stagePath: string,
  stage: string,
  collectionName: string,
  clientName: string
): Promise<AssetItem[]> {
  try {
    // Get all files in this stage directory
    const files = fs.readdirSync(stagePath)
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.webp'].includes(ext);
      });
    
    // Convert files to asset items
    return files.map(file => {
      const filePath = path.join(stagePath, file);
      const stats = fs.statSync(filePath);
      
      // Determine asset type based on filename or path
      let assetType: 'product-front' | 'product-back' | 'product-detail' | 'model' | 'lighting' | 'pose' = 'product-front';
      
      if (file.toLowerCase().includes('back')) {
        assetType = 'product-back';
      } else if (file.toLowerCase().includes('detail')) {
        assetType = 'product-detail';
      } else if (file.toLowerCase().includes('model')) {
        assetType = 'model';
      }
      
      // Create asset item
      return {
        id: `${clientName}-${collectionName}-${stage}-${file}`,
        name: file,
        preview: `file://${filePath}`, // Use file:// protocol for local files
        type: assetType,
        stage: stage,
        size: stats.size,
        dateCreated: stats.birthtime,
        dateUpdated: stats.mtime,
        tags: [collectionName, stage, assetType],
        productId: 0, // We'll need to determine this from filename or directory structure
        collectionId: 0 // We'll assign this based on collection name
      };
    });
  } catch (error) {
    console.error(`Error processing stage directory ${stagePath}:`, error);
    return [];
  }
}

// Function to get all clients
export async function getClients(): Promise<string[]> {
  try {
    return fs.readdirSync(ASSETS_BASE_PATH, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
  } catch (error) {
    console.error('Error getting clients:', error);
    return [];
  }
}

// Function to get collections for a client
export async function getClientCollections(clientName: string): Promise<string[]> {
  const clientPath = path.join(ASSETS_BASE_PATH, clientName);
  
  try {
    return fs.readdirSync(clientPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
  } catch (error) {
    console.error(`Error getting collections for client ${clientName}:`, error);
    return [];
  }
} 