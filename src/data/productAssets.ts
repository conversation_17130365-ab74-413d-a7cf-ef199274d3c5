import { AssetType, WorkflowStage } from '../contexts/FilterContext';
import { products } from '../pages/clients/OrganizationData';

export interface AssetItem {
  id: string;
  name: string;
  type: AssetType;
  preview: string;
  size: string;
  dateCreated: string;
  dateUpdated: string; // ISO date format for filtering
  tags: string[];
  stage: WorkflowStage;
  productId: string;
  metadata?: Record<string, any>; // Added for thumbnail paths and other processing info
  retouch_substage?: string; // Added for retouch workflow substages
}

export const productAssets: AssetItem[] = [
  // Product 1 - Input assets
  {
    id: '1-input-1',
    name: 'Front Reference.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '2.4 MB',
    dateCreated: '2 days ago',
    dateUpdated: '2023-06-14T10:30:00',
    tags: ['reference', 'front'],
    stage: 'images',
    productId: '1'
  },
  {
    id: '1-input-2',
    name: 'Back Reference.jpg',
    type: 'product-back',
    preview: '/placeholder.svg',
    size: '2.1 MB',
    dateCreated: '2 days ago',
    dateUpdated: '2023-06-14T10:35:00',
    tags: ['reference', 'back'],
    stage: 'images',
    productId: '1'
  },
  {
    id: '1-input-3',
    name: 'Detail Reference.jpg',
    type: 'product-detail',
    preview: '/placeholder.svg',
    size: '1.8 MB',
    dateCreated: '2 days ago',
    dateUpdated: '2023-06-14T10:40:00',
    tags: ['reference', 'detail'],
    stage: 'images',
    productId: '1'
  },
  {
    id: '1-input-4',
    name: 'Model Reference.jpg',
    type: 'model',
    preview: '/placeholder.svg',
    size: '3.2 MB',
    dateCreated: '3 days ago',
    dateUpdated: '2023-06-13T14:20:00',
    tags: ['model', 'reference'],
    stage: 'images',
    productId: '1'
  },
  {
    id: '1-input-5',
    name: 'Lighting Setup.jpg',
    type: 'lighting',
    preview: '/placeholder.svg',
    size: '1.5 MB',
    dateCreated: '3 days ago',
    dateUpdated: '2023-06-13T14:30:00',
    tags: ['lighting', 'reference'],
    stage: 'images',
    productId: '1'
  },
  {
    id: '1-input-6',
    name: 'Pose Reference.jpg',
    type: 'pose',
    preview: '/placeholder.svg',
    size: '1.9 MB',
    dateCreated: '3 days ago',
    dateUpdated: '2023-06-13T14:40:00',
    tags: ['pose', 'reference'],
    stage: 'images',
    productId: '1'
  },
  
  // Product 1 - Raw AI Images
  {
    id: '1-raw-ai-1',
    name: 'Front Raw AI 1.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '3.1 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:20:00',
    tags: ['raw_ai_images', 'front'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  {
    id: '1-raw-ai-2',
    name: 'Front Raw AI 2.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '3.3 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:25:00',
    tags: ['raw_ai_images', 'front'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  {
    id: '1-raw-ai-3',
    name: 'Back Raw AI 1.jpg',
    type: 'product-back',
    preview: '/placeholder.svg',
    size: '2.9 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:30:00',
    tags: ['raw_ai_images', 'back'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  {
    id: '1-raw-ai-4',
    name: 'Back Raw AI 2.jpg',
    type: 'product-back',
    preview: '/placeholder.svg',
    size: '3.0 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:35:00',
    tags: ['raw_ai_images', 'back'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  {
    id: '1-raw-ai-5',
    name: 'Detail Raw AI 1.jpg',
    type: 'product-detail',
    preview: '/placeholder.svg',
    size: '2.7 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:40:00',
    tags: ['raw_ai_images', 'detail'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  {
    id: '1-raw-ai-6',
    name: 'Detail Raw AI 2.jpg',
    type: 'product-detail',
    preview: '/placeholder.svg',
    size: '2.8 MB',
    dateCreated: '1 day ago',
    dateUpdated: '2023-06-15T09:45:00',
    tags: ['raw_ai_images', 'detail'],
    stage: 'raw_ai_images',
    productId: '1'
  },
  
  // Product 1 - Upscaled assets
  {
    id: '1-upscaled-1',
    name: 'Front Upscaled 1.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '5.2 MB',
    dateCreated: '8 hours ago',
    dateUpdated: '2023-06-15T16:20:00',
    tags: ['upscaled', 'front'],
    stage: 'upscaled',
    productId: '1'
  },
  {
    id: '1-upscaled-2',
    name: 'Back Upscaled 1.jpg',
    type: 'product-back',
    preview: '/placeholder.svg',
    size: '5.1 MB',
    dateCreated: '8 hours ago',
    dateUpdated: '2023-06-15T16:25:00',
    tags: ['upscaled', 'back'],
    stage: 'upscaled',
    productId: '1'
  },
  {
    id: '1-upscaled-3',
    name: 'Detail Upscaled 1.jpg',
    type: 'product-detail',
    preview: '/placeholder.svg',
    size: '4.9 MB',
    dateCreated: '8 hours ago',
    dateUpdated: '2023-06-15T16:30:00',
    tags: ['upscaled', 'detail'],
    stage: 'upscaled',
    productId: '1'
  },
  
  // Product 1 - Retouched assets
  {
    id: '1-retouched-1',
    name: 'Front Final.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '6.1 MB',
    dateCreated: '3 hours ago',
    dateUpdated: '2023-06-15T21:20:00',
    tags: ['retouched', 'front', 'final'],
    stage: 'retouched',
    productId: '1'
  },
  {
    id: '1-retouched-2',
    name: 'Back Final.jpg',
    type: 'product-back',
    preview: '/placeholder.svg',
    size: '5.9 MB',
    dateCreated: '3 hours ago',
    dateUpdated: '2023-06-15T21:25:00',
    tags: ['retouched', 'back', 'final'],
    stage: 'retouched',
    productId: '1'
  },
  
  // Product 2 - sample assets for all stages
  {
    id: '2-input-1',
    name: 'Product 2 Front Reference.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '2.5 MB',
    dateCreated: '5 days ago',
    dateUpdated: '2023-06-10T10:30:00',
    tags: ['reference', 'front'],
    stage: 'images',
    productId: '2'
  },
  {
    id: '2-raw-ai-1',
    name: 'Product 2 Front Raw AI.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '3.2 MB',
    dateCreated: '4 days ago',
    dateUpdated: '2023-06-11T10:30:00',
    tags: ['raw_ai_images', 'front'],
    stage: 'raw_ai_images',
    productId: '2'
  },
  {
    id: '2-upscaled-1',
    name: 'Product 2 Front Upscaled.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '5.0 MB',
    dateCreated: '3 days ago',
    dateUpdated: '2023-06-12T10:30:00',
    tags: ['upscaled', 'front'],
    stage: 'upscaled',
    productId: '2'
  },
  {
    id: '2-retouched-1',
    name: 'Product 2 Front Final.jpg',
    type: 'product-front',
    preview: '/placeholder.svg',
    size: '6.0 MB',
    dateCreated: '2 days ago',
    dateUpdated: '2023-06-13T10:30:00',
    tags: ['retouched', 'front', 'final'],
    stage: 'retouched',
    productId: '2'
  }
];

// Helper function to get assets by product ID and apply filters
export const getFilteredAssets = (
  productId: string,
  stage: WorkflowStage | 'all',
  filters: {
    workflowStages: WorkflowStage[];
    assetTypes: AssetType[];
    timeUpdated: string;
  }
): AssetItem[] => {
  // Filter by product ID
  let filteredAssets = productAssets.filter(asset => asset.productId === productId);
  
  // Apply workflow stage filter if specified
  if (filters.workflowStages.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      filters.workflowStages.includes(asset.stage)
    );
  }
  
  // Apply asset type filter if specified
  if (filters.assetTypes.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      filters.assetTypes.includes(asset.type)
    );
  }
  
  // Apply time filter if not set to 'all'
  if (filters.timeUpdated !== 'all') {
    const now = new Date();
    const getStartDate = () => {
      switch (filters.timeUpdated) {
        case 'today':
          return new Date(now.setHours(0, 0, 0, 0));
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          weekStart.setHours(0, 0, 0, 0);
          return weekStart;
        case 'month':
          return new Date(now.getFullYear(), now.getMonth(), 1);
        case 'quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          return new Date(now.getFullYear(), quarter * 3, 1);
        default:
          return new Date(0); // Beginning of time
      }
    };
    
    const startDate = getStartDate();
    filteredAssets = filteredAssets.filter(asset => {
      const assetDate = new Date(asset.dateUpdated);
      return assetDate >= startDate;
    });
  }
  
  return filteredAssets;
};

// Helper function to get all assets for a collection
export const getCollectionAssets = (
  collectionId: number,
  filters: {
    workflowStages: WorkflowStage[];
    assetTypes: AssetType[];
    timeUpdated: string;
    productIds: string[];
  }
): AssetItem[] => {
  // Get all products in this collection
  const collectionProductIds = products
    .filter(p => p.collectionId === collectionId)
    .map(p => p.id);
  
  // Start with all assets for products in this collection
  let filteredAssets = productAssets.filter(asset => 
    collectionProductIds.includes(asset.productId)
  );
  
  // Apply product filter if specified
  if (filters.productIds && filters.productIds.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      filters.productIds.includes(asset.productId)
    );
  }
  
  // Apply workflow stage filter if specified
  if (filters.workflowStages.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      filters.workflowStages.includes(asset.stage)
    );
  }
  
  // Apply asset type filter if specified
  if (filters.assetTypes.length > 0) {
    filteredAssets = filteredAssets.filter(asset => 
      filters.assetTypes.includes(asset.type)
    );
  }
  
  // Apply time filter if not set to 'all'
  if (filters.timeUpdated !== 'all') {
    const now = new Date();
    const getStartDate = () => {
      switch (filters.timeUpdated) {
        case 'today':
          return new Date(now.setHours(0, 0, 0, 0));
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay());
          weekStart.setHours(0, 0, 0, 0);
          return weekStart;
        case 'month':
          return new Date(now.getFullYear(), now.getMonth(), 1);
        case 'quarter':
          const quarter = Math.floor(now.getMonth() / 3);
          return new Date(now.getFullYear(), quarter * 3, 1);
        default:
          return new Date(0); // Beginning of time
      }
    };
    
    const startDate = getStartDate();
    filteredAssets = filteredAssets.filter(asset => {
      const assetDate = new Date(asset.dateUpdated);
      return assetDate >= startDate;
    });
  }
  
  return filteredAssets;
};
