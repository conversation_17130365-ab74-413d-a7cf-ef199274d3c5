import React, { useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Plus, Users, Settings, Upload } from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import PageTitle from '../components/ui/PageTitle';
import { Skeleton } from '../components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../components/ui/dialog";
import { Label } from "../components/ui/label";
import { Input } from "../components/ui/input";
import { Textarea } from "../components/ui/textarea";

// Import refactored components
import CollectionList from './clients/CollectionList';
import ClientMetrics from './clients/ClientMetrics';
import { useOrganization } from '../components/common/hooks/useOrganizations';
import { useCollections } from '../components/common/hooks/useCollections';
import { useProducts } from '../components/common/hooks/useProducts';
import { useAssets } from '../components/common/hooks/useAssets';
import { useSupabase } from '../contexts/SupabaseContext';
import { useToast } from '../components/ui/use-toast';
import { supabase, STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { useFreelancerStatus } from '../components/common/hooks/useIsFreelancer';

// Add type definitions for database entities
interface Product {
  id: string;
  collection_id: string;
}

interface Collection {
  id: string;
  organization_id: string;
  name: string;
  description: string | null;
  status: string;
  updated_at: string | null;
  cover_image_url: string | null;
}

// Add a description field to the organization type
interface ExtendedOrganization {
  id: string;
  name: string;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
  description?: string | null;
}

// Define the collection type locally
interface CollectionType {
  id: string;
  name: string;
  description: string;
  cover_image_url?: string | null;
  productCount: number;
  status: string;
  lastUpdated: string;
}

export function OrganizationDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { canCreateCollections, canManageOrgSettings, canInviteUsers } = useFreelancerStatus();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [editOrganizationName, setEditOrganizationName] = useState('');
  const [editOrganizationDescription, setEditOrganizationDescription] = useState('');
  const [editOrganizationLogo, setEditOrganizationLogo] = useState<File | null>(null);
  const [isUpdatingOrganization, setIsUpdatingOrganization] = useState(false);
  
  // Fetch organization data
  const { 
    data: organizationData, 
    isLoading: isLoadingOrganization, 
    isError: isOrganizationError,
    error: organizationError
  } = useOrganization(id);
  
  // Cast organization data to extended type
  const organization = organizationData as ExtendedOrganization | undefined;
  
  // Fetch collections for this client
  const { 
    data: collectionsData = [], 
    isLoading: isLoadingCollections,
    refetch: refetchCollections
  } = useCollections({
    organizationId: id
  });
  
  // Get all collection IDs for this client
  const collectionIds = collectionsData.map(collection => collection.id);
  
  // Fetch products for these collections
  const { 
    data: products = [], 
    isLoading: isLoadingProducts 
  } = useProducts();
  
  // Filter products that belong to this client's collections
  const clientProducts = products.filter(product => 
    product.collection_id && collectionIds.includes(product.collection_id)
  );
  
  // Calculate metrics
  const activeCollections = collectionsData.filter(c => c.status === 'active').length;
  const totalProducts = clientProducts.length;
  
  // Calculate products by stage from assets
  const { data: assets = [] } = useAssets({ collectionId: collectionIds.length === 1 ? collectionIds[0] : undefined });
  const productsInProgress = assets.filter(a => ['upscale', 'retouch'].includes(a.stage)).length;
  const completedProducts = assets.filter(a => a.stage === 'final').length;
  
  // Transform collections data to match CollectionType
  const collections: CollectionType[] = collectionsData.map(collection => ({
    id: collection.id,
    name: collection.name,
    description: collection.description || '',
    cover_image_url: collection.cover_image_url,
    productCount: products.filter(p => p.collection_id === collection.id).length,
    status: collection.status.charAt(0).toUpperCase() + collection.status.slice(1), // Capitalize first letter
    lastUpdated: new Date(collection.updated_at || new Date()).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }));
  
  // Check if collections are empty
  const hasCollections = collections.length > 0;
  
  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setEditOrganizationLogo(e.target.files[0]);
    }
  };
  
  // Open edit dialog with current organization data
  const handleEditClick = () => {
    if (organization) {
      setEditOrganizationName(organization.name);
      setEditOrganizationDescription(organization.description || '');
      setIsEditDialogOpen(true);
    }
  };
  
  // Handle organization update
  const handleUpdateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isUpdatingOrganization || !organization) return;
    if (!editOrganizationName.trim()) {
      toast({
        title: "Error",
        description: "Organization name is required",
        variant: "destructive",
      });
      return;
    }
    
    setIsUpdatingOrganization(true);
    
    try {
      let logoUrl = organization.logo_url;
      
      // Upload new logo if provided
      if (editOrganizationLogo) {
        const filePath = `organizations/${id}/logo/${editOrganizationLogo.name}`;
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .upload(filePath, editOrganizationLogo);
        
        if (uploadError) {
          console.error('Error uploading logo:', uploadError);
          throw uploadError;
        }
        
        // Get public URL for database reference
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .getPublicUrl(filePath);
        
        logoUrl = publicUrlData.publicUrl;
      }
      
      // Update organization
      const { error } = await supabase
        .from('organizations')
        .update({
          name: editOrganizationName,
          description: editOrganizationDescription || null,
          logo_url: logoUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', organization.id);
      
      if (error) throw error;
      
      // Show success toast
      toast({
        title: "Brand updated",
        description: "Brand information has been updated successfully.",
        variant: "default",
      });
      
      // Close dialog
      setIsEditDialogOpen(false);
    } catch (error: any) {
      console.error('Error updating organization:', error);
      
      // Show error toast
      toast({
        title: "Error updating brand",
        description: error.message || "An error occurred while updating the brand.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingOrganization(false);
    }
  };
  
  // Removed handleAddCollection - now using new collection creation flow

  // Removed handleNewCoverImageChange - now using new collection creation flow

  // Navigate to collection detail page
  const handleSelectCollection = (collectionId: string) => {
    navigate(`/organizations/${id}/collections/${collectionId}`);
  };
  
  // Handle organization deletion
  const handleDeleteOrganization = async () => {
    if (!organization || isDeleting) return;
    
    setIsDeleting(true);
    try {
      // Get all collections for this organization
      const { data: orgCollections, error: collectionsError } = await supabase
        .from('collections')
        .select('id')
        .eq('organization_id', organization.id);

      if (collectionsError) throw collectionsError;

      // Delete all assets associated with these collections
      if (orgCollections?.length > 0) {
        const { error: deleteAssetsError } = await supabase
          .from('assets')
          .delete()
          .in('collection_id', orgCollections.map(c => c.id));
        
        if (deleteAssetsError) throw deleteAssetsError;
      }

      // Delete all products associated with the collections
      if (orgCollections?.length > 0) {
        const { error: deleteProductsError } = await supabase
          .from('products')
          .delete()
          .in('collection_id', orgCollections.map(c => c.id));

        if (deleteProductsError) throw deleteProductsError;
      }

      // Delete all collections associated with this organization
      const { error: deleteCollectionsError } = await supabase
        .from('collections')
        .delete()
        .eq('organization_id', organization.id);
      
      if (deleteCollectionsError) throw deleteCollectionsError;

      // Finally, delete the organization
      const { error: organizationError } = await supabase
        .from('organizations')
        .delete()
        .eq('id', organization.id);
      
      if (organizationError) throw organizationError;

      toast({
        title: "Brand deleted",
        description: "The brand and all associated data have been removed.",
        variant: "default",
      });

      // Navigate back to organizations list
      navigate('/organizations');
    } catch (error: any) {
      console.error('Error deleting organization:', error);
      toast({
        title: "Error deleting brand",
        description: error.message || "An error occurred while deleting the brand.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setIsEditDialogOpen(false);
    }
  };
  
  // Handle loading state
  if (isLoadingOrganization || isLoadingCollections || isLoadingProducts) {
    return (
      <div className="space-y-6 page-transition">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/organizations')} className="mr-2">
            <ArrowLeft size={18} />
          </Button>
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-9 w-40" />
          </div>
          
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map(i => (
              <Skeleton key={i} className="h-[180px] rounded-lg" />
            ))}
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-3">
          {[1, 2, 3].map(i => (
            <Skeleton key={i} className="h-[120px] rounded-lg" />
          ))}
        </div>
      </div>
    );
  }
  
  // Handle error state
  if (isOrganizationError || !organization) {
    return (
      <div className="space-y-6 page-transition">
        <div className="py-4 px-6 border-b">
          <h1 className="text-xl font-semibold">Brand not found</h1>
          <p className="text-muted-foreground">
            {organizationError?.message || "The brand you're looking for doesn't exist or has been removed."}
          </p>
        </div>
        <div className="px-6">
          <Button onClick={() => navigate('/organizations')}>Go Back to Brands</Button>
        </div>
      </div>
    );
  }

  // Prepare organization data for metrics component
  const organizationMetricsData = {
    id: parseInt(organization.id),
    name: organization.name,
    logo: organization.logo_url || '/placeholder.svg',
    status: 'active',
    products: totalProducts,
    description: organization.description || '',
    activeGenerations: productsInProgress,
    completedItems: completedProducts
  };

  return (
    <div className="space-y-6">
      <div className="px-6">
        <PageTitle 
          title={organization?.name || ''}
          subtitle="Brand Dashboard"
          action={
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => navigate(`/organizations/${id}/bulk-upload`)}>
                <Upload className="mr-2 h-4 w-4" />
                Bulk Upload
              </Button>
              {canInviteUsers && (
                <Button variant="outline" onClick={() => navigate(`/organizations/${id}/members`)}>
                  <Users className="mr-2 h-4 w-4" />
                  Manage Members
                </Button>
              )}
              {canManageOrgSettings && (
                <Button variant="outline" onClick={() => navigate(`/organizations/${id}/settings`)}>
                  <Settings className="mr-2 h-4 w-4" />
                  Brand Settings
                </Button>
              )}
            </div>
          }
        />
      </div>

      <div className="px-6">
        <CollectionList 
          collections={collections}
          onSelectCollection={handleSelectCollection}
          onAddCollection={() => navigate(`/organizations/${id}/collections/new`)}
          isLoading={isLoadingCollections}
          onCollectionUpdated={() => refetchCollections()}
          canCreateCollections={canCreateCollections}
        />
      </div>

      {/* Organization Overview Cards */}
      <ClientMetrics 
        clientData={organizationMetricsData} 
        productsInProgress={productsInProgress}
        completedProducts={completedProducts}
      />
      
      {/* Edit Organization Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Brand</DialogTitle>
            <DialogDescription>
              Update brand information. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateOrganization}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name" className="required">Brand Name</Label>
                <Input
                  id="edit-name"
                  placeholder="Enter brand name"
                  value={editOrganizationName}
                  onChange={(e) => setEditOrganizationName(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  placeholder="Enter brand description"
                  value={editOrganizationDescription}
                  onChange={(e) => setEditOrganizationDescription(e.target.value)}
                  rows={3}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-logo">Brand Logo</Label>
                <Input
                  id="edit-logo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-muted-foreground">
                  Leave empty to keep current logo
                </p>
              </div>
            </div>
            <DialogFooter className="flex-col-reverse sm:flex-row gap-2">
              <div className="flex gap-2 w-full sm:w-auto">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="button"
                  variant="destructive"
                  onClick={() => setIsDeleteDialogOpen(true)}
                >
                  Delete Brand
                </Button>
              </div>
              <Button type="submit" disabled={isUpdatingOrganization}>
                {isUpdatingOrganization ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Brand</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this brand? This action cannot be undone and will remove all associated campaigns and data.
            </DialogDescription>
          </DialogHeader>
          <div className="pt-4">
            <div className="bg-muted/50 border rounded-lg p-3 text-sm">
              <strong>This will delete:</strong>
              <ul className="list-disc list-inside mt-2 space-y-1 text-muted-foreground">
                <li>Brand profile and settings</li>
                <li>All campaigns ({collections.length})</li>
                <li>All associated products ({clientProducts.length})</li>
                {organization?.logo_url && <li>Brand logo</li>}
              </ul>
            </div>
          </div>
          <DialogFooter className="gap-2 mt-4">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteOrganization}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deleting...' : 'Delete Brand'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
    </div>
  );
}

export default OrganizationDetail;
