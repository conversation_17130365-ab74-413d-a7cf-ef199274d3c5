import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { StepIndicator } from '@/components/ui/step-indicator';
import { 
  Upload, 
  AlertCircle, 
  CheckCircle2, 
  XCircle, 
  FileArchive,
  ChevronLeft,
  ChevronRight,
  Download,
  Loader2,
  FileImage,
  FolderOpen,
  AlertTriangle
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useOrganization } from '@/components/common/hooks/useOrganizations';
import { useCollections } from '@/components/common/hooks/useCollections';
import { supabase } from '@/components/common/utils/supabase';
import J<PERSON><PERSON><PERSON> from 'jszip';

// Types for the wizard
interface FilePreview {
  path: string;
  size: number;
  type: string;
  isValid: boolean;
  error?: string;
  workflowStage?: string;
  sku?: string;
}

interface ValidationIssue {
  type: 'error' | 'warning';
  message: string;
  files?: string[];
}

interface WizardState {
  step: number;
  file: File | null;
  files: FilePreview[];
  validationIssues: ValidationIssue[];
  selectedCollectionId: string;
  isProcessing: boolean;
  uploadProgress: number;
  uploadStats: {
    total: number;
    processed: number;
    successful: number;
    failed: number;
  };
  errors: Array<{ file: string; error: string }>;
}

const WIZARD_STEPS = [
  { id: 1, title: 'Upload', description: 'Select your ZIP file' },
  { id: 2, title: 'Validate', description: 'Check files and structure' },
  { id: 3, title: 'Configure', description: 'Set collection and options' },
  { id: 4, title: 'Process', description: 'Upload and organize assets' }
];

const MAX_FILE_SIZE = 3 * 1024 * 1024 * 1024; // 3GB max
const SUPPORTED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/webp', 'image/tiff'];
const SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.tiff'];

export default function BulkUploadWizard() {
  const navigate = useNavigate();
  const { orgId } = useParams<{ orgId: string }>();
  const { toast } = useToast();
  const { data: organization } = useOrganization(orgId || '');
  const { data: collections = [], isLoading: isLoadingCollections } = useCollections({ 
    organizationId: orgId,
    enabled: !!orgId 
  });
  
  const [state, setState] = useState<WizardState>({
    step: 1,
    file: null,
    files: [],
    validationIssues: [],
    selectedCollectionId: '',
    isProcessing: false,
    uploadProgress: 0,
    uploadStats: {
      total: 0,
      processed: 0,
      successful: 0,
      failed: 0
    },
    errors: []
  });

  // Step 1: Handle file selection
  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.zip')) {
      toast({
        title: 'Invalid file type',
        description: 'Please select a ZIP file',
        variant: 'destructive'
      });
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      toast({
        title: 'File too large',
        description: `File size must be under ${(MAX_FILE_SIZE / 1024 / 1024 / 1024).toFixed(1)}GB`,
        variant: 'destructive'
      });
      return;
    }

    setState(prev => ({ ...prev, file, isProcessing: true }));

    try {
      // Extract and preview files
      const zip = new JSZip();
      const contents = await zip.loadAsync(file);
      const files: FilePreview[] = [];
      const issues: ValidationIssue[] = [];

      // Process each file in the ZIP
      for (const [path, zipEntry] of Object.entries(contents.files)) {
        if (zipEntry.dir) continue;

        // Skip system files
        const filename = path.split('/').pop() || '';
        if (filename.startsWith('.') || filename.startsWith('__')) continue;
        if (filename === 'Thumbs.db' || filename === 'desktop.ini') continue;
        
        // Check for image extension
        const extension = path.substring(path.lastIndexOf('.')).toLowerCase();
        if (!extension || extension === path) continue; // Skip files without extension
        
        const isValidType = SUPPORTED_EXTENSIONS.includes(extension);
        
        // Only process image files
        if (!isValidType) continue;
        
        // Extract workflow stage from path
        const pathParts = path.split('/');
        const workflowStage = pathParts.length > 1 ? pathParts[0].toLowerCase() : 'draft';
        
        // Extract SKU from filename
        const skuMatch = filename.match(/^([A-Z0-9-]+)_/);
        const sku = skuMatch ? skuMatch[1] : null;

        const filePreview: FilePreview = {
          path,
          size: zipEntry._data?.uncompressedSize || 0,
          type: extension,
          isValid: true,
          workflowStage,
          sku: sku || undefined
        };

        files.push(filePreview);

        if (!sku) {
          issues.push({
            type: 'warning',
            message: `No SKU found in filename`,
            files: [path]
          });
        }
      }

      // Check for empty ZIP or no valid image files
      if (files.length === 0) {
        issues.push({
          type: 'error',
          message: 'ZIP file contains no valid image files (JPG, PNG, WebP, TIFF)'
        });
      }

      // Check for duplicate filenames
      const filenames = files.map(f => f.path.split('/').pop()!);
      const duplicates = filenames.filter((item, index) => filenames.indexOf(item) !== index);
      if (duplicates.length > 0) {
        issues.push({
          type: 'warning',
          message: 'Duplicate filenames detected',
          files: [...new Set(duplicates)]
        });
      }

      setState(prev => ({
        ...prev,
        files,
        validationIssues: issues,
        isProcessing: false,
        step: 2 // Move to validation step
      }));

    } catch (error) {
      console.error('Error processing ZIP file:', error);
      toast({
        title: 'Error processing file',
        description: 'The ZIP file may be corrupted or invalid',
        variant: 'destructive'
      });
      setState(prev => ({ ...prev, isProcessing: false }));
    }
  }, [toast]);

  // Step 2: Validation results
  const canProceedFromValidation = state.validationIssues.filter(i => i.type === 'error').length === 0;

  // Step 3: Collection selection
  const handleCollectionSelect = (collectionId: string) => {
    setState(prev => ({ ...prev, selectedCollectionId: collectionId }));
  };

  // Step 4: Process upload
  const handleUpload = async () => {
    if (!state.file || !state.selectedCollectionId) return;

    setState(prev => ({ 
      ...prev, 
      isProcessing: true,
      step: 4, // Move to progress step
      uploadStats: {
        total: state.files.filter(f => f.isValid).length,
        processed: 0,
        successful: 0,
        failed: 0
      }
    }));

    try {
      // Import the bulk upload processor
      const { processBulkUpload } = await import('@/components/common/utils/bulkUploadProcessor');
      
      const result = await processBulkUpload(
        state.file,
        state.selectedCollectionId,
        (progress) => {
          setState(prev => ({
            ...prev,
            uploadProgress: progress.percentage,
            uploadStats: {
              total: progress.total,
              processed: progress.processed,
              successful: progress.successful,
              failed: progress.failed
            }
          }));
        }
      );

      if (result.errors.length > 0) {
        setState(prev => ({
          ...prev,
          errors: result.errors,
          isProcessing: false
        }));
      } else {
        toast({
          title: 'Upload complete',
          description: `Successfully uploaded ${result.successful} files`,
        });
        
        // Navigate to collection after short delay
        setTimeout(() => {
          navigate(`/organizations/${orgId}/collections/${state.selectedCollectionId}`);
        }, 2000);
      }
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: 'Upload failed',
        description: 'An error occurred during upload',
        variant: 'destructive'
      });
      setState(prev => ({ ...prev, isProcessing: false }));
    }
  };

  // Navigation handlers
  const handleNext = () => {
    if (state.step === 3 && state.selectedCollectionId) {
      handleUpload();
    } else if (state.step === 4 && !state.isProcessing) {
      // Navigate to the collection detail page
      navigate(`/organizations/${orgId}/collections/${state.selectedCollectionId}`);
    } else if (state.step < 4) {
      setState(prev => ({ ...prev, step: prev.step + 1 }));
    }
  };

  const handleBack = () => {
    if (state.step > 1) {
      setState(prev => ({ ...prev, step: prev.step - 1 }));
    }
  };

  // Download error report
  const downloadErrorReport = () => {
    const report = state.errors.map(e => `${e.file}: ${e.error}`).join('\n');
    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'upload-errors.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate(`/organizations/${orgId}`)}
            className="mb-4"
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to {organization?.name || 'Brand'}
          </Button>
          
          <h1 className="text-3xl font-bold text-gray-900">Bulk Upload Assets</h1>
          <p className="text-gray-600 mt-2">Upload multiple assets at once using a ZIP file</p>
        </div>

        {/* Step Indicator */}
        <StepIndicator 
          steps={WIZARD_STEPS} 
          currentStep={state.step}
          className="mb-8"
        />

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle>{WIZARD_STEPS[state.step - 1].title}</CardTitle>
            <CardDescription>{WIZARD_STEPS[state.step - 1].description}</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Step 1: Upload */}
            {state.step === 1 && (
              <div className="space-y-6">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center">
                  <FileArchive className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600 mb-4">
                    Select a ZIP file containing your assets
                  </p>
                  <input
                    type="file"
                    accept=".zip"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="file-upload"
                    disabled={state.isProcessing}
                  />
                  <label htmlFor="file-upload">
                    <Button
                      asChild
                      disabled={state.isProcessing}
                    >
                      <span>
                        {state.isProcessing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Upload className="h-4 w-4 mr-2" />
                            Choose File
                          </>
                        )}
                      </span>
                    </Button>
                  </label>
                  <p className="text-sm text-gray-500 mt-4">
                    Maximum file size: {(MAX_FILE_SIZE / 1024 / 1024 / 1024).toFixed(1)}GB
                  </p>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>File Structure Guidelines:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Organize files by workflow stage folders (input_assets, raw, upscaled, retouched, final)</li>
                      <li>Use consistent naming: SKU_stage_size_type_version.ext</li>
                      <li>Supported formats: JPG, PNG, WebP, TIFF</li>
                      <li>Files will be automatically grouped by SKU</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Step 2: Validate */}
            {state.step === 2 && (
              <div className="space-y-6">
                {/* Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold">{state.files.length}</div>
                      <p className="text-sm text-gray-600">Total Files</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold text-green-600">
                        {state.files.filter(f => f.isValid).length}
                      </div>
                      <p className="text-sm text-gray-600">Valid Files</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="pt-6">
                      <div className="text-2xl font-bold text-red-600">
                        {state.files.filter(f => !f.isValid).length}
                      </div>
                      <p className="text-sm text-gray-600">Invalid Files</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Validation Issues */}
                {state.validationIssues.length > 0 && (
                  <div className="space-y-3">
                    <h3 className="font-semibold">Validation Issues</h3>
                    {state.validationIssues.map((issue, index) => (
                      <Alert key={index} variant={issue.type === 'error' ? 'destructive' : 'default'}>
                        {issue.type === 'error' ? (
                          <XCircle className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                        <AlertDescription>
                          <p className="font-semibold">{issue.message}</p>
                          {issue.files && issue.files.length > 0 && (
                            <ul className="mt-2 text-sm">
                              {issue.files.slice(0, 5).map((file, i) => (
                                <li key={i} className="truncate">• {file}</li>
                              ))}
                              {issue.files.length > 5 && (
                                <li className="text-gray-500">
                                  ... and {issue.files.length - 5} more
                                </li>
                              )}
                            </ul>
                          )}
                        </AlertDescription>
                      </Alert>
                    ))}
                  </div>
                )}

                {/* File Preview */}
                <div>
                  <h3 className="font-semibold mb-3">File Preview</h3>
                  <div className="border rounded-lg max-h-64 overflow-y-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-gray-50">
                          <th className="text-left p-2">File</th>
                          <th className="text-left p-2">Stage</th>
                          <th className="text-left p-2">SKU</th>
                          <th className="text-left p-2">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {state.files.slice(0, 50).map((file, index) => (
                          <tr key={index} className="border-b">
                            <td className="p-2">
                              <div className="flex items-center">
                                <FileImage className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="truncate max-w-xs" title={file.path}>
                                  {file.path}
                                </span>
                              </div>
                            </td>
                            <td className="p-2">
                              <Badge variant="outline">
                                {file.workflowStage || 'unknown'}
                              </Badge>
                            </td>
                            <td className="p-2">
                              <span className="font-mono text-xs">
                                {file.sku || '-'}
                              </span>
                            </td>
                            <td className="p-2">
                              {file.isValid ? (
                                <CheckCircle2 className="h-4 w-4 text-green-600" />
                              ) : (
                                <XCircle className="h-4 w-4 text-red-600" />
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    {state.files.length > 50 && (
                      <div className="p-2 text-center text-sm text-gray-500">
                        Showing 50 of {state.files.length} files
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Configure */}
            {state.step === 3 && (
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold mb-3">Select Campaign</h3>
                  {isLoadingCollections ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                    </div>
                  ) : collections && collections.length > 0 ? (
                    <div className="grid grid-cols-2 gap-3">
                      {collections.map((collection) => (
                      <Card
                        key={collection.id}
                        className={`cursor-pointer transition-colors ${
                          state.selectedCollectionId === collection.id
                            ? 'border-blue-600 bg-blue-50'
                            : 'hover:border-gray-400'
                        }`}
                        onClick={() => handleCollectionSelect(collection.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-semibold">{collection.name}</p>
                              <p className="text-sm text-gray-600">
                                {collection.description || 'No description'}
                              </p>
                            </div>
                            {state.selectedCollectionId === collection.id && (
                              <CheckCircle2 className="h-5 w-5 text-blue-600" />
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          No campaigns found. Please create a campaign first.
                        </AlertDescription>
                      </Alert>
                      <Button 
                        onClick={() => navigate(`/organizations/${orgId}/collections/new`)}
                        className="w-full"
                      >
                        Create New Campaign
                      </Button>
                    </div>
                  )}
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Upload Settings:</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1">
                      <li>Files will be organized by SKU</li>
                      <li>Products will be created automatically if they don't exist</li>
                      <li>Workflow stages will be detected from folder structure</li>
                      <li>All images will be compressed and thumbnails generated</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            {/* Step 4: Process */}
            {state.step === 4 && (
              <div className="space-y-6">
                {state.isProcessing ? (
                  <>
                    <div className="text-center py-8">
                      <Loader2 className="h-12 w-12 animate-spin mx-auto text-blue-600 mb-4" />
                      <p className="text-lg font-semibold mb-2">
                        Processing {state.uploadStats.processed} of {state.uploadStats.total} files
                      </p>
                      <p className="text-gray-600">Please don't close this page</p>
                    </div>

                    <Progress value={state.uploadProgress} className="h-2" />

                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-2xl font-bold text-blue-600">
                          {state.uploadStats.processed}
                        </p>
                        <p className="text-sm text-gray-600">Processed</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-green-600">
                          {state.uploadStats.successful}
                        </p>
                        <p className="text-sm text-gray-600">Successful</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-red-600">
                          {state.uploadStats.failed}
                        </p>
                        <p className="text-sm text-gray-600">Failed</p>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="text-center py-8">
                      {state.errors.length === 0 ? (
                        <>
                          <CheckCircle2 className="h-12 w-12 text-green-600 mx-auto mb-4" />
                          <p className="text-lg font-semibold mb-2">Upload Complete!</p>
                          <p className="text-gray-600">
                            Successfully uploaded {state.uploadStats.successful} files
                          </p>
                        </>
                      ) : (
                        <>
                          <AlertTriangle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
                          <p className="text-lg font-semibold mb-2">Upload Completed with Errors</p>
                          <p className="text-gray-600">
                            {state.uploadStats.successful} files uploaded successfully, 
                            {state.errors.length} failed
                          </p>
                        </>
                      )}
                    </div>

                    {state.errors.length > 0 && (
                      <div>
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-semibold">Failed Uploads</h3>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={downloadErrorReport}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download Report
                          </Button>
                        </div>
                        <div className="border rounded-lg max-h-48 overflow-y-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b bg-gray-50">
                                <th className="text-left p-2">File</th>
                                <th className="text-left p-2">Error</th>
                              </tr>
                            </thead>
                            <tbody>
                              {state.errors.map((error, index) => (
                                <tr key={index} className="border-b">
                                  <td className="p-2 font-mono text-xs">{error.file}</td>
                                  <td className="p-2 text-red-600">{error.error}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-6">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={state.step === 1 || state.isProcessing}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back
          </Button>

          <Button
            onClick={handleNext}
            disabled={
              (state.step === 1 && !state.file) ||
              (state.step === 2 && !canProceedFromValidation) ||
              (state.step === 3 && !state.selectedCollectionId) ||
              (state.step === 4 && state.isProcessing) ||
              state.isProcessing
            }
          >
            {state.step === 3 ? (
              <>
                Start Upload
                <Upload className="h-4 w-4 ml-2" />
              </>
            ) : state.step === 4 && !state.isProcessing ? (
              <>
                Go to Campaign
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            ) : (
              <>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}