import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useToast } from '../components/ui/use-toast';
import { useSupabase } from '../contexts/SupabaseContext';
import { useOrganization } from '../components/common/hooks/useOrganizations';
import { useUserRole } from '../contexts/UserRoleContext';
import { STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { PageTitle } from '../components/ui/PageTitle';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Button } from '../components/ui/button';
import { Textarea } from '../components/ui/textarea';
import { Separator } from '../components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Switch } from '../components/ui/switch';
import { Loader2, ArrowLeft, Upload, Camera, Trash2, Settings, Users, Building, PaintBucket, Globe, Lock, Bell, AlertTriangle } from 'lucide-react';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from "../components/ui/alert-dialog";
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '../components/ui/tooltip';
import browser_image_compression from 'browser-image-compression';
import { useFreelancerStatus } from '../components/common/hooks/useIsFreelancer';

interface ExtendedOrganization {
  id: string;
  name: string;
  logo_url: string | null;
  description: string | null;
  company_domain: string | null;
  primary_color: string | null;
  is_private: boolean;
  created_at: string;
  updated_at: string;
}

export function OrganizationSettings() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { isPlatformUser } = useUserRole();
  const { isFreelancer, canManageOrgSettings, canDeleteOrganizations } = useFreelancerStatus();
  
  // Get organization data
  const { 
    data: organizationData, 
    isLoading: isLoadingOrganization, 
    isError: isOrganizationError,
    error: organizationError,
    refetch
  } = useOrganization(id);
  
  // Cast to extended organization type
  const organization = organizationData as ExtendedOrganization | undefined;
  
  // States for form values
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [companyDomain, setCompanyDomain] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#007aff');
  const [isPrivate, setIsPrivate] = useState(false);
  const [newLogo, setNewLogo] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteConfirmationInput, setDeleteConfirmationInput] = useState('');
  
  // States for loading states
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // States for validation
  const [nameError, setNameError] = useState('');
  const [domainError, setDomainError] = useState('');
  
  // Initialize form values when organization data loads
  useEffect(() => {
    if (organization) {
      setName(organization.name || '');
      setDescription(organization.description || '');
      setCompanyDomain(organization.company_domain || '');
      setPrimaryColor(organization.primary_color || '#007aff');
      setIsPrivate(organization.is_private || false);
      setLogoPreview(organization.logo_url);
    }
  }, [organization]);
  
  // Handle validation for name
  const validateName = (value: string) => {
    if (!value.trim()) {
      setNameError('Brand name is required');
      return false;
    }
    setNameError('');
    return true;
  };
  
  // Handle validation for domain
  const validateDomain = (value: string) => {
    if (value && !/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/.test(value)) {
      setDomainError('Please enter a valid domain (e.g., example.com)');
      return false;
    }
    setDomainError('');
    return true;
  };
  
  // Handle logo upload click
  const handleLogoUploadClick = () => {
    const fileInput = document.getElementById('logo-upload');
    if (fileInput) {
      fileInput.click();
    }
  };
  
  // Handle logo change
  const handleLogoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file",
          description: "Please upload an image file.",
          variant: "destructive",
        });
        return;
      }
      
      try {
        // Compress the image
        const options = {
          maxSizeMB: 1, // Max file size in MB
          maxWidthOrHeight: 800, // Max width or height in pixels
          useWebWorker: true,
        };
        
        const compressedFile = await browser_image_compression(file, options);
        
        // Create a preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setLogoPreview(e.target?.result as string);
        };
        reader.readAsDataURL(compressedFile);
        
        setNewLogo(compressedFile);
      } catch (error) {
        console.error('Error compressing image:', error);
        toast({
          title: "Error",
          description: "Failed to process the image.",
          variant: "destructive",
        });
      }
    }
  };
  
  // Handle logo delete
  const handleLogoDelete = () => {
    setNewLogo(null);
    setLogoPreview(null);
  };
  
  // Handle general settings form submission
  const handleGeneralSettingsSave = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate fields
    const isNameValid = validateName(name);
    const isDomainValid = validateDomain(companyDomain);
    
    if (!isNameValid || !isDomainValid) {
      return;
    }
    
    setIsSaving(true);
    
    try {
      let logoUrl = organization?.logo_url || null;
      
      // Upload new logo if provided
      if (newLogo) {
        setIsUploading(true);
        
        const fileName = `${Date.now()}-${newLogo.name}`;
        const filePath = `organizations/${id}/logo/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .upload(filePath, newLogo, {
            cacheControl: '3600',
            upsert: true
          });
        
        if (uploadError) throw uploadError;
        
        // Get public URL for the uploaded file
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .getPublicUrl(filePath);
        
        logoUrl = publicUrlData.publicUrl;
        setIsUploading(false);
      }
      
      // If logo was removed (logoPreview is null but organization had a logo)
      if (logoPreview === null && organization?.logo_url) {
        logoUrl = null;
      }
      
      console.log('Updating organization with ID:', id);
      console.log('Update data:', {
        name,
        description,
        company_domain: companyDomain || null,
        primary_color: primaryColor || null,
        is_private: isPrivate,
        logo_url: logoUrl,
        updated_at: new Date().toISOString()
      });
      
      // Update organization data
      const { data: updateData, error: updateError } = await supabase
        .from('organizations')
        .update({
          name,
          description,
          company_domain: companyDomain || null,
          primary_color: primaryColor || null,
          is_private: isPrivate,
          logo_url: logoUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select();
      
      console.log('Update response:', { data: updateData, error: updateError });
      
      if (updateError) throw updateError;
      
      // Refetch organization data
      refetch();
      
      toast({
        title: "Settings saved",
        description: "Brand settings have been updated successfully.",
      });
    } catch (error: any) {
      console.error('Error updating organization settings:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update brand settings.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle organization deletion
  const handleDeleteOrganization = async () => {
    if (!id || isDeleting) return;
    
    setIsDeleting(true);
    
    try {
      // Get all collections for this organization
      const { data: collections, error: collectionsError } = await supabase
        .from('collections')
        .select('id')
        .eq('organization_id', id);
      
      if (collectionsError) throw collectionsError;
      
      if (collections && collections.length > 0) {
        // Delete related assets
        const collectionIds = collections.map(c => c.id);
        const { error: assetsError } = await supabase
          .from('assets')
          .delete()
          .in('collection_id', collectionIds);
        
        if (assetsError) throw assetsError;
        
        // Delete related products
        const { error: productsError } = await supabase
          .from('products')
          .delete()
          .in('collection_id', collectionIds);
        
        if (productsError) throw productsError;
        
        // Delete collections
        const { error: deleteCollectionsError } = await supabase
          .from('collections')
          .delete()
          .eq('organization_id', id);
        
        if (deleteCollectionsError) throw deleteCollectionsError;
      }
      
      // Delete organization memberships
      const { error: membershipError } = await supabase
        .from('organization_memberships')
        .delete()
        .eq('organization_id', id);
      
      if (membershipError) throw membershipError;
      
      // Delete the organization
      const { error: deleteOrgError } = await supabase
        .from('organizations')
        .delete()
        .eq('id', id);
      
      if (deleteOrgError) throw deleteOrgError;
      
      // Show success toast
      toast({
        title: "Brand deleted",
        description: "The brand and all associated data have been removed.",
      });
      
      // Navigate to organizations list
      navigate('/organizations');
    } catch (error: any) {
      console.error('Error deleting organization:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete the brand.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };
  
  // Add useEffect for role-based access control
  useEffect(() => {
    // Only execute the redirect if data is loaded
    // Allow access to platform users and non-freelancer brand admins
    if (!isLoadingOrganization) {
      if (!isPlatformUser && !canManageOrgSettings) {
        console.log('Access denied: Insufficient permissions to manage organization settings');
        navigate(`/organizations/${id}`);
      }
    }
  }, [isPlatformUser, canManageOrgSettings, id, navigate, isLoadingOrganization]);

  // If loading, show skeleton
  if (isLoadingOrganization) {
    return (
      <div className="container py-6 space-y-8">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate(`/organizations/${id}`)} className="mr-4">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <div className="h-7 w-64 bg-muted rounded animate-pulse"></div>
            <div className="h-5 w-40 bg-muted rounded animate-pulse mt-2"></div>
          </div>
        </div>
        
        <div className="grid gap-6">
          <div className="h-96 bg-muted rounded animate-pulse"></div>
        </div>
      </div>
    );
  }
  
  // If error or organization not found
  if (isOrganizationError || !organization) {
    return (
      <div className="container py-6">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate('/organizations')} className="mr-4">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-semibold">Brand not found</h1>
            <p className="text-muted-foreground">
              {organizationError?.message || "The brand you're looking for doesn't exist or has been removed."}
            </p>
          </div>
        </div>
        
        <Button onClick={() => navigate('/organizations')}>
          Return to Brands
        </Button>
      </div>
    );
  }
  
  return (
    <div className="container py-6 space-y-8">
      <div className="flex items-center">
        <Button variant="ghost" size="icon" onClick={() => navigate(`/organizations/${id}`)} className="mr-4">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <PageTitle
          title={`${organization.name} Settings`}
          description="Manage and customize your brand settings"
          icon={<Settings className="h-6 w-6" />}
        />
      </div>
      
      <div className="space-y-6">
        {/* Brand Information Card */}
        <Card>
          <CardHeader>
            <CardTitle>Brand Information</CardTitle>
            <CardDescription>
              Update your brand's basic information and details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleGeneralSettingsSave} className="space-y-6">
              <div className="space-y-4">
                {/* Brand Logo */}
                <div className="space-y-2">
                  <Label>Brand Logo</Label>
                  <div className="flex items-center gap-4">
                    <div className="relative group">
                      <Avatar className="h-24 w-24 rounded-md border border-border">
                        <AvatarImage src={logoPreview || ""} alt={name} />
                        <AvatarFallback className="rounded-md text-xl bg-muted">
                          {name.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div 
                        className="absolute inset-0 bg-black/50 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                        onClick={handleLogoUploadClick}
                      >
                        <Camera className="h-6 w-6 text-white" />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Button 
                          type="button" 
                          variant="outline" 
                          size="sm"
                          onClick={handleLogoUploadClick}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Upload
                        </Button>
                        
                        {logoPreview && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleLogoDelete}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remove
                          </Button>
                        )}
                      </div>
                      
                      <input
                        id="logo-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleLogoChange}
                      />
                      
                      <p className="text-xs text-muted-foreground">
                        Recommended: Square image, at least 200x200px. Max 1MB.
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Brand Name */}
                <div className="space-y-2">
                  <Label htmlFor="name" className="required">Brand Name</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => {
                      setName(e.target.value);
                      validateName(e.target.value);
                    }}
                    placeholder="Enter brand name"
                    className={nameError ? "border-destructive" : ""}
                  />
                  {nameError && (
                    <p className="text-xs text-destructive">{nameError}</p>
                  )}
                </div>
                
                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter brand description"
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground">
                    Brief description of your brand (optional)
                  </p>
                </div>
                
                {/* Brand Appearance */}
                <div className="space-y-2">
                  <Label htmlFor="primary-color">Brand Color</Label>
                  <div className="flex items-center gap-3">
                    <div 
                      className="h-10 w-10 rounded-md border border-border flex-shrink-0"
                      style={{ backgroundColor: primaryColor }}
                    ></div>
                    <Input
                      id="primary-color"
                      type="color"
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={primaryColor}
                      onChange={(e) => setPrimaryColor(e.target.value)}
                      className="flex-1"
                      placeholder="#007aff"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    This color will be used for brand elements across the platform
                  </p>
                </div>
              </div>
              
              <CardFooter className="px-0 pt-4 flex-col items-start sm:flex-row sm:justify-between sm:items-center gap-4">
                <p className="text-sm text-muted-foreground">
                  Last updated: {new Date(organization.updated_at).toLocaleString()}
                </p>
                <Button 
                  type="submit" 
                  disabled={isSaving || isUploading}
                  className="w-full sm:w-auto"
                >
                  {(isSaving || isUploading) && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {isSaving ? 'Saving Changes...' : 'Save Changes'}
                </Button>
              </CardFooter>
            </form>
          </CardContent>
        </Card>
        
        {/* Danger Zone Card - Only show for users who can delete organizations */}
        {(isPlatformUser || canDeleteOrganizations) && (
          <Card className="border-destructive">
            <CardHeader className="text-destructive">
              <CardTitle>Danger Zone</CardTitle>
              <CardDescription className="text-destructive/80">
                Irreversible actions that affect your brand
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-destructive/10 rounded-lg p-4 border border-destructive/20">
                <h3 className="text-base font-medium mb-2 text-destructive">Delete Brand</h3>
                <p className="text-sm text-destructive/80 mb-4">
                  Permanently delete this brand and all of its data, including campaigns, assets, and member access. This action cannot be undone.
                </p>
                <div className="bg-muted/50 border rounded-lg p-3 text-sm text-destructive/80">
                  <strong>This will delete:</strong>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Brand profile and settings</li>
                    <li>All campaigns and collections</li>
                    <li>All products and assets</li>
                    <li>All member access permissions</li>
                  </ul>
                </div>
                <Button
                  variant="destructive"
                  className="mt-4"
                  onClick={() => setIsDeleteDialogOpen(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Brand
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={isDeleteDialogOpen} 
        onOpenChange={(open) => {
          setIsDeleteDialogOpen(open);
          if (!open) {
            setDeleteConfirmationInput(''); // Reset input when dialog closes
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Brand</AlertDialogTitle>
            <AlertDialogDescription>
              Are you absolutely sure you want to delete <strong>{organization.name}</strong>? This action cannot be undone and will permanently delete all data associated with this brand.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4 space-y-4">
            {/* Warning about permanent deletion */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 text-sm">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div className="space-y-1">
                  <p className="font-semibold text-amber-900">⚠️ This action is permanent</p>
                  <p className="text-amber-800">
                    Currently, there is no archive or recovery system. Once deleted, all data will be permanently lost.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Confirmation input */}
            <div className="bg-destructive/10 border border-destructive/30 rounded-lg p-4 text-sm text-destructive/80">
              <p className="font-semibold mb-2">To confirm, please type the brand name:</p>
              <Input
                placeholder={organization.name}
                value={deleteConfirmationInput}
                onChange={(e) => setDeleteConfirmationInput(e.target.value)}
                className="border-destructive/30"
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={(e) => {
                e.preventDefault();
                handleDeleteOrganization();
              }}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              disabled={deleteConfirmationInput !== organization.name || isDeleting}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : 'Delete Brand'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

export default OrganizationSettings;