import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { useToast } from "../components/common/hooks/use-toast";
import { useSupabase } from '../contexts/SupabaseContext';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../components/ui/card";
import { Label } from "../components/ui/label";
import { Alert, AlertDescription } from "../components/ui/alert";
import { AlertCircle, ArrowLeft } from 'lucide-react';
import { isDevelopmentEnvironment } from '../components/common/utils/utils';

const ResetPassword = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [resetSent, setResetSent] = useState(false);
  const { supabase } = useSupabase();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: "Error",
        description: "Please enter your email address",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // First, trigger Supabase's built-in reset flow
      const { error: supabaseError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/update-password`,
      });
      
      if (supabaseError) {
        throw supabaseError;
      }
      
      // For now, we'll rely on Supabase's built-in email
      // In production, custom emails would be sent through a backend service
      
      // Show success message
      toast({
        title: "Success",
        description: "Password reset instructions have been sent to your email",
      });
      
      setResetSent(true);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to send reset instructions",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // For development, show the Resend configuration status
  const isDevelopment = isDevelopmentEnvironment();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex flex-col items-center mb-8">
            <img 
              src="/fashionlab-logo.svg" 
              alt="FashionLab" 
              className="h-12 w-auto mb-8"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Link to="/login" className="hover:text-blue-700">
              <ArrowLeft size={16} />
            </Link>
            <CardTitle className="text-2xl font-bold">Reset Password</CardTitle>
          </div>
          <CardDescription>
            Enter your email address and we'll send you instructions to reset your password
          </CardDescription>
        </CardHeader>
        
        {resetSent ? (
          <CardContent className="space-y-4">
            <Alert>
              <AlertDescription>
                If an account exists with the email you provided, you'll receive instructions
                to reset your password shortly. Please check your email inbox and spam folder.
                <br /><br />
                <strong>Note:</strong> If the reset link doesn't work, you can use the 6-digit code 
                from the email instead.
              </AlertDescription>
            </Alert>
            <div className="text-center space-y-2">
              <Link 
                to="/login" 
                className="text-blue-500 hover:text-blue-700 block"
              >
                Return to login
              </Link>
              <Link 
                to="/manual-reset" 
                className="text-blue-500 hover:text-blue-700 block"
              >
                Use reset code instead
              </Link>
            </div>
          </CardContent>
        ) : (
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>
              
              {/* Development notes about email delivery */}
              {isDevelopment && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    For local development, check the Supabase Studio to view the password reset link.
                    <br />
                    <a 
                      href="http://127.0.0.1:54323/project/default/auth/users" 
                      target="_blank" 
                      rel="noreferrer"
                      className="text-blue-500 hover:text-blue-700"
                    >
                      Open Supabase Studio
                    </a>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
            <CardFooter>
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? "Sending..." : "Send reset instructions"}
              </Button>
            </CardFooter>
          </form>
        )}
      </Card>
    </div>
  );
};

export default ResetPassword; 