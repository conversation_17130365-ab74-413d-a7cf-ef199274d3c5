import React, { useState } from 'react';
import { useNavi<PERSON>, Link } from 'react-router-dom';
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Label } from "../components/ui/label";
import { PasswordInput } from "../components/ui/password-input";
import { useToast } from "../components/common/hooks/use-toast";
import { useSupabase } from '../contexts/SupabaseContext';
import { isDevelopmentEnvironment } from '../components/common/utils/utils';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { supabase } = useSupabase();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast({
        title: "Error",
        description: "Please enter both email and password",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        throw error;
      }
      
      if (data?.user) {
        toast({
          title: "Success",
          description: "You have been logged in",
        });
        console.log("Login successful, redirecting to root for role-based routing");
        navigate('/');
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to log in",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions to fill test credentials (only in development)
  const testCredentials = [
    { role: 'Platform Super Admin', email: '<EMAIL>', password: 'test123456' },
    { role: 'Platform Admin', email: '<EMAIL>', password: 'test123456' },
    { role: 'Brand Admin (Vero Moda)', email: '<EMAIL>', password: 'test123456' },
    { role: 'Brand Member (Vero Moda)', email: '<EMAIL>', password: 'test123456' },
    { role: 'External Retoucher', email: '<EMAIL>', password: 'test123456' },
    { role: 'External Prompter', email: '<EMAIL>', password: 'test123456' },
    { role: 'Brand Admin (H&M)', email: '<EMAIL>', password: 'test123456' },
  ];

  const fillTestCredentials = (email: string, password: string) => {
    setEmail(email);
    setPassword(password);
  };

  // Determine if we're in development mode
  const isDevelopment = isDevelopmentEnvironment();

  return (
    <div className="w-full min-h-screen md:grid md:grid-cols-2">
      <style>{`
        input[type="password"]::-ms-reveal,
        input[type="password"]::-ms-clear {
          display: none;
        }
      `}</style>

      {/* Left Column: The Form */}
      <div className="flex h-screen items-center justify-center p-6 md:h-auto md:p-0 md:py-12">
        <div className="mx-auto grid w-[350px] gap-2">
          <form onSubmit={handleSubmit} autoComplete="on" className="flex flex-col gap-8">
            <div className="flex flex-col items-center gap-2 text-center">
              <img 
                src="/fashionlab-logo.svg" 
                alt="FashionLab" 
                className="h-12 w-auto mb-4"
              />
              <h1 className="text-2xl font-bold">Welcome back</h1>
              <p className="text-balance text-sm text-muted-foreground">
                Sign in to create inclusive fashion imagery with AI
              </p>
            </div>
            
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  placeholder="<EMAIL>" 
                  required 
                  autoComplete="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              
              <div className="grid gap-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link 
                    to="/reset-password" 
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Forgot password?
                  </Link>
                </div>
                <PasswordInput 
                  id="password"
                  name="password" 
                  required 
                  autoComplete="current-password" 
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                />
              </div>
              
              <Button 
                type="submit" 
                variant="outline" 
                className="mt-2"
                disabled={isLoading}
              >
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </div>
          </form>

          {/* Development helper tools */}
          {isDevelopment && (
            <div className="mt-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
              <p className="text-xs font-semibold text-muted-foreground mb-3">Development Quick Login</p>
              <div className="space-y-2">
                {testCredentials.map((cred) => (
                  <Button 
                    key={cred.email}
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => fillTestCredentials(cred.email, cred.password)}
                    className="w-full text-xs justify-start"
                  >
                    <span className="truncate">{cred.role}</span>
                  </Button>
                ))}
              </div>
              <p className="text-xs text-muted-foreground mt-2">All test accounts use password: test123456</p>
            </div>
          )}
        </div>
      </div>

      {/* Right Column: The Image and Quote */}
      <div
        className="hidden md:block relative bg-cover bg-center"
        style={{ 
          backgroundImage: `url(https://images.unsplash.com/photo-**********-cb1aea458c5e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1374&q=80)` 
        }}
      >
        {/* Overlay for text readability */}
        <div className="absolute inset-0 bg-black/50" />
        
        {/* Centered Quote */}
        <div className="relative z-10 flex h-full items-center justify-center p-10">
          <blockquote className="space-y-2 text-center text-white">
            <p className="text-lg font-medium">
              "FashionLab enables us to show products in all sizes using AI-generated imagery. 
              Our customers can finally see themselves in every piece, leading to increased sales and reduced returns."
            </p>
            <cite className="block text-sm font-light text-neutral-300 not-italic">
              — Fashion that fits everyone
            </cite>
          </blockquote>
        </div>
      </div>
    </div>
  );
};

export default Login;