import PageTitle from '../components/ui/PageTitle';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Skeleton } from '../components/ui/skeleton';
import { useSupabase } from '../contexts/SupabaseContext';
import { useUserRole } from '../contexts/UserRoleContext';
import DashboardStats from '../components/dashboard/DashboardStats';
import RecentActivities from '../components/dashboard/RecentActivities';
import StorageUsageDetails from '../components/dashboard/StorageUsageDetails';
import OrganizationOverview from '../components/organizations/OrganizationOverview';

export function Dashboard() {
  const { user, isLoadingUser } = useSupabase();
  const { isPlatformUser, isLoadingRole } = useUserRole();
  
  return (
    <div className="space-y-6 page-transition">
      <PageTitle
        title="Dashboard"
        subtitle={isLoadingUser ? "" : `Welcome, ${user?.email || 'User'}`}
      />

      {/* Platform User Dashboard */}
      {isPlatformUser && !isLoadingRole && (
        <>
          <DashboardStats />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>Brands</CardTitle>
                </CardHeader>
                <CardContent>
                  <OrganizationOverview />
                </CardContent>
              </Card>
            </div>

            <div className="lg:col-span-1 space-y-6">
              <StorageUsageDetails />
              <RecentActivities />
            </div>
          </div>
        </>
      )}

      {/* Brand User Dashboard */}
      {!isPlatformUser && !isLoadingRole && (
        <Card>
          <CardHeader>
            <CardTitle>Select Your Brand</CardTitle>
          </CardHeader>
          <CardContent>
            <OrganizationOverview
              viewMode="grid"
              allowCreate={false}
            />
          </CardContent>
        </Card>
      )}

      {/* Show loading indicator while role is loading */}
      {isLoadingRole && (
        <div className="mt-4">
          <Skeleton className="h-32 rounded-lg" />
        </div>
      )}
    </div>
  );
}

export default Dashboard;
