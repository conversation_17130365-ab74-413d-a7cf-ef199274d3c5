import React from 'react'
import { PageTitle } from '@/components/ui/PageTitle'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { User, Settings, Shield } from 'lucide-react'
import { ProfileSection } from '@/components/profile/ProfileSection'
import { AccountSettings } from '@/components/profile/AccountSettings'
import { SecuritySettings } from '@/components/profile/SecuritySettings'
import { useProfile } from '@/contexts/ProfileContext'
import { Skeleton } from '@/components/ui/skeleton'

const Profile: React.FC = () => {
  const { loading } = useProfile()

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <PageTitle title="Profile" />
        <div className="mt-6 space-y-6">
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
          <Skeleton className="h-48 w-full" />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <PageTitle title="Profile" />
      
      <Tabs defaultValue="profile" className="mt-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Account
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="mt-6">
          <ProfileSection />
        </TabsContent>
        
        <TabsContent value="account" className="mt-6">
          <AccountSettings />
        </TabsContent>
        
        <TabsContent value="security" className="mt-6">
          <SecuritySettings />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Profile