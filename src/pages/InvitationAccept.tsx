import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useSupabase } from '../contexts/SupabaseContext';
import { useToast } from '../components/ui/use-toast';
import { Database } from '../components/common/types/database.types';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '../components/ui/alert';
import { Badge } from '../components/ui/badge';
import { CheckCircle2, XCircle, Loader2, UserPlus, Mail, Clock, Info, AlertCircle } from 'lucide-react';

type OrganizationRole = Database['public']['Enums']['organization_role'];

interface PendingInvitation {
  id: string;
  email: string;
  organization_id: string;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  token: string;
  accepted: boolean;
  inviter?: {
    email: string;
    first_name: string | null;
    last_name: string | null;
  };
  organization?: {
    name: string;
    logo_url: string | null;
  };
}

export function InvitationAccept() {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [invitation, setInvitation] = useState<PendingInvitation | null>(null);
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('signup'); // Default to signup for new users
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');

  useEffect(() => {
    const fetchInvitation = async () => {
      if (!token) {
        setError('Invalid invitation link');
        setIsLoading(false);
        return;
      }
      
      setIsLoading(true);
      console.log('🔍 Fetching invitation with token:', token);
      
      try {
        // First, let's see if the invitation exists at all (without the accepted filter)
        const { data: allInvitations, error: allError } = await supabase
          .from('pending_invitations')
          .select('id, email, accepted, expires_at, token')
          .eq('token', token);
          
        console.log('🔍 All invitations with this token:', allInvitations);
        console.log('🔍 All invitations error:', allError);
        
        // Fetch invitation with this token
        const { data: invitationData, error: invitationError } = await supabase
          .from('pending_invitations')
          .select(`
            id,
            email,
            organization_id,
            invited_by,
            invited_at,
            expires_at,
            token,
            accepted,
            organizations:organization_id (
              name,
              logo_url
            )
          `)
          .eq('token', token)
          .eq('accepted', false)
          .single();
          
        console.log('🔍 Invitation query result:', { invitationData, invitationError });
          
        if (invitationError) {
          if (invitationError.code === 'PGRST116') {
            // Check if invitation exists but was already accepted
            if (allInvitations && allInvitations.length > 0) {
              const inv = allInvitations[0];
              if (inv.accepted) {
                throw new Error('This invitation has already been accepted. You should already have access to the organization.');
              } else if (new Date(inv.expires_at) < new Date()) {
                throw new Error('This invitation has expired. Please ask the administrator to send a new invitation.');
              }
            }
            throw new Error('This invitation link is invalid. Please check the URL or contact the person who sent it.');
          }
          throw invitationError;
        }
        
        // Check if invitation has expired
        if (new Date(invitationData.expires_at) < new Date()) {
          throw new Error('This invitation has expired');
        }
        
        // Get inviter details
        const { data: inviterData, error: inviterError } = await supabase
          .from('users')
          .select(`
            email,
            first_name,
            last_name
          `)
          .eq('id', invitationData.invited_by)
          .single();
          
        if (inviterError && inviterError.code !== 'PGRST116') {
          throw inviterError;
        }
        
        // Combine the data
        const invitationWithDetails = {
          ...invitationData,
          inviter: inviterError ? undefined : inviterData
        };
        
        setInvitation(invitationWithDetails as PendingInvitation);
        setEmail(invitationWithDetails.email); // Pre-fill email from invitation
        
        // Check if user is already logged in with a confirmed email
        const { data: { user } } = await supabase.auth.getUser();
        if (user && user.email_confirmed_at) {
          // User is logged in and confirmed
          const userMetadata = user.user_metadata;
          const invitationId = userMetadata?.invitation_id;
          
          // Check if this user was invited with this specific invitation
          if (invitationId === invitationData.id) {
            // This user was created for this invitation
            console.log('User logged in with confirmed email, completing invitation...');
            
            // The database trigger should have already created the user profile and membership
            // Just mark the invitation as accepted
            await acceptInvitation(user.id);
            return;
          }
        }
      } catch (err: any) {
        console.error('Error fetching invitation:', err);
        
        // Provide more specific error messages
        let errorMessage = 'Failed to load invitation';
        
        if (err.message?.includes('expired')) {
          errorMessage = 'This invitation has expired. Please contact the administrator for a new invitation.';
        } else if (err.message?.includes('already been accepted')) {
          errorMessage = err.message;
        } else if (err.message?.includes('invalid')) {
          errorMessage = err.message;
        } else if (err.code === '22P02') {
          errorMessage = 'Invalid invitation link format. Please check the URL.';
        } else if (err.message) {
          errorMessage = err.message;
        }
        
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchInvitation();
  }, [token, supabase]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!invitation) return;
    
    setIsAccepting(true);
    
    try {
      // Try to sign in
      console.log('🔐 Attempting login with email:', email);
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      console.log('🔐 Sign in response:', { signInData, signInError });
      
      if (signInError) {
        console.error('🔐 Sign in error details:', {
          message: signInError.message,
          status: signInError.status,
          code: signInError.code,
          name: signInError.name
        });
        throw signInError;
      }
      
      // Get current user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) throw userError;
      
      // Accept invitation
      await acceptInvitation(userData.user.id);
    } catch (err: any) {
      console.error('Error logging in:', err);
      
      let errorTitle = "Login failed";
      let errorDescription = "An error occurred while logging in";
      
      if (err.message?.includes('Invalid login credentials')) {
        errorTitle = "Invalid credentials";
        errorDescription = "The email or password is incorrect. Please try again.";
      } else if (err.message?.includes('Email not confirmed')) {
        errorTitle = "Email not confirmed";
        errorDescription = "Please check your email and confirm your account first.";
      } else if (err.message?.includes('Too many requests')) {
        errorTitle = "Too many attempts";
        errorDescription = "Please wait a few minutes before trying again.";
      } else if (err.message) {
        errorDescription = err.message;
      }
      
      toast({
        title: errorTitle,
        description: errorDescription,
        variant: "destructive",
      });
      setIsAccepting(false);
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!invitation) return;
    
    setIsAccepting(true);
    
    try {
      // Check if email matches invitation
      if (email.toLowerCase() !== invitation.email.toLowerCase()) {
        throw new Error('Email address must match the invitation');
      }
      
      // Try to sign up
      console.log('🔐 Attempting signup with email:', email);
      console.log('🔐 Signup metadata:', {
        first_name: firstName,
        last_name: lastName,
        invitation_id: invitation.id
      });
      
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            invitation_id: invitation.id // Store invitation ID for database trigger
          }
        }
      });
      
      console.log('🔐 Sign up response:', { signUpData, signUpError });
      
      if (signUpError) {
        console.error('🔐 Sign up error details:', {
          message: signUpError.message,
          status: signUpError.status,
          code: signUpError.code,
          name: signUpError.name,
          details: signUpError
        });
        
        // Check if this is a "User already registered" error
        if (signUpError.message === 'User already registered') {
          console.log('User already exists in Auth');
          toast({
            title: "Account already exists",
            description: "An account with this email already exists. Please use the login tab to sign in and accept the invitation.",
            variant: "destructive",
          });
          setActiveTab('login');
          setIsAccepting(false);
          return;
        } else {
          // For any other sign up error, throw it
          throw signUpError;
        }
      }
      
      if (signUpData.user) {
        // Check if user is immediately confirmed (no email confirmation required)
        if (signUpData.user.email_confirmed_at) {
          // User is confirmed, the database trigger will handle profile creation
          // Just accept the invitation
          await acceptInvitation(signUpData.user.id);
        } else {
          // Email confirmation is required
          toast({
            title: "Account created successfully",
            description: "Please check your email to confirm your account. Your invitation will be automatically accepted once you confirm your email.",
            duration: 8000,
          });
          setIsAccepting(false);
          
          // Redirect to login page with a message
          setTimeout(() => {
            navigate('/login', { 
              state: { 
                message: 'Please check your email to confirm your account. After confirming, you can sign in to access your invitation.',
                email: email
              }
            });
          }, 3000);
        }
      } else {
        // Unexpected case
        toast({
          title: "Signup issue",
          description: "Account creation had an unexpected result. Please try again or contact support.",
          variant: "destructive"
        });
        setIsAccepting(false);
      }
    } catch (err: any) {
      console.error('Error signing up:', err);
      toast({
        title: "Signup failed",
        description: err.message || "An error occurred while creating your account",
        variant: "destructive",
      });
      setIsAccepting(false);
    }
  };

  const acceptInvitation = async (userId: string) => {
    if (!invitation) return;
    
    try {
      // The database trigger handles creating user profile and organization membership
      // We just need to mark the invitation as accepted
      console.log('📝 Marking invitation as accepted for user:', userId);
      
      // First, ensure the user profile exists (wait for trigger to complete)
      let retryCount = 0;
      const maxRetries = 5;
      let userProfileExists = false;
      
      while (!userProfileExists && retryCount < maxRetries) {
        const { data: userProfile, error: profileError } = await supabase
          .from('users')
          .select('id')
          .eq('id', userId)
          .single();
        
        if (userProfile && !profileError) {
          userProfileExists = true;
          console.log('✅ User profile exists, proceeding with invitation acceptance');
        } else {
          retryCount++;
          console.log(`⏳ Waiting for user profile creation... (attempt ${retryCount}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
        }
      }
      
      if (!userProfileExists) {
        throw new Error('User profile was not created. Please try logging in again or contact support.');
      }
      
      // Mark invitation as accepted
      const { error: updateError } = await supabase
        .from('pending_invitations')
        .update({ 
          accepted: true,
          accepted_at: new Date().toISOString()
        })
        .eq('id', invitation.id);
        
      if (updateError) throw updateError;
      
      // Success!
      if (invitation.organization_id) {
        toast({
          title: "Invitation accepted",
          description: `You have been added to ${invitation.organization?.name}`,
        });
        
        // Redirect to organization page
        setTimeout(() => {
          navigate(`/organizations/${invitation.organization_id}`);
        }, 1500);
      } else {
        toast({
          title: "Platform admin access granted",
          description: "You now have platform administrator privileges",
        });
        
        // Redirect to dashboard
        setTimeout(() => {
          navigate('/dashboard');
        }, 1500);
      }
    } catch (err: any) {
      console.error('Error accepting invitation:', err);
      
      let errorTitle = "Error accepting invitation";
      let errorDescription = "An error occurred while accepting the invitation";
      
      if (err.message?.includes('permission')) {
        errorTitle = "Permission denied";
        errorDescription = "You don't have permission to accept this invitation.";
      } else if (err.message) {
        errorDescription = err.message;
      }
      
      toast({
        title: errorTitle,
        description: errorDescription,
        variant: "destructive",
      });
      setIsAccepting(false);
    }
  };

  const getRoleName = (role: OrganizationRole) => {
    switch (role) {
      case 'org_admin':
        return 'Brand Admin';
      case 'org_member':
        return 'Brand Member';
      case 'org_retoucher':
        return 'Retoucher';
      case 'org_prompter':
        return 'Prompter';
      default:
        return role;
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex flex-col items-center mb-6">
              <img 
                src="/fashionlab-logo.svg" 
                alt="FashionLab" 
                className="h-12 w-auto mb-4"
              />
            </div>
            <CardTitle>Loading Invitation</CardTitle>
            <CardDescription>Please wait while we load your invitation details</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !invitation) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Card className="w-full max-w-md border-destructive">
          <CardHeader className="text-center">
            <div className="flex flex-col items-center mb-6">
              <img 
                src="/fashionlab-logo.svg" 
                alt="FashionLab" 
                className="h-12 w-auto mb-4"
              />
            </div>
            <XCircle className="mx-auto h-12 w-12 text-destructive" />
            <CardTitle className="mt-4 text-destructive">Invitation Error</CardTitle>
            <CardDescription className="text-destructive/80">{error || 'Invalid invitation link'}</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Invalid or Expired</AlertTitle>
              <AlertDescription>
                This invitation link is invalid, has expired, or has already been accepted.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-center gap-4">
            <Button variant="outline" onClick={() => navigate('/login')}>
              Sign In
            </Button>
            <Button onClick={() => navigate('/')}>
              Go to Homepage
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4 bg-muted/30">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <div className="flex flex-col items-center mb-6">
            <img 
              src="/fashionlab-logo.svg" 
              alt="FashionLab" 
              className="h-12 w-auto mb-4"
            />
          </div>
          {invitation.organization_id && invitation.organization?.logo_url && (
            <div className="flex justify-center mb-4">
              <img 
                src={invitation.organization.logo_url} 
                alt={invitation.organization.name}
                className="max-h-16 max-w-[200px] object-contain" 
              />
            </div>
          )}
          <div className="flex justify-center mb-2">
            <Badge className="px-3 py-1 text-sm">
              <Mail className="h-3.5 w-3.5 mr-1" />
              Invitation
            </Badge>
          </div>
          <CardTitle className="text-xl md:text-2xl">
            {invitation.organization_id 
              ? `You're invited to join ${invitation.organization?.name}`
              : "You're invited to become a Platform Administrator"
            }
          </CardTitle>
          <CardDescription className="mt-2">
            <span className="font-medium">
              {invitation.inviter 
                ? `${invitation.inviter.first_name || ''} ${invitation.inviter.last_name || ''}`.trim() || invitation.inviter.email 
                : 'Someone'}
            </span> has invited you to {invitation.organization_id ? 'join their organization' : 'join the platform admin team'}
          </CardDescription>
          <div className="flex justify-center mt-3">
            <Badge variant="outline" className="flex items-center gap-1.5 px-2 py-1">
              <Clock className="h-3 w-3" />
              Expires in {Math.ceil((new Date(invitation.expires_at).getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'login' | 'signup')}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">I have an account</TabsTrigger>
              <TabsTrigger value="signup">I'm new here</TabsTrigger>
            </TabsList>
            <TabsContent value="login">
              <form onSubmit={handleLogin}>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="login-email">Email</Label>
                    <Input
                      id="login-email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isAccepting}
                      placeholder="<EMAIL>"
                      className="focus:border-primary"
                      required
                    />
                    {email.toLowerCase() !== invitation.email.toLowerCase() && email && (
                      <p className="text-xs text-amber-600 mt-1">
                        <AlertCircle className="inline h-3 w-3 mr-1" />
                        This email is different from the invitation ({invitation.email})
                      </p>
                    )}
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="login-password">Password</Label>
                    <Input
                      id="login-password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={isAccepting}
                      placeholder="••••••••"
                      className="focus:border-primary"
                      required
                    />
                  </div>
                  <Alert className="mb-4 mt-2 bg-blue-50">
                    <Info className="h-4 w-4" />
                    <AlertTitle>Sign in to accept</AlertTitle>
                    <AlertDescription>
                      Sign in with your existing account to accept this invitation
                    </AlertDescription>
                  </Alert>
                  
                  <Button 
                    type="submit" 
                    disabled={isAccepting}
                    className="w-full"
                  >
                    {isAccepting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Accepting Invitation...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Sign In & Accept Invitation
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </TabsContent>
            <TabsContent value="signup">
              <form onSubmit={handleSignup}>
                <div className="grid gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="signup-email">Email</Label>
                    <Input
                      id="signup-email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      disabled={isAccepting}
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      Must match the email address the invitation was sent to
                    </p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="grid gap-2">
                      <Label htmlFor="first-name">First Name</Label>
                      <Input
                        id="first-name"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        disabled={isAccepting}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="last-name">Last Name</Label>
                      <Input
                        id="last-name"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        disabled={isAccepting}
                      />
                    </div>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="signup-password">Password</Label>
                    <Input
                      id="signup-password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={isAccepting}
                      required
                      minLength={8}
                    />
                    <p className="text-xs text-muted-foreground">
                      Password must be at least 8 characters long
                    </p>
                  </div>
                  <Alert className="mb-4 mt-2 bg-blue-50">
                    <Info className="h-4 w-4" />
                    <AlertTitle>New to the platform?</AlertTitle>
                    <AlertDescription>
                      Create a new account to accept this invitation
                    </AlertDescription>
                  </Alert>
                  
                  <Button 
                    type="submit" 
                    disabled={isAccepting}
                    className="w-full"
                  >
                    {isAccepting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <UserPlus className="mr-2 h-4 w-4" />
                        Create Account & Accept
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

export default InvitationAccept; 