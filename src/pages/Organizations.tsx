import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, LayoutGrid, List, SlidersHorizontal } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import PageTitle from '../components/ui/PageTitle';
import OrganizationOverview from '../components/organizations/OrganizationOverview';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '../components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../components/ui/dialog";
import { Label } from "../components/ui/label";
import { useNavigate } from 'react-router-dom';
import { useSupabase } from '../contexts/SupabaseContext';
import { useToast } from '../components/ui/use-toast';
import { useOrganizations } from '../components/common/hooks/useOrganizations';
import { supabase, STORAGE_BUCKETS } from "../components/common/utils/supabase";

export function Organizations() {
  const navigate = useNavigate();
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortOption, setSortOption] = useState('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isCreatingClient, setIsCreatingClient] = useState(false);
  const [isAddClientDialogOpen, setIsAddClientDialogOpen] = useState(false);
  const [newClientName, setNewClientName] = useState('');
  const [newClientLogo, setNewClientLogo] = useState<File | null>(null);

  // Fetch organizations count for display
  const { data: organizations = [], isLoading } = useOrganizations();
  const organizationCount = organizations.length;

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    setSortOption(value);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
  };

  // Handle file input change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setNewClientLogo(e.target.files[0]);
    }
  };

  // Handle add client dialog open
  const handleAddClientClick = () => {
    setIsAddClientDialogOpen(true);
  };

  // Handle add client form submit
  const handleAddClientSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isCreatingClient) return; // Prevent multiple submissions
    if (!newClientName.trim()) {
      toast({
        title: "Error",
        description: "Client name is required",
        variant: "destructive",
      });
      return;
    }
    
    setIsCreatingClient(true);
    
    try {
      let logoUrl: string | null = null;
      
      // Upload logo if provided
      if (newClientLogo) {
        const fileExt = newClientLogo.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `client-logos/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .upload(filePath, newClientLogo);
        
        if (uploadError) throw uploadError;
        
        // Get public URL
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.PROFILES.name)
          .getPublicUrl(filePath);
        
        logoUrl = publicUrlData.publicUrl;
      }
      
      // Insert a new client
      const { data, error } = await supabase
        .from('organizations')
        .insert({
          name: newClientName,
          logo_url: logoUrl,
          // Organizations table has additional fields that need defaults
          type: 'brand', // Set as brand type
          is_active: true
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Show success toast
      toast({
        title: "Client created",
        description: "New client has been created successfully.",
        variant: "default",
      });
      
      // Reset form and close dialog
      setNewClientName('');
      setNewClientLogo(null);
      setIsAddClientDialogOpen(false);
      
      // Navigate to the new organization's page
      if (data) {
        navigate(`/organizations/${data.id}`);
      }
    } catch (error: any) {
      console.error('Error creating client:', error);
      
      // Show error toast
      toast({
        title: "Error creating client",
        description: error.message || "An error occurred while creating the client.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingClient(false);
    }
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchTerm('');
  };

  // Function to generate client logo preview url
  const getClientLogoUrl = (path: string | null) => {
    if (!path) return null;
    
    try {
      const { data } = supabase.storage
        .from(STORAGE_BUCKETS.PROFILES.name)
        .getPublicUrl(path);
      
      return data.publicUrl;
    } catch (error) {
      console.error('Error getting logo URL:', error);
      return null;
    }
  };

  return (
    <div className="space-y-6 page-transition">
      <PageTitle 
        title="Brands" 
        subtitle={`Manage your brand accounts (${organizationCount})`}
        action={
          <Button onClick={handleAddClientClick}>
            <Plus size={16} className="mr-2" />
            Add Brand
          </Button>
        }
      />
      
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">All Brands</CardTitle>
              <CardDescription className="text-sm">
                View and manage all brand accounts in the system.
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-3">
              <Select 
                defaultValue="name" 
                value={sortOption}
                onValueChange={handleSortChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name A-Z</SelectItem>
                  <SelectItem value="name_desc">Name Z-A</SelectItem>
                  <SelectItem value="created_at">Newest First</SelectItem>
                  <SelectItem value="created_at_asc">Oldest First</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex border rounded-md">
                <Button 
                  variant={viewMode === 'grid' ? 'default' : 'ghost'} 
                  size="icon" 
                  className="rounded-none border-r"
                  onClick={() => handleViewModeChange('grid')}
                >
                  <LayoutGrid size={16} />
                </Button>
                <Button 
                  variant={viewMode === 'list' ? 'default' : 'ghost'} 
                  size="icon" 
                  className="rounded-none"
                  onClick={() => handleViewModeChange('list')}
                >
                  <List size={16} />
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input 
                placeholder="Search brands..." 
                className="pl-10 pr-10"
                value={searchTerm}
                onChange={handleSearchChange}
              />
              {searchTerm && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-10 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  onClick={handleClearSearch}
                >
                  <span className="sr-only">Clear search</span>
                  <span className="text-lg">&times;</span>
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  >
                    <SlidersHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem>
                      All Clients
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      Active Clients
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      With Active Collections
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      Recently Updated
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          <OrganizationOverview 
            searchTerm={searchTerm}
            sortOption={sortOption}
            viewMode={viewMode}
          />
        </CardContent>
      </Card>

      {/* Add Client Dialog */}
      <Dialog open={isAddClientDialogOpen} onOpenChange={setIsAddClientDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Client</DialogTitle>
            <DialogDescription>
              Create a new client account. You can add more details after creation.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddClientSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name" className="required">Client Name</Label>
                <Input
                  id="name"
                  placeholder="Enter client name"
                  value={newClientName}
                  onChange={(e) => setNewClientName(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="logo">Client Logo (Optional)</Label>
                <Input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-muted-foreground">
                  Recommended: Square image, at least 200x200px
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsAddClientDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreatingClient}>
                {isCreatingClient ? 'Creating...' : 'Create Client'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default Organizations;
