import { useState } from 'react';
import { Progress } from '../../components/ui/progress';
import { Badge } from '../../components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { Card } from "../../components/ui/card";
import { Button } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { 
  Plus, 
  CheckCircle, 
  CircleDashed, 
  Clock, 
  Search,
  ArrowUpDown,
  ArrowDown,
  ArrowUp,
  Info,
  Image,
  Filter,
  X,
  SlidersHorizontal,
  ChevronDown,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "../../components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "../../components/ui/tooltip";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "../../components/ui/hover-card";

interface ProductListProps {
  products: ProductType[];
  activeTab: string;
  onSelectProduct: (id: number) => void;
}

export interface ProductType {
  id: number;
  sku: string;
  name: string;
  status: string;
  progress: number;
  thumbnail: string;
  inputAssets: number;
  rawAIImages: number;
  upscaled: number;
  retouched: number;
}

type SortDirection = 'asc' | 'desc' | null;
type SortField = 'sku' | 'name' | 'status' | 'progress' | 'assets' | null;
type FilterState = {
  sku: string[];
  name: string[];
  status: string[];
  progress: number[];
  assets: number[];
};

export function ProductList({ products, activeTab, onSelectProduct }: ProductListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<SortField>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [filters, setFilters] = useState<FilterState>({
    sku: [],
    name: [],
    status: activeTab !== 'all' ? [activeTab === 'not-started' ? 'Not Started' : activeTab === 'in-progress' ? 'In Progress' : 'Completed'] : [],
    progress: [],
    assets: [],
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed": return <CheckCircle className="h-3.5 w-3.5 text-emerald-500" />;
      case "In Progress": return <Clock className="h-3.5 w-3.5 text-blue-500" />;
      case "Not Started": return <CircleDashed className="h-3.5 w-3.5 text-gray-400" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-emerald-200";
      case "In Progress": return "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200";
      case "Not Started": return "bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200";
      default: return "";
    }
  };

  const getStatusChipColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-emerald-100 text-emerald-800 hover:bg-emerald-200 border-emerald-200";
      case "In Progress": return "bg-blue-100 text-blue-800 hover:bg-blue-200 border-blue-200";
      case "Not Started": return "bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200";
      default: return "bg-secondary text-secondary-foreground";
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress < 30) return "bg-red-500";
    if (progress < 70) return "bg-amber-500";
    return "bg-emerald-500";
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' 
      ? <ArrowUp className="ml-2 h-4 w-4" /> 
      : <ArrowDown className="ml-2 h-4 w-4" />;
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortField(null);
        setSortDirection(null);
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleStatusFilterChange = (status: string) => {
    setFilters(prev => {
      // If the status is already filtered, remove it (toggle off)
      if (prev.status.includes(status)) {
        return {
          ...prev,
          status: prev.status.filter(s => s !== status)
        };
      } 
      // Otherwise add it to the filters
      else {
        return {
          ...prev,
          status: [...prev.status, status]
        };
      }
    });
  };

  const clearAllFilters = () => {
    setFilters({
      sku: [],
      name: [],
      status: [],
      progress: [],
      assets: [],
    });
    setSearchQuery('');
  };

  // Filter products based on active filters and search query
  let filteredProducts = products.filter(product => {
    // Search query filter
    const searchMatch = 
      searchQuery === '' || 
      product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Status filter
    const statusFilterMatch = filters.status.length === 0 || filters.status.includes(product.status);
    
    // Progress filter
    const progressFilterMatch = filters.progress.length === 0 || 
      filters.progress.some(threshold => {
        if (threshold === 25) return product.progress <= 25;
        if (threshold === 50) return product.progress > 25 && product.progress <= 50;
        if (threshold === 75) return product.progress > 50 && product.progress <= 75;
        if (threshold === 100) return product.progress > 75;
        return true;
      });
    
    return searchMatch && statusFilterMatch && progressFilterMatch;
  });

  // Sort the filtered products
  if (sortField && sortDirection) {
    filteredProducts = [...filteredProducts].sort((a, b) => {
      let aValue, bValue;
      
      switch(sortField) {
        case 'sku':
          aValue = a.sku;
          bValue = b.sku;
          break;
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'progress':
          aValue = a.progress;
          bValue = b.progress;
          break;
        case 'assets':
          aValue = a.inputAssets + a.rawAIImages + a.upscaled + a.retouched;
          bValue = b.inputAssets + b.rawAIImages + b.upscaled + b.retouched;
          break;
        default:
          return 0;
      }
      
      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }

  // Calculate total assets for a product
  const getTotalAssets = (product: ProductType) => {
    return product.inputAssets + product.rawAIImages + product.upscaled + product.retouched;
  };

  // Get percentage of each asset type
  const getAssetPercentage = (count: number, total: number) => {
    return total === 0 ? 0 : (count / total) * 100;
  };

  // Define milestones for the progress bar (e.g., at 25%, 50%, 75%)
  const defaultMilestones = [25, 50, 75];

  // Get unique status values
  const uniqueStatuses = Array.from(new Set(products.map(p => p.status)));

  // Get active filter count
  const activeFilterCount = 
    filters.sku.length + 
    filters.name.length + 
    filters.status.length + 
    filters.progress.length + 
    filters.assets.length;

  return (
    <Card>
      <div className="p-4 flex justify-between items-center border-b">
        <h3 className="font-medium">Products</h3>
        <Button size="sm" className="text-xs">
          <Plus className="h-3.5 w-3.5 mr-1" />
          Add Product
        </Button>
      </div>
      
      <div className="p-4 border-b space-y-3">
        {/* Integrated search and filter bar */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search by SKU or product name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
          
          <div className="flex gap-2 items-center">
            <Button 
              variant="outline" 
              size="sm"
              className="h-10 flex items-center gap-1"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            >
              <SlidersHorizontal className="h-4 w-4" />
              <span>Filters</span>
              {activeFilterCount > 0 && (
                <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center rounded-full">
                  {activeFilterCount}
                </Badge>
              )}
              <ChevronDown className={`h-4 w-4 transition-transform ${showAdvancedFilters ? 'rotate-180' : ''}`} />
            </Button>
            
            {activeFilterCount > 0 && (
              <Button 
                variant="ghost" 
                size="sm"
                className="h-10 text-muted-foreground"
                onClick={clearAllFilters}
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Status filter chips */}
        <div className={`flex flex-wrap gap-2 ${filters.status.length > 0 || showAdvancedFilters ? '' : 'hidden'}`}>
          {uniqueStatuses.map(status => (
            <Badge
              key={status}
              variant="outline"
              className={`cursor-pointer transition-colors ${getStatusChipColor(status)} ${filters.status.includes(status) ? 'ring-2 ring-primary ring-offset-1' : ''}`}
              onClick={() => handleStatusFilterChange(status)}
            >
              <span className="flex items-center gap-1.5">
                {getStatusIcon(status)}
                <span>{status}</span>
                {filters.status.includes(status) && <X className="h-3 w-3 ml-1" />}
              </span>
            </Badge>
          ))}
        </div>

        {/* Advanced filter section */}
        {showAdvancedFilters && (
          <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-3 pt-2 border-t mt-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Progress</label>
              <div className="flex flex-wrap gap-2">
                <Badge
                  variant="outline"
                  className={`cursor-pointer ${filters.progress.includes(25) ? 'bg-red-100 text-red-800 ring-2 ring-primary ring-offset-1' : ''}`}
                  onClick={() => {
                    setFilters(prev => ({
                      ...prev,
                      progress: prev.progress.includes(25) 
                        ? prev.progress.filter(p => p !== 25)
                        : [...prev.progress, 25]
                    }));
                  }}
                >
                  0-25%
                </Badge>
                <Badge
                  variant="outline"
                  className={`cursor-pointer ${filters.progress.includes(50) ? 'bg-amber-100 text-amber-800 ring-2 ring-primary ring-offset-1' : ''}`}
                  onClick={() => {
                    setFilters(prev => ({
                      ...prev,
                      progress: prev.progress.includes(50) 
                        ? prev.progress.filter(p => p !== 50)
                        : [...prev.progress, 50]
                    }));
                  }}
                >
                  26-50%
                </Badge>
                <Badge
                  variant="outline"
                  className={`cursor-pointer ${filters.progress.includes(75) ? 'bg-yellow-100 text-yellow-800 ring-2 ring-primary ring-offset-1' : ''}`}
                  onClick={() => {
                    setFilters(prev => ({
                      ...prev,
                      progress: prev.progress.includes(75) 
                        ? prev.progress.filter(p => p !== 75)
                        : [...prev.progress, 75]
                    }));
                  }}
                >
                  51-75%
                </Badge>
                <Badge
                  variant="outline"
                  className={`cursor-pointer ${filters.progress.includes(100) ? 'bg-emerald-100 text-emerald-800 ring-2 ring-primary ring-offset-1' : ''}`}
                  onClick={() => {
                    setFilters(prev => ({
                      ...prev,
                      progress: prev.progress.includes(100) 
                        ? prev.progress.filter(p => p !== 100)
                        : [...prev.progress, 100]
                    }));
                  }}
                >
                  76-100%
                </Badge>
              </div>
            </div>
            
            {/* Add more filter sections here if needed */}
          </div>
        )}
      </div>
      
      <div className="rounded-md max-h-[600px] overflow-auto">
        <Table>
          <TableHeader className="sticky top-0 bg-card z-10">
            <TableRow>
              <TableHead className="group relative">
                <div 
                  className="flex items-center cursor-pointer hover:text-primary transition-colors"
                  onClick={() => handleSort('sku')}
                >
                  SKU
                  <span className={`ml-2 transition-opacity ${sortField === 'sku' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                    {getSortIcon('sku') || <ArrowUpDown className="h-4 w-4" />}
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-0 ml-1 absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    <div className="p-2">
                      <Input
                        placeholder="Filter SKUs..."
                        className="h-8 mb-2"
                      />
                    </div>
                    <DropdownMenuSeparator />
                    <div className="max-h-[200px] overflow-auto p-1">
                      {Array.from(new Set(products.map(p => p.sku.substring(0, 2)))).map(prefix => (
                        <DropdownMenuCheckboxItem
                          key={prefix}
                          checked={filters.sku.includes(prefix)}
                          onCheckedChange={(checked) => {
                            setFilters(prev => ({
                              ...prev,
                              sku: checked 
                                ? [...prev.sku, prefix]
                                : prev.sku.filter(s => s !== prefix)
                            }));
                          }}
                        >
                          {prefix}*
                        </DropdownMenuCheckboxItem>
                      ))}
                    </div>
                    {filters.sku.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="w-full h-7 text-xs"
                            onClick={() => setFilters(prev => ({ ...prev, sku: [] }))}
                          >
                            <X className="h-3.5 w-3.5 mr-1" /> Clear Filters
                          </Button>
                        </div>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableHead>

              <TableHead className="group relative">
                <div 
                  className="flex items-center cursor-pointer hover:text-primary transition-colors"
                  onClick={() => handleSort('name')}
                >
                  Product Name
                  <span className={`ml-2 transition-opacity ${sortField === 'name' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                    {getSortIcon('name') || <ArrowUpDown className="h-4 w-4" />}
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-0 ml-1 absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    <div className="p-2">
                      <Input
                        placeholder="Filter by name..."
                        className="h-8 mb-2"
                      />
                    </div>
                    <DropdownMenuSeparator />
                    <div className="max-h-[200px] overflow-auto p-1">
                      {/* Name filters would go here */}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableHead>

              <TableHead className="group relative">
                <div 
                  className="flex items-center cursor-pointer hover:text-primary transition-colors"
                  onClick={() => handleSort('status')}
                >
                  Status
                  <span className={`ml-2 transition-opacity ${sortField === 'status' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                    {getSortIcon('status') || <ArrowUpDown className="h-4 w-4" />}
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-0 ml-1 absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-40">
                    <div className="max-h-[200px] overflow-auto p-1">
                      {uniqueStatuses.map(status => (
                        <DropdownMenuCheckboxItem
                          key={status}
                          checked={filters.status.includes(status)}
                          onCheckedChange={(checked) => {
                            setFilters(prev => ({
                              ...prev,
                              status: checked 
                                ? [...prev.status, status]
                                : prev.status.filter(s => s !== status)
                            }));
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <div className={`inline-flex items-center justify-center rounded-full border w-5 h-5 ${getStatusColor(status)}`}>
                              {getStatusIcon(status)}
                            </div>
                            <span>{status}</span>
                          </div>
                        </DropdownMenuCheckboxItem>
                      ))}
                    </div>
                    {filters.status.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="w-full h-7 text-xs"
                            onClick={() => setFilters(prev => ({ ...prev, status: [] }))}
                          >
                            <X className="h-3.5 w-3.5 mr-1" /> Clear
                          </Button>
                        </div>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableHead>

              <TableHead className="group relative">
                <div 
                  className="flex items-center cursor-pointer hover:text-primary transition-colors"
                  onClick={() => handleSort('progress')}
                >
                  Progress
                  <span className={`ml-2 transition-opacity ${sortField === 'progress' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                    {getSortIcon('progress') || <ArrowUpDown className="h-4 w-4" />}
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-0 ml-1 absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-40">
                    <div className="max-h-[200px] overflow-auto p-1">
                      <DropdownMenuCheckboxItem
                        checked={filters.progress.includes(25)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            progress: checked 
                              ? [...prev.progress, 25]
                              : prev.progress.filter(p => p !== 25)
                          }));
                        }}
                      >
                        <span>0-25%</span>
                      </DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem
                        checked={filters.progress.includes(50)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            progress: checked 
                              ? [...prev.progress, 50]
                              : prev.progress.filter(p => p !== 50)
                          }));
                        }}
                      >
                        <span>26-50%</span>
                      </DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem
                        checked={filters.progress.includes(75)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            progress: checked 
                              ? [...prev.progress, 75]
                              : prev.progress.filter(p => p !== 75)
                          }));
                        }}
                      >
                        <span>51-75%</span>
                      </DropdownMenuCheckboxItem>
                      <DropdownMenuCheckboxItem
                        checked={filters.progress.includes(100)}
                        onCheckedChange={(checked) => {
                          setFilters(prev => ({
                            ...prev,
                            progress: checked 
                              ? [...prev.progress, 100]
                              : prev.progress.filter(p => p !== 100)
                          }));
                        }}
                      >
                        <span>76-100%</span>
                      </DropdownMenuCheckboxItem>
                    </div>
                    {filters.progress.length > 0 && (
                      <>
                        <DropdownMenuSeparator />
                        <div className="p-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="w-full h-7 text-xs"
                            onClick={() => setFilters(prev => ({ ...prev, progress: [] }))}
                          >
                            <X className="h-3.5 w-3.5 mr-1" /> Clear
                          </Button>
                        </div>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableHead>

              <TableHead className="group relative">
                <div 
                  className="flex items-center cursor-pointer hover:text-primary transition-colors"
                  onClick={() => handleSort('assets')}
                >
                  Assets
                  <span className={`ml-2 transition-opacity ${sortField === 'assets' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}>
                    {getSortIcon('assets') || <ArrowUpDown className="h-4 w-4" />}
                  </span>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 p-0 ml-1 absolute right-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100"
                    >
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-40">
                    <div className="max-h-[200px] overflow-auto p-1">
                      {/* Asset count filters would go here */}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredProducts.length > 0 ? (
              filteredProducts.map((product) => {
                const totalAssets = getTotalAssets(product);
                const inputPercent = getAssetPercentage(product.inputAssets, totalAssets);
                const rawAIImagesPercent = getAssetPercentage(product.rawAIImages, totalAssets);
                const upscaledPercent = getAssetPercentage(product.upscaled, totalAssets);
                const retouchedPercent = getAssetPercentage(product.retouched, totalAssets);
                
                return (
                  <TableRow 
                    key={product.id} 
                    className="cursor-pointer hover:bg-muted"
                    onClick={() => onSelectProduct(product.id)}
                  >
                    <TableCell className="font-medium">{product.sku}</TableCell>
                    <TableCell>
                      <HoverCard>
                        <HoverCardTrigger asChild>
                          <div className="flex items-center gap-2">
                            <div className="w-8 h-8 rounded bg-secondary flex items-center justify-center overflow-hidden">
                              <img src={product.thumbnail} alt={product.name} className="w-6 h-6 object-contain" />
                            </div>
                            {product.name}
                          </div>
                        </HoverCardTrigger>
                        <HoverCardContent className="w-80 p-0">
                          <div className="relative aspect-video w-full overflow-hidden rounded-t-md">
                            {product.thumbnail ? (
                              <img 
                                src={product.thumbnail} 
                                alt={product.name} 
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="w-full h-full bg-muted flex items-center justify-center">
                                <Image className="h-8 w-8 text-muted-foreground" />
                              </div>
                            )}
                          </div>
                          <div className="p-4 space-y-2">
                            <h4 className="font-medium">{product.name}</h4>
                            <p className="text-sm text-muted-foreground">SKU: {product.sku}</p>
                          </div>
                        </HoverCardContent>
                      </HoverCard>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className={`inline-flex items-center justify-center rounded-full border w-7 h-7 ${getStatusColor(product.status)}`}>
                              {getStatusIcon(product.status)}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>{product.status}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                    <TableCell>
                      <div className="w-full max-w-[120px]">
                        <Progress 
                          value={product.progress} 
                          className="h-1.5" 
                          indicatorClassName={getProgressColor(product.progress)}
                          milestones={defaultMilestones}
                          showPercentageOverlay={true}
                        />
                      </div>
                    </TableCell>
                    <TableCell>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="space-y-1.5">
                              <div className="flex items-center gap-1">
                                <div className="flex h-2 w-full rounded-full overflow-hidden">
                                  {inputPercent > 0 && <div className="bg-blue-400" style={{ width: `${inputPercent}%` }}></div>}
                                  {rawAIImagesPercent > 0 && <div className="bg-indigo-400" style={{ width: `${rawAIImagesPercent}%` }}></div>}
                                  {upscaledPercent > 0 && <div className="bg-purple-400" style={{ width: `${upscaledPercent}%` }}></div>}
                                  {retouchedPercent > 0 && <div className="bg-green-400" style={{ width: `${retouchedPercent}%` }}></div>}
                                </div>
                                <Info className="h-3.5 w-3.5 text-muted-foreground" />
                              </div>
                              <p className="text-xs text-muted-foreground">{totalAssets} assets</p>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent className="w-auto">
                            <div className="space-y-1 px-1">
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                                <span className="text-xs">{product.inputAssets} Input</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-indigo-400"></div>
                                <span className="text-xs">{product.rawAIImages} Raw AI Images</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-purple-400"></div>
                                <span className="text-xs">{product.upscaled} Upscaled</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className="w-3 h-3 rounded-full bg-green-400"></div>
                                <span className="text-xs">{product.retouched} Retouched</span>
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No products found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </Card>
  );
}

export default ProductList;
