
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardContent,
  CardTitle,
} from "../../components/ui/card";
import { Package, Image, Users } from 'lucide-react';

interface ClientMetricsProps {
  clientData: {
    id: number;
    name: string;
    logo: string;
    status: string;
    products: number;
    description: string;
    activeGenerations: number;
    completedItems: number;
  };
  productsInProgress: number;
  completedProducts: number;
}

export function ClientMetrics({ clientData, productsInProgress, completedProducts }: ClientMetricsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Products</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{clientData.products}</div>
          <p className="text-xs text-muted-foreground">
            {clientData.activeGenerations} in active generation
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Generations</CardTitle>
          <Image className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{clientData.activeGenerations}</div>
          <p className="text-xs text-muted-foreground">
            Across {productsInProgress} products
          </p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Completed Items</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{clientData.completedItems}</div>
          <p className="text-xs text-muted-foreground">
            {completedProducts} products fully completed
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

export default ClientMetrics;
