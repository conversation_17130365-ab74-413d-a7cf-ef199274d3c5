import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader } from "../../components/ui/card";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Plus, Search, Filter, FolderPlus, Edit } from "lucide-react";
import { Badge } from "../../components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Label } from "../../components/ui/label";
import { Textarea } from "../../components/ui/textarea";
import { useSupabase } from '../../contexts/SupabaseContext';
import { useToast } from '../../components/ui/use-toast';

interface CollectionType {
  id: string;
  name: string;
  description: string;
  cover_image_url?: string | null;
  productCount: number;
  status: string;
  lastUpdated: string;
}

interface CollectionListProps {
  collections: CollectionType[];
  onSelectCollection: (id: string) => void;
  onAddCollection: () => void;
  isLoading?: boolean;
  onCollectionUpdated?: () => void;
  canCreateCollections?: boolean;
}

export function CollectionList({ 
  collections, 
  onSelectCollection, 
  onAddCollection,
  isLoading = false,
  onCollectionUpdated,
  canCreateCollections = true
}: CollectionListProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { supabase } = useSupabase();
  const { toast } = useToast();
  
  // Add state for edit functionality
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editCollectionId, setEditCollectionId] = useState<string | null>(null);
  const [editCollectionName, setEditCollectionName] = useState('');
  const [editCollectionDescription, setEditCollectionDescription] = useState('');
  const [isUpdatingCollection, setIsUpdatingCollection] = useState(false);

  // Calculate workflow stage counts
  const stageCounts = useMemo(() => {
    return collections.reduce((acc, collection) => {
      const status = collection.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [collections]);

  // Calculate total product count
  const totalProductCount = useMemo(() => {
    return collections.reduce((sum, collection) => sum + collection.productCount, 0);
  }, [collections]);

  // Filter collections based on search query
  const filteredCollections = collections.filter(collection => 
    collection.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    collection.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get status color based on collection status
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-800";
      case "Draft": return "bg-amber-100 text-amber-800";
      case "Archived": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  // Render empty state
  const renderEmptyState = () => (
    <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
      <FolderPlus className="h-12 w-12 text-muted-foreground mb-4" />
      <h3 className="text-lg font-medium mb-2">No Campaigns Found</h3>
      <p className="text-sm text-muted-foreground mb-4">
        {searchQuery 
          ? "No campaigns match your search criteria. Try adjusting your search."
          : "Get started by creating your first campaign."}
      </p>
      {!searchQuery && canCreateCollections && (
        <Button onClick={onAddCollection} className="gap-2">
          <Plus className="h-4 w-4" />
          Create Campaign
        </Button>
      )}
    </div>
  );

  // Handle edit click
  const handleEditClick = (e: React.MouseEvent, collection: CollectionType) => {
    e.stopPropagation(); // Prevent card click
    setEditCollectionId(collection.id);
    setEditCollectionName(collection.name);
    setEditCollectionDescription(collection.description);
    setIsEditDialogOpen(true);
  };

  // Handle collection update
  const handleUpdateCollection = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isUpdatingCollection || !editCollectionId) return;
    if (!editCollectionName.trim()) {
      toast({
        title: "Error",
        description: "Campaign name is required",
        variant: "destructive",
      });
      return;
    }
    
    setIsUpdatingCollection(true);
    
    try {
      const { error } = await supabase
        .from('collections')
        .update({
          name: editCollectionName,
          description: editCollectionDescription || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', editCollectionId);
      
      if (error) throw error;
      
      toast({
        title: "Campaign updated",
        description: "Campaign information has been updated successfully.",
      });
      
      // Call the update callback if provided
      if (onCollectionUpdated) {
        onCollectionUpdated();
      }
      
      setIsEditDialogOpen(false);
    } catch (error: any) {
      console.error('Error updating collection:', error);
      toast({
        title: "Error updating campaign",
        description: error.message || "An error occurred while updating the campaign.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingCollection(false);
    }
  };


  return (
    <Card className="flex-1">
      <CardHeader className="p-4 flex-row items-center justify-between border-b">
        <div>
          <h3 className="font-medium">Campaigns</h3>
          {!isLoading && (
            <p className="text-sm text-muted-foreground mt-1">
              {collections.length} campaign{collections.length !== 1 ? 's' : ''} • {totalProductCount} product{totalProductCount !== 1 ? 's' : ''}
            </p>
          )}
        </div>
        {canCreateCollections && (
          <Button 
            size="sm"
            onClick={onAddCollection}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Create
          </Button>
        )}
      </CardHeader>
      
      {collections.length > 0 && (
        <div className="p-4 border-b">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search campaigns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10 text-sm"
            />
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              <Filter size={16} />
            </Button>
          </div>
        </div>
      )}
      
      <CardContent className="p-0">
        {isLoading ? (
          // Loading state
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 p-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border rounded-lg overflow-hidden">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-3">
                  <div className="h-5 bg-muted rounded animate-pulse mb-2" />
                  <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
                </div>
              </div>
            ))}
          </div>
        ) : filteredCollections.length > 0 ? (
          // Collections grid
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 p-4">
            {filteredCollections.map(collection => (
              <div 
                key={collection.id}
                className="border rounded-lg overflow-hidden cursor-pointer hover:border-primary transition-colors group"
                onClick={() => onSelectCollection(collection.id)}
              >
                <div className="aspect-video bg-muted relative group">
                  {collection.cover_image_url ? (
                    <div className="absolute inset-0 overflow-hidden">
                      <img 
                        src={collection.cover_image_url} 
                        alt={collection.name} 
                        className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                      />
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-muted-foreground bg-muted/50">
                      <FolderPlus className="h-8 w-8" />
                    </div>
                  )}
                  {/* Overlay gradient for better text readability */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none" />
                  
                  <div className="absolute bottom-2 right-2 flex gap-2 items-center z-10">
                    <span className="bg-background/90 text-foreground text-xs px-2 py-1 rounded-md backdrop-blur-sm">
                      {collection.productCount} product{collection.productCount !== 1 ? 's' : ''}
                    </span>
                  </div>
                  
                  {/* Edit button with improved hover state */}
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-background/90 backdrop-blur-sm hover:bg-background z-10"
                    onClick={(e) => handleEditClick(e, collection)}
                  >
                    <Edit size={16} />
                  </Button>
                </div>
                <div className="p-4">
                  <div className="flex items-start justify-between gap-2">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium truncate">{collection.name}</h4>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{collection.description}</p>
                    </div>
                  </div>
                  <div className="flex justify-between items-center mt-3 pt-3 border-t">
                    <Badge className={`${getStatusColor(collection.status)}`}>
                      {collection.status}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      Updated {collection.lastUpdated}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Empty state
          renderEmptyState()
        )}
      </CardContent>

      {/* Edit Campaign Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Campaign</DialogTitle>
            <DialogDescription>
              Update campaign information. To manage cover images and more detailed settings, visit the campaign detail page.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateCollection}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name" className="required">Campaign Name</Label>
                <Input
                  id="edit-name"
                  placeholder="Enter campaign name"
                  value={editCollectionName}
                  onChange={(e) => setEditCollectionName(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  placeholder="Enter campaign description"
                  value={editCollectionDescription}
                  onChange={(e) => setEditCollectionDescription(e.target.value)}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isUpdatingCollection}>
                {isUpdatingCollection ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Card>
  );
}

export default CollectionList; 