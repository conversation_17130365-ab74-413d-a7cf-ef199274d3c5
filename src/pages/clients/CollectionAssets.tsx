import { useMemo, useState } from 'react';
import { useFilters } from '../../contexts/FilterContext';
import { AssetSelectionHeader } from '../../components/asset-management/AssetSelectionHeader';
import { AssetStageGroup } from '../../components/asset-management/AssetStageGroup';
import { EmptyAssetsState } from '../../components/asset-management/EmptyAssetsState';
import { FloatingActionBar } from '../../components/asset-management/FloatingActionBar';
import { Badge } from '../../components/ui/badge';
import { Asset } from '../../components/common/types/database.types';
import BulkTagManager from '../../components/asset-management/BulkTagManager';
import BulkWorkflowStageManager from '../../components/asset-management/BulkWorkflowStageManager';
import BulkProductManager from '../../components/asset-management/BulkProductManager';
import { BulkSizeManager } from '../../components/asset-management/BulkSizeManager';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../../components/ui/alert-dialog';

// Custom hooks
import { useAssets } from '../../components/common/hooks/useAssets';
import { useAssetTags } from '../../components/common/hooks/useAssetTags';
import { useAssetSelection } from '../../components/common/hooks/useAssetSelection';
import { useAssetOperations } from '../../components/common/hooks/useAssetOperations';
import { useAssetFiltering } from '../../components/common/hooks/useAssetFiltering';
import { useProducts } from '../../components/common/hooks/useProducts';

// Utilities
import {
  convertAssetToAssetItem,
  getStageDisplayName,
  getStageStyles
} from '../../components/common/utils/assetConversion';
import { useUserRole } from '../../contexts/UserRoleContext';
import { getAllowedWorkflowStages } from '../../components/common/utils/workflowStageUtils';
import { useIsFreelancer } from '../../components/common/hooks/useIsFreelancer';

interface CollectionAssetsProps {
  collectionId: string;
  assets?: Asset[]; // Keep for backward compatibility but mark as deprecated
  isLoading?: boolean; // Keep for backward compatibility but mark as deprecated
  onAssetClick?: (assetId: string) => void;
  onAssetDeleted?: (assetId: string) => void;
  clientId?: string;
  orgId?: string;
}

export function CollectionAssets({ 
  collectionId, 
  assets: propsAssets, // Renamed to avoid confusion
  isLoading: propsIsLoading, // Renamed to avoid confusion
  onAssetClick,
  onAssetDeleted,
  clientId,
  orgId
}: CollectionAssetsProps) {
  const { filters } = useFilters();
  const [isTagManagerOpen, setIsTagManagerOpen] = useState(false);
  const [isWorkflowStageManagerOpen, setIsWorkflowStageManagerOpen] = useState(false);
  const [isProductManagerOpen, setIsProductManagerOpen] = useState(false);
  const [isSizeManagerOpen, setIsSizeManagerOpen] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [refreshTimestamp, setRefreshTimestamp] = useState<number>(0);

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stages, considering freelancer status
  const allowedWorkflowStages = getAllowedWorkflowStages(userRole, isFreelancer);

  // Fetch assets directly in this component instead of receiving as props
  // This ensures we always have fresh data when cache is invalidated
  const {
    data: assets = [],
    isLoading,
    isError,
    refetch
  } = useAssets({
    collectionId,
    enabled: !!collectionId,
    // Force refetch on mount to ensure fresh data
    sortBy: 'created_at',
    sortOrder: 'desc',
    forceRefresh: refreshTimestamp
  });

  // Convert assets to UI format
  const assetItems = useMemo(() => 
    assets.map(convertAssetToAssetItem), 
    [assets]
  );

  // Fetch products for size filtering
  const { data: products = [] } = useProducts({
    collectionId,
    enabled: !!collectionId
  });

  // Custom hooks for functionality
  const { data: assetTagMap = {} } = useAssetTags(collectionId);
  
  const {
    filteredAssets,
    assetsByStage,
    stageCounts,
    productNames,
    isFiltering,
  } = useAssetFiltering({ assets: assetItems, filters, assetTagMap, products });

  // Get filtered asset IDs for selection management
  const filteredAssetIds = useMemo(() => 
    filteredAssets.map(item => item.id), 
    [filteredAssets]
  );
  
  const {
    selectedAssets,
    toggleAsset,
    toggleAll,
    clearSelection,
    hasSelection,
    selectionCount,
  } = useAssetSelection(filteredAssetIds);

  const {
    deleteAssets,
    downloadAssets,
    isDeleting,
    isDownloading,
  } = useAssetOperations({ 
    collectionId, 
    onAssetsDeleted: (deletedIds) => {
      clearSelection();
      deletedIds.forEach(id => onAssetDeleted?.(id));
    }
  });

  // Handle asset selection
  const handleAssetSelect = (assetId: string) => {
    toggleAsset(assetId);
  };

  // Handle asset click for navigation
  const handleAssetClick = (assetId: string) => {
    onAssetClick?.(assetId);
  };

  // Handle bulk delete with confirmation
  const handleBulkDelete = async () => {
    setShowDeleteDialog(false);
    await deleteAssets(selectedAssets);
  };

  // Handle bulk download
  const handleBulkDownload = async () => {
    await downloadAssets(selectedAssets);
  };

  // Use role-based workflow stages for display
  const workflowStages = allowedWorkflowStages;

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-center items-center h-64">
          <p className="text-muted-foreground">Loading assets...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col justify-center items-center h-64">
          <p className="text-muted-foreground mb-4">Failed to load assets</p>
          <button 
            onClick={() => refetch()} 
            className="text-sm text-blue-600 hover:text-blue-700 underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Collection stats */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Badge variant="outline" className="px-3 py-1 text-xs">
          {filteredAssets.length} total assets
        </Badge>
        
        {productNames.length > 0 && (
          <Badge variant="outline" className="px-3 py-1 text-xs">
            {productNames.length} products
          </Badge>
        )}
        
        {allowedWorkflowStages.map(stage => stageCounts[stage] > 0 && (
          <Badge 
            key={stage} 
            variant="outline" 
            className={`px-3 py-1 text-xs ${getStageStyles(stage)}`}
          >
            {stageCounts[stage]} {getStageDisplayName(stage)}
          </Badge>
        ))}
      </div>
      
      {/* Asset selection header */}
      {filteredAssets.length > 0 && (
        <AssetSelectionHeader
          selectedCount={selectionCount}
          totalCount={filteredAssets.length}
          isFiltering={isFiltering}
          onSelectAll={toggleAll}
        />
      )}
      
      {/* Asset display */}
      {filteredAssets.length === 0 ? (
        <EmptyAssetsState 
          isFiltering={isFiltering}
          collectionId={collectionId}
          clientId={clientId}
          orgId={orgId}
        />
      ) : (
        // Group by workflow stage
        <div className="space-y-8">
          {allowedWorkflowStages.map(stage => assetsByStage[stage]?.length > 0 && (
            <AssetStageGroup
              key={stage}
              stage={stage}
              assets={assetsByStage[stage]}
              selectedAssets={selectedAssets}
              onAssetSelect={handleAssetSelect}
              onAssetClick={handleAssetClick}
              onAssetDeleted={onAssetDeleted}
              productRetouched={0} // TODO: Calculate actual retouched percentage
              clientId={clientId}
              orgId={orgId}
            />
          ))}
        </div>
      )}
      
      {/* Add padding when items are selected to prevent content being hidden */}
      {hasSelection && <div className="h-20" />}
      
      {/* Bulk tag manager */}
      {isTagManagerOpen && (
        <BulkTagManager
          assetIds={selectedAssets}
          collectionId={collectionId}
          isOpen={isTagManagerOpen}
          onClose={() => setIsTagManagerOpen(false)}
          onTagsUpdated={() => {
            // Tags will be automatically refetched by React Query
          }}
        />
      )}
      
      {/* Bulk workflow stage manager */}
      {isWorkflowStageManagerOpen && (
        <BulkWorkflowStageManager
          assetIds={selectedAssets}
          isOpen={isWorkflowStageManagerOpen}
          onClose={() => setIsWorkflowStageManagerOpen(false)}
          collectionId={collectionId}
          onStagesUpdated={() => {
            // Clear selection after successful update
            clearSelection();
            setRefreshTimestamp(Date.now());
          }}
        />
      )}

      {/* Bulk product manager */}
      {isProductManagerOpen && (
        <BulkProductManager
          assetIds={selectedAssets}
          isOpen={isProductManagerOpen}
          onClose={() => setIsProductManagerOpen(false)}
          collectionId={collectionId}
          onProductsUpdated={() => {
            // Clear selection after successful update
            clearSelection();
            setRefreshTimestamp(Date.now());
          }}
        />
      )}

      {/* Bulk size manager */}
      {isSizeManagerOpen && (
        <BulkSizeManager
          assetIds={selectedAssets}
          isOpen={isSizeManagerOpen}
          onClose={() => setIsSizeManagerOpen(false)}
          collectionId={collectionId}
          onSizesUpdated={() => {
            // Clear selection after successful update
            clearSelection();
            setRefreshTimestamp(Date.now());
          }}
        />
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Assets</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectionCount} {selectionCount === 1 ? 'asset' : 'assets'}? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleBulkDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Floating action bar */}
      <FloatingActionBar
        selectedCount={selectionCount}
        onManageTags={() => setIsTagManagerOpen(true)}
        onManageWorkflowStages={() => setIsWorkflowStageManagerOpen(true)}
        onManageProducts={() => setIsProductManagerOpen(true)}
        onManageSizes={() => setIsSizeManagerOpen(true)}
        onDownload={handleBulkDownload}
        onDelete={() => setShowDeleteDialog(true)}
        onClearSelection={clearSelection}
        isDeleting={isDeleting}
        isDownloading={isDownloading}
      />
    </div>
  );
}

export default CollectionAssets;