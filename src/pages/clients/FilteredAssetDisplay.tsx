import { useState } from 'react';
import { ProductType } from './ProductList';
import { useFilters } from '../../contexts/FilterContext';
import { getFilteredAssets, AssetItem } from '../../data/productAssets';
import { AssetSelectionHeader } from '../../components/asset-management/AssetSelectionHeader';
import { AssetStageGroup } from '../../components/asset-management/AssetStageGroup';
import { EmptyAssetsState } from '../../components/asset-management/EmptyAssetsState';
import { useUserRole } from '../../contexts/UserRoleContext';
import { getAllowedWorkflowStages } from '../../components/common/utils/workflowStageUtils';
import { useIsFreelancer } from '../../components/common/hooks/useIsFreelancer';

interface FilteredAssetDisplayProps {
  product: ProductType;
}

export function FilteredAssetDisplay({ product }: FilteredAssetDisplayProps) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const { filters } = useFilters();

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stages, considering freelancer status
  const allowedWorkflowStages = getAllowedWorkflowStages(userRole, isFreelancer);
  
  // Get all assets for this product, then apply filters
  const filteredAssets = getFilteredAssets(product.id.toString(), 'all', filters);
  
  // Group assets by workflow stage
  const assetsByStage = filteredAssets.reduce((acc, asset) => {
    if (!acc[asset.stage]) {
      acc[asset.stage] = [];
    }
    acc[asset.stage].push(asset);
    return acc;
  }, {} as Record<string, AssetItem[]>);
  
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedAssets.length === filteredAssets.length) {
      setSelectedAssets([]);
    } else {
      setSelectedAssets(filteredAssets.map(asset => asset.id));
    }
  };
  
  const hasSelection = selectedAssets.length > 0;
  const isFiltering = filters.workflowStages.length > 0 || 
                      filters.assetTypes.length > 0 || 
                      filters.timeUpdated !== 'all';
  
  // Use role-based workflow stages instead of hardcoded list
  const workflowStages = allowedWorkflowStages;
  
  return (
    <div className="space-y-8">
      {/* Unified actions bar */}
      <AssetSelectionHeader
        selectedCount={selectedAssets.length}
        totalCount={filteredAssets.length}
        isFiltering={isFiltering}
        onSelectAll={handleSelectAll}
        hasSelection={hasSelection}
      />
      
      {/* Empty states */}
      {(isFiltering && filteredAssets.length === 0) || 
       (!isFiltering && Object.keys(assetsByStage).length === 0) ? (
        <EmptyAssetsState isFiltering={isFiltering} />
      ) : null}
      
      {/* Asset stage groups */}
      {workflowStages.map(stage => (
        <AssetStageGroup
          key={stage}
          stage={stage}
          assets={assetsByStage[stage] || []}
          selectedAssets={selectedAssets}
          onAssetSelect={handleAssetSelect}
          productRetouched={product.retouched}
        />
      ))}
    </div>
  );
}
