
import React from 'react';
import { TabsContent, AssetTabs, AssetTabsList, AssetTabsTrigger } from "../../components/ui/tabs";
import { ProductType } from './ProductList';
import {
  ProductInputAssets,
  ProductRawAIImages,
  ProductUpscaled,
  ProductRetouched
} from './ProductAssets';
import {
  Camera,
  Pencil,
  Zap,
  Check,
  Home,
  FilePlus,
  Package
} from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';

interface ProductWorkflowContentProps {
  product: ProductType;
  activeTab: string;
  currentStep: number;
}

export function ProductWorkflowContent({ product, activeTab, currentStep }: ProductWorkflowContentProps) {
  // Get primary action button based on current step
  const getPrimaryActionButton = () => {
    const actions = [
      { step: 0, label: "Upload Images", icon: <Camera size={14} />, disabled: product.inputAssets > 0 },
      { step: 1, label: "Generate Raw AI Images", icon: <Pencil size={14} />, disabled: product.rawAIImages > 0 || product.inputAssets === 0 },
      { step: 2, label: "Upscale Selected", icon: <Zap size={14} />, disabled: product.upscaled > 0 || product.rawAIImages === 0 },
      { step: 3, label: "Retouch Images", icon: <Check size={14} />, disabled: product.retouched > 0 || product.upscaled === 0 }
    ];
    
    const currentAction = actions.find(a => a.step === currentStep);
    
    if (!currentAction) return null;
    
    return (
      <Button 
        size="sm"
        variant="default"
        className="ml-auto"
        disabled={currentAction.disabled}
      >
        {currentAction.icon}
        <span className="ml-1">{currentAction.label}</span>
      </Button>
    );
  };

  const getContextualMessage = () => {
    if (currentStep === 0) return "Start by uploading images";
    if (currentStep === 1) return "Create raw AI images from uploaded images";
    if (currentStep === 2) return "Upscale selected raw AI images";
    if (currentStep === 3) return "Final step: Retouch images";
    return "All workflow steps completed";
  };

  // Asset content tabs
  const [assetContentTab, setAssetContentTab] = React.useState("overview");

  return (
    <div className="space-y-3">
      {/* Contextual header with primary action */}
      <div className="flex items-center justify-between rounded-md p-1">
        <div className="flex items-center gap-2">
          <Badge 
            variant={currentStep === 4 ? "success" : "outline"}
            className="text-xs font-normal"
          >
            {currentStep === 4 ? "Completed" : "In Progress"}
          </Badge>
          <span className="text-xs text-muted-foreground">
            {getContextualMessage()}
          </span>
        </div>
        
        {getPrimaryActionButton()}
      </div>
      
      {/* Content tabs with new asset tabs */}
      <div className="bg-background/50 rounded-lg transition-all animate-fade-in">
        <TabsContent value="images" className="mt-0 p-0">
          <AssetTabs value={assetContentTab} onValueChange={setAssetContentTab}>
            <AssetTabsList className="w-full justify-center">
              <AssetTabsTrigger value="overview" icon={<Home size={16} />}>
                Overview
              </AssetTabsTrigger>
              <AssetTabsTrigger value="projects" icon={<FilePlus size={16} />}>
                Projects
              </AssetTabsTrigger>
              <AssetTabsTrigger value="packages" icon={<Package size={16} />}>
                Packages
              </AssetTabsTrigger>
            </AssetTabsList>
            
            <div className="p-4 min-h-[400px]">
              <TabsContent value="overview" className="mt-0">
                <ProductInputAssets product={product} />
              </TabsContent>
              <TabsContent value="projects" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Projects content for images</p>
                </div>
              </TabsContent>
              <TabsContent value="packages" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Packages content for images</p>
                </div>
              </TabsContent>
            </div>
          </AssetTabs>
        </TabsContent>
        
        <TabsContent value="raw_ai_images" className="mt-0 p-0">
          <AssetTabs value={assetContentTab} onValueChange={setAssetContentTab}>
            <AssetTabsList className="w-full justify-center">
              <AssetTabsTrigger value="overview" icon={<Home size={16} />}>
                Overview
              </AssetTabsTrigger>
              <AssetTabsTrigger value="projects" icon={<FilePlus size={16} />}>
                Projects
              </AssetTabsTrigger>
              <AssetTabsTrigger value="packages" icon={<Package size={16} />}>
                Packages
              </AssetTabsTrigger>
            </AssetTabsList>
            
            <div className="p-4 min-h-[400px]">
              <TabsContent value="overview" className="mt-0">
                <ProductRawAIImages product={product} />
              </TabsContent>
              <TabsContent value="projects" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Projects content for raw AI images</p>
                </div>
              </TabsContent>
              <TabsContent value="packages" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Packages content for raw AI images</p>
                </div>
              </TabsContent>
            </div>
          </AssetTabs>
        </TabsContent>
        
        <TabsContent value="upscaled" className="mt-0 p-0">
          <AssetTabs value={assetContentTab} onValueChange={setAssetContentTab}>
            <AssetTabsList className="w-full justify-center">
              <AssetTabsTrigger value="overview" icon={<Home size={16} />}>
                Overview
              </AssetTabsTrigger>
              <AssetTabsTrigger value="projects" icon={<FilePlus size={16} />}>
                Projects
              </AssetTabsTrigger>
              <AssetTabsTrigger value="packages" icon={<Package size={16} />}>
                Packages
              </AssetTabsTrigger>
            </AssetTabsList>
            
            <div className="p-4 min-h-[400px]">
              <TabsContent value="overview" className="mt-0">
                <ProductUpscaled product={product} />
              </TabsContent>
              <TabsContent value="projects" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Projects content for upscaled assets</p>
                </div>
              </TabsContent>
              <TabsContent value="packages" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Packages content for upscaled assets</p>
                </div>
              </TabsContent>
            </div>
          </AssetTabs>
        </TabsContent>
        
        <TabsContent value="retouched" className="mt-0 p-0">
          <AssetTabs value={assetContentTab} onValueChange={setAssetContentTab}>
            <AssetTabsList className="w-full justify-center">
              <AssetTabsTrigger value="overview" icon={<Home size={16} />}>
                Overview
              </AssetTabsTrigger>
              <AssetTabsTrigger value="projects" icon={<FilePlus size={16} />}>
                Projects
              </AssetTabsTrigger>
              <AssetTabsTrigger value="packages" icon={<Package size={16} />}>
                Packages
              </AssetTabsTrigger>
            </AssetTabsList>
            
            <div className="p-4 min-h-[400px]">
              <TabsContent value="overview" className="mt-0">
                <ProductRetouched product={product} />
              </TabsContent>
              <TabsContent value="projects" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Projects content for retouched assets</p>
                </div>
              </TabsContent>
              <TabsContent value="packages" className="mt-0">
                <div className="text-center py-8 text-muted-foreground">
                  <p>Packages content for retouched assets</p>
                </div>
              </TabsContent>
            </div>
          </AssetTabs>
        </TabsContent>
      </div>
    </div>
  );
}
