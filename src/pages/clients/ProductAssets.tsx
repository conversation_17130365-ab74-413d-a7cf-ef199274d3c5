
import { useState } from 'react';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { 
  MoreHorizontal, 
  Upload, 
  Plus, 
  ArrowRight, 
  Check, 
  RefreshCcw, 
  Zap, 
  Download,
  CheckSquare,
  Square,
  Trash,
  Filter
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../../components/ui/dropdown-menu';
import { ProductType } from './ProductList';
import { EnhancedAssetGrid, AssetItem } from '../../components/products/EnhancedAssetGrid';

interface ProductAssetsProps {
  product: ProductType;
  assetType: 'input' | 'raw_ai_images' | 'upscaled' | 'retouched';
}

// Sample asset data
const generateSampleAssets = (count: number, type: 'input' | 'raw_ai_images' | 'upscaled' | 'retouched'): AssetItem[] => {
  if (count === 0) return [];
  
  let assets: AssetItem[] = [];
  
  if (type === 'input') {
    // Input assets are typically reference and product images
    assets = [
      {
        id: '1',
        name: 'Front Reference.jpg',
        type: 'product-front',
        preview: '/placeholder.svg',
        size: '2.4 MB',
        dateCreated: '2 days ago',
        tags: ['reference', 'front']
      },
      {
        id: '2',
        name: 'Back Reference.jpg',
        type: 'product-back',
        preview: '/placeholder.svg',
        size: '2.1 MB',
        dateCreated: '2 days ago',
        tags: ['reference', 'back']
      }
    ];
    
    // Add detail shots if we have more than 2 assets
    if (count > 2) {
      assets.push({
        id: '3',
        name: 'Detail Reference.jpg',
        type: 'product-detail',
        preview: '/placeholder.svg',
        size: '1.8 MB',
        dateCreated: '2 days ago',
        tags: ['reference', 'detail']
      });
    }
    
    // Add model references if we have more than 3 assets
    if (count > 3) {
      assets.push({
        id: '4',
        name: 'Model Reference.jpg',
        type: 'model',
        preview: '/placeholder.svg',
        size: '3.2 MB',
        dateCreated: '3 days ago',
        tags: ['model', 'reference']
      });
    }
    
    // Add lighting and pose references for larger counts
    if (count > 4) {
      assets.push({
        id: '5',
        name: 'Lighting Setup.jpg',
        type: 'lighting',
        preview: '/placeholder.svg',
        size: '1.5 MB',
        dateCreated: '3 days ago',
        tags: ['lighting', 'reference']
      });
      
      assets.push({
        id: '6',
        name: 'Pose Reference.jpg',
        type: 'pose',
        preview: '/placeholder.svg',
        size: '1.9 MB',
        dateCreated: '3 days ago',
        tags: ['pose', 'reference']
      });
    }
  } else {
    // For other asset types, we'll create a mix of front, back, and detail shots
    const assetTypes = ['product-front', 'product-back', 'product-detail'];
    const tagPrefix = type === 'raw_ai_images' ? 'raw_ai_images' : (type === 'upscaled' ? 'upscaled' : 'retouched');
    
    for (let i = 0; i < count; i++) {
      const assetType = assetTypes[i % assetTypes.length] as any;
      assets.push({
        id: `${type}-${i+1}`,
        name: `${assetType.split('-')[1].charAt(0).toUpperCase() + assetType.split('-')[1].slice(1)} ${tagPrefix} ${i+1}.jpg`,
        type: assetType,
        preview: '/placeholder.svg',
        size: `${(Math.random() * 3 + 1).toFixed(1)} MB`,
        dateCreated: `${Math.floor(Math.random() * 5) + 1} days ago`,
        tags: [tagPrefix, assetType.split('-')[1]]
      });
    }
  }
  
  return assets.slice(0, count);
};

export function ProductInputAssets({ product }: { product: ProductType }) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const assets = generateSampleAssets(product.inputAssets, 'input');
  const isNextStepReady = product.inputAssets > 0;
  
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedAssets.length === assets.length) {
      setSelectedAssets([]);
    } else {
      setSelectedAssets(assets.map(asset => asset.id));
    }
  };
  
  const isAllSelected = assets.length > 0 && selectedAssets.length === assets.length;
  const hasSelection = selectedAssets.length > 0;
  
  return (
    <div className="space-y-4">
      {/* Unified actions bar */}
      <div className="flex flex-wrap justify-between items-center gap-4 pb-2 border-b">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={handleSelectAll}
          >
            {isAllSelected ? <CheckSquare size={14} /> : <Square size={14} />}
            {isAllSelected ? "Deselect All" : "Select All"}
          </Button>
          
          {hasSelection && (
            <>
              <Badge variant="secondary">{selectedAssets.length} selected</Badge>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-destructive hover:text-destructive"
              >
                <Trash size={14} className="mr-1" />
                Delete
              </Button>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
          >
            <Filter size={14} className="mr-1" />
            Filter
          </Button>
          
          <Button 
            variant="default" 
            size="sm"
            className="gap-2"
          >
            <Upload size={14} />
            Upload Images
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-8 w-8">
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSelectAll}>
                {isAllSelected ? "Deselect All" : "Select All"}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download size={14} className="mr-2" /> Export Assets
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash size={14} className="mr-2" /> Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <EnhancedAssetGrid 
        assets={assets}
        title="Reference Images"
        emptyMessage="No reference images uploaded yet. Upload images to begin the creation process."
        onAssetClick={(asset) => handleAssetSelect(asset.id)}
        selectedAssets={selectedAssets}
      />
      
      {/* Contextual next step action */}
      {isNextStepReady && (
        <div className="bg-muted/30 rounded-lg p-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium">Ready for next step</h4>
              <p className="text-sm text-muted-foreground">You've uploaded {product.inputAssets} reference images. Generate raw AI images to proceed.</p>
            </div>
            <Button
              disabled={product.rawAIImages !== 0}
              className="gap-1.5"
            >
              <Plus size={16} />
              Generate First Raw AI Images
              <ArrowRight size={14} />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export function ProductRawAIImages({ product }: { product: ProductType }) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const assets = generateSampleAssets(product.rawAIImages, 'raw_ai_images');
  const isNextStepReady = product.rawAIImages > 0;
  
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedAssets.length === assets.length) {
      setSelectedAssets([]);
    } else {
      setSelectedAssets(assets.map(asset => asset.id));
    }
  };
  
  const isAllSelected = assets.length > 0 && selectedAssets.length === assets.length;
  const hasSelection = selectedAssets.length > 0;
  
  return (
    <div className="space-y-4">
      {/* Unified actions bar */}
      <div className="flex flex-wrap justify-between items-center gap-4 pb-2 border-b">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={handleSelectAll}
          >
            {isAllSelected ? <CheckSquare size={14} /> : <Square size={14} />}
            {isAllSelected ? "Deselect All" : "Select All"}
          </Button>
          
          {hasSelection && (
            <>
              <Badge variant="secondary">{selectedAssets.length} selected</Badge>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-destructive hover:text-destructive"
              >
                <Trash size={14} className="mr-1" />
                Delete
              </Button>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
          >
            <Filter size={14} className="mr-1" />
            Filter
          </Button>
          
          <Button 
            variant="outline" 
            size="sm"
          >
            <RefreshCcw size={14} className="mr-1" />
            Regenerate
          </Button>
          
          {hasSelection && (
            <Button 
              variant="default" 
              size="sm"
              className="gap-2"
            >
              <Zap size={14} />
              Upscale Selected
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-8 w-8">
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSelectAll}>
                {isAllSelected ? "Deselect All" : "Select All"}
              </DropdownMenuItem>
              <DropdownMenuItem disabled={!hasSelection}>
                <Zap size={14} className="mr-2" /> Upscale Selected
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download size={14} className="mr-2" /> Export Assets
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash size={14} className="mr-2" /> Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <EnhancedAssetGrid
        assets={assets}
        title="Raw AI Images"
        emptyMessage="No raw AI images have been generated yet."
        onAssetClick={(asset: AssetItem) => handleAssetSelect(asset.id)}
        selectedAssets={selectedAssets}
      />

      {/* Contextual next step action */}
      {isNextStepReady && (
        <div className="bg-muted/30 rounded-lg p-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium">Ready for upscaling</h4>
              <p className="text-sm text-muted-foreground">Select the raw AI images you want to upscale to proceed.</p>
            </div>
            <Button
              disabled={product.upscaled !== 0 || !hasSelection}
              className="gap-1.5"
            >
              <Zap size={16} />
              Upscale Selected Images
              <ArrowRight size={14} />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export function ProductUpscaled({ product }: { product: ProductType }) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const assets = generateSampleAssets(product.upscaled, 'upscaled');
  const isNextStepReady = product.upscaled > 0;
  
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedAssets.length === assets.length) {
      setSelectedAssets([]);
    } else {
      setSelectedAssets(assets.map(asset => asset.id));
    }
  };
  
  const isAllSelected = assets.length > 0 && selectedAssets.length === assets.length;
  const hasSelection = selectedAssets.length > 0;
  
  return (
    <div className="space-y-4">
      {/* Unified actions bar */}
      <div className="flex flex-wrap justify-between items-center gap-4 pb-2 border-b">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={handleSelectAll}
          >
            {isAllSelected ? <CheckSquare size={14} /> : <Square size={14} />}
            {isAllSelected ? "Deselect All" : "Select All"}
          </Button>
          
          {hasSelection && (
            <>
              <Badge variant="secondary">{selectedAssets.length} selected</Badge>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-destructive hover:text-destructive"
              >
                <Trash size={14} className="mr-1" />
                Delete
              </Button>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
          >
            <Filter size={14} className="mr-1" />
            Filter
          </Button>

          {hasSelection && (
            <Button 
              variant="default" 
              size="sm"
              className="gap-2"
            >
              <Check size={14} />
              Send to Retouching
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-8 w-8">
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSelectAll}>
                {isAllSelected ? "Deselect All" : "Select All"}
              </DropdownMenuItem>
              <DropdownMenuItem disabled={!hasSelection}>
                <Check size={14} className="mr-2" /> Send to Retouching
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download size={14} className="mr-2" /> Export Assets
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash size={14} className="mr-2" /> Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <EnhancedAssetGrid 
        assets={assets}
        title="Upscaled Images"
        emptyMessage="No images have been upscaled yet."
        onAssetClick={(asset) => handleAssetSelect(asset.id)}
        selectedAssets={selectedAssets}
      />
      
      {/* Contextual next step action */}
      {isNextStepReady && (
        <div className="bg-muted/30 rounded-lg p-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium">Ready for retouching</h4>
              <p className="text-sm text-muted-foreground">Your images are upscaled. Send them to retouching to finalize.</p>
            </div>
            <Button 
              disabled={product.retouched !== 0 || !hasSelection}
              className="gap-1.5"
            >
              <Check size={16} />
              Send to Retouching
              <ArrowRight size={14} />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export function ProductRetouched({ product }: { product: ProductType }) {
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);
  const assets = generateSampleAssets(product.retouched, 'retouched');
  const isComplete = product.retouched > 0;
  
  const handleAssetSelect = (assetId: string) => {
    setSelectedAssets(prev => 
      prev.includes(assetId) 
        ? prev.filter(id => id !== assetId)
        : [...prev, assetId]
    );
  };
  
  const handleSelectAll = () => {
    if (selectedAssets.length === assets.length) {
      setSelectedAssets([]);
    } else {
      setSelectedAssets(assets.map(asset => asset.id));
    }
  };
  
  const isAllSelected = assets.length > 0 && selectedAssets.length === assets.length;
  const hasSelection = selectedAssets.length > 0;
  
  return (
    <div className="space-y-4">
      {/* Unified actions bar */}
      <div className="flex flex-wrap justify-between items-center gap-4 pb-2 border-b">
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-2"
            onClick={handleSelectAll}
          >
            {isAllSelected ? <CheckSquare size={14} /> : <Square size={14} />}
            {isAllSelected ? "Deselect All" : "Select All"}
          </Button>
          
          {hasSelection && (
            <>
              <Badge variant="secondary">{selectedAssets.length} selected</Badge>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-destructive hover:text-destructive"
              >
                <Trash size={14} className="mr-1" />
                Delete
              </Button>
            </>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm"
          >
            <Filter size={14} className="mr-1" />
            Filter
          </Button>
          
          <Button 
            variant={isComplete ? "default" : "outline"}
            size="sm"
            className="gap-2"
            disabled={!isComplete}
          >
            <Check size={14} />
            Approve Images
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="h-8 w-8">
                <MoreHorizontal size={14} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleSelectAll}>
                {isAllSelected ? "Deselect All" : "Select All"}
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Download size={14} className="mr-2" /> Export Assets
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">
                <Trash size={14} className="mr-2" /> Delete Selected
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      
      <EnhancedAssetGrid 
        assets={assets}
        title="Final Images"
        emptyMessage="No images have been retouched yet."
        onAssetClick={(asset: AssetItem) => handleAssetSelect(asset.id)}
        selectedAssets={selectedAssets}
            />
            
            {/* Completion status */}
      {isComplete && (
        <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="font-medium text-green-600">Product workflow complete</h4>
              <p className="text-sm text-muted-foreground">All images have been retouched and are ready for approval.</p>
            </div>
            <Button 
              variant="default"
              className="gap-1.5 bg-green-500 hover:bg-green-600"
            >
              <Check size={16} />
              Approve All Images
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
