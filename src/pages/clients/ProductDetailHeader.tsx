import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { ArrowLeft, Pencil, Image as ImageIcon, Zap, Check } from 'lucide-react';
import { CardTitle } from "../../components/ui/card";
import { ProductType } from './ProductList';

interface ProductDetailHeaderProps {
  product: ProductType;
  onBackClick: () => void;
  currentStep: number;
  collectionName?: string;
}

export function ProductDetailHeader({ product, onBackClick, currentStep, collectionName }: ProductDetailHeaderProps) {
  const getStatusVariant = (status: string): "completed" | "in-progress" | "not-started" => {
    switch (status) {
      case "Completed": return "completed";
      case "In Progress": return "in-progress";
      case "Not Started": return "not-started";
      default: return "not-started";
    }
  };

  // Get contextual action button based on workflow stage
  const getContextualAction = () => {
    if (currentStep === 0) {
      return (
        <Button
          className="gap-2"
          onClick={() => {}}
        >
          <ImageIcon size={14} />
          Upload Reference Images
        </Button>
      );
    } else if (currentStep === 1) {
      return (
        <Button
          className="gap-2"
          onClick={() => {}}
        >
          <Pencil size={14} />
          Generate Drafts
        </Button>
      );
    } else if (currentStep === 2) {
      return (
        <Button
          className="gap-2"
          onClick={() => {}}
        >
          <Zap size={14} />
          Upscale Selected Images
        </Button>
      );
    } else if (currentStep === 3) {
      return (
        <Button
          className="gap-2"
          onClick={() => {}}
        >
          <Check size={14} />
          Retouch Images
        </Button>
      );
    } else {
      return (
        <Button variant="outline">
          View Final Images
        </Button>
      );
    }
  };

  return (
    <>
      {/* Back navigation and breadcrumbs */}
      <div className="flex items-center gap-2">
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-8 w-8 rounded-full" 
          onClick={onBackClick}
        >
          <ArrowLeft size={16} />
        </Button>
        <div className="text-sm text-muted-foreground">
          <span>Bubbleroom</span>
          {collectionName && (
            <>
              <span className="mx-2 text-muted-foreground/60">›</span>
              <span>{collectionName}</span>
            </>
          )}
          <span className="mx-2 text-muted-foreground/60">›</span>
          <span className="font-medium text-foreground">
            {product.name}
          </span>
          <span className="text-xs text-muted-foreground ml-2 opacity-70">({product.sku})</span>
        </div>
      </div>
      
      {/* Product header with title and actions */}
      <div className="flex justify-between items-center pt-1">
        <div className="flex items-center gap-3">
          <CardTitle className="text-xl">{product.name}</CardTitle>
          <Badge variant={getStatusVariant(product.status)}>
            {product.status}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          {/* Contextual action based on workflow step */}
          {getContextualAction()}
          <Button variant="outline" size="sm">
            <Pencil size={14} className="mr-1" />
            Edit
          </Button>
        </div>
      </div>
    </>
  );
}
