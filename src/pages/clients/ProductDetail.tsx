import { 
  Card,
  CardHeader,
  CardContent
} from "../../components/ui/card";
import { ProductType } from './ProductList';
import { ProductDetailHeader } from './ProductDetailHeader';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { useFilters } from '../../contexts/FilterContext';
import { FilteredAssetDisplay } from './FilteredAssetDisplay';

interface ProductDetailProps {
  product: ProductType;
  onBackClick: () => void;
  collectionName?: string;
}

export function ProductDetail({ product, onBackClick, collectionName }: ProductDetailProps) {  
  // Calculate which step we're currently on (0-indexed)
  const getCurrentStep = (): number => {
    if (product.inputAssets === 0) return 0;
    if (product.rawAIImages === 0) return 1;
    if (product.upscaled === 0) return 2;
    if (product.retouched === 0) return 3;
    return 4; // All complete
  };
  
  const currentStep = getCurrentStep();
  
  // Get contextual message based on current step
  const getContextualMessage = () => {
    if (currentStep === 0) return "Start by uploading images";
    if (currentStep === 1) return "Create raw AI images from uploaded images";
    if (currentStep === 2) return "Upscale selected raw AI images";
    if (currentStep === 3) return "Final step: Retouch images";
    return "All workflow steps completed";
  };
  
  // Get primary action button based on current step
  const getPrimaryActionButton = () => {
    const actions = [
      { step: 0, label: "Upload Images", disabled: product.inputAssets > 0 },
      { step: 1, label: "Generate Raw AI Images", disabled: product.rawAIImages > 0 || product.inputAssets === 0 },
      { step: 2, label: "Upscale Selected", disabled: product.upscaled > 0 || product.rawAIImages === 0 },
      { step: 3, label: "Retouch Images", disabled: product.retouched > 0 || product.upscaled === 0 }
    ];
    
    const currentAction = actions.find(a => a.step === currentStep);
    
    if (!currentAction) return null;
    
    return (
      <Button 
        size="sm"
        variant="default"
        className="ml-auto"
        disabled={currentAction.disabled}
      >
        <span className="ml-1">{currentAction.label}</span>
      </Button>
    );
  };

  return (
    <div className="h-full w-full">
      <Card className="shadow-md rounded-none h-full w-full">
        <CardHeader className="border-b pb-3 space-y-3">
          <ProductDetailHeader 
            product={product} 
            onBackClick={onBackClick} 
            currentStep={currentStep} 
            collectionName={collectionName}
          />
        </CardHeader>
        
        <CardContent className="pt-4 space-y-6">
          {/* Status and action section */}
          <div className="flex items-center justify-between rounded-md p-1">
            <div className="flex items-center gap-2">
              <Badge 
                variant={currentStep === 4 ? "success" : "outline"}
                className="text-xs font-normal"
              >
                {currentStep === 4 ? "Completed" : "In Progress"}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {getContextualMessage()}
              </span>
            </div>
            
            {getPrimaryActionButton()}
          </div>
          
          {/* Content area - all assets with filtering */}
          <div className="min-h-[400px]">
            <FilteredAssetDisplay product={product} />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ProductDetail;
