
import React from 'react';
import { cn } from '../../components/common/utils/utils';
import { Check, CircleDot, ArrowRight } from 'lucide-react';
import { WorkflowTabsList, WorkflowTabsTrigger } from "../../components/ui/tabs";
import { 
  <PERSON>lt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger 
} from "../../components/ui/tooltip";

interface WorkflowStep {
  id: string;
  name: string;
  icon: React.ReactNode;
  count: number;
}

interface WorkflowStepsListProps {
  steps: WorkflowStep[];
  currentStep: number;
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export function WorkflowStepsList({ 
  steps, 
  currentStep, 
  activeTab,
  onTabChange 
}: WorkflowStepsListProps) {
  // Get status for each workflow step
  const getTabStatus = (stepIndex: number) => {
    if (stepIndex < currentStep) return "completed";
    if (stepIndex === currentStep) return "current";
    return "upcoming";
  };

  // Get appropriate icon based on status
  const getStepIcon = (stepIndex: number, defaultIcon: React.ReactNode) => {
    const status = getTabStatus(stepIndex);
    if (status === "completed") return <Check size={12} className="text-primary/70" />;
    if (status === "current") return <CircleDot size={12} className="text-primary" />;
    return null;
  };

  // Calculate progress value
  const progressValue = Math.min(100, (currentStep / (steps.length - 1)) * 100);

  return (
    <div className="space-y-2 mb-3">
      {/* Minimal progress indicator */}
      <div className="relative h-0.5 bg-muted/40 rounded-full w-full mb-3">
        <div 
          className="absolute h-full bg-primary/40 rounded-full transition-all duration-300 ease-in-out" 
          style={{ width: `${progressValue}%` }}
        />
      </div>
      
      <WorkflowTabsList className="relative border-none bg-transparent p-0 gap-1">
        {steps.map((step, index) => {
          const status = getTabStatus(index);
          const isCompleted = status === "completed";
          const isCurrent = status === "current";
          const isActive = activeTab === step.id;
          const showCount = isCompleted || isCurrent; // Only show count for completed or current steps
          
          return (
            <TooltipProvider key={step.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center">
                    <WorkflowTabsTrigger 
                      value={step.id}
                      completed={isCompleted}
                      active={isActive}
                      icon={getStepIcon(index, step.icon)}
                      onClick={() => onTabChange(step.id)}
                      className={cn(
                        "z-10 border-none shadow-none bg-transparent px-1 py-1",
                        isActive ? "text-primary font-medium" : "",
                        !isActive && isCompleted ? "text-muted-foreground" : "",
                        !isActive && !isCompleted ? "text-muted-foreground/60" : ""
                      )}
                    >
                      <span className="flex items-center gap-1">
                        <span className={cn(
                          "text-xs",
                          isActive ? "font-medium" : ""
                        )}>
                          {step.name}
                        </span>
                        {showCount && step.count > 0 && (
                          <span className={cn(
                            "text-xs px-1 py-0.5 rounded-full",
                            isCompleted ? "bg-muted/20 text-muted-foreground" : 
                            isCurrent ? "bg-primary/10 text-primary" : "hidden"
                          )}>
                            {step.count}
                          </span>
                        )}
                      </span>
                    </WorkflowTabsTrigger>
                    
                    {index < steps.length - 1 && (
                      <ArrowRight 
                        size={10} 
                        className={cn(
                          "mx-1",
                          isCompleted ? "text-primary/40" : "text-muted-foreground/30"
                        )} 
                      />
                    )}
                  </div>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="text-xs">
                  {isCompleted ? (
                    <p className="flex items-center gap-1">
                      <Check size={12} className="text-primary" />
                      {step.count} {step.name.toLowerCase()} completed
                    </p>
                  ) : isCurrent ? (
                    <p className="flex items-center gap-1">
                      <CircleDot size={12} className="text-primary" />
                      Current step: {step.name}
                    </p>
                  ) : (
                    <p className="flex items-center gap-1">
                      <CircleDot size={12} />
                      Upcoming: {step.name}
                    </p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </WorkflowTabsList>
    </div>
  );
}
