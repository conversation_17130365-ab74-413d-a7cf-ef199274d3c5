import { useState, useCallback, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDropzone } from 'react-dropzone';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Progress } from '../components/ui/progress';
import { useToast } from '../components/common/hooks/use-toast';
import { ToastAction } from '../components/ui/toast';
import { useCollection } from '../components/common/hooks/useCollections';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '../components/common/utils/supabase';
import PageTitle from '../components/ui/PageTitle';
import { AssetMetadata } from '../components/common/types/assetTypes';
import ProductSelector from '../components/products/ProductSelector';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { X, Plus, FileImage } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { uploadImagesWithProcessing } from '../components/common/utils/imageProcessor';
import { useOrganizations } from '../contexts/OrganizationContext';
import { useUserRole } from '../contexts/UserRoleContext';
import { getWorkflowStageConfigs } from '../components/common/utils/workflowStageUtils';
import { useIsFreelancer } from '../components/common/hooks/useIsFreelancer';

// Define Tag and WorkflowStage types if they're not properly imported
interface Tag {
  id: string;
  name: string;
  category: string;
}

type WorkflowStage = 'upload' | 'raw_ai_images' | 'upscale' | 'retouch' | 'final';

interface UploadingFile {
  id: string;
  file: File;
  progress: number;
  compressionProgress: number;
  uploadProgress: number;
  error?: string;
  uploaded?: boolean;
  compressed?: boolean;
  isDuplicate?: boolean;
  thumbnailPath?: string;
  compressedPath?: string;
  originalPath?: string;
}

export function AssetUpload() {
  const { collectionId, orgId, clientId } = useParams<{ collectionId: string; orgId?: string; clientId?: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentOrganization } = useOrganizations();
  const organizationName = currentOrganization?.name || 'Organization';
  const [uploadStats, setUploadStats] = useState({
    total: 0,
    successful: 0,
    failed: 0,
    inProgress: 0
  });
  const uploadStatsRef = useRef(uploadStats);
  
  // Keep ref in sync with state
  useEffect(() => {
    uploadStatsRef.current = uploadStats;
  }, [uploadStats]);
  
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isCheckingDuplicates, setIsCheckingDuplicates] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [newTagName, setNewTagName] = useState('');
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [selectedWorkflowStage, setSelectedWorkflowStage] = useState<WorkflowStage>('upload');
  const queryClient = useQueryClient();
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);

  // Get user role for filtering
  const { userRole } = useUserRole();
  const isFreelancer = useIsFreelancer();

  // Get role-based workflow stage configurations, considering freelancer status
  const workflowStageConfigs = getWorkflowStageConfigs(userRole, isFreelancer);
  
  // Fetch collection data
  const { data: collection } = useCollection(collectionId);
  
  // Fetch available tags
  useEffect(() => {
    const fetchTags = async () => {
      if (!collectionId) return;
      
      setIsLoadingTags(true);
      try {
        const { data, error } = await supabase
          .from('tags')
          .select('*')
          .order('name');
        
        if (error) throw error;
        setAvailableTags(data || []);
      } catch (error) {
        console.error('Error fetching tags:', error);
        toast({
          title: 'Error',
          description: 'Failed to load tags',
        });
      } finally {
        setIsLoadingTags(false);
      }
    };
    
    fetchTags();
  }, [collectionId, toast]);
  
  // Function to refresh the assets data
  const refreshAssets = useCallback(() => {
    if (collectionId) {
      queryClient.invalidateQueries({
        queryKey: ['assets', null, collectionId]
      });
    }
  }, [collectionId, queryClient]);
  
  // Function to check for duplicate file names
  const checkForDuplicates = async (files: File[]) => {
    if (!files.length || !collectionId) return [];
    
    setIsCheckingDuplicates(true);
    
    try {
      // Extract file names
      const fileNames = files.map(file => file.name);
      
      // Check if any files with these names already exist in the collection
      const { data, error } = await supabase
        .from('assets')
        .select('file_name')
        .eq('collection_id', collectionId)
        .in('file_name', fileNames);
      
      if (error) {
        console.error('Error checking for duplicates:', error);
        return [];
      }
      
      // Return list of duplicate file names
      return data.map(item => item.file_name);
    } catch (error) {
      console.error('Error checking for duplicates:', error);
      return [];
    } finally {
      setIsCheckingDuplicates(false);
    }
  };
  
  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!collectionId || !acceptedFiles.length) return;
    
    // Reset upload success state when new files are dropped
    setUploadSuccess(false);
    
    // Check for duplicates
    const duplicates = await checkForDuplicates(acceptedFiles);
    
    // Process files, marking duplicates
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      file,
      progress: 0,
      compressionProgress: 0,
      uploadProgress: 0,
      isDuplicate: duplicates.includes(file.name),
      compressed: false,
    }));
    
    // Show warning if duplicates found
    if (duplicates.length > 0) {
      toast({
        title: 'Duplicate Files Detected',
        description: `${duplicates.length} file(s) with the same name already exist in this collection.`,
        variant: 'destructive'
      });
    }
    
    setUploadingFiles(prev => [...prev, ...newFiles]);
  }, [collectionId, toast, checkForDuplicates]);
  
  // Handle tag selection
  const toggleTag = (tag: Tag) => {
    setSelectedTags(prev => {
      const isSelected = prev.some(t => t.id === tag.id);
      if (isSelected) {
        return prev.filter(t => t.id !== tag.id);
      } else {
        return [...prev, tag];
      }
    });
  };
  
  // Create a new tag
  const createTag = async () => {
    if (!newTagName.trim()) return;
    
    try {
      const { data, error } = await supabase
        .from('tags')
        .insert({
          name: newTagName.trim(),
          category: 'collection'
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Add to available tags
      setAvailableTags(prev => [...prev, data]);
      
      // Select the new tag
      setSelectedTags(prev => [...prev, data]);
      
      // Clear input
      setNewTagName('');
      
      toast({
        title: 'Tag Created',
        description: `Successfully created tag "${data.name}"`,
      });
    } catch (error: any) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: `Failed to create tag: ${error.message}`,
      });
    }
  };
  
  // Get tag color based on category
  const getTagColor = (category: string) => {
    switch (category) {
      case 'view_type':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'workflow_stage':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'product_specific':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'custom':
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    },
    maxSize: 50485760, // 50MB
    disabled: isCheckingDuplicates || isUploading // Disable dropzone while checking duplicates or uploading
  });
  
  // Handle file upload with compression
  const handleUpload = async () => {
    if (uploadingFiles.length === 0 || !collectionId) return;
    
    // Reset upload success state when starting a new upload
    setUploadSuccess(false);
    
    // Ask for confirmation if duplicates exist
    const duplicateFiles = uploadingFiles.filter(f => f.isDuplicate);
    if (duplicateFiles.length > 0) {
      const confirmUpload = window.confirm(
        `${duplicateFiles.length} file(s) with the same name already exist in this collection. Do you want to continue and potentially overwrite them?`
      );
      
      if (!confirmUpload) return;
    }
    
    setIsCompressing(true);
    setIsUploading(true);
    
    try {
      // Prepare files for processing
      const filesToProcess = uploadingFiles
        .filter(f => !f.uploaded)
        .map(f => f.file);
      
      // Initialize upload stats
      setUploadStats({
        total: filesToProcess.length,
        successful: 0,
        failed: 0,
        inProgress: filesToProcess.length
      });
      
      // Generate asset IDs upfront
      const assetIds = filesToProcess.map(() => crypto.randomUUID());
      
      // Process and upload images with compression and thumbnail generation
      await uploadImagesWithProcessing(
        filesToProcess,
        collectionId,
        // Progress callback
        (fileId, type, progress) => {
          setUploadingFiles(prev => 
            prev.map(item => {
              // Find matching file by name since we don't have the internal fileId
              if (filesToProcess.findIndex((_, index) => index === parseInt(fileId)) === parseInt(fileId)) {
                if (type === 'compression') {
                  return { 
                    ...item, 
                    compressionProgress: progress,
                    // Set overall progress as weighted average
                    progress: (progress * 0.4) + (item.uploadProgress * 0.6)
                  };
                } else { // upload
                  return { 
                    ...item, 
                    uploadProgress: progress,
                    // Set overall progress as weighted average
                    progress: (item.compressionProgress * 0.4) + (progress * 0.6)
                  };
                }
              }
              return item;
            })
          );
        },
        // Completion callback
        async (fileId, result) => {
          // Find the matching file
          const fileIndex = filesToProcess.findIndex((_, index) => index === parseInt(fileId));
          if (fileIndex === -1) return;
          
          const file = uploadingFiles.find(item => 
            item.file.name === filesToProcess[fileIndex].name
          );
          if (!file) return;
          
          // Calculate compression ratio
          const compressionRatio = result.originalSize > 0 
            ? Math.round((result.compressedSize / result.originalSize) * 100) 
            : 100;
          
          // Create asset metadata with image processing information
          const imageProcessingMetadata: AssetMetadata = {
            imageProcessing: {
              originalSize: result.originalSize,
              compressedSize: result.compressedSize,
              thumbnailSize: result.thumbnailSize,
              compressionRatio: compressionRatio,
              originalPath: result.originalPath,
              compressedPath: result.compressedPath,
              thumbnailPath: result.thumbnailPath || undefined,
              processedAt: new Date().toISOString(),
            }
          };
          
          // Create asset record in database with the same ID used for storage
          const assetData = {
            id: result.assetId, // Use the same ID that was used for file storage
            collection_id: collectionId,
            file_name: file.file.name,
            file_path: result.compressedPath, // Use compressed path as main file path for backward compatibility
            original_path: result.originalPath, // Store original file path (FAS-73)
            compressed_path: result.compressedPath, // Store compressed file path (FAS-73)
            thumbnail_path: result.thumbnailPath, // Store thumbnail file path (FAS-73)
            file_type: file.file.type,
            file_size: result.compressedSize,
            workflow_stage: selectedWorkflowStage,
            product_id: selectedProductId || null,
            metadata: imageProcessingMetadata
          };
          
          // Insert asset record
          const { data: assetRecord, error } = await supabase
            .from('assets')
            .insert(assetData)
            .select()
            .single();
          
          if (error) {
            console.error(`Error inserting asset record:`, error);
            throw error;
          }
          
          // Add tags to the asset if any are selected
          if (selectedTags.length > 0 && assetRecord) {
            const assetTagsData = selectedTags.map(tag => ({
              asset_id: assetRecord.id,
              tag_id: tag.id
            }));
            
            const { error: tagError } = await supabase
              .from('asset_tags')
              .insert(assetTagsData);
            
            if (tagError) {
              console.error(`Error adding tags to asset:`, tagError);
            }
          }
          
          // Update upload stats
          setUploadStats(prev => ({
            ...prev,
            successful: prev.successful + 1,
            inProgress: prev.inProgress - 1
          }));
          
          // Mark file as uploaded in state
          setUploadingFiles(prev => 
            prev.map(item => {
              if (item.file.name === file.file.name) {
                return { 
                  ...item, 
                  uploaded: true, 
                  compressed: true,
                  progress: 100,
                  compressionProgress: 100,
                  uploadProgress: 100,
                  thumbnailPath: result.thumbnailPath || undefined,
                  compressedPath: result.compressedPath,
                  originalPath: result.originalPath
                };
              }
              return item;
            })
          );
          
          // Only show individual toasts if there are 3 or fewer files
          if (filesToProcess.length <= 3) {
            toast({
              title: 'File uploaded',
              description: `${file.file.name} uploaded successfully`,
              duration: 2000,
            });
          }
        },
        // Error callback
        (fileId, error) => {
          // Find the matching file
          const fileIndex = filesToProcess.findIndex((_, index) => index === parseInt(fileId));
          if (fileIndex === -1) return;
          
          const filename = filesToProcess[fileIndex].name;
          
          // Update upload stats
          setUploadStats(prev => ({
            ...prev,
            failed: prev.failed + 1,
            inProgress: prev.inProgress - 1
          }));
          
          // Update error state
          setUploadingFiles(prev => 
            prev.map((item) => {
              if (item.file.name === filename) {
                return { 
                  ...item, 
                  error: error.message || 'Processing failed'
                };
              }
              return item;
            })
          );
          
          // Only show individual error toasts if there are 3 or fewer files
          if (filesToProcess.length <= 3) {
            toast({
              title: 'Upload failed',
              description: `${filename}: ${error.message || 'Processing failed'}`,
              variant: 'destructive',
              duration: 3000,
            });
          }
        },
        assetIds
      );
      
      // Wait for all uploads to complete (either success or failure)
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          const stats = uploadStatsRef.current;
          if (stats.inProgress === 0) {
            clearInterval(checkInterval);
            resolve(true);
          }
        }, 100);
      });
      
      // Refresh the assets data
      refreshAssets();
      
      // Get final stats
      const finalStats = uploadStatsRef.current;
      
      // Show completion toast with correct counts
      if (finalStats.successful > 0) {
        toast({
          title: 'Upload Complete',
          description: `Successfully uploaded ${finalStats.successful} of ${finalStats.total} files${finalStats.failed > 0 ? ` (${finalStats.failed} failed)` : ''}`,
          action: (
            <ToastAction 
              altText="Return to Collection"
              onClick={() => {
                if (orgId) {
                  navigate(`/organizations/${orgId}/collections/${collectionId}`);
                } else if (clientId) {
                  navigate(`/organizations/${clientId}/collections/${collectionId}`);
                }
              }}
            >
              Return to Collection
            </ToastAction>
          ),
        });
      } else {
        toast({
          title: 'Upload Failed',
          description: `All ${finalStats.total} files failed to upload`,
          variant: 'destructive',
        });
      }
      
      // Set upload success state
      setUploadSuccess(finalStats.successful > 0);
      
    } catch (error: any) {
      console.error('Upload error:', error);
      
      toast({
        title: 'Upload Failed',
        description: error.message || 'There was an error uploading your files',
      });
    } finally {
      setIsUploading(false);
      setIsCompressing(false);
    }
  };
  
  // Remove a file from the upload list
  const handleRemoveFile = (id: string) => {
    setUploadingFiles(prev => prev.filter(file => file.id !== id));
  };
  
  // Clear all files
  const handleClearFiles = () => {
    setUploadingFiles([]);
    setUploadSuccess(false);
  };
  
  // Navigate back to collection
  const handleBack = () => {
    // Refresh the assets data before navigating
    refreshAssets();
    if (orgId && collectionId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}`);
    } else if (clientId && collectionId) {
      navigate(`/organizations/${clientId}/collections/${collectionId}`);
    } else {
      navigate(-1); // Fallback
    }
  };
  
  // Calculate overall compression progress
  const overallCompressionProgress = uploadingFiles.length 
    ? uploadingFiles.reduce((sum, file) => sum + file.compressionProgress, 0) / uploadingFiles.length
    : 0;
  
  // Calculate overall upload progress
  const overallUploadProgress = uploadingFiles.length 
    ? uploadingFiles.reduce((sum, file) => sum + file.uploadProgress, 0) / uploadingFiles.length
    : 0;
  
  // Calculate overall progress (weighted)
  const overallProgress = uploadingFiles.length 
    ? (overallCompressionProgress * 0.4) + (overallUploadProgress * 0.6)
    : 0;
  
  return (
    <div className="container mx-auto p-3 sm:p-6 max-w-5xl">
      <PageTitle
        title="Upload Assets"
        subtitle={collection ? `Adding to ${collection.name}` : 'Upload new assets'}
        breadcrumbs={[
          { label: 'Organizations', href: '/dashboard' },
          { label: organizationName, href: `/organizations/${orgId || clientId}` },
          { label: collection?.name || 'Collection', href: `/organizations/${orgId || clientId}/collections/${collectionId}` },
          { label: 'Upload', href: '#' }
        ]}
        action={
          <Button variant="outline" size="sm" className="h-8 sm:h-10" onClick={handleBack}>
            Back to Collection
          </Button>
        }
      />
      
      <div className="mt-4 sm:mt-6 space-y-4 sm:space-y-6">
        {/* Product Selection */}
        {collectionId && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-4">Associate with Product</h3>
              <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
                Optionally select a product to associate with all uploaded assets.
              </p>
              <ProductSelector
                collectionId={collectionId}
                selectedProductId={selectedProductId || undefined}
                onProductSelect={(productId) => setSelectedProductId(productId)}
              />
            </CardContent>
          </Card>
        )}
        
        {/* Workflow Stage Selection */}
        {collectionId && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-4">Workflow Stage</h3>
              <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
                Select the workflow stage for the uploaded assets.
              </p>
              <Select
                value={selectedWorkflowStage}
                onValueChange={(value) => setSelectedWorkflowStage(value as WorkflowStage)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select workflow stage" />
                </SelectTrigger>
                <SelectContent>
                  {workflowStageConfigs.map(config => (
                    <SelectItem key={config.id} value={config.id}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>
        )}
        
        {/* Tag Selection */}
        {collectionId && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-4">Add Tags</h3>
              <p className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
                Optionally add tags to all uploaded assets.
              </p>
              
              {/* Selected Tags */}
              <div className="mb-3 sm:mb-4">
                <Label className="mb-1 sm:mb-2 block text-sm">Selected Tags</Label>
                <div className="flex flex-wrap gap-1.5 sm:gap-2 min-h-10 p-2 border rounded-md max-h-32 overflow-y-auto">
                  {selectedTags.length === 0 ? (
                    <p className="text-xs sm:text-sm text-muted-foreground">No tags selected</p>
                  ) : (
                    selectedTags.map(tag => (
                      <Badge 
                        key={tag.id} 
                        variant="outline"
                        className={`${getTagColor(tag.category)} px-1.5 sm:px-2 py-0.5 sm:py-1 flex items-center gap-1 text-xs`}
                      >
                        {tag.name}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-3 w-3 sm:h-4 sm:w-4 p-0 ml-1 rounded-full"
                          onClick={() => toggleTag(tag)}
                        >
                          <X className="h-2 w-2 sm:h-3 sm:w-3" />
                        </Button>
                      </Badge>
                    ))
                  )}
                </div>
              </div>
              
              {/* Create New Tag */}
              <div className="mb-3 sm:mb-4">
                <Label className="mb-1 sm:mb-2 block text-sm">Create New Tag</Label>
                <div className="flex gap-2">
                  <Input
                    value={newTagName}
                    onChange={(e) => setNewTagName(e.target.value)}
                    placeholder="Enter new tag name"
                    className="flex-1 h-8 sm:h-10 text-xs sm:text-sm"
                  />
                  <Button 
                    onClick={createTag} 
                    disabled={!newTagName.trim()}
                    size="sm"
                    className="h-8 sm:h-10"
                  >
                    <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-0 sm:mr-1" />
                    <span className="hidden sm:inline">Create</span>
                  </Button>
                </div>
              </div>
              
              {/* Available Tags */}
              <div>
                <Label className="mb-1 sm:mb-2 block text-sm">Available Tags</Label>
                {isLoadingTags ? (
                  <p className="text-xs sm:text-sm text-muted-foreground">Loading tags...</p>
                ) : availableTags.length === 0 ? (
                  <p className="text-xs sm:text-sm text-muted-foreground">No tags available</p>
                ) : (
                  <div className="flex flex-wrap gap-1.5 sm:gap-2 max-h-36 overflow-y-auto p-2 border rounded-md">
                    {availableTags
                      .filter(tag => !selectedTags.some(t => t.id === tag.id))
                      .map(tag => (
                        <Badge 
                          key={tag.id} 
                          variant="outline"
                          className={`${getTagColor(tag.category)} px-1.5 sm:px-2 py-0.5 sm:py-1 cursor-pointer text-xs`}
                          onClick={() => toggleTag(tag)}
                        >
                          {tag.name}
                        </Badge>
                      ))
                    }
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* Dropzone */}
        <Card>
          <CardContent className="p-4 sm:p-6">
            <div 
              {...getRootProps()} 
              className={`border-2 border-dashed rounded-lg p-6 sm:p-12 text-center cursor-pointer transition-colors ${
                isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'
              } ${isCheckingDuplicates || isUploading ? 'opacity-70 cursor-wait' : ''}`}
            >
              <input {...getInputProps()} />
              <div className="space-y-2">
                <div className="text-3xl sm:text-4xl text-muted-foreground/50">
                  <FileImage className="h-12 w-12 mx-auto" />
                </div>
                <h3 className="text-base sm:text-lg font-medium">
                  {isDragActive ? 'Drop files here' : 
                   isCheckingDuplicates ? 'Checking for duplicates...' : 
                   'Drag & drop files here'}
                </h3>
                <p className="text-xs sm:text-sm text-muted-foreground">
                  or click to browse (max 50MB per file)
                </p>
                <p className="text-xs text-muted-foreground">
                  Images will be compressed and optimized automatically
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* File list */}
        {uploadingFiles.length > 0 && (
          <Card>
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 mb-4">
                <h3 className="text-base sm:text-lg font-medium">
                  {uploadingFiles.length} file{uploadingFiles.length !== 1 ? 's' : ''} selected
                </h3>
                <div className="flex gap-2 justify-end">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleClearFiles}
                    disabled={isUploading}
                    className="h-8 sm:h-9"
                  >
                    Clear All
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleUpload}
                    disabled={isUploading || uploadingFiles.length === 0}
                    className="h-8 sm:h-9"
                  >
                    {isCompressing && !isUploading ? 'Compressing...' : 
                     isUploading ? 'Uploading...' : 'Process & Upload'}
                  </Button>
                </div>
              </div>
              
              {/* Overall progress */}
              {(isCompressing || isUploading) && (
                <div className="space-y-2 mb-4">
                  {/* Compression progress */}
                  <div>
                    <div className="flex justify-between text-xs sm:text-sm mb-1">
                      <span>Compression Progress</span>
                      <span>{Math.round(overallCompressionProgress)}%</span>
                    </div>
                    <Progress value={overallCompressionProgress} className="h-2 bg-blue-100" indicatorClassName="bg-blue-500" />
                  </div>
                  
                  {/* Upload progress */}
                  <div>
                    <div className="flex justify-between text-xs sm:text-sm mb-1">
                      <span>Upload Progress</span>
                      <span>{Math.round(overallUploadProgress)}%</span>
                    </div>
                    <Progress value={overallUploadProgress} className="h-2 bg-green-100" indicatorClassName="bg-green-500" />
                  </div>
                  
                  {/* Overall progress */}
                  <div>
                    <div className="flex justify-between text-xs sm:text-sm mb-1">
                      <span>Overall Progress</span>
                      <span>{Math.round(overallProgress)}%</span>
                    </div>
                    <Progress value={overallProgress} className="h-2" />
                  </div>
                </div>
              )}
              
              {/* File list */}
              <div className="space-y-2 sm:space-y-3 mt-4">
                {uploadingFiles.map(file => (
                  <div 
                    key={file.id} 
                    className={`flex items-center justify-between p-2 sm:p-3 border rounded-md ${
                      file.isDuplicate ? 'border-amber-300 bg-amber-50' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-2 sm:space-x-3">
                      <div className="text-xl sm:text-2xl">
                        {file.file.type.startsWith('image/') ? '🖼️' : '📄'}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs sm:text-sm font-medium truncate">
                          {file.file.name}
                          {file.isDuplicate && (
                            <span className="ml-1 sm:ml-2 text-amber-600 text-[10px] sm:text-xs">
                              (Duplicate)
                            </span>
                          )}
                        </p>
                        <p className="text-[10px] sm:text-xs text-muted-foreground">
                          {(file.file.size / 1024 / 1024).toFixed(2)} MB
                          {file.compressed && file.compressedPath && (
                            <span className="ml-1 text-green-600">
                              → {((file.file.size * 0.6) / 1024 / 1024).toFixed(2)} MB (compressed)
                            </span>
                          )}
                        </p>
                        
                        {/* Show file processing status */}
                        {(isCompressing || isUploading) && !file.error && !file.uploaded && (
                          <div className="flex flex-col mt-1 space-y-1">
                            <div className="flex justify-between items-center w-full">
                              <span className="text-[10px] text-muted-foreground">
                                {file.compressionProgress < 100 ? 'Compressing' : 'Uploading'}
                              </span>
                              <span className="text-[10px] text-muted-foreground">
                                {Math.round(file.progress)}%
                              </span>
                            </div>
                            <Progress value={file.progress} className="h-1" />
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 sm:space-x-3">
                      {file.error ? (
                        <span className="text-[10px] sm:text-xs text-destructive max-w-[100px] sm:max-w-none truncate">{file.error}</span>
                      ) : file.uploaded ? (
                        <span className="text-[10px] sm:text-xs text-green-600">Uploaded</span>
                      ) : isUploading ? (
                        <div className="w-12 sm:w-20">
                          <Progress value={file.progress} className="h-1" />
                        </div>
                      ) : null}
                      
                      {!isUploading && (
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={() => handleRemoveFile(file.id)}
                          className="h-6 w-6 sm:h-8 sm:w-8 p-0"
                        >
                          ✕
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Upload summary */}
        {isUploading && uploadStats.total > 0 && (
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-2">Upload Progress</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Total Files:</span>
                  <span className="font-medium">{uploadStats.total}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Successful:</span>
                  <span className="font-medium text-green-600">{uploadStats.successful}</span>
                </div>
                {uploadStats.failed > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Failed:</span>
                    <span className="font-medium text-red-600">{uploadStats.failed}</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span>In Progress:</span>
                  <span className="font-medium text-blue-600">{uploadStats.inProgress}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Success message and button */}
        {uploadSuccess && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col items-center justify-center text-center space-y-3 sm:space-y-4">
                <div className="text-3xl sm:text-4xl">✅</div>
                <h3 className="text-base sm:text-lg font-medium text-green-800">Upload Complete</h3>
                <p className="text-xs sm:text-sm text-green-700">
                  Successfully uploaded {uploadStats.successful} of {uploadStats.total} files
                  {uploadStats.failed > 0 && ` (${uploadStats.failed} failed)`}
                </p>
                <Button 
                  onClick={() => {
              if (orgId) {
                navigate(`/organizations/${orgId}/collections/${collectionId}`);
              } else if (clientId) {
                navigate(`/organizations/${clientId}/collections/${collectionId}`);
              }
            }}
                  className="mt-1 sm:mt-2 text-xs sm:text-sm h-8 sm:h-10"
                  size="sm"
                >
                  Return to Collection
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default AssetUpload; 