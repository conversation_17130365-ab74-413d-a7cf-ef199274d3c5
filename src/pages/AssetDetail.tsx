import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, MessageSquare, Info, Download, Plus, Minus, RotateCcw, FileText, ChevronRight, Eye, Calendar, Pencil, Layers, Upload } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Skeleton } from '../components/ui/skeleton';
import { supabase, STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { getOptimizedAssetUrl } from '../components/common/utils/assetStorage';
import { useSupabase } from '../contexts/SupabaseContext';
import { useOrganizations } from '../contexts/OrganizationContext';
import { useToast } from '../components/common/hooks/use-toast';
import { CommentsTab } from '../components/asset-detail/AssetDetailPage/CommentsTab';
import {
  Tooltip,
  TooltipContent,
  <PERSON>lt<PERSON><PERSON>rovider,
  TooltipTrigger
} from '../components/ui/tooltip';
import { cn } from '../components/common/utils/utils';
import { getAssetUrl } from '../components/common/utils/utils';
import { Json } from '../integrations/supabase/types';
import { Asset as DatabaseAsset } from '../components/common/types/database.types';

interface Comment {
  id: string;
  asset_id: string;
  content: string;
  author: string;
  created_at: string;
  is_annotation: boolean;
  coordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null;
}

interface PendingAnnotation {
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
}

// Using DatabaseAsset type from database.types.ts

interface Product {
  id: string;
  name: string;
  sku?: string | null;
}

interface Collection {
  id: string;
  name: string;
}

const AssetDetail: React.FC = () => {
  const { orgId, clientId, collectionId, assetId } = useParams<{ orgId?: string; clientId?: string; collectionId: string; assetId: string }>();
  const [asset, setAsset] = useState<DatabaseAsset | null>(null);
  const [collection, setCollection] = useState<Collection | null>(null);
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [zoom, setZoom] = useState<number>(100);
  const [showSidebar, setShowSidebar] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<'comments' | 'data' | 'versions'>('comments');
  
  // Annotation-related state
  const [isAnnotationMode, setIsAnnotationMode] = useState<boolean>(false);
  const [showAnnotations, setShowAnnotations] = useState<boolean>(true);
  const [pendingAnnotation, setPendingAnnotation] = useState<PendingAnnotation | null>(null);
  const [annotations, setAnnotations] = useState<Comment[]>([]);
  
  const imageRef = useRef<HTMLImageElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  
  const { user } = useSupabase();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const { currentOrganization } = useOrganizations();
  const organizationName = currentOrganization?.id === orgId && currentOrganization?.name ? currentOrganization.name : (orgId ? 'Organization' : '');
  const collectionName = collection?.name || 'Collection';
  
  // Fetch asset data
  useEffect(() => {
    const fetchAsset = async () => {
      if (!assetId) return;
      
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('assets')
          .select('*')
          .eq('id', assetId)
          .single();
          
        if (error) {
          throw error;
        }
        
        console.log('Asset loaded from DB:', data);
        console.log('Asset metadata type:', typeof data.metadata);
        console.log('Asset metadata content:', data.metadata);
        
        // Try to fully analyze the metadata
        if (data.metadata) {
          console.log('Attempting to inspect metadata...');
          
          if (typeof data.metadata === 'string') {
            try {
              const parsed = JSON.parse(data.metadata);
              console.log('Parsed metadata from string:', parsed);
            } catch (e) {
              console.error('Failed to parse metadata string', e);
            }
          } else if (typeof data.metadata === 'object') {
            console.log('Metadata properties:', Object.keys(data.metadata));
            
            // Try to find thumbnail path in all possible structures
            const findThumbnailPaths = (obj: any, path = '') => {
              if (!obj) return;
              
              if (typeof obj === 'object') {
                Object.keys(obj).forEach(key => {
                  const currentPath = path ? `${path}.${key}` : key;
                  if (key.toLowerCase().includes('thumbnail') || 
                      (typeof obj[key] === 'string' && obj[key].toLowerCase().includes('thumbnail'))) {
                    console.log(`Found potential thumbnail at ${currentPath}:`, obj[key]);
                  }
                  findThumbnailPaths(obj[key], currentPath);
                });
              }
            };
            
            findThumbnailPaths(data.metadata);
          }
        }
        
        setAsset(data as DatabaseAsset);
        
        // Fetch collection data
        if (data.collection_id) {
          const { data: collectionData, error: collectionError } = await supabase
            .from('collections')
            .select('id, name')
            .eq('id', data.collection_id)
            .single();
            
          if (!collectionError && collectionData) {
            setCollection(collectionData);
          }
        }
        
        // Fetch product data if exists
        if (data.product_id) {
          const { data: productData, error: productError } = await supabase
            .from('products')
            .select('id, name, sku')
            .eq('id', data.product_id)
            .single();
            
          if (!productError && productData) {
            setProduct(productData);
          }
        }
      } catch (error) {
        console.error('Error fetching asset:', error);
        toast({
          title: 'Error',
          description: 'Failed to load asset details',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAsset();
  }, [assetId, toast]);
  
  // Fetch annotations
  useEffect(() => {
    const fetchAnnotations = async () => {
      if (!assetId) return;
      
      try {
        const { data, error } = await supabase
          .from('comments')
          .select('*')
          .eq('asset_id', assetId)
          .eq('is_annotation', true)
          .order('created_at', { ascending: true });
          
        if (error) throw error;
        
        console.log('Fetched annotations:', data);
        setAnnotations(data || []);
      } catch (error) {
        console.error('Error fetching annotations:', error);
      }
    };
    
    fetchAnnotations();
  }, [assetId, pendingAnnotation]); // Refetch when pendingAnnotation changes
  
  
  // Handle navigation back to collection
  const handleBack = () => {
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}`);
    } else if (clientId) {
      navigate(`/organizations/${clientId}/collections/${collectionId}`);
    }
  };
  
  
  // Format date in a readable format
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Format date in a short format
  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Calculate file size in KB or MB
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    }
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };
  
  // Generate download URL for the asset
  const getDownloadUrl = () => {
    if (!asset) return '';

    // Use the optimized asset URL function for download - now defaults to original (FAS-73)
    return getAssetUrl(asset, false, true); // true for download (original version)
  };

  // Handle download
  const handleDownload = async () => {
    if (!asset) return;

    try {
      // Get the public URL using optimized function - now defaults to original (FAS-73)
      const url = getAssetUrl(asset, false, true); // true for download (original version)
      if (!url) throw new Error("Could not get asset URL.");

      // Fetch the file as a blob
      const response = await fetch(url);
      const blob = await response.blob();

      // Create object URL from blob
      const objectUrl = window.URL.createObjectURL(blob);

      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = objectUrl;
      link.download = asset.file_name || 'download'; // Force download with original filename
      link.style.display = 'none';

      // Append to the document, click, and remove
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(objectUrl);

      toast({
        title: 'Download started',
        description: `Downloading ${asset.file_name} (best available quality)`,
      });

    } catch (error: any) {
      console.error("Download error:", error);
      toast({
        title: 'Error',
        description: `Failed to initiate download: ${error.message}`,
        variant: 'destructive',
      });
    }
  };
  
  // Get asset URL for preview
  const getAssetPreviewUrl = () => {
    if (!asset) return '';
    
    // Use the utility function for a consistent approach
    return getAssetUrl(asset, false); // false = use full image, not thumbnail for detail view
  };

  // Get image dimensions
  const getImageDimensions = () => {
    if (!asset) return '';
    
    // Try to get dimensions from metadata
    if (asset.metadata) {
      const metadata = asset.metadata as any;
      
      // Check imageProcessing metadata first
      if (metadata.imageProcessing?.width && metadata.imageProcessing?.height) {
        return `${metadata.imageProcessing.width} × ${metadata.imageProcessing.height}`;
      }
      
      // Then check direct metadata properties
      if (metadata.width && metadata.height) {
        return `${metadata.width} × ${metadata.height}`;
      }
      
      // Check if dimensions are in the file name
      if (asset.file_type.startsWith('image/')) {
        const resMatch = asset.file_name.match(/(\d+)x(\d+)/i);
        if (resMatch && resMatch.length >= 3) {
          return `${resMatch[1]} × ${resMatch[2]}`;
        }
      }
      
      // If we have an image ref, get dimensions from the DOM
      if (imageRef.current) {
        return `${imageRef.current.naturalWidth} × ${imageRef.current.naturalHeight}`;
      }
    }
    
    return 'Unknown';
  };
  
  // Get original file URL
  const getOriginalFileUrl = () => {
    if (!asset?.metadata) return null;
    
    const metadata = asset.metadata as any;
    if (metadata.imageProcessing?.originalPath) {
      // Use the original path directly without path cleaning
      // Use the optimized bucket for original files
      const originalPath = metadata.imageProcessing.originalPath;
      const { data } = supabase.storage
        .from(STORAGE_BUCKETS.MEDIA_ORIGINALS.name)
        .getPublicUrl(originalPath);
      return data.publicUrl;
    }
    return null;
  };
  
  // Get file format
  const getFileFormat = () => {
    if (!asset) return '';
    
    if (asset.file_type === 'image/jpeg' || asset.file_type === 'image/jpg') return 'JPG';
    if (asset.file_type === 'image/png') return 'PNG';
    if (asset.file_type === 'image/gif') return 'GIF';
    if (asset.file_type === 'image/webp') return 'WEBP';
    
    // Otherwise, try to extract from filename
    const extension = asset.file_name.split('.').pop()?.toUpperCase();
    return extension || 'Unknown';
  };

  // Get workflow stage display name
  const getWorkflowStageDisplay = () => {
    if (!asset) return '';
    
    // Check client asset type in metadata first
    if (asset.metadata) {
      const metadata = asset.metadata as any;
      
      if (metadata.client_asset_type === 'input') return 'Product Image';
      if (metadata.client_asset_type === 'brief') return 'Reference/Inspiration';
    }
    
    // Fall back to workflow_stage from DB
    switch (asset.workflow_stage) {
      case 'upload': return 'Input Asset';
      case 'raw_ai_images': return 'Raw AI Images';
      case 'selected': return 'Selected';
      case 'upscale': return 'Upscaled';
      case 'retouch': return 'Retouched';
      case 'final': return 'Final Asset';
      default: return asset.workflow_stage;
    }
  };

  // Zoom controls
  const zoomIn = () => setZoom(prev => Math.min(prev + 25, 200));
  const zoomOut = () => setZoom(prev => Math.max(prev - 25, 25));
  const resetZoom = () => setZoom(100);
  
  // Annotation-related functions
  const toggleAnnotationMode = () => {
    setIsAnnotationMode(!isAnnotationMode);
    if (isAnnotationMode) {
      // Reset annotation state when exiting mode
      setPendingAnnotation(null);
    }
  };
  
  const handleImageClick = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!isAnnotationMode || !imageRef.current) return;
    
    const rect = imageRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    const pointSize = 0.02; // Small size for point annotation
    
    // Create a pending annotation and switch to comments tab
    setPendingAnnotation({
      x,
      y,
      width: pointSize,
      height: pointSize
    });
    
    // Switch to comments tab and open sidebar
    setActiveTab('comments');
    setShowSidebar(true);
  };
  
  const cancelAnnotation = () => {
    setPendingAnnotation(null);
    // Trigger re-fetch of annotations
    setPendingAnnotation((prev) => prev === null ? null : null);
  };
  
  if (isLoading) {
    return (
      <div className="p-6">
        <Skeleton className="h-12 w-1/3 mb-4" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }
  
  if (!asset) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-bold mb-2">Asset Not Found</h2>
        <p className="text-muted-foreground mb-4">
          The asset you're looking for doesn't exist or you don't have permission to view it.
        </p>
        <Button onClick={handleBack}>Back to Collection</Button>
      </div>
    );
  }

  const isImage = asset.file_type.startsWith('image/');
  
  return (
    <div className="flex flex-col min-h-0 h-full bg-gray-50">
      {/* Compact Header */}
      <div className="bg-white border-b border-gray-200 px-4 md:px-6 py-3 shrink-0">
        <div className="flex items-center justify-between">
          {/* Left: Back button and breadcrumbs */}
          <div className="flex items-center gap-2 md:gap-3 min-w-0 flex-1">
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 shrink-0" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center text-sm text-gray-600 min-w-0">
              <span className="hover:text-gray-900 cursor-pointer hidden md:block" onClick={() => {
                if (orgId) {
                  navigate(`/organizations/${orgId}/collections/${collectionId}`);
                } else if (clientId) {
                  navigate(`/organizations/${clientId}/collections/${collectionId}`);
                }
              }}>
                {collectionName}
              </span>
              <ChevronRight className="h-3 w-3 mx-2 hidden md:block" />
              <span className="font-medium text-gray-900 truncate">
                {asset?.file_name || 'Asset'}
              </span>
            </div>
          </div>
          
          {/* Right: Action buttons */}
          <div className="flex items-center gap-2 shrink-0">
            <Button variant="outline" size="sm" onClick={handleDownload} className="h-8">
              <Download className="h-3 w-3 md:mr-2" />
              <span className="hidden md:inline">Download</span>
            </Button>
            <Button 
              variant={showSidebar ? "default" : "outline"}
              size="sm" 
              onClick={() => setShowSidebar(!showSidebar)}
              className="h-8"
            >
              <Info className="h-3 w-3 md:mr-2" />
              <span className="hidden md:inline">Details</span>
            </Button>
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="flex flex-1 min-h-0 overflow-hidden">
        {/* Asset viewer */}
        <div className={`relative bg-white flex items-center justify-center overflow-hidden ${showSidebar ? 'flex-1' : 'w-full'}`}>
          {/* Annotation mode indicator */}
          {isAnnotationMode && (
            <div className="absolute top-6 left-6 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg z-20">
              <div className="flex items-center gap-2 text-sm font-medium">
                <Pencil className="h-4 w-4" />
                <span>Click on the image to place annotation</span>
              </div>
            </div>
          )}
          
          {isImage ? (
            <div 
              ref={imageContainerRef}
              className="relative flex items-center justify-center w-full h-full p-4 md:p-6"
              style={{ 
                height: 'calc(100vh - 120px)', // Account for main nav + header
                maxHeight: 'calc(100vh - 120px)'
              }}
            >
              {/* Image container with subtle border */}
              <div 
                className="relative bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden max-w-full max-h-full"
                style={{ 
                  transform: `scale(${zoom / 100})`,
                  transformOrigin: 'center center'
                }}
              >
                <img
                  ref={imageRef}
                  src={getAssetPreviewUrl()}
                  alt={asset.file_name}
                  className={cn(
                    "block object-contain",
                    isAnnotationMode && "cursor-crosshair"
                  )}
                  style={{
                    maxWidth: `calc((100vw - ${showSidebar ? '384px' : '0px'}) - 2rem)`,
                    maxHeight: 'calc(100vh - 180px)', // Leave room for controls and padding
                    width: 'auto',
                    height: 'auto'
                  }}
                  onClick={handleImageClick}
                />
                
                
                {/* Display existing annotations */}
                {showAnnotations && annotations.map((annotation) => {
                  if (!annotation.coordinates) return null;
                  
                  const coords = typeof annotation.coordinates === 'string' 
                    ? JSON.parse(annotation.coordinates) 
                    : annotation.coordinates;
                  
                  return (
                    <div
                      key={annotation.id}
                      className="absolute cursor-pointer group"
                      style={{
                        left: `${coords.x * 100}%`,
                        top: `${coords.y * 100}%`,
                        transform: `translate(-50%, -50%)`,
                        transformOrigin: 'center center'
                      }}
                      onClick={() => {
                        setActiveTab('comments');
                        setShowSidebar(true);
                      }}
                    >
                      <div 
                        className="h-6 w-6 rounded-full bg-blue-600 border-2 border-white shadow-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
                        style={{
                          transform: `scale(${100 / zoom})`, // Counter-scale only the annotation dot
                          transformOrigin: 'center center'
                        }}
                      >
                        <div className="h-2 w-2 rounded-full bg-white" />
                      </div>
                      
                      {/* Hover tooltip with fixed size */}
                      <div 
                        className="absolute bottom-full left-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity z-20 pointer-events-none"
                        style={{
                          transform: `translateX(-50%) scale(${100 / zoom})`, // Counter-scale the tooltip
                          transformOrigin: 'center bottom'
                        }}
                      >
                        <div className="bg-gray-900 text-white px-3 py-2 rounded-lg shadow-lg text-sm whitespace-nowrap max-w-xs">
                          <p className="font-medium">{annotation.author}</p>
                          <p className="text-gray-300 text-xs">{annotation.content}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
                
                {/* Show visual feedback for pending annotation */}
                {pendingAnnotation && (
                  <div
                    className="absolute pointer-events-none"
                    style={{
                      left: `${pendingAnnotation.x * 100}%`,
                      top: `${pendingAnnotation.y * 100}%`,
                      transform: `translate(-50%, -50%)`,
                      transformOrigin: 'center center'
                    }}
                  >
                    <div 
                      className="h-6 w-6 rounded-full bg-blue-600/80 border-2 border-white shadow-lg animate-pulse"
                      style={{
                        transform: `scale(${100 / zoom})`, // Counter-scale the pending annotation
                        transformOrigin: 'center center'
                      }}
                    />
                  </div>
                )}
              </div>
              
          {/* Enhanced floating controls */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center gap-4">
            {/* Zoom controls */}
            <div className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-xl shadow-lg p-2 flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={zoomOut} className="h-9 w-9 p-0 hover:bg-gray-100">
                      <Minus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <div className="flex items-center px-3 text-sm font-semibold text-gray-700 min-w-[60px] justify-center">
                {zoom}%
              </div>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={zoomIn} className="h-9 w-9 p-0 hover:bg-gray-100">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <div className="w-px h-5 bg-gray-300 mx-1" />
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="sm" onClick={resetZoom} className="h-9 w-9 p-0 hover:bg-gray-100">
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Reset Zoom</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            {/* Annotation controls */}
            <div className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-xl shadow-lg p-2 flex items-center gap-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant={isAnnotationMode ? "default" : "ghost"} 
                      size="sm" 
                      onClick={toggleAnnotationMode} 
                      className={cn(
                        "h-9 w-9 p-0 transition-all", 
                        isAnnotationMode 
                          ? "bg-blue-600 hover:bg-blue-700 text-white shadow-md" 
                          : "hover:bg-gray-100"
                      )}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isAnnotationMode ? "Exit Annotation Mode" : "Add Annotation"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant={showAnnotations ? "default" : "ghost"} 
                      size="sm" 
                      onClick={() => setShowAnnotations(!showAnnotations)}
                      className={cn(
                        "h-9 w-9 p-0 transition-all", 
                        showAnnotations 
                          ? "bg-gray-900 hover:bg-gray-800 text-white shadow-md" 
                          : "hover:bg-gray-100"
                      )}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {showAnnotations ? "Hide Annotations" : "Show Annotations"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
            </div>
          ) : (
            <div className="text-center p-8">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <FileText className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Preview not available</h3>
                <p className="text-gray-500 mb-6">This file type cannot be previewed in the browser.</p>
                <Button onClick={handleDownload} className="bg-blue-600 hover:bg-blue-700">
                  <Download className="mr-2 h-4 w-4" />
                  Download File
                </Button>
              </div>
            </div>
          )}
        </div>
        
        {/* Mobile backdrop */}
        {showSidebar && (
          <div 
            className="fixed inset-0 bg-black/20 z-20 md:hidden" 
            onClick={() => setShowSidebar(false)}
          />
        )}
        
        {/* Modern sidebar */}
        {showSidebar && (
          <div className="w-full md:w-96 bg-white border-l border-gray-200 flex flex-col overflow-hidden md:relative fixed inset-y-0 right-0 z-30 md:z-auto shadow-xl md:shadow-none">
            {/* Sidebar header */}
            <div className="border-b border-gray-200 bg-gray-50">
              <div className="flex">
                <button 
                  className={`flex items-center px-6 py-4 relative transition-colors ${
                    activeTab === 'comments' 
                      ? 'bg-white border-b-2 border-blue-600 text-blue-600 font-medium' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('comments')}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Comments
                </button>
                <button 
                  className={`flex items-center px-6 py-4 relative transition-colors ${
                    activeTab === 'data' 
                      ? 'bg-white border-b-2 border-blue-600 text-blue-600 font-medium' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('data')}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Details
                </button>
                <button 
                  className={`flex items-center px-6 py-4 relative transition-colors ${
                    activeTab === 'versions' 
                      ? 'bg-white border-b-2 border-blue-600 text-blue-600 font-medium' 
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  onClick={() => setActiveTab('versions')}
                >
                  <Layers className="h-4 w-4 mr-2" />
                  Versions
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-hidden">
              {/* Comments Tab */}
              {activeTab === 'comments' && (
                <CommentsTab
                  asset={asset}
                  pendingAnnotation={pendingAnnotation}
                  onPendingAnnotationClear={cancelAnnotation}
                  activeAnnotationId={undefined}
                  onCommentSelect={undefined}
                />
              )}
              
              {/* Details Tab */}
              {activeTab === 'data' && (
                <div className="h-full overflow-y-auto">
                  {/* Asset header */}
                  <div className="p-6 border-b border-gray-100">
                    <h2 className="font-semibold text-lg text-gray-900 mb-2 truncate">{asset.file_name}</h2>
                    <p className="text-sm text-gray-500 flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      Uploaded {formatShortDate(asset.created_at)}
                    </p>
                  </div>
                  
                  {/* Quick stats */}
                  <div className="p-6 border-b border-gray-100">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Format</p>
                        <p className="font-semibold text-gray-900">{getFileFormat()}</p>
                      </div>
                      
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Resolution</p>
                        <p className="font-semibold text-gray-900">{getImageDimensions()}</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Detailed information */}
                  <div className="p-6 space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="text-sm font-medium text-gray-500">Asset Type</span>
                      <span className="text-sm font-semibold text-gray-900">{getWorkflowStageDisplay()}</span>
                    </div>
                    
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="text-sm font-medium text-gray-500">File Size</span>
                      <span className="text-sm font-semibold text-gray-900">{formatFileSize(asset.file_size)}</span>
                    </div>
                    
                    {product && (
                      <div className="flex justify-between items-center py-3 border-b border-gray-100">
                        <span className="text-sm font-medium text-gray-500">Product</span>
                        <div className="text-right">
                          <div className="text-sm font-semibold text-gray-900 truncate max-w-[180px]">
                            {product.name}
                          </div>
                          {product.sku && (
                            <div className="text-xs text-gray-500">SKU: {product.sku}</div>
                          )}
                        </div>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center py-3 border-b border-gray-100">
                      <span className="text-sm font-medium text-gray-500">Last Updated</span>
                      <span className="text-sm font-semibold text-gray-900">{formatShortDate(asset.updated_at)}</span>
                    </div>
                    
                    {/* Original File Link */}
                    {getOriginalFileUrl() && (
                      <div className="flex justify-between items-center py-3">
                        <span className="text-sm font-medium text-gray-500">Original File</span>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="h-8"
                          onClick={async () => {
                            const url = getOriginalFileUrl();
                            if (!url) {
                              toast({
                                title: 'Error',
                                description: 'Original file not available',
                                variant: 'destructive',
                              });
                              return;
                            }
                            
                            try {
                              toast({
                                title: 'Download starting...',
                                description: 'Preparing original file',
                              });
                              
                              const response = await fetch(url);
                              if (!response.ok) throw new Error('Failed to fetch file');
                              
                              const blob = await response.blob();
                              const blobUrl = URL.createObjectURL(blob);
                              const link = document.createElement('a');
                              link.href = blobUrl;
                              link.download = `${asset.file_name?.split('.')[0]}_original.${asset.file_name?.split('.').pop()}`;
                              
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                              
                              setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
                              
                              toast({
                                title: 'Download complete',
                                description: 'Original file downloaded',
                              });
                            } catch (error: any) {
                              toast({
                                title: 'Error',
                                description: `Failed to download: ${error.message}`,
                                variant: 'destructive',
                              });
                            }
                          }}
                        >
                          Download
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Versions Tab */}
              {activeTab === 'versions' && (
                <div className="h-full overflow-y-auto p-6">
                  <div className="space-y-4">
                    {/* Version History Header */}
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-2">Version History</h3>
                      <p className="text-sm text-gray-600">Track all iterations of this asset</p>
                    </div>

                    {/* Mock Version Data */}
                    <div className="space-y-3">
                      {/* Current Version */}
                      <div className="border rounded-lg p-4 bg-blue-50 border-blue-200">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-semibold text-sm">v3.0</span>
                              <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded">Current</span>
                            </div>
                            <p className="text-sm text-gray-600">Final retouched version with color correction</p>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleDownload()}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>John Designer</span>
                          <span>•</span>
                          <span>{formatDate(new Date().toISOString())}</span>
                          <span>•</span>
                          <span>{formatFileSize(asset.file_size || 2500000)}</span>
                        </div>
                      </div>

                      {/* Previous Versions */}
                      <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-semibold text-sm">v2.1</span>
                            </div>
                            <p className="text-sm text-gray-600">Adjusted lighting and removed background elements</p>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              toast({
                                title: 'Download v2.1',
                                description: 'Downloading previous version',
                              });
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Sarah Editor</span>
                          <span>•</span>
                          <span>{formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString())}</span>
                          <span>•</span>
                          <span>{formatFileSize((asset.file_size || 2500000) * 0.95)}</span>
                        </div>
                      </div>

                      <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-semibold text-sm">v1.0</span>
                              <span className="px-2 py-1 bg-gray-200 text-gray-600 text-xs rounded">Original</span>
                            </div>
                            <p className="text-sm text-gray-600">Original upload from photoshoot</p>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              toast({
                                title: 'Download v1.0',
                                description: 'Downloading original version',
                              });
                            }}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Mike Photographer</span>
                          <span>•</span>
                          <span>{formatDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())}</span>
                          <span>•</span>
                          <span>{formatFileSize((asset.file_size || 2500000) * 1.4)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Upload New Version */}
                    <div className="mt-6 pt-6 border-t">
                      <Button variant="outline" className="w-full">
                        <Upload className="h-4 w-4 mr-2" />
                        Upload New Version
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssetDetail; 