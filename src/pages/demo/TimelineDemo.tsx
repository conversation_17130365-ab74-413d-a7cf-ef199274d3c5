import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MessageSquare, 
  Eye, 
  Download, 
  ChevronRight, 
  GitBranch, 
  Layers,
  Link2,
  Clock,
  User,
  Filter,
  ZoomIn,
  ZoomOut,
  Maximize2
} from 'lucide-react';
import { cn } from '@/components/common/utils/utils';

// Mock data for demonstration
const mockTimelineData = {
  lineageId: 'lineage-uuid-1',
  collection: 'Summer 2024',
  product: 'T-Shirt Basic',
  size: 'M',
  viewType: 'Front',
  stages: [
    {
      name: 'Upload',
      stageKey: 'upload',
      stageType: 'manual',
      description: 'Original product photos',
      assets: [
        {
          id: 'asset-001',
          thumbnailUrl: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=200&h=200&fit=crop',
          fileName: 'tshirt_white_front_raw.jpg',
          stage: 'upload',
          commentCount: 3,
          viewCount: 12,
          createdBy: 'Sarah Designer',
          createdAt: '2024-01-10 10:30',
          metadata: { 
            size: 'M', 
            color: 'White',
            resolution: '4000x6000',
            fileSize: '8.2 MB'
          }
        },
        {
          id: 'asset-002',
          thumbnailUrl: 'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?w=200&h=200&fit=crop',
          fileName: 'tshirt_white_front_alt.jpg',
          stage: 'upload',
          commentCount: 1,
          viewCount: 8,
          createdBy: 'Sarah Designer',
          createdAt: '2024-01-10 10:32',
          metadata: { 
            size: 'M', 
            color: 'White',
            resolution: '4000x6000',
            fileSize: '7.9 MB'
          }
        }
      ]
    },
    {
      name: 'AI Generated',
      stageKey: 'raw_ai_images',
      stageType: 'ai-generated',
      description: 'AI-enhanced variations',
      assets: [
        {
          id: 'asset-003',
          thumbnailUrl: 'https://images.unsplash.com/photo-1622445275463-afa2ab738c34?w=200&h=200&fit=crop',
          fileName: 'tshirt_ai_gen_001.jpg',
          stage: 'raw_ai_images',
          commentCount: 5,
          viewCount: 24,
          createdBy: 'AI System',
          createdAt: '2024-01-11 14:20',
          parentId: 'asset-001',
          metadata: { 
            model: 'fashion-enhance-v3',
            prompt: 'studio lighting, clean background',
            resolution: '2048x3072',
            fileSize: '3.1 MB'
          }
        },
        {
          id: 'asset-004',
          thumbnailUrl: 'https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=200&h=200&fit=crop',
          fileName: 'tshirt_ai_gen_002.jpg',
          stage: 'raw_ai_images',
          commentCount: 2,
          viewCount: 18,
          createdBy: 'AI System',
          createdAt: '2024-01-11 14:22',
          parentId: 'asset-001',
          metadata: { 
            model: 'fashion-enhance-v3',
            variation: 'warm tone',
            resolution: '2048x3072',
            fileSize: '3.3 MB'
          }
        },
        {
          id: 'asset-005',
          thumbnailUrl: 'https://images.unsplash.com/photo-1627225793904-a2f900a6e4cf?w=200&h=200&fit=crop',
          fileName: 'tshirt_ai_gen_003.jpg',
          stage: 'raw_ai_images',
          commentCount: 0,
          viewCount: 15,
          createdBy: 'AI System',
          createdAt: '2024-01-11 14:23',
          parentId: 'asset-002',
          metadata: { 
            model: 'fashion-enhance-v3',
            variation: 'high contrast',
            resolution: '2048x3072',
            fileSize: '3.0 MB'
          }
        }
      ]
    },
    {
      name: 'Selected',
      stageKey: 'selected',
      stageType: 'manual',
      description: 'Approved for refinement',
      assets: [
        {
          id: 'asset-006',
          thumbnailUrl: 'https://images.unsplash.com/photo-1618354691373-d851c5c3a990?w=200&h=200&fit=crop',
          fileName: 'tshirt_selected_001.jpg',
          stage: 'selected',
          commentCount: 2,
          viewCount: 30,
          isSelected: true,
          createdBy: 'Mike Reviewer',
          createdAt: '2024-01-12 09:15',
          parentId: 'asset-004',
          metadata: { 
            approvedBy: 'Creative Director',
            notes: 'Best lighting and color balance'
          }
        }
      ]
    },
    {
      name: 'Refined',
      stageKey: 'refined',
      stageType: 'manual',
      description: 'Enhanced and color corrected',
      assets: [
        {
          id: 'asset-007',
          thumbnailUrl: 'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=200&h=200&fit=crop',
          fileName: 'tshirt_refined_001.jpg',
          stage: 'refined',
          commentCount: 4,
          viewCount: 35,
          createdBy: 'Tom Retoucher',
          createdAt: '2024-01-13 16:45',
          parentId: 'asset-006',
          metadata: { 
            adjustments: 'Color grading, shadow enhancement',
            software: 'Photoshop 2024',
            resolution: '4096x6144',
            fileSize: '12.4 MB'
          }
        }
      ]
    },
    {
      name: 'Final',
      stageKey: 'final',
      stageType: 'final',
      description: 'Ready for production',
      assets: [
        {
          id: 'asset-008',
          thumbnailUrl: 'https://images.unsplash.com/photo-1576566588028-4147f3842f27?w=200&h=200&fit=crop',
          fileName: 'tshirt_final_4k.jpg',
          stage: 'final',
          commentCount: 1,
          viewCount: 50,
          isFinal: true,
          createdBy: 'System Export',
          createdAt: '2024-01-14 11:00',
          parentId: 'asset-007',
          metadata: { 
            exportFormat: 'JPEG 100%',
            colorSpace: 'sRGB',
            resolution: '4096x6144',
            fileSize: '18.2 MB',
            deliverables: ['web', 'print', 'social']
          }
        }
      ]
    }
  ],
  comments: [
    {
      id: 'comment-1',
      assetId: 'asset-001',
      stage: 'Upload',
      author: 'Sarah Designer',
      content: 'Initial upload - color looks a bit dark in this lighting',
      timestamp: '2 days ago',
      status: 'resolved'
    },
    {
      id: 'comment-2',
      assetId: 'asset-003',
      stage: 'AI Generated',
      author: 'Mike Reviewer',
      content: 'Much better lighting! The AI really enhanced the fabric texture',
      timestamp: '1 day ago',
      status: 'open'
    },
    {
      id: 'comment-3',
      assetId: 'asset-007',
      stage: 'Refined',
      author: 'Creative Director',
      content: 'Perfect! The color correction is exactly what we needed. Ready for final export.',
      timestamp: '4 hours ago',
      status: 'resolved'
    }
  ]
};

// Additional mock data for edge cases
const mockEdgeCases = {
  multiInput: {
    name: 'Multi-Input → 360° View',
    description: 'Multiple angles combined into single output',
    inputs: ['Front View', 'Side View', 'Back View'],
    output: '360° Interactive View'
  },
  branching: {
    name: 'Workflow Branching',
    description: 'One asset processed through multiple paths',
    branches: [
      { name: 'Print Path', stages: ['Upscale', 'Print Prep'] },
      { name: 'Web Path', stages: ['Compress', 'Web Optimize'] }
    ]
  }
};

export default function TimelineDemo() {
  const [viewMode, setViewMode] = useState<'expanded' | 'compact' | 'comparison'>('expanded');
  const [selectedStage, setSelectedStage] = useState<string | null>(null);
  const [showDataFlow, setShowDataFlow] = useState(false);

  return (
    <div className="container mx-auto py-8 space-y-6 max-w-7xl">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold mb-2">Asset Timeline Demo</h1>
        <p className="text-muted-foreground">
          Interactive demonstration of the asset timeline feature showing how assets progress through workflow stages
        </p>
      </div>

      {/* Context Bar */}
      <Card>
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-muted-foreground">
              <span className="font-medium text-foreground">Collection: {mockTimelineData.collection}</span>
              <ChevronRight className="w-4 h-4 mx-2" />
              <span className="font-medium text-foreground">Product: {mockTimelineData.product}</span>
              <ChevronRight className="w-4 h-4 mx-2" />
              <span className="font-medium text-foreground">Size: {mockTimelineData.size}</span>
              <ChevronRight className="w-4 h-4 mx-2" />
              <span className="font-medium text-foreground">View: {mockTimelineData.viewType}</span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDataFlow(!showDataFlow)}
              >
                <GitBranch className="w-4 h-4 mr-2" />
                {showDataFlow ? 'Hide' : 'Show'} Data Flow
              </Button>
              <Select value={viewMode} onValueChange={(v: any) => setViewMode(v)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="expanded">Expanded</SelectItem>
                  <SelectItem value="compact">Compact</SelectItem>
                  <SelectItem value="comparison">Comparison</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">
            <Layers className="w-3 h-3 inline mr-1" />
            Lineage ID: {mockTimelineData.lineageId} • 8 total assets across 5 workflow stages
          </div>
        </CardContent>
      </Card>

      {/* Main Timeline View */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="w-5 h-5" />
            Asset Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-4 overflow-x-auto pb-4">
            {mockTimelineData.stages.map((stage, stageIndex) => (
              <React.Fragment key={stage.stageKey}>
                {/* Stage Column */}
                <div className="flex-shrink-0 w-64">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-semibold text-sm">{stage.name}</h3>
                      <Badge variant={stage.stageType === 'ai-generated' ? 'secondary' : 'outline'}>
                        {stage.stageType === 'ai-generated' ? 'AI' : 'Manual'}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">{stage.description}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {stage.assets.length} asset{stage.assets.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  
                  <div className={cn(
                    "space-y-3",
                    viewMode === 'compact' && stage.assets.length > 1 && "space-y-1"
                  )}>
                    {stage.assets.map((asset, index) => (
                      <AssetCard 
                        key={asset.id} 
                        asset={asset} 
                        stageType={stage.stageType}
                        viewMode={viewMode}
                        isHidden={viewMode === 'compact' && index > 0}
                      />
                    ))}
                    {viewMode === 'compact' && stage.assets.length > 1 && (
                      <button className="text-xs text-muted-foreground hover:text-foreground">
                        +{stage.assets.length - 1} more
                      </button>
                    )}
                  </div>
                </div>

                {/* Connection Arrow */}
                {stageIndex < mockTimelineData.stages.length - 1 && (
                  <div className="flex-shrink-0 flex items-center justify-center w-16 h-full pt-12">
                    <ConnectionArrow 
                      from={stage.stageType} 
                      to={mockTimelineData.stages[stageIndex + 1].stageType}
                      fromCount={stage.assets.length}
                      toCount={mockTimelineData.stages[stageIndex + 1].assets.length}
                    />
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Data Flow Explanation */}
          {showDataFlow && (
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
              <h4 className="text-sm font-semibold mb-3 flex items-center gap-2">
                <GitBranch className="w-4 h-4" />
                How Timeline Data Relationships Work
              </h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h5 className="text-xs font-medium">Database Structure:</h5>
                  <div className="text-xs space-y-1 font-mono bg-background p-2 rounded">
                    <p>• lineage_id: {mockTimelineData.lineageId}</p>
                    <p>• asset_id: asset-001 → asset-008</p>
                    <p>• parent_asset_id: tracks derivation</p>
                    <p>• variant_group_id: groups similar assets</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <h5 className="text-xs font-medium">Key Relationships:</h5>
                  <div className="text-xs space-y-1 text-muted-foreground">
                    <p>• Each asset has a unique ID in the lineage</p>
                    <p>• Parent IDs track which asset it came from</p>
                    <p>• Comments aggregate across all lineage assets</p>
                    <p>• Filters work on variant group properties</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Comments Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              Timeline Comments
            </span>
            <Badge variant="outline">
              {mockTimelineData.comments.length} total
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockTimelineData.comments.map((comment) => (
              <CommentThread key={comment.id} comment={comment} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Edge Cases Demonstration */}
      <Tabs defaultValue="multi-input" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="multi-input">Multi-Input</TabsTrigger>
          <TabsTrigger value="branching">Branching</TabsTrigger>
          <TabsTrigger value="linking">Retroactive Linking</TabsTrigger>
        </TabsList>
        
        <TabsContent value="multi-input">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edge Case: Multiple Inputs → Single Output</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center gap-4 p-6">
                {mockEdgeCases.multiInput.inputs.map((input, i) => (
                  <React.Fragment key={input}>
                    <div className="text-center">
                      <div className="w-20 h-20 bg-muted rounded flex items-center justify-center mb-2">
                        <span className="text-xs">{input}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">Input {i + 1}</Badge>
                    </div>
                    {i < mockEdgeCases.multiInput.inputs.length - 1 && (
                      <span className="text-muted-foreground">+</span>
                    )}
                  </React.Fragment>
                ))}
                <ChevronRight className="w-6 h-6 text-muted-foreground mx-4" />
                <div className="text-center">
                  <div className="w-24 h-24 bg-primary/10 rounded flex items-center justify-center mb-2">
                    <span className="text-xs font-medium">{mockEdgeCases.multiInput.output}</span>
                  </div>
                  <Badge className="text-xs">AI Generated</Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground text-center">
                Multiple parent_asset_ids stored in lineage_metadata
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="branching">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edge Case: Workflow Branching</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center gap-8 p-6">
                <div className="text-center">
                  <div className="w-24 h-24 bg-muted rounded flex items-center justify-center">
                    <span className="text-sm">Selected Asset</span>
                  </div>
                </div>
                <div className="space-y-4">
                  {mockEdgeCases.branching.branches.map((branch) => (
                    <div key={branch.name} className="flex items-center gap-2">
                      <ChevronRight className="w-4 h-4" />
                      <Badge variant="outline">{branch.name}</Badge>
                      <span className="text-xs text-muted-foreground">
                        → {branch.stages.join(' → ')}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              <p className="text-sm text-muted-foreground text-center">
                Same lineage_id maintained, different processing paths tracked
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="linking">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edge Case: Retroactive Asset Linking</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-6 bg-muted/50 rounded-lg">
                <h4 className="font-medium mb-4">Link Related Assets</h4>
                <div className="space-y-2 mb-4">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm">White T-Shirt Front (M) - Uploaded Jan 1</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" defaultChecked />
                    <span className="text-sm">White T-Shirt Back (M) - Uploaded Jan 3</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className="text-sm">White T-Shirt Front (L) - Uploaded Jan 1</span>
                  </label>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">Cancel</Button>
                  <Button size="sm">
                    <Link2 className="w-4 h-4 mr-2" />
                    Create Timeline Group
                  </Button>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mt-4">
                Creates new lineage_id linking previously separate assets
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Asset Card Component
function AssetCard({ asset, stageType, viewMode, isHidden }: any) {
  if (isHidden) return null;
  
  return (
    <Card className={cn(
      "cursor-pointer transition-all hover:shadow-md",
      asset.isSelected && "ring-2 ring-primary",
      asset.isFinal && "border-green-500",
      viewMode === 'compact' && "p-2"
    )}>
      <div className={cn(
        "aspect-square bg-muted rounded mb-3 relative overflow-hidden",
        viewMode === 'compact' && "mb-2"
      )}>
        <img 
          src={asset.thumbnailUrl} 
          alt={asset.fileName}
          className="w-full h-full object-cover"
        />
        {asset.isFinal && (
          <Badge className="absolute top-2 right-2 bg-green-500">
            Final
          </Badge>
        )}
        {asset.parentId && viewMode !== 'compact' && (
          <div className="absolute bottom-2 left-2 text-xs bg-background/80 px-2 py-1 rounded">
            From: #{asset.parentId.split('-')[1]}
          </div>
        )}
      </div>
      
      {viewMode !== 'compact' && (
        <>
          <div className="px-3 pb-3">
            <p className="text-xs font-medium truncate mb-1">{asset.fileName}</p>
            <div className="flex items-center justify-between text-xs mb-2">
              <div className="flex items-center gap-3">
                <span className="flex items-center gap-1">
                  <MessageSquare className="w-3 h-3" />
                  {asset.commentCount}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="w-3 h-3" />
                  {asset.viewCount}
                </span>
              </div>
              <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                <Download className="w-3 h-3" />
              </Button>
            </div>
            
            <div className="space-y-1 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <User className="w-3 h-3" />
                {asset.createdBy}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {asset.createdAt}
              </div>
            </div>
            
            {asset.metadata && (
              <div className="mt-2 pt-2 border-t">
                <p className="text-xs font-medium mb-1">Metadata:</p>
                <div className="text-xs space-y-0.5 text-muted-foreground">
                  {Object.entries(asset.metadata).slice(0, 3).map(([key, value]) => (
                    <div key={key}>
                      {key}: {value as string}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </Card>
  );
}

// Connection Arrow Component
function ConnectionArrow({ from, to, fromCount, toCount }: any) {
  const isAiGeneration = to === 'ai-generated';
  const isBranching = fromCount < toCount;
  
  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <svg width="60" height="100" className="overflow-visible">
        {isAiGeneration ? (
          <g>
            <line 
              x1="0" y1="50" x2="60" y2="50" 
              stroke="currentColor" 
              strokeWidth="2" 
              strokeDasharray="5,5"
              className="text-blue-500"
            />
            {isBranching && (
              <>
                <line x1="30" y1="50" x2="60" y2="30" stroke="currentColor" strokeWidth="1.5" strokeDasharray="5,5" className="text-blue-500" />
                <line x1="30" y1="50" x2="60" y2="70" stroke="currentColor" strokeWidth="1.5" strokeDasharray="5,5" className="text-blue-500" />
              </>
            )}
          </g>
        ) : (
          <line x1="0" y1="50" x2="60" y2="50" stroke="currentColor" strokeWidth="2" />
        )}
        
        <polygon 
          points="60,50 55,45 55,55" 
          fill="currentColor"
          className={isAiGeneration ? "text-blue-500" : ""}
        />
      </svg>
      
      {isAiGeneration && (
        <span className="absolute -top-6 text-xs text-blue-500 whitespace-nowrap">
          AI Generated
        </span>
      )}
      {isBranching && !isAiGeneration && (
        <span className="absolute -bottom-6 text-xs text-muted-foreground">
          1:{toCount}
        </span>
      )}
    </div>
  );
}

// Comment Thread Component
function CommentThread({ comment }: any) {
  return (
    <div className={cn(
      "flex gap-3 p-3 rounded-lg",
      comment.status === 'resolved' ? "bg-green-50 dark:bg-green-950/20" : "bg-muted/50"
    )}>
      <MessageSquare className={cn(
        "w-5 h-5 mt-0.5",
        comment.status === 'resolved' ? "text-green-600" : "text-muted-foreground"
      )} />
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium text-sm">{comment.author}</span>
          <Badge variant="outline" className="text-xs">
            {comment.stage}
          </Badge>
          <span className="text-xs text-muted-foreground">{comment.timestamp}</span>
          {comment.status === 'resolved' && (
            <Badge className="text-xs bg-green-500">Resolved</Badge>
          )}
        </div>
        <p className="text-sm">{comment.content}</p>
        <p className="text-xs text-muted-foreground mt-1">
          on asset #{comment.assetId.split('-')[1]}
        </p>
      </div>
    </div>
  );
}