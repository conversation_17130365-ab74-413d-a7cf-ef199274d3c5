import React, { useState, useMemo } from 'react';
import { useUserRole } from '../contexts/UserRoleContext';
import { Navigate, useNavigate } from 'react-router-dom';
import { PageTitle } from '../components/ui/PageTitle';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Label } from '../components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { Plus, Package, ImageIcon, X } from 'lucide-react';
import { ModelLibraryTable, Model } from '../components/model-library/ModelLibraryTable';
import { CreateModelDialog } from '../components/model-library/CreateModelDialog';
import { EditModelDialog } from '../components/model-library/EditModelDialog';
import { OrganizationSelector } from '../components/model-library/OrganizationSelector';
import { useModelLibrary, useUpdateModel, useDeleteModel } from '../hooks/useModelLibrary';

export default function PlatformAdminModelLibrary() {
  const { isPlatformUser, isLoadingRole } = useUserRole();
  const navigate = useNavigate();
  const { models, isLoading, refetch } = useModelLibrary();
  const updateModelMutation = useUpdateModel();
  const deleteModelMutation = useDeleteModel();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingModel, setEditingModel] = useState<Model | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Filter state
  const [scopeFilter, setScopeFilter] = useState<string>('all');
  const [orgFilter, setOrgFilter] = useState<string>('all');

  // Filter models based on selected filters
  const filteredModels = useMemo(() => {
    if (!models) return [];

    return models.filter(model => {
      // Scope filter
      if (scopeFilter !== 'all') {
        const modelScope = model.scope_type || 'global';
        if (modelScope !== scopeFilter) return false;
      }

      // Organization filter
      if (orgFilter !== 'all') {
        if (scopeFilter === 'organization' && model.organization_id !== orgFilter) return false;
        if (scopeFilter === 'collection' && model.organization_id !== orgFilter) return false;
      }

      return true;
    });
  }, [models, scopeFilter, orgFilter]);

  const clearFilters = () => {
    setScopeFilter('all');
    setOrgFilter('all');
  };

  // Redirect if not platform admin
  if (!isLoadingRole && !isPlatformUser) {
    return <Navigate to="/dashboard" replace />;
  }

  if (isLoadingRole || isLoading) {
    return (
      <div className="container mx-auto p-6 text-center">
        <p>Loading...</p>
      </div>
    );
  }

  const handleRowClick = (model: Model) => {
    // Navigate to model detail page
    navigate(`/admin/model-library/${model.id}`);
  };

  const handleEdit = (model: Model) => {
    setEditingModel(model);
    setIsEditDialogOpen(true);
  };

  const handleDelete = async (model: Model) => {
    if (!confirm(`Are you sure you want to delete "${model.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteModelMutation.mutateAsync(model.id);
      refetch();
    } catch (error) {
      console.error('Error deleting model:', error);
    }
  };

  const handleToggleActive = async (model: Model) => {
    try {
      await updateModelMutation.mutateAsync({
        id: model.id,
        is_active: !model.is_active
      });
      refetch();
    } catch (error) {
      console.error('Error toggling model status:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="flex justify-between items-start mb-6">
        <PageTitle 
          title="Model Library" 
          subtitle="Manage model images for the AI image generator"
        />
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Model
        </Button>
      </div>

      {/* Statistics Card */}
      <Card className="mb-6">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Models</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{models?.length || 0}</div>
          <p className="text-xs text-muted-foreground">
            {models?.filter(m => m.is_active).length || 0} active models
          </p>
        </CardContent>
      </Card>

      {/* Filter Models */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Models</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="scope-filter">Scope</Label>
              <Select value={scopeFilter} onValueChange={setScopeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All scopes" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Scopes</SelectItem>
                  <SelectItem value="global">Global Only</SelectItem>
                  <SelectItem value="organization">Organization Only</SelectItem>
                  <SelectItem value="collection">Collection Only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1">
              <Label htmlFor="org-filter">Organization</Label>
              <OrganizationSelector
                value={orgFilter}
                onChange={setOrgFilter}
                allowAll={true}
                placeholder="All organizations"
              />
            </div>

            <Button variant="outline" onClick={clearFilters}>
              <X className="w-4 h-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Models Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Models</CardTitle>
          <CardDescription>
            Click on a model to manage its images for different angles
            {filteredModels.length !== models?.length && (
              <span className="ml-2 text-sm">
                (Showing {filteredModels.length} of {models?.length || 0} models)
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ModelLibraryTable
            models={filteredModels}
            isLoading={isLoading}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleActive={handleToggleActive}
          />
          <div className="mt-4 text-sm text-muted-foreground">
            <p>Click on a model to manage its images</p>
          </div>
        </CardContent>
      </Card>

      {/* Angle Reference */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Angle Reference</CardTitle>
          <CardDescription>
            Each model should have images for all these angles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">Half-body Angles</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Half-body Front</li>
                <li>Half-body Back</li>
                <li>Half-body 3/4 Left</li>
                <li>Half-body 3/4 Right</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Full-height Angles</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>Full-height Front</li>
                <li>Full-height Back</li>
                <li>Full-height Side Left</li>
                <li>Full-height Side Right</li>
                <li>Full-height 3/4 Left</li>
                <li>Full-height 3/4 Right</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <CreateModelDialog 
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={() => {
          setIsCreateDialogOpen(false);
          refetch();
        }}
      />

      {editingModel && (
        <EditModelDialog
          model={editingModel}
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setEditingModel(null);
          }}
          onSuccess={() => {
            setIsEditDialogOpen(false);
            setEditingModel(null);
            refetch();
          }}
        />
      )}
    </div>
  );
}