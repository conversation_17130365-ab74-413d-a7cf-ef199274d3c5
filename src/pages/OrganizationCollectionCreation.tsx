import React, { useState, FC, useEffect } from 'react';
import { useNavigate, Link, useParams } from 'react-router-dom';
import { ArrowLeft, ArrowRight, Check, X, Upload, Info, FileText, Image as ImageIcon } from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '../components/ui/radio-group';
import { Separator } from '../components/ui/separator';
import { useToast } from '../components/ui/use-toast';
import { useSupabase } from '../contexts/SupabaseContext';
import { Progress } from '../components/ui/progress';
import { InfoTooltip } from '../components/ui/info-tooltip';
import { StepIndicator } from '../components/ui/step-indicator';
import { supabase, sanitizeFileName, STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { Checkbox } from '../components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../components/ui/alert-dialog';
import { useOrganizations } from '../contexts/OrganizationContext';
import { EnhancedDropzone } from '../components/ui/EnhancedDropzone';
import { AngleSelector } from '../components/ui/AngleSelector';

// Define step types
type CollectionType = 'product' | 'campaign' | '';
type ModelChoice = 'custom' | 'library' | '';
type ImageCount = '5' | '6' | '7' | '8';
type ProductsPerImage = '1' | '2' | '3';

interface CampaignDetails {
  purpose: string;
  lookAndFeel: string;
  poses: string;
  targetGroup: string;
}

interface CustomModel {
  size: string;
  gender: string;
  ethnicity: string;
  age: string;
  referenceImages: {
    hasReferences: 'yes_now' | 'yes_later' | 'no';
    face?: File[];
    body?: File[];
  };
}

interface ImageOutputTypes {
  fullHeightFront: number;
  fullHeightBack: number;
  fullHeightSide: number;
  halfBody: number;
  portrait: number;
}

interface ImageOutputSettings {
  types: ImageOutputTypes;
  deadline: string;
  pixelSize: {
    width: number;
    height: number;
  };
  format: 'WebP' | 'JPG' | 'TIFF' | 'PNG' | 'Other';
  customFormat?: string;
  maxResolution: number;
}

interface ReferenceSection {
  existingShoot: File[];
  productTraining: File[];
  stylingElements: File[];
}

// New interfaces for Phase 2 enhanced form structure
interface TargetGarmentImages {
  files: File[];
}

interface SecondaryGarment {
  files: File[];
  description: string;
}

interface ShoesSection {
  files: File[];
}

interface StylingDetails {
  images: File[];
  textDescriptions: string[];
}

interface PhotoshootStyle {
  exampleImages: File[];
  backgroundColor: string;
  dropShadow: string;
  lightDirection: string;
}

interface ModelProfile {
  faceExamples: File[];
  bodyTypeImages: File[];
  ageRange: string;
  overallAppearance: string;
}

interface RequiredAngles {
  selectedAngles: string[];
  halfBodyFront: boolean;
  halfBodyBack: boolean;
  halfBodySide: boolean;
  customAngles: string[];
}

interface FormatSelection {
  portrait: boolean;
  landscape: boolean;
}

interface EnhancedBriefSections {
  targetGarmentImages: TargetGarmentImages;
  secondaryGarment: SecondaryGarment;
  shoes: ShoesSection;
  stylingDetails: StylingDetails;
  photoshootStyle: PhotoshootStyle;
  modelProfile: ModelProfile;
  requiredAngles: RequiredAngles;
  formatSelection: FormatSelection;
  videoUploads: File[];
}

// Form data type
interface CollectionFormData {
  name: string;
  description: string;
  coverImage: File | null;
  collectionType: CollectionType;
  modelChoice: ModelChoice;
  customModels?: {
    count: number;
    models: CustomModel[];
  };
  productCount?: number;
  imageCount?: ImageCount;
  productsPerImage?: ProductsPerImage;
  // New AI image calculation fields
  aiImagesPerProduct?: number;
  totalAiImages?: number;
  campaignDetails?: CampaignDetails;
  briefFiles?: File[] | null;
  references: ReferenceSection;
  imageOutput: ImageOutputSettings;
  // New enhanced brief sections
  enhancedBrief?: EnhancedBriefSections;
}

// Add a function to get initial form data from localStorage or use default values
const getInitialFormData = (): CollectionFormData => {
  const savedData = localStorage.getItem('collectionFormData');
  const savedStep = localStorage.getItem('collectionCurrentStep');
  
  if (savedData) {
    const parsedData = JSON.parse(savedData);
    // Reconstruct File objects for files that were stored
    if (parsedData.coverImage) {
      parsedData.coverImage = null; // Files can't be stored in localStorage, reset to null
    }
    if (parsedData.briefFiles) {
      parsedData.briefFiles = null;
    }
    if (parsedData.references) {
      parsedData.references = {
        existingShoot: [],
        productTraining: [],
        stylingElements: []
      };
    }
    // Ensure imageOutput exists with default values if not present
    if (!parsedData.imageOutput) {
      parsedData.imageOutput = {
        types: {
          fullHeightFront: 0,
          fullHeightBack: 0,
          fullHeightSide: 0,
          halfBody: 0,
          portrait: 0
        },
        deadline: '',
        pixelSize: {
          width: 1200,
          height: 1600
        },
        format: 'JPG',
        maxResolution: 10
      };
    }

    // Ensure enhancedBrief is properly initialized
    if (!parsedData.enhancedBrief) {
      parsedData.enhancedBrief = {
        targetGarmentImages: { files: [] },
        secondaryGarment: { files: [], description: '' },
        shoes: { files: [] },
        stylingDetails: { images: [], textDescriptions: [] },
        photoshootStyle: {
          exampleImages: [],
          backgroundColor: '',
          dropShadow: '',
          lightDirection: ''
        },
        modelProfile: {
          faceExamples: [],
          bodyTypeImages: [],
          ageRange: '',
          overallAppearance: ''
        },
        requiredAngles: {
          selectedAngles: [],
          halfBodyFront: false,
          halfBodyBack: false,
          halfBodySide: false,
          customAngles: []
        },
        formatSelection: { portrait: false, landscape: false },
        videoUploads: []
      };
    } else {
      // Ensure all nested objects exist
      if (!parsedData.enhancedBrief.requiredAngles) {
        parsedData.enhancedBrief.requiredAngles = {
          selectedAngles: [],
          halfBodyFront: false,
          halfBodyBack: false,
          halfBodySide: false,
          customAngles: []
        };
      }
      if (!parsedData.enhancedBrief.requiredAngles.selectedAngles) {
        parsedData.enhancedBrief.requiredAngles.selectedAngles = [];
      }
      if (!parsedData.enhancedBrief.formatSelection) {
        parsedData.enhancedBrief.formatSelection = { portrait: false, landscape: false };
      }
      if (!parsedData.enhancedBrief.targetGarmentImages) {
        parsedData.enhancedBrief.targetGarmentImages = { files: [] };
      }
      if (!parsedData.enhancedBrief.secondaryGarment) {
        parsedData.enhancedBrief.secondaryGarment = { files: [], description: '' };
      }
      if (!parsedData.enhancedBrief.shoes) {
        parsedData.enhancedBrief.shoes = { files: [] };
      }
    }
    return parsedData;
  }

  return {
    name: '',
    description: '',
    coverImage: null,
    collectionType: '',
    modelChoice: '',
    briefFiles: null,
    references: {
      existingShoot: [],
      productTraining: [],
      stylingElements: []
    },
    imageOutput: {
      types: {
        fullHeightFront: 0,
        fullHeightBack: 0,
        fullHeightSide: 0,
        halfBody: 0,
        portrait: 0
      },
      deadline: '',
      pixelSize: {
        width: 1200,
        height: 1600
      },
      format: 'JPG',
      maxResolution: 10
    },
    enhancedBrief: {
      targetGarmentImages: { files: [] },
      secondaryGarment: { files: [], description: '' },
      shoes: { files: [] },
      stylingDetails: { images: [], textDescriptions: [] },
      photoshootStyle: {
        exampleImages: [],
        backgroundColor: '',
        dropShadow: '',
        lightDirection: ''
      },
      modelProfile: {
        faceExamples: [],
        bodyTypeImages: [],
        ageRange: '',
        overallAppearance: ''
      },
      requiredAngles: {
        selectedAngles: [],
        halfBodyFront: false,
        halfBodyBack: false,
        halfBodySide: false,
        customAngles: []
      },
      formatSelection: { portrait: false, landscape: false },
      videoUploads: []
    }
  };
};

const OrganizationCollectionCreation: FC = () => {
  // Initialize state with data from localStorage if available
  const [currentStep, setCurrentStep] = useState(() => {
    const savedStep = localStorage.getItem('collectionCurrentStep');
    return savedStep ? parseInt(savedStep) : 1;
  });
  
  const { orgId, clientId } = useParams<{ orgId?: string; clientId?: string }>();
  const { currentOrganization, switchOrganization } = useOrganizations();
  const [completedSteps, setCompletedSteps] = useState<number[]>(() => {
    const savedCompletedSteps = localStorage.getItem('collectionCompletedSteps');
    return savedCompletedSteps ? JSON.parse(savedCompletedSteps) : [];
  });

  const [formData, setFormData] = useState<CollectionFormData>(getInitialFormData());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showExitDialog, setShowExitDialog] = useState(false);
  const [exitPath, setExitPath] = useState("");
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useSupabase();
  
  const totalSteps = 5;
  
  const steps = [
    { title: 'Basic Info' },
    { title: 'Collection Type' },
    { title: 'Models & References' },
    { title: 'Details' },
    { title: 'Review' }
  ];
  
  // Save form data to localStorage whenever it changes
  useEffect(() => {
    const formDataToSave = { ...formData };
    // Remove File objects before storing
    if (formDataToSave.coverImage) {
      formDataToSave.coverImage = null;
    }
    if (formDataToSave.briefFiles) {
      formDataToSave.briefFiles = null;
    }
    if (formDataToSave.references) {
      formDataToSave.references = {
        existingShoot: [],
        productTraining: [],
        stylingElements: []
      };
    }
    localStorage.setItem('collectionFormData', JSON.stringify(formDataToSave));
  }, [formData]);

  // Save current step to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('collectionCurrentStep', currentStep.toString());
  }, [currentStep]);

  // Save completed steps to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('collectionCompletedSteps', JSON.stringify(completedSteps));
  }, [completedSteps]);

  // Clear localStorage when form is submitted successfully
  const clearStoredFormData = () => {
    localStorage.removeItem('collectionFormData');
    localStorage.removeItem('collectionCurrentStep');
    localStorage.removeItem('collectionCompletedSteps');
  };
  
  // Handle form field changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle radio selection changes
  const handleRadioChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };
  
  // Handle file uploads
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      setFormData(prev => ({ ...prev, briefFiles: files }));
    }
  };
  
  // Add handler for cover image upload
  const handleCoverImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({
        ...prev,
        coverImage: e.target.files![0]
      }));
    }
  };
  
  // Move to next step
  const nextStep = () => {
    if (validateCurrentStep()) {
      // Add current step to completed steps if not already included
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };
  
  // Handle step click from indicator
  const handleStepClick = (stepIndex: number) => {
    if (completedSteps.includes(stepIndex) || stepIndex < currentStep) {
      setCurrentStep(stepIndex);
    }
  };
  
  // Move to previous step
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };
  
  // Validate current step
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1: // Basic info
        if (!formData.name.trim()) {
          toast({
            title: "Missing information",
            description: "Please provide a collection name",
            variant: "destructive"
          });
          return false;
        }
        return true;
        
      case 2: // Collection type
        if (!formData.collectionType) {
          toast({
            title: "Missing selection",
            description: "Please select a collection type",
            variant: "destructive"
          });
          return false;
        }
        return true;
        
      case 3: // Model selection
        if (!formData.modelChoice) {
          toast({
            title: "Missing selection",
            description: "Please select a model option",
            variant: "destructive"
          });
          return false;
        }
        return true;
        
      case 4: // Collection details
        if (formData.collectionType === 'product' && !formData.productCount) {
          toast({
            title: "Missing information",
            description: "Please specify the number of products",
            variant: "destructive"
          });
          return false;
        }
        return true;
        
      default:
        return true;
    }
  };
  
  // Add effect to sync organization context with URL
  useEffect(() => {
    if (orgId && (!currentOrganization || currentOrganization.id !== orgId)) {
      console.log(`OrgCollectionCreation: Setting context to match URL orgId ${orgId}`);
      switchOrganization(orgId);
    }
  }, [orgId, currentOrganization, switchOrganization]);
  
  // Submit the form
  const handleSubmit = async () => {
    if (!user) return;
    
    try {
      setIsSubmitting(true);
      
      // Ensure we have an organization ID
      const organizationId = orgId || clientId;
      if (!organizationId) {
        throw new Error('No organization ID found. Please ensure you are accessing this page from a valid organization context.');
      }
      
      let coverImageUrl = null;

      // Upload cover image if selected
      if (formData.coverImage) {
        const fileExt = formData.coverImage.name.split('.').pop();
        const filePath = `collections/covers/${Date.now()}-${sanitizeFileName(formData.coverImage.name)}`;

        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .upload(filePath, formData.coverImage);

        if (uploadError) throw uploadError;

        const { data: urlData } = supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .getPublicUrl(filePath);

        coverImageUrl = urlData.publicUrl;
      }
      
      // Create collection in database with metadata and organization_id
      const collectionData = {
        name: formData.name,
        description: formData.description,
        cover_image_url: coverImageUrl,
        status: 'active',
        organization_id: organizationId, // Validated organization ID
        metadata: {
          creation_flow: {
            // Basic info
            collection_type: formData.collectionType,
            model_choice: formData.modelChoice,
            // Product/Campaign specific details
            product_count: formData.productCount,
            products_per_image: formData.productsPerImage,
            campaign_details: formData.campaignDetails,
            // Image output settings
            imageOutput: formData.imageOutput,
            // Custom models if applicable
            customModels: formData.customModels
          }
        }
      };
      
      const { data: collection, error } = await supabase
        .from('collections')
        .insert(collectionData as any)
        .select('id')
        .single();
      
      if (error) throw error;
      
      // Handle briefFiles uploads if any
      if (formData.briefFiles && formData.briefFiles.length > 0 && collection) {
        const uploadPromises = formData.briefFiles.map(async (file) => {
          const filePath = `collections/${collection.id}/briefing/${sanitizeFileName(file.name)}`;
          const { error: uploadError } = await supabase.storage
            .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
            .upload(filePath, file);
            
          if (uploadError) console.error('Error uploading file:', uploadError);
          return filePath;
        });
        
        // Wait for all uploads to complete
        const uploadedPaths = await Promise.all(uploadPromises);
        
        // Update collection metadata to include briefing file paths
        if (uploadedPaths.length > 0) {
          const { error: updateError } = await supabase
            .from('collections')
            .update({ 
              metadata: { 
                ...collectionData.metadata,
                briefingFilePaths: uploadedPaths 
              } 
            } as any)
            .eq('id', collection.id);
            
          if (updateError) console.error('Error updating collection with briefing file paths:', updateError);
        }
      }
      
      // ---> NEW: Upload Reference Files (Step 4 files) as Assets <---
      const referenceFileTypes: Array<keyof ReferenceSection> = ['existingShoot', 'productTraining', 'stylingElements'];
      const assetInserts = []; // Array to hold asset data for insertion
      const uploadErrors: string[] = []; // To collect errors

      for (const fileType of referenceFileTypes) {
          const files = formData.references[fileType];
          if (files && files.length > 0) {
              console.log(`Uploading ${files.length} files for reference type: ${fileType}`);
              for (const file of files) {
                  const sanitizedName = sanitizeFileName(file.name);
                  // Define path structure based on type
                  const referenceTypeFolder = fileType.toLowerCase(); // e.g., 'existingshoot'
                  const filePath = `collections/${collection.id}/references/${referenceTypeFolder}/${Date.now()}-${sanitizedName}`;

                  // Upload the file - using MEDIA_ORIGINALS bucket for references
                  const { data: uploadData, error: uploadError } = await supabase.storage
                      .from(STORAGE_BUCKETS.MEDIA_ORIGINALS.name)
                      .upload(filePath, file);

                  if (uploadError) {
                      console.error(`Error uploading reference file ${file.name}:`, uploadError);
                      uploadErrors.push(`Failed to upload ${file.name}`);
                      continue; // Skip asset creation if upload failed
                  }

                  // Prepare asset record for database insertion
                  assetInserts.push({
                      collection_id: collection.id,
                      file_name: sanitizedName,
                      file_path: filePath, // Store the actual path
                      file_type: file.type,
                      file_size: file.size,
                      workflow_stage: 'upload' as 'upload' | 'draft' | 'upscale' | 'retouch' | 'final', // Cast to valid enum
                      product_id: null,
                      metadata: {
                          reference_type: referenceTypeFolder, // Tag the asset type
                          uploaded_during_creation: true
                      }
                  });
              }
          }
      }

      // Bulk insert asset records if any were prepared
      if (assetInserts.length > 0) {
          console.log(`Inserting ${assetInserts.length} reference asset records...`);
          const { error: assetInsertError } = await supabase
              .from('assets')
              .insert(assetInserts);

          if (assetInsertError) {
              console.error("Error inserting reference asset records:", assetInsertError);
              // Add specific error to toast or log
              toast({
                  title: "Warning",
                  description: "Collection created, but failed to save some reference assets to the database.",
                  variant: "destructive",
              });
          }
      }

      // Report any upload errors
      if (uploadErrors.length > 0) {
           toast({
              title: "Upload Warning",
              description: `Failed to upload ${uploadErrors.length} reference file(s). Check console for details.`,
              variant: "destructive",
          });
      }
      // ---> End NEW Logic <---
      
      // Clear stored form data after successful submission
      clearStoredFormData();
      
      toast({
        title: "Success",
        description: "Collection created successfully!",
      });
      
      // Redirect to the new collection with organization path
      // Navigate to the new collection
      navigate(`/organizations/${organizationId}/collections/${collection?.id}`);
    } catch (error: any) {
      console.error('Error creating collection:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create collection",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Render step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">{formData.collectionType === 'product' ? 'Project Name' : formData.collectionType === 'campaign' ? 'Campaign Name' : 'Collection Name'}</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={formData.collectionType === 'product' ? 'Enter project name' : formData.collectionType === 'campaign' ? 'Enter campaign name' : 'Enter collection name'}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder={formData.collectionType === 'product' ? 'Describe your project' : formData.collectionType === 'campaign' ? 'Describe your campaign' : 'Describe your collection'}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="coverImage">Cover Image</Label>
              <div className="mt-2">
                <Input
                  id="coverImage"
                  type="file"
                  onChange={handleCoverImageUpload}
                  accept="image/*"
                  className="cursor-pointer"
                />
                {formData.coverImage && (
                  <div className="mt-4">
                    <p className="text-sm text-muted-foreground mb-2">Preview:</p>
                    <div className="relative aspect-video w-full max-w-sm overflow-hidden rounded-lg border">
                      <img
                        src={URL.createObjectURL(formData.coverImage)}
                        alt="Cover preview"
                        className="h-full w-full object-cover"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute right-2 top-2 h-8 w-8 rounded-full bg-black/50 hover:bg-black/70"
                        onClick={() => setFormData(prev => ({ ...prev, coverImage: null }))}
                      >
                        <X className="h-4 w-4 text-white" />
                      </Button>
                    </div>
                  </div>
                )}
                <p className="text-sm text-muted-foreground mt-2">
                  Upload a cover image for your {formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'}. This will be displayed in the {formData.collectionType === 'product' ? 'projects' : formData.collectionType === 'campaign' ? 'campaigns' : 'collections'} list.
                </p>
              </div>
            </div>
          </div>
        );
        
      case 2:
        return (
          <div className="space-y-4">
            <Label>What type of images do you want?</Label>
            <RadioGroup 
              value={formData.collectionType} 
              onValueChange={(value) => handleRadioChange('collectionType', value)}
            >
              <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                <RadioGroupItem value="product" id="product" />
                <Label htmlFor="product" className="cursor-pointer font-normal">
                  Product images for e-commerce
                </Label>
              </div>
              <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                <RadioGroupItem value="campaign" id="campaign" />
                <Label htmlFor="campaign" className="cursor-pointer font-normal">
                  Campaign images
                </Label>
              </div>
            </RadioGroup>
          </div>
        );
        
      case 3:
        return (
          <div className="space-y-4">
            <div>
              <Label className="text-lg font-semibold">Upload Briefing Materials</Label>
              <p className="text-sm text-muted-foreground mt-1 mb-4">
                Share your creative brief, moodboards, reference images, or any documents that help us understand your vision
              </p>
              <div className="mt-2 mb-4">
                <div className="border-2 border-dashed border-primary/30 rounded-lg p-8 text-center cursor-pointer bg-primary/5 hover:bg-primary/10 transition-all duration-200 hover:border-primary/50">
                  <input
                    type="file"
                    id="briefFiles"
                    multiple
                    onChange={handleFileUpload}
                    accept="image/*,.pdf,.ppt,.pptx,.doc,.docx"
                    className="hidden"
                  />
                  <label htmlFor="briefFiles" className="cursor-pointer flex flex-col items-center">
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                      <Upload className="h-8 w-8 text-primary" />
                    </div>
                    <p className="text-lg font-medium text-gray-900 mb-2">
                      Drop your files here or click to browse
                    </p>
                    <p className="text-sm text-muted-foreground mb-3">
                      Supports PDF, DOC, PPT, and image files
                    </p>
                    <div className="flex flex-wrap gap-2 justify-center">
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">PDF</span>
                      <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">DOC</span>
                      <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded">PPT</span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded">Images</span>
                    </div>
                  </label>
                </div>
                {formData.briefFiles && formData.briefFiles.length > 0 && (
                  <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center mb-2">
                      <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-2">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-green-800">
                        {formData.briefFiles.length} file(s) uploaded successfully
                      </p>
                    </div>
                    <div className="space-y-2">
                      {formData.briefFiles.slice(0, 5).map((file, index) => {
                        const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
                        const isPdf = fileExtension === 'pdf';
                        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(fileExtension);

                        return (
                          <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                            <div className="flex items-center space-x-2">
                              <div className="flex-shrink-0">
                                {isPdf ? (
                                  <FileText className="h-4 w-4 text-red-500" />
                                ) : isImage ? (
                                  <ImageIcon className="h-4 w-4 text-blue-500" />
                                ) : (
                                  <FileText className="h-4 w-4 text-gray-500" />
                                )}
                              </div>
                              <span className="text-sm text-gray-700 truncate max-w-xs">{file.name}</span>
                            </div>
                            <span className="text-xs text-gray-500 uppercase">{fileExtension}</span>
                          </div>
                        );
                      })}
                      {formData.briefFiles.length > 5 && (
                        <p className="text-xs text-green-700 text-center">
                          ...and {formData.briefFiles.length - 5} more files
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Label>Should we create custom models for you or will you pick models from our library?</Label>
                <InfoTooltip 
                  content={
                    <div className="space-y-2 text-sm">
                      <p><strong>Custom models:</strong> We create AI models based on your specifications.</p>
                      <p><strong>Library models:</strong> Choose from our pre-existing model library.</p>
                      <p>Note: Custom models have additional costs. Library is available after collection creation.</p>
                    </div>
                  } 
                />
              </div>
              <RadioGroup 
                value={formData.modelChoice} 
                onValueChange={(value) => handleRadioChange('modelChoice', value)}
              >
                <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                  <RadioGroupItem value="custom" id="custom-models" />
                  <Label htmlFor="custom-models" className="cursor-pointer font-normal">
                    I want FashionLab to create custom models
                  </Label>
                </div>
                <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                  <RadioGroupItem value="library" id="library-models" />
                  <Label htmlFor="library-models" className="cursor-pointer font-normal">
                    I want to choose models from library
                  </Label>
                </div>
              </RadioGroup>
              
              <div className="flex items-center p-3 bg-blue-50 rounded-lg text-blue-700 text-sm mt-2">
                <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                <p>Library is added to your menu when you've finished the onboarding - you can pick models there later</p>
              </div>
            </div>
          </div>
        );
        
      case 4:
        return (
          <div className="space-y-6">
            {formData.collectionType === 'product' ? (
              <>
                {/* AI Image Calculation Section */}
                <div className="space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h3 className="text-lg font-medium text-blue-900">AI Image Calculation</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="productCount">How many products?</Label>
                      <Input
                        id="productCount"
                        name="productCount"
                        type="number"
                        min="1"
                        value={formData.productCount || ''}
                        onChange={(e) => {
                          const count = parseInt(e.target.value) || undefined;
                          setFormData(prev => ({
                            ...prev,
                            productCount: count,
                            totalAiImages: count && prev.aiImagesPerProduct ? count * prev.aiImagesPerProduct : undefined
                          }));
                        }}
                        placeholder="Enter number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="aiImagesPerProduct">AI images per product?</Label>
                      <Input
                        id="aiImagesPerProduct"
                        name="aiImagesPerProduct"
                        type="number"
                        min="1"
                        value={formData.aiImagesPerProduct || ''}
                        onChange={(e) => {
                          const count = parseInt(e.target.value) || undefined;
                          setFormData(prev => ({
                            ...prev,
                            aiImagesPerProduct: count,
                            totalAiImages: count && prev.productCount ? count * prev.productCount : undefined
                          }));
                        }}
                        placeholder="Enter number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Total AI Images</Label>
                      <div className="p-2 bg-white border rounded-md text-center font-medium text-lg">
                        {formData.totalAiImages || (formData.productCount && formData.aiImagesPerProduct ? formData.productCount * formData.aiImagesPerProduct : '—')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  
                  <div className="space-y-2">
                    <Label>How many products per image?</Label>
                    <RadioGroup 
                      value={formData.productsPerImage || ''} 
                      onValueChange={(value) => handleRadioChange('productsPerImage', value)}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                        <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                          <RadioGroupItem value="1" id="products-1" />
                          <Label htmlFor="products-1" className="cursor-pointer font-normal">
                            1 product
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                          <RadioGroupItem value="2" id="products-2" />
                          <Label htmlFor="products-2" className="cursor-pointer font-normal">
                            2 products
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                          <RadioGroupItem value="3" id="products-3" />
                          <Label htmlFor="products-3" className="cursor-pointer font-normal">
                            3 products
                          </Label>
                        </div>
                      </div>
                    </RadioGroup>
                  </div>
                </div>

                {/* Target Garment Images Section */}
                <Separator className="my-6" />
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Target Garment Images</h3>
                    <p className="text-sm text-muted-foreground">Minimum 5 images required</p>
                  </div>

                  <EnhancedDropzone
                    title="Upload Target Garment Images"
                    description="Drag and drop or click to browse"
                    onFilesChange={(files) => handleEnhancedBriefUpload('targetGarmentImages', 'files', files)}
                    currentFiles={formData.enhancedBrief?.targetGarmentImages.files || []}
                    maxFiles={20}
                    minFiles={5}
                    maxSize={10 * 1024 * 1024} // 10MB
                    showPreviews={true}
                    validationRules={{
                      minFiles: 5,
                      maxFiles: 20,
                      maxSize: 10 * 1024 * 1024,
                      allowedTypes: ['jpg', 'jpeg', 'png', 'webp']
                    }}
                  />
                </div>

                {/* Secondary Garment Section */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Secondary Garment</h3>
                    <p className="text-sm text-muted-foreground">Either top or bottom, depending on the main product</p>
                  </div>

                  <EnhancedDropzone
                    title="Upload Secondary Garment Images"
                    description="Drag and drop or click to browse"
                    onFilesChange={(files) => handleEnhancedBriefUpload('secondaryGarment', 'files', files)}
                    currentFiles={formData.enhancedBrief?.secondaryGarment.files || []}
                    maxFiles={10}
                    maxSize={10 * 1024 * 1024} // 10MB
                    showPreviews={true}
                    validationRules={{
                      maxFiles: 10,
                      maxSize: 10 * 1024 * 1024,
                      allowedTypes: ['jpg', 'jpeg', 'png', 'webp']
                    }}
                  />

                  <div className="space-y-2">
                    <Label htmlFor="secondaryGarmentDescription">Description</Label>
                    <Textarea
                      id="secondaryGarmentDescription"
                      value={formData.enhancedBrief?.secondaryGarment.description || ''}
                      onChange={(e) => handleEnhancedBriefTextChange('secondaryGarment', 'description', e.target.value)}
                      placeholder="Describe the secondary garment (e.g., 'white cotton t-shirt', 'black denim jeans')"
                      rows={2}
                    />
                  </div>
                </div>

                {/* Shoes Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Shoes</h3>

                  <EnhancedDropzone
                    title="Upload Shoes Images"
                    description="Drag and drop or click to browse"
                    onFilesChange={(files) => handleEnhancedBriefUpload('shoes', 'files', files)}
                    currentFiles={formData.enhancedBrief?.shoes.files || []}
                    maxFiles={10}
                    maxSize={10 * 1024 * 1024} // 10MB
                    showPreviews={true}
                    validationRules={{
                      maxFiles: 10,
                      maxSize: 10 * 1024 * 1024,
                      allowedTypes: ['jpg', 'jpeg', 'png', 'webp']
                    }}
                  />
                </div>

                {/* References Section */}
                <Separator className="my-6" />
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">References</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label>Please add a folder with existing shoot you want to resemble</Label>
                      <div className="mt-2">
                        <Input 
                          type="file" 
                          onChange={(e) => handleReferenceUpload('existingShoot', e.target.files)}
                          accept="image/*"
                          multiple
                          className="cursor-pointer"
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Please add folders for each product for the training of our AI</Label>
                      <p className="text-sm text-muted-foreground mb-2">Minimum 5 images per product</p>
                      <div className="mt-2">
                        <Input 
                          type="file" 
                          onChange={(e) => handleReferenceUpload('productTraining', e.target.files)}
                          accept="image/*"
                          multiple
                          className="cursor-pointer"
                        />
                      </div>
                    </div>

                    <div>
                      <Label>Please add shoes for each look if shoes is not the main product</Label>
                      <p className="text-sm text-muted-foreground mb-2">At least 1 image, same for tops and other secondary styling elements</p>
                      <div className="mt-2">
                        <Input 
                          type="file" 
                          onChange={(e) => handleReferenceUpload('stylingElements', e.target.files)}
                          accept="image/*"
                          multiple
                          className="cursor-pointer"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Required Angles Section */}
                <Separator className="my-6" />
                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Required Angles</h3>
                    <p className="text-sm text-muted-foreground">Select the angles you need for your project using reference images</p>
                  </div>

                  <AngleSelector
                    selectedAngles={formData.enhancedBrief?.requiredAngles.selectedAngles || []}
                    onAnglesChange={(angles) => handleEnhancedBriefTextChange('requiredAngles', 'selectedAngles', angles)}
                  />
                </div>

                {/* Format Selection */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Format Selection</h3>
                  <p className="text-sm text-muted-foreground">Choose one or more formats for your images</p>

                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="formatPortrait"
                        checked={formData.enhancedBrief?.formatSelection.portrait || false}
                        onChange={(e) => handleEnhancedBriefCheckboxChange('formatSelection', 'portrait', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="formatPortrait">Portrait</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="formatLandscape"
                        checked={formData.enhancedBrief?.formatSelection.landscape || false}
                        onChange={(e) => handleEnhancedBriefCheckboxChange('formatSelection', 'landscape', e.target.checked)}
                        className="rounded border-gray-300"
                      />
                      <Label htmlFor="formatLandscape">Landscape</Label>
                    </div>
                  </div>
                </div>

                {/* Image Output Settings */}
                <Separator className="my-6" />
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Image Output Settings</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label>How many images of each type do you need per product?</Label>
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        {formData.imageOutput && formData.imageOutput.types && Object.entries(formData.imageOutput.types).map(([key, count]) => (
                          <div key={key} className="flex items-center space-x-2">
                            <Input
                              type="number"
                              min="0"
                              value={count || ''}
                              onChange={(e) => handleImageTypeCountChange(key as keyof ImageOutputTypes, e.target.value)}
                              className="w-16 mr-2"
                            />
                            <Label htmlFor={key} className="capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deadline">When do you need the final images?</Label>
                      <Input
                        type="date"
                        id="deadline"
                        value={formData.imageOutput.deadline}
                        onChange={(e) => handleImageOutputChange('deadline', e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>What pixel size should images have?</Label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          placeholder="Width"
                          value={formData.imageOutput.pixelSize.width}
                          onChange={(e) => handlePixelSizeChange('width', parseInt(e.target.value))}
                        />
                        <span className="flex items-center">×</span>
                        <Input
                          type="number"
                          placeholder="Height"
                          value={formData.imageOutput.pixelSize.height}
                          onChange={(e) => handlePixelSizeChange('height', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>What format do you want images delivered in?</Label>
                      <Select
                        value={formData.imageOutput.format}
                        onValueChange={(value) => handleImageOutputChange('format', value as ImageOutputSettings['format'])}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          {['WebP', 'JPG', 'TIFF', 'PNG', 'Other'].map(format => (
                            <SelectItem key={format} value={format}>
                              {format}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formData.imageOutput.format === 'Other' && (
                        <Input
                          placeholder="Specify format"
                          value={formData.imageOutput.customFormat}
                          onChange={(e) => handleImageOutputChange('customFormat', e.target.value)}
                          className="mt-2"
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>What is the max. resolution you want images delivered? (MB)</Label>
                      <Input
                        type="number"
                        min="1"
                        value={formData.imageOutput.maxResolution}
                        onChange={(e) => handleImageOutputChange('maxResolution', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              </>
            ) : formData.collectionType === 'campaign' ? (
              <div className="space-y-4">
                {/* AI Image Calculation Section for Campaign */}
                <div className="space-y-4 p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <h3 className="text-lg font-medium text-purple-900">Campaign AI Image Calculation</h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="campaignProductCount">How many products in this campaign?</Label>
                      <Input
                        id="campaignProductCount"
                        name="campaignProductCount"
                        type="number"
                        min="1"
                        value={formData.productCount || ''}
                        onChange={(e) => {
                          const count = parseInt(e.target.value) || undefined;
                          setFormData(prev => ({
                            ...prev,
                            productCount: count,
                            totalAiImages: count && prev.aiImagesPerProduct ? count * prev.aiImagesPerProduct : undefined
                          }));
                        }}
                        placeholder="Enter number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="campaignAiImagesPerProduct">AI images per product?</Label>
                      <Input
                        id="campaignAiImagesPerProduct"
                        name="campaignAiImagesPerProduct"
                        type="number"
                        min="1"
                        value={formData.aiImagesPerProduct || ''}
                        onChange={(e) => {
                          const count = parseInt(e.target.value) || undefined;
                          setFormData(prev => ({
                            ...prev,
                            aiImagesPerProduct: count,
                            totalAiImages: count && prev.productCount ? count * prev.productCount : undefined
                          }));
                        }}
                        placeholder="Enter number"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Total AI Images</Label>
                      <div className="p-2 bg-white border rounded-md text-center font-medium text-lg">
                        {formData.totalAiImages || (formData.productCount && formData.aiImagesPerProduct ? formData.productCount * formData.aiImagesPerProduct : '—')}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="purpose">What's the purpose of the campaign?</Label>
                  <Textarea 
                    id="purpose" 
                    name="purpose" 
                    value={formData.campaignDetails?.purpose || ''} 
                    onChange={(e) => handleCampaignDetailChange('purpose', e.target.value)} 
                    placeholder="Describe the purpose of your campaign" 
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lookAndFeel">What is the look and feel, styling and filter you want to use for images?</Label>
                  <Textarea 
                    id="lookAndFeel" 
                    name="lookAndFeel" 
                    value={formData.campaignDetails?.lookAndFeel || ''} 
                    onChange={(e) => handleCampaignDetailChange('lookAndFeel', e.target.value)} 
                    placeholder="Describe the desired aesthetic" 
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="poses">What kind of poses should models do?</Label>
                  <Textarea 
                    id="poses" 
                    name="poses" 
                    value={formData.campaignDetails?.poses || ''} 
                    onChange={(e) => handleCampaignDetailChange('poses', e.target.value)} 
                    placeholder="Describe desired poses" 
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="targetGroup">Who is the target group for this campaign?</Label>
                  <Textarea 
                    id="targetGroup" 
                    name="targetGroup" 
                    value={formData.campaignDetails?.targetGroup || ''} 
                    onChange={(e) => handleCampaignDetailChange('targetGroup', e.target.value)} 
                    placeholder="Describe your target audience" 
                    rows={2}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>How many products per image?</Label>
                  <RadioGroup 
                    value={formData.productsPerImage || ''} 
                    onValueChange={(value) => handleRadioChange('productsPerImage', value)}
                  >
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
                      <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                        <RadioGroupItem value="1" id="products-1-campaign" />
                        <Label htmlFor="products-1-campaign" className="cursor-pointer font-normal">
                          1 product
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                        <RadioGroupItem value="2" id="products-2-campaign" />
                        <Label htmlFor="products-2-campaign" className="cursor-pointer font-normal">
                          2 products
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-muted cursor-pointer">
                        <RadioGroupItem value="3" id="products-3-campaign" />
                        <Label htmlFor="products-3-campaign" className="cursor-pointer font-normal">
                          3 products
                        </Label>
                      </div>
                    </div>
                  </RadioGroup>
                </div>

                {/* Image Output Settings for Campaign */}
                <Separator className="my-6" />
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Image Output Settings</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <Label>How many images of each type do you need per product?</Label>
                      <div className="grid grid-cols-2 gap-4 mt-2">
                        {formData.imageOutput && formData.imageOutput.types && Object.entries(formData.imageOutput.types).map(([key, count]) => (
                          <div key={key} className="flex items-center space-x-2">
                            <Input
                              type="number"
                              min="0"
                              value={count || ''}
                              onChange={(e) => handleImageTypeCountChange(key as keyof ImageOutputTypes, e.target.value)}
                              className="w-16 mr-2"
                            />
                            <Label htmlFor={key} className="capitalize">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="deadline">When do you need the final images?</Label>
                      <Input
                        type="date"
                        id="deadline"
                        value={formData.imageOutput.deadline}
                        onChange={(e) => handleImageOutputChange('deadline', e.target.value)}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>What pixel size should images have?</Label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          placeholder="Width"
                          value={formData.imageOutput.pixelSize.width}
                          onChange={(e) => handlePixelSizeChange('width', parseInt(e.target.value))}
                        />
                        <span className="flex items-center">×</span>
                        <Input
                          type="number"
                          placeholder="Height"
                          value={formData.imageOutput.pixelSize.height}
                          onChange={(e) => handlePixelSizeChange('height', parseInt(e.target.value))}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>What format do you want images delivered in?</Label>
                      <Select
                        value={formData.imageOutput.format}
                        onValueChange={(value) => handleImageOutputChange('format', value as ImageOutputSettings['format'])}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          {['WebP', 'JPG', 'TIFF', 'PNG', 'Other'].map(format => (
                            <SelectItem key={format} value={format}>
                              {format}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formData.imageOutput.format === 'Other' && (
                        <Input
                          placeholder="Specify format"
                          value={formData.imageOutput.customFormat}
                          onChange={(e) => handleImageOutputChange('customFormat', e.target.value)}
                          className="mt-2"
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>What is the max. resolution you want images delivered? (MB)</Label>
                      <Input
                        type="number"
                        min="1"
                        value={formData.imageOutput.maxResolution}
                        onChange={(e) => handleImageOutputChange('maxResolution', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="text-center">
                  <X className="mx-auto h-10 w-10 text-muted-foreground mb-3" />
                  <p>Please select a collection type in the previous step</p>
                  <Button onClick={prevStep} variant="outline" className="mt-4">
                    Go Back
                  </Button>
                </div>
              </div>
            )}
          </div>
        );
        
      case 5:
        return (
          <div className="space-y-6">
            {/* Collection Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Collection Summary</CardTitle>
                <CardDescription>Review your collection details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="font-medium text-lg mb-4">Basic Information</h3>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">Name</h4>
                      <p>{formData.name}</p>
                    </div>
                    {formData.description && (
                      <div className="col-span-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Description</h4>
                        <p>{formData.description}</p>
                      </div>
                    )}
                    {formData.coverImage && (
                      <div className="col-span-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Cover Image</h4>
                        <div className="mt-2 aspect-video w-full max-w-sm overflow-hidden rounded-lg border">
                          <img
                            src={URL.createObjectURL(formData.coverImage)}
                            alt="Cover preview"
                            className="h-full w-full object-cover"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Project/Campaign Requirements */}
                <div>
                  <h3 className="font-medium text-lg mb-4">{formData.collectionType === 'product' ? 'Project Requirements' : formData.collectionType === 'campaign' ? 'Campaign Requirements' : 'Collection Requirements'}</h3>
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">{formData.collectionType === 'product' ? 'Project Type' : formData.collectionType === 'campaign' ? 'Campaign Type' : 'Collection Type'}</h4>
                      <p>{formData.collectionType === 'product' ? 'Product Images' : 'Campaign Images'}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-1">Model Choice</h4>
                      <p>{formData.modelChoice === 'custom' ? 'Custom Models' : 'Library Models'}</p>
                    </div>

                    {formData.productCount && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Product Count</h4>
                        <p>{formData.productCount}</p>
                      </div>
                    )}

                    {formData.productsPerImage && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Products Per Image</h4>
                        <p>{formData.productsPerImage}</p>
                      </div>
                    )}

                    {formData.aiImagesPerProduct && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">AI Images Per Product</h4>
                        <p>{formData.aiImagesPerProduct}</p>
                      </div>
                    )}

                    {formData.totalAiImages && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Total AI Images</h4>
                        <p className="font-semibold text-lg">{formData.totalAiImages}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Enhanced Brief Sections */}
                {formData.enhancedBrief && (
                  <div>
                    <h3 className="font-medium text-lg mb-4">Enhanced Brief Details</h3>
                    <div className="space-y-4">
                      {formData.enhancedBrief.targetGarmentImages.files.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Target Garment Images</h4>
                          <p>{formData.enhancedBrief.targetGarmentImages.files.length} file(s) uploaded</p>
                        </div>
                      )}

                      {formData.enhancedBrief.secondaryGarment.files.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Secondary Garment</h4>
                          <p>{formData.enhancedBrief.secondaryGarment.files.length} file(s) uploaded</p>
                          {formData.enhancedBrief.secondaryGarment.description && (
                            <p className="text-sm text-gray-600 mt-1">{formData.enhancedBrief.secondaryGarment.description}</p>
                          )}
                        </div>
                      )}

                      {formData.enhancedBrief.shoes.files.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Shoes</h4>
                          <p>{formData.enhancedBrief.shoes.files.length} file(s) uploaded</p>
                        </div>
                      )}

                      {formData.enhancedBrief.requiredAngles.selectedAngles.length > 0 && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Required Angles</h4>
                          <div className="flex flex-wrap gap-2">
                            {formData.enhancedBrief.requiredAngles.selectedAngles.map(angleId => {
                              // Convert angle ID to display name
                              const displayName = angleId
                                .replace(/_/g, ' ')
                                .replace(/\b\w/g, l => l.toUpperCase())
                                .replace('3 4', '3/4');
                              return (
                                <span key={angleId} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                  {displayName}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {(formData.enhancedBrief.formatSelection.portrait || formData.enhancedBrief.formatSelection.landscape) && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Format Selection</h4>
                          <div className="flex flex-wrap gap-2">
                            {formData.enhancedBrief.formatSelection.portrait && (
                              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Portrait</span>
                            )}
                            {formData.enhancedBrief.formatSelection.landscape && (
                              <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">Landscape</span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Campaign Details */}
                {formData.collectionType === 'campaign' && formData.campaignDetails && (
                  <div>
                    <h3 className="font-medium text-lg mb-4">Campaign Details</h3>
                    <div className="space-y-4">
                      {formData.campaignDetails.purpose && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Purpose</h4>
                          <p>{formData.campaignDetails.purpose}</p>
                        </div>
                      )}
                      
                      {formData.campaignDetails.lookAndFeel && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Look and Feel</h4>
                          <p>{formData.campaignDetails.lookAndFeel}</p>
                        </div>
                      )}
                      
                      {formData.campaignDetails.poses && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Poses</h4>
                          <p>{formData.campaignDetails.poses}</p>
                        </div>
                      )}
                      
                      {formData.campaignDetails.targetGroup && (
                        <div>
                          <h4 className="font-medium text-sm text-muted-foreground mb-1">Target Group</h4>
                          <p>{formData.campaignDetails.targetGroup}</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* References & Uploads */}
                <div>
                  <h3 className="font-medium text-lg mb-4">References & Uploads</h3>
                  <div className="space-y-4">
                    {formData.briefFiles && formData.briefFiles.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Briefing Materials</h4>
                        <p>{formData.briefFiles.length} file(s) uploaded</p>
                        <ul className="text-xs text-muted-foreground mt-1">
                          {formData.briefFiles.slice(0, 5).map((file, index) => (
                            <li key={index}>{file.name}</li>
                          ))}
                          {formData.briefFiles.length > 5 && (
                            <li>...and {formData.briefFiles.length - 5} more</li>
                          )}
                        </ul>
                      </div>
                    )}
                    
                    {formData.references && (
                      <>
                        {formData.references.existingShoot.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm text-muted-foreground mb-1">Existing Shoot References</h4>
                            <p>{formData.references.existingShoot.length} file(s) uploaded</p>
                          </div>
                        )}
                        
                        {formData.references.productTraining.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm text-muted-foreground mb-1">Product Training Images</h4>
                            <p>{formData.references.productTraining.length} file(s) uploaded</p>
                          </div>
                        )}
                        
                        {formData.references.stylingElements.length > 0 && (
                          <div>
                            <h4 className="font-medium text-sm text-muted-foreground mb-1">Styling Elements</h4>
                            <p>{formData.references.stylingElements.length} file(s) uploaded</p>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Image Output Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle>Image Output Settings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium text-sm text-muted-foreground mb-3">Image Types and Counts</h4>
                        <div className="grid grid-cols-2 gap-4">
                          {Object.entries(formData.imageOutput.types)
                            .filter(([_, count]) => count > 0)
                            .map(([key, count]) => (
                              <div key={key} className="flex items-center justify-between border rounded p-2">
                                <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                                <span className="font-medium">{count}</span>
                              </div>
                            ))}
                          {Object.values(formData.imageOutput.types).every(count => count === 0) && (
                            <p className="text-muted-foreground col-span-2">No image types selected</p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Deadline</h4>
                        <p>{formData.imageOutput.deadline || 'Not specified'}</p>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Pixel Size</h4>
                        <p>{formData.imageOutput.pixelSize.width} × {formData.imageOutput.pixelSize.height}</p>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Format</h4>
                        <p>{formData.imageOutput.format === 'Other' 
                          ? formData.imageOutput.customFormat 
                          : formData.imageOutput.format}</p>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium text-sm text-muted-foreground mb-1">Max Resolution</h4>
                        <p>{formData.imageOutput.maxResolution} MB</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CardContent>
            </Card>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Basic Information';
      case 2: return formData.collectionType === 'product' ? 'Project Type' : formData.collectionType === 'campaign' ? 'Campaign Type' : 'Collection Type';
      case 3: return 'Models & References';
      case 4: return formData.collectionType === 'product' ? 'Project Details' : formData.collectionType === 'campaign' ? 'Campaign Details' : 'Collection Details';
      case 5: return 'Review & Create';
      default: return '';
    }
  };
  
  const getStepDescription = () => {
    switch (currentStep) {
      case 1: return `Start by naming your ${formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'}`;
      case 2: return 'What kind of images do you need?';
      case 3: return 'Choose model options and upload references';
      case 4: return `Configure the details of your ${formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'}`;
      case 5: return 'Review your information before creating';
      default: return '';
    }
  };

  // Handle back button navigation
  const handleBack = () => {
    if (currentStep > 1 && !isFormEmpty()) {
      setShowExitDialog(true);
      setExitPath(`/organization/${orgId}/collections`);
    } else {
      if (orgId) {
        navigate(`/organizations/${orgId}/collections`);
      } else if (clientId) {
        navigate(`/organizations/${clientId}`);
      }
    }
  };
  
  // Check if form is empty
  const isFormEmpty = (): boolean => {
    return (
      formData.name === '' && 
      formData.description === '' && 
      formData.collectionType === '' && 
      formData.modelChoice === '' && 
      (!formData.briefFiles || formData.briefFiles.length === 0)
    );
  };
  
  // Confirm exit navigation
  const confirmExit = () => {
    clearStoredFormData();
    navigate(exitPath);
  };

  // Add new handler functions
  const handleReferenceUpload = (type: keyof ReferenceSection, files: FileList | null) => {
    if (!files) return;

    setFormData(prev => ({
      ...prev,
      references: {
        ...prev.references,
        [type]: Array.from(files)
      }
    }));
  };

  // Handler for enhanced brief section uploads
  const handleEnhancedBriefUpload = (
    section: keyof EnhancedBriefSections,
    field: string,
    files: FileList | null | File[]
  ) => {
    if (!files) return;

    const fileArray = files instanceof FileList ? Array.from(files) : files;

    setFormData(prev => {
      // Ensure enhancedBrief exists
      if (!prev.enhancedBrief) {
        return prev;
      }

      return {
        ...prev,
        enhancedBrief: {
          ...prev.enhancedBrief,
          [section]: {
            ...prev.enhancedBrief[section],
            [field]: fileArray
          }
        }
      };
    });
  };

  // Handler for enhanced brief text fields
  const handleEnhancedBriefTextChange = (
    section: keyof EnhancedBriefSections,
    field: string,
    value: string | string[]
  ) => {
    setFormData(prev => {
      // Ensure enhancedBrief exists
      if (!prev.enhancedBrief) {
        return prev;
      }

      return {
        ...prev,
        enhancedBrief: {
          ...prev.enhancedBrief,
          [section]: {
            ...prev.enhancedBrief[section],
            [field]: value
          }
        }
      };
    });
  };

  // Handler for enhanced brief checkbox fields
  const handleEnhancedBriefCheckboxChange = (
    section: keyof EnhancedBriefSections,
    field: string,
    checked: boolean
  ) => {
    setFormData(prev => {
      // Ensure enhancedBrief exists
      if (!prev.enhancedBrief) {
        return prev;
      }

      return {
        ...prev,
        enhancedBrief: {
          ...prev.enhancedBrief,
          [section]: {
            ...prev.enhancedBrief[section],
            [field]: checked
          }
        }
      };
    });
  };

  const handleImageTypeCountChange = (type: keyof ImageOutputTypes, value: string) => {
    // Convert to number, if empty or invalid, set to 0
    const count = parseInt(value) || 0;
    setFormData(prev => ({
      ...prev,
      imageOutput: {
        ...prev.imageOutput,
        types: {
          ...prev.imageOutput.types,
          [type]: count
        }
      }
    }));
  };

  const handleImageOutputChange = (field: keyof Omit<ImageOutputSettings, 'types' | 'pixelSize'>, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      imageOutput: {
        ...prev.imageOutput,
        [field]: value
      }
    }));
  };

  const handlePixelSizeChange = (dimension: 'width' | 'height', value: number) => {
    setFormData(prev => ({
      ...prev,
      imageOutput: {
        ...prev.imageOutput,
        pixelSize: {
          ...prev.imageOutput.pixelSize,
          [dimension]: value
        }
      }
    }));
  };

  // Update the campaign details handlers to ensure all fields are initialized
  const handleCampaignDetailChange = (field: keyof CampaignDetails, value: string) => {
    setFormData(prev => ({
      ...prev,
      campaignDetails: {
        purpose: prev.campaignDetails?.purpose || '',
        lookAndFeel: prev.campaignDetails?.lookAndFeel || '',
        poses: prev.campaignDetails?.poses || '',
        targetGroup: prev.campaignDetails?.targetGroup || '',
        ...prev.campaignDetails,
        [field]: value
      }
    }));
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Back button */}
      <Button variant="ghost" className="p-0" onClick={handleBack}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to {formData.collectionType === 'product' ? 'Projects' : formData.collectionType === 'campaign' ? 'Campaigns' : 'Collections'}
      </Button>

      <Card className="w-full">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{getStepTitle()}</CardTitle>
              <CardDescription className="mt-1">
                {getStepDescription()}
                {currentOrganization && (
                  <div className="mt-1">
                    Creating {formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'} for {currentOrganization.name}
                  </div>
                )}
              </CardDescription>
            </div>
            <div className="text-sm font-medium">
              Step {currentStep} of {totalSteps}
            </div>
          </div>
          <StepIndicator 
            steps={steps} 
            currentStep={currentStep} 
            className="mt-6 mb-2"
            onStepClick={handleStepClick}
          />
          <Progress value={(currentStep / totalSteps) * 100} className="h-2 mt-4" />
        </CardHeader>
        
        <CardContent>
          {renderStepContent()}
        </CardContent>
        
        <CardFooter className="flex flex-col sm:flex-row sm:justify-between gap-4 pt-2">
          <Button
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1 || isSubmitting}
            className="w-full sm:w-auto order-2 sm:order-1"
          >
            Previous
          </Button>
          
          {currentStep < totalSteps ? (
            <Button 
              onClick={nextStep}
              disabled={isSubmitting}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {isSubmitting ? 'Creating...' : `Create ${formData.collectionType === 'product' ? 'Project' : formData.collectionType === 'campaign' ? 'Campaign' : 'Collection'}`}
              <Check className="ml-2 h-4 w-4" />
            </Button>
          )}
        </CardFooter>
      </Card>
      
      {/* Navigation confirmation dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure you want to leave?</AlertDialogTitle>
            <AlertDialogDescription>
              Your {formData.collectionType === 'product' ? 'project' : formData.collectionType === 'campaign' ? 'campaign' : 'collection'} information hasn't been saved. If you leave now, your progress will be lost.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Stay on Page</AlertDialogCancel>
            <AlertDialogAction onClick={confirmExit}>Leave Anyway</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default OrganizationCollectionCreation; 