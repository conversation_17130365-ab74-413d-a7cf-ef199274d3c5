import React, { useState } from 'react';
import { useUserRole } from '../contexts/UserRoleContext';
import { Navigate } from 'react-router-dom';
import { PageTitle } from '../components/ui/PageTitle';
import { Button } from '../components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { UserPlus, Users, Shield, Building2, UserCheck } from 'lucide-react';
import CreateUserDirectForm from '../components/organizations/CreateUserDirectForm';
import { AllUsersTable } from '../components/organizations/AllUsersTable';
import { useUserStatistics } from '../components/common/hooks/useAllUsers';

export default function PlatformAdminUserManagement() {
  const { isPlatformUser, isLoadingRole } = useUserRole();
  const { stats, isLoading: isLoadingStats } = useUserStatistics();
  const [activeTab, setActiveTab] = useState('users');

  // Redirect if not platform admin
  if (!isLoadingRole && !isPlatformUser) {
    return <Navigate to="/dashboard" replace />;
  }

  if (isLoadingRole) {
    return (
      <div className="container mx-auto p-6 text-center">
        <p>Loading...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <PageTitle 
        title="User Management" 
        subtitle="Create and manage user accounts across the platform"
      />

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStats ? '-' : stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {isLoadingStats ? 'Loading...' : `${stats.recentlyAdded} added this week`}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Platform Admins</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoadingStats ? '-' : (stats.byRole['platform_admin'] || 0) + (stats.byRole['platform_super'] || 0)}
            </div>
            <p className="text-xs text-muted-foreground">System administrators</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Brand Users</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoadingStats ? '-' : (stats.byRole['brand_admin'] || 0) + (stats.byRole['brand_member'] || 0)}
            </div>
            <p className="text-xs text-muted-foreground">Brand admins & members</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Freelancers</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{isLoadingStats ? '-' : stats.freelancers}</div>
            <p className="text-xs text-muted-foreground">External contractors</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-6">
        <TabsList className="grid w-full grid-cols-2 max-w-[400px]">
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            All Users
          </TabsTrigger>
          <TabsTrigger value="direct" className="flex items-center gap-2">
            <UserPlus className="h-4 w-4" />
            Create User
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>All Users</CardTitle>
              <CardDescription>
                View and manage all users in the system. Search, filter, and perform actions on user accounts.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AllUsersTable />
            </CardContent>
          </Card>
        </TabsContent>


        <TabsContent value="direct" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Create New User</CardTitle>
              <CardDescription>
                Create user accounts directly in the system. Users will receive a password reset email to set up their access.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">How it works:</h4>
                  <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                    <li>Account is created instantly with the specified role</li>
                    <li>User receives a password reset email to set their password</li>
                    <li>Perfect for all user types including freelancers</li>
                    <li>No complex invitation flow or confirmation required</li>
                  </ul>
                </div>
                <CreateUserDirectForm />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>User Role Overview</CardTitle>
          <CardDescription>
            Understanding the different user roles and their permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <h4 className="font-medium">Platform Admin</h4>
              <p className="text-sm text-muted-foreground">
                Full platform access, can manage all organizations and users
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Brand Admin</h4>
              <p className="text-sm text-muted-foreground">
                Manages their organization, campaigns, and can invite brand members
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Brand Member</h4>
              <p className="text-sm text-muted-foreground">
                Can view and work with campaigns within their organization
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Freelancer (Brand Admin)</h4>
              <p className="text-sm text-muted-foreground">
                External contractor with brand admin access to multiple organizations
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}