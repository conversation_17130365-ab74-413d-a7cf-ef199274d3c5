import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import { Button } from '../components/ui/button';
import CollectionAssets from './clients/CollectionAssets';
import { FilterSidebar } from '../components/layout/FilterSidebar';
import { FilterProvider } from '../contexts/FilterContext';
import { useOrganization } from '../components/common/hooks/useOrganizations';
import { useCollection } from '../components/common/hooks/useCollections';
import { Skeleton } from '../components/ui/skeleton';
import { useSupabase } from '../contexts/SupabaseContext';
import { useToast } from '../components/ui/use-toast';
import { useOrganizations } from '../contexts/OrganizationContext';
import { CollectionBriefDisplay } from '../components/collections/CollectionBriefDisplay';
import { isFeatureEnabled } from '../utils/featureFlags';
import type { J<PERSON> } from '../components/common/types/database.types';
import { useUserRole } from '../contexts/UserRoleContext';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../components/ui/alert-dialog";
import { Label } from "../components/ui/label";
import { Input } from "../components/ui/input";
import { Textarea } from "../components/ui/textarea";
import { Edit, Trash2, ChevronRight, Upload, BarChart3, Sparkles } from 'lucide-react';
import { STORAGE_BUCKETS } from '../components/common/utils/supabase';
import { deleteAsset } from '../components/common/utils/assetStorage';

// Add a simple media query hook
function useMediaQuery(query: string) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }

    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
}

// Define the metadata types
interface ImageOutputTypes {
  fullHeightFront: boolean;
  fullHeightBack: boolean;
  fullHeightSide: boolean;
  halfBody: boolean;
  portrait: boolean;
}

interface ImageOutputSettings {
  types: ImageOutputTypes;
  deadline: string;
  pixelSize: {
    width: number;
    height: number;
  };
  format: 'WebP' | 'JPG' | 'TIFF' | 'PNG' | 'Other';
  customFormat?: string;
  maxResolution: number;
}

interface CollectionMetadata {
  creation_flow?: {
    collection_type?: string;
    model_choice?: string;
    product_count?: number;
    image_count?: string;
    products_per_image?: string;
    campaign_details?: {
      purpose?: string;
      lookAndFeel?: string;
      poses?: string;
      targetGroup?: string;
    };
    imageOutput?: ImageOutputSettings;
  };
  moodboard_paths?: string[];
}

export function CollectionDetail() {
  const { clientId, collectionId, orgId } = useParams<{ clientId?: string; collectionId: string; orgId?: string }>();
  const navigate = useNavigate();
  const isMobile = useMediaQuery('(max-width: 1023px)');
  const { supabase } = useSupabase();
  const { toast } = useToast();
  const { currentOrganization, userMemberships } = useOrganizations();
  const { isPlatformUser, isBrandAdmin } = useUserRole();
  const organizationName = currentOrganization && currentOrganization.id === orgId ? currentOrganization.name : 'Organization';
  
  // Add state for collection brief view
  const [showCollectionBrief, setShowCollectionBrief] = useState<boolean>(false);
  
  // Add state for edit functionality
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editCollectionName, setEditCollectionName] = useState('');
  const [editCollectionDescription, setEditCollectionDescription] = useState('');
  const [isUpdatingCollection, setIsUpdatingCollection] = useState(false);
  const [editCollectionCoverImage, setEditCollectionCoverImage] = useState<File | null>(null);
  
  // Add state for delete confirmation
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Fetch organization data (only if orgId or clientId is provided)
  const { 
    data: organization, 
    isLoading: isLoadingOrganization,
    isError: isOrganizationError 
  } = useOrganization(orgId || clientId);
  
  // Fetch collection data
  const { 
    data: collection, 
    isLoading: isLoadingCollection,
    isError: isCollectionError 
  } = useCollection(collectionId);
  
  // Handle loading state - removed isLoadingAssets
  const isLoading = isLoadingCollection || ((orgId || clientId) ? isLoadingOrganization : false);
  
  // Check if user can edit the collection
  // This must be calculated before any conditional returns
  const canEditCollection = React.useMemo(() => {
    if (isPlatformUser) return true; // Platform users can always edit
    
    if (!collection) return false;
    
    // Check if user is brand_admin and has membership in this organization
    const hasMembership = userMemberships.some(
      m => m.organization_id === collection.organization_id
    );
    
    return isBrandAdmin && hasMembership;
  }, [collection, userMemberships, isPlatformUser, isBrandAdmin]);
  

  // Handle edit dialog open
  const handleEditClick = () => {
    // Use dialog for both organization and client contexts
    if (collection) {
      setEditCollectionName(collection.name);
      setEditCollectionDescription(collection.description || '');
      setEditCollectionCoverImage(null);
      setIsEditDialogOpen(true);
    }
  };

  // Handle file input change
  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setEditCollectionCoverImage(e.target.files[0]);
    }
  };

  // Handle cover image delete
  const handleDeleteCoverImage = async () => {
    if (!collection) return;
    
    setIsUpdatingCollection(true);
    try {
      const { error } = await supabase
        .from('collections')
        .update({
          cover_image_url: null,
          updated_at: new Date().toISOString()
        })
        .eq('id', collection.id);
      
      if (error) throw error;
      
      toast({
        title: "Cover image removed",
        description: "The campaign cover image has been removed.",
      });
      
    } catch (error: any) {
      console.error('Error removing cover image:', error);
      toast({
        title: "Error removing cover image",
        description: error.message || "An error occurred while removing the cover image.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingCollection(false);
    }
  };

  // Handle collection update
  const handleUpdateCollection = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isUpdatingCollection || !collection) return;
    if (!editCollectionName.trim()) {
      toast({
        title: "Error",
        description: "Campaign name is required",
        variant: "destructive",
      });
      return;
    }
    
    setIsUpdatingCollection(true);
    
    try {
      let coverImageUrl = collection.cover_image_url;
      
      // Upload new cover image if provided
      if (editCollectionCoverImage) {
        const fileExt = editCollectionCoverImage.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const filePath = `collection-covers/${fileName}`;
        
        const { error: uploadError } = await supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .upload(filePath, editCollectionCoverImage);
        
        if (uploadError) throw uploadError;
        
        // Get public URL
        const { data: publicUrlData } = supabase.storage
          .from(STORAGE_BUCKETS.GENERAL_UPLOADS.name)
          .getPublicUrl(filePath);
        
        coverImageUrl = publicUrlData.publicUrl;
      }
      
      const { error } = await supabase
        .from('collections')
        .update({
          name: editCollectionName,
          description: editCollectionDescription || null,
          cover_image_url: coverImageUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', collection.id);
      
      if (error) throw error;
      
      toast({
        title: "Campaign updated",
        description: "Campaign information has been updated successfully.",
      });
      
      setIsEditDialogOpen(false);
    } catch (error: any) {
      console.error('Error updating collection:', error);
      toast({
        title: "Error updating campaign",
        description: error.message || "An error occurred while updating the campaign.",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingCollection(false);
    }
  };

  // Handle delete collection
  const handleDeleteCollection = async () => {
    if (!collection || isDeleting) return;
    
    setIsDeleting(true);
    try {
      // First delete all assets associated with this collection
      const { data: assets } = await supabase
        .from('assets')
        .select('id, file_path')
        .eq('collection_id', collection.id);
      
      if (assets && assets.length > 0) {
        // Delete asset files from all storage buckets
        for (const asset of assets) {
          try {
            await deleteAsset(asset.id, collection.id);
          } catch (storageError) {
            console.error('Error deleting asset files:', storageError);
          }
        }
        
        // Delete asset records
        const { error: assetsError } = await supabase
          .from('assets')
          .delete()
          .eq('collection_id', collection.id);
        
        if (assetsError) throw assetsError;
      }
      
      // Delete the collection
      const { error: deleteError } = await supabase
        .from('collections')
        .delete()
        .eq('id', collection.id);
      
      if (deleteError) throw deleteError;
      
      toast({
        title: "Campaign deleted",
        description: "The campaign and all its assets have been removed.",
      });
      
      // Navigate back to client page
      navigate(`/organizations/${clientId}`);
      
    } catch (error: any) {
      console.error('Error deleting collection:', error);
      toast({
        title: "Error deleting campaign",
        description: error.message || "An error occurred while deleting the campaign.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };

  // Ensure collection data matches the expected type
  const collectionData = collection ? {
    id: collection.id,
    name: collection.name,
    description: collection.description,
    status: collection.status,
    created_at: collection.created_at,
    updated_at: collection.updated_at,
    cover_image_url: collection.cover_image_url,
    organization_id: collection.organization_id,
    metadata: (collection.metadata as Json) as CollectionMetadata | undefined
  } : null;

  // Handle error state
  if (isCollectionError || (!isLoading && !collection)) {
    return (
      <div className="space-y-6">
        <div className="py-4 px-6 border-b">
          <h1 className="text-xl font-semibold">Campaign not found</h1>
          <p className="text-muted-foreground">The campaign you're looking for doesn't exist or has been removed.</p>
        </div>
        <div className="px-6">
          <Button onClick={() => navigate(-1)}>Go Back</Button>
        </div>
      </div>
    );
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-6">
          <Skeleton className="h-8 w-64 mb-4" />
          <Skeleton className="h-64 w-full rounded-lg" />
        </div>
      </div>
    );
  }

  // Determine context for display
  const contextName = orgId ? organizationName : (organization ? organization.name : '');

  // Handle asset upload
  const handleUploadClick = () => {
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}/upload`);
    } else if (clientId) {
      navigate(`/organizations/${clientId}/collections/${collectionId}/upload`);
    }
  };

  // Handle compare view navigation
  const handleCompareClick = () => {
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}/compare`);
    } else if (clientId) {
      navigate(`/organizations/${clientId}/collections/${collectionId}/compare`);
    }
  };

  // Handle generate navigation
  const handleGenerateClick = () => {
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/${collectionId}/generate`);
    } else if (clientId) {
      navigate(`/organizations/${clientId}/collections/${collectionId}/generate`);
    }
  };

  return (
    <FilterProvider>
      <div className="flex flex-col h-full">
        {/* Enhanced header with gradient background */}
        <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 text-white">
          <div className="px-4 md:px-6 py-6 md:py-8">
            {/* Breadcrumbs - Hidden on mobile */}
            <nav className="hidden md:flex items-center text-sm text-gray-300 mb-4">
              <Link to="/dashboard" className="hover:text-white transition-colors">
                Home
              </Link>
              <ChevronRight size={14} className="mx-2" />
              <Link 
                to={`/organizations/${orgId || clientId}`}
                className="hover:text-white transition-colors"
              >
                {contextName}
              </Link>
              <ChevronRight size={14} className="mx-2" />
              <span className="text-white font-medium">{collection?.name || 'Campaign'}</span>
            </nav>
            
            {/* Title and actions */}
            <div className="flex flex-col lg:flex-row lg:items-end justify-between gap-4">
              <div className="flex-1">
                <h1 className="text-2xl md:text-3xl font-bold mb-2 leading-tight">{collection?.name || 'Campaign'}</h1>
                <p className="text-gray-300">Campaign from {contextName}</p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                {isFeatureEnabled('ENHANCED_COLLECTION_BRIEF') && (
                  <Button
                    variant="secondary"
                    size="lg"
                    onClick={() => setShowCollectionBrief(!showCollectionBrief)}
                    className="bg-white/10 hover:bg-white/20 text-white border-white/20 w-full sm:w-auto"
                  >
                    <span className="hidden sm:inline">{showCollectionBrief ? 'Show Assets' : 'Campaign Brief'}</span>
                    <span className="sm:hidden">{showCollectionBrief ? 'Assets' : 'Brief'}</span>
                  </Button>
                )}
                {isFeatureEnabled('ASSET_COMPARE_VIEW') && (
                  <Button
                    variant="secondary"
                    size="lg"
                    onClick={handleCompareClick}
                    className="bg-white/10 hover:bg-white/20 text-white border-white/20 w-full sm:w-auto"
                  >
                    <BarChart3 size={18} className="mr-2" />
                    <span className="hidden sm:inline">Compare View</span>
                    <span className="sm:hidden">Compare</span>
                  </Button>
                )}
                <Button
                  size="lg"
                  onClick={handleGenerateClick}
                  className="bg-purple-600 hover:bg-purple-700 text-white w-full sm:w-auto"
                >
                  <Sparkles size={18} className="mr-2" />
                  <span className="hidden sm:inline">Generate AI</span>
                  <span className="sm:hidden">AI</span>
                </Button>
                {canEditCollection && (
                  <Button variant="outline" size="lg" onClick={handleEditClick} className="bg-white/5 hover:bg-white/10 text-white border-white/30 w-full sm:w-auto">
                    <Edit size={18} className="mr-2" />
                    <span className="hidden sm:inline">Edit</span>
                    <span className="sm:hidden">Edit</span>
                  </Button>
                )}
                {canEditCollection && (
                  <Button 
                    variant="outline" 
                    size="lg" 
                    onClick={() => navigate(`/organizations/${orgId || clientId}/bulk-upload`)} 
                    className="bg-white/5 hover:bg-white/10 text-white border-white/30 w-full sm:w-auto"
                  >
                    <Upload size={18} className="mr-2" />
                    <span className="hidden sm:inline">Bulk Upload</span>
                    <span className="sm:hidden">Bulk</span>
                  </Button>
                )}
                <Button size="lg" onClick={handleUploadClick} className="bg-blue-600 hover:bg-blue-700 text-white w-full sm:w-auto">
                  <span className="hidden sm:inline">Upload Assets</span>
                  <span className="sm:hidden">Upload</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {isFeatureEnabled('ENHANCED_COLLECTION_BRIEF') && showCollectionBrief ? (
            // Collection Brief View
            <div className="flex-1 overflow-auto p-4 md:p-6">
              <CollectionBriefDisplay collection={collectionData} />
            </div>
          ) : (
            // Assets View
            <div className="flex flex-1 overflow-hidden relative">
              <FilterSidebar 
                collectionId={collectionId}
                isMobile={isMobile}
              />
              
              <div className={`overflow-auto p-4 md:p-6 ${isMobile ? 'w-full' : 'flex-1'}`}>
                <CollectionAssets 
                  collectionId={collectionId || ''} 
                  clientId={clientId}
                  orgId={orgId}
                  onAssetClick={(assetId) => {
                    if (orgId) {
                      navigate(`/organizations/${orgId}/collections/${collectionId}/assets/${assetId}`);
                    } else if (clientId) {
                      navigate(`/organizations/${clientId}/collections/${collectionId}/assets/${assetId}`);
                    }
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Edit Campaign Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Edit Campaign</DialogTitle>
              <DialogDescription>
                Update campaign information. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleUpdateCollection}>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-name" className="required">Campaign Name</Label>
                  <Input
                    id="edit-name"
                    placeholder="Enter campaign name"
                    value={editCollectionName}
                    onChange={(e) => setEditCollectionName(e.target.value)}
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    placeholder="Enter campaign description"
                    value={editCollectionDescription}
                    onChange={(e) => setEditCollectionDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="edit-cover">Cover Image</Label>
                  <Input
                    id="edit-cover"
                    type="file"
                    accept="image/*"
                    onChange={handleCoverImageChange}
                  />
                  {collection?.cover_image_url && !editCollectionCoverImage && (
                    <div className="mt-2">
                      <div className="relative">
                        <img 
                          src={collection.cover_image_url} 
                          alt="Current cover" 
                          className="w-full h-32 object-cover rounded-md"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={handleDeleteCoverImage}
                          disabled={isUpdatingCollection}
                        >
                          Remove
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Current cover image
                      </p>
                    </div>
                  )}
                  {editCollectionCoverImage && (
                    <div className="mt-2">
                      <img 
                        src={URL.createObjectURL(editCollectionCoverImage)} 
                        alt="New cover preview" 
                        className="w-full h-32 object-cover rounded-md"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        New cover image preview
                      </p>
                    </div>
                  )}
                </div>
              </div>
              <DialogFooter className="gap-2">
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="mr-auto"
                >
                  <Trash2 size={16} className="mr-2" />
                  Delete Campaign
                </Button>
                
                <Button type="submit" disabled={isUpdatingCollection}>
                  {isUpdatingCollection ? 'Saving...' : 'Save Changes'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Campaign</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this campaign? This action cannot be undone and will remove all associated assets.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteCollection}
                disabled={isDeleting}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {isDeleting ? 'Deleting...' : 'Delete Campaign'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      </div>
    </FilterProvider>
  );
}

export default CollectionDetail;