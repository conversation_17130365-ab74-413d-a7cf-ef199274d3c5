import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useOrganizations } from '../contexts/OrganizationContext';
import PageTitle from '../components/ui/PageTitle';
import { Skeleton } from '../components/ui/skeleton';
import CollectionList from '../pages/clients/CollectionList'; // Reuse existing list component
import { useCollections } from '../components/common/hooks/useCollections';    // Reuse existing hook
import { useProducts } from '../components/common/hooks/useProducts'; // Needed for product counts
import { Button } from '../components/ui/button';
import { Plus, Folder } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { useToast } from '../components/ui/use-toast';
import { useFreelancerStatus } from '../components/common/hooks/useIsFreelancer';


// Define the collection type locally for mapping needed by CollectionList
interface CollectionType {
  id: string;
  name: string;
  description: string;
  cover_image_url?: string | null;
  productCount: number;
  status: string;
  lastUpdated: string;
}

export function OrganizationCollections() {
  const { orgId } = useParams<{ orgId: string }>();
  const navigate = useNavigate();
  const { currentOrganization, switchOrganization, isLoading: isLoadingOrgContext, error: orgError } = useOrganizations();
  const { toast } = useToast(); // Import toast
  const { canCreateCollections } = useFreelancerStatus();


  // Ensure the context matches the URL parameter on load/change
  useEffect(() => {
    if (!isLoadingOrgContext && orgId && (!currentOrganization || currentOrganization.id !== orgId)) {
      console.log(`OrganizationCollections: URL orgId ${orgId} doesn't match context ${currentOrganization?.id}. Switching context.`);
      switchOrganization(orgId); // Ensure context is switched if needed
    }
  }, [orgId, currentOrganization, isLoadingOrgContext, switchOrganization]);

  // Fetch collections for the CURRENT organization from context
  const {
    data: collectionsData = [],
    isLoading: isLoadingCollections,
    isError: isCollectionsError,
    error: collectionsFetchError,
    refetch: refetchCollections
  } = useCollections({
    enabled: !!currentOrganization && currentOrganization.id === orgId, // Only fetch if context matches URL
    organizationId: currentOrganization?.id // Now we can pass organizationId to the hook
  });

  // Fetch products (needed for product count in CollectionList, might need optimization later)
   const { data: products = [], isLoading: isLoadingProducts } = useProducts();

   // Transform collections data for the CollectionList component
   // Ensure collectionsData is not null before mapping
    const collectionsForList: CollectionType[] = collectionsData?.map((collection: any) => ({
        id: collection.id,
        name: collection.name,
        description: collection.description || '',
        cover_image_url: collection.cover_image_url,
        // Calculate product count safely
        productCount: products?.filter((p: any) => p.collection_id === collection.id).length ?? 0,
        status: collection.status ? collection.status.charAt(0).toUpperCase() + collection.status.slice(1) : 'Unknown',
        lastUpdated: collection.updated_at
            ? new Date(collection.updated_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
            : 'Never',
    })) ?? []; // Fallback to empty array if collectionsData is null/undefined


  const isLoading = isLoadingOrgContext || isLoadingCollections || isLoadingProducts;

  const handleSelectCollection = (collectionId: string) => {
    if (!orgId) return; // Guard against missing orgId
    navigate(`/organizations/${orgId}/collections/${collectionId}`);
  };

  const handleAddCollection = () => {
    if (orgId) {
      navigate(`/organizations/${orgId}/collections/new`);
    } else {
      toast({ title: "Error", description: "Cannot determine organization.", variant: "destructive" });
    }
  };

  // Display loading skeleton
  if (isLoading || !currentOrganization || currentOrganization.id !== orgId) {
     return (
        <div className="space-y-6">
          <Skeleton className="h-10 w-1/3 mb-2" /> {/* Adjusted size */}
          <Skeleton className="h-5 w-2/3" /> {/* Adjusted size */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
             {[1, 2, 3].map((i) => <Skeleton key={i} className="h-60 rounded-lg" />)} {/* Adjusted size */}
          </div>
        </div>
     );
   }

   // Display error state
   if (orgError || isCollectionsError) {
       return (
           <Card className="bg-destructive/10 border-destructive text-destructive-foreground">
               <CardHeader><CardTitle>Error Loading Organization Data</CardTitle></CardHeader>
               <CardContent>
                   <p>{orgError || collectionsFetchError?.message || 'An unknown error occurred.'}</p>
                   <Button onClick={() => window.location.reload()} variant="secondary" className="mt-4">Retry</Button>
               </CardContent>
           </Card>
       );
   }


  return (
    <div className="space-y-6">
      <PageTitle
        title={currentOrganization?.name || 'Organization'}
        subtitle="Collections"
        breadcrumbs={[
             { label: 'Organizations', href: '/organizations' },
             { label: currentOrganization?.name || '...', href: `/organizations/${orgId}/collections` }
        ]}
         action={
            canCreateCollections && (
              <Button onClick={handleAddCollection} size="sm">
                <Plus size={16} className="mr-2" />
                Add Collection
              </Button>
            )
          }
      />

      {/* Reuse CollectionList, passing the filtered collections */}
      <CollectionList
        collections={collectionsForList}
        onSelectCollection={handleSelectCollection}
        onAddCollection={handleAddCollection} // Pass handler
        isLoading={isLoadingCollections}
        onCollectionUpdated={refetchCollections} // Pass refetch
        canCreateCollections={canCreateCollections} // Pass permission
      />
    </div>
  );
}

export default OrganizationCollections; 