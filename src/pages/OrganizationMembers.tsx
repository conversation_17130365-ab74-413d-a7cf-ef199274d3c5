import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useOrganizations } from '../contexts/OrganizationContext';
import OrganizationMembersTable from '../components/organizations/OrganizationMembersTable';
import { Button } from '../components/ui/button';
import { ArrowLeft, UserPlus } from 'lucide-react';
import { useUserRole } from '../contexts/UserRoleContext';
import { PageTitle } from '../components/ui/PageTitle';
import { Input } from '../components/ui/input';
import { Database } from '../components/common/types/database.types';
import PendingInvitationsTable from '../components/organizations/PendingInvitationsTable';
import InviteMemberForm from '../components/organizations/InviteMemberForm';
import { Skeleton } from '../components/ui/skeleton';
import { useFreelancerStatus } from '../components/common/hooks/useIsFreelancer';

export function OrganizationMembers() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentOrganization, userMemberships, isLoading, error, switchOrganization } = useOrganizations();
  const { isPlatformUser } = useUserRole();
  const { canInviteUsers } = useFreelancerStatus();
  const [searchTerm, setSearchTerm] = useState('');
  
  // Refresh trigger to reload data after inviting members
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const refreshData = () => setRefreshTrigger(prev => prev + 1);

  // Ensure id is available
  const organizationId = id || '';

  // Ensure the context matches the URL parameter on load/change
  useEffect(() => {
    if (!isLoading && organizationId && (!currentOrganization || currentOrganization.id !== organizationId)) {
      console.log(`OrganizationMembers: URL organizationId ${organizationId} doesn't match context ${currentOrganization?.id}. Switching context.`);
      switchOrganization(organizationId); // Ensure context is switched if needed
    }
  }, [organizationId, currentOrganization, isLoading, switchOrganization]);

  // Add debugging for organization role
  useEffect(() => {
    console.log('Organization members page - role status:', {
      organizationId,
      isPlatformUser,
      currentOrganizationId: currentOrganization?.id,
      currentOrganizationName: currentOrganization?.name
    });
  }, [organizationId, isPlatformUser, currentOrganization]);
  
  // Add effect to handle edge cases - verify the organization is valid
  useEffect(() => {
    if (!isLoading && organizationId) {
      // If we're done loading but can't find this organization in our memberships
      const isValidOrg = isPlatformUser || 
                        userMemberships.some(m => m.organization_id === organizationId);
      
      if (!isValidOrg) {
        console.warn(`Organization with ID ${organizationId} not found in user's memberships`);
        navigate('/organizations');
      }
    }
  }, [organizationId, isLoading, userMemberships, isPlatformUser, navigate]);
  
  // Add access control for freelancers
  useEffect(() => {
    if (!isLoading && !canInviteUsers) {
      console.log('Access denied: Freelancers cannot manage organization members');
      navigate(`/organizations/${organizationId}`);
    }
  }, [isLoading, canInviteUsers, organizationId, navigate]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Determine if the user has permission to manage members  
  const hasManagePermission = isPlatformUser;

  // Show error state if something went wrong
  if (error) {
    return (
      <div className="container my-6 py-12 text-center">
        <h2 className="text-2xl font-bold">Error Loading Brand Data</h2>
        <p className="text-muted-foreground mt-2">{error}</p>
        <Button className="mt-6" onClick={() => navigate('/organizations')}>
          Back to Brands
        </Button>
      </div>
    );
  }

  // Enhance loading state with skeletons for better UX
  if (isLoading) {
    return (
      <div className="container my-6 space-y-6">
        <div className="flex items-center mb-6">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="ml-4">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-40 mt-2" />
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-10 w-40" />
        </div>
        
        <Skeleton className="h-12 w-full" />
        
        <div className="space-y-4">
          <div className="flex justify-between mb-4">
            <Skeleton className="h-6 w-24" />
          </div>
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      </div>
    );
  }

  if (!currentOrganization) {
    return (
      <div className="container my-6 py-12 text-center">
        <h2 className="text-2xl font-bold">Brand Not Found</h2>
        <p className="text-muted-foreground mt-2">The brand you're looking for doesn't exist or you don't have access to it.</p>
        <Button className="mt-6" onClick={() => navigate('/organizations')}>
          Back to Brands
        </Button>
      </div>
    );
  }

  return (
    <div className="container my-6 space-y-6">
      <div className="flex items-center mb-6">
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={() => navigate(`/organizations/${organizationId}`)} 
          className="mr-2"
        >
          <ArrowLeft size={18} />
        </Button>
        <PageTitle
          title={`${currentOrganization.name} Members`}
          subtitle="Manage users with access to this brand"
        />
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="w-full md:w-80">
          <Input
            placeholder="Search members by name or email..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>
        
        {hasManagePermission && (
          <InviteMemberForm 
            organizationId={organizationId} 
            organizationName={currentOrganization.name}
            onInviteSent={refreshData}
            triggerButton={
              <Button size="lg" className="min-w-[180px]">
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Members
              </Button>
            }
          />
        )}
      </div>

      {/* All Members Table */}
      <div className="space-y-8">
        <OrganizationMembersTable 
          organizationId={organizationId}
          allowInvite={hasManagePermission}
          key={`all-${refreshTrigger}`}
        />
        
        {/* Spacing between tables */}
        <div className="h-8 border-t pt-8"></div>
        
        {/* Pending Invitations Table */}
        <PendingInvitationsTable 
          organizationId={organizationId} 
          key={`pending-${refreshTrigger}`}
        />
      </div>
    </div>
  );
}

export default OrganizationMembers;