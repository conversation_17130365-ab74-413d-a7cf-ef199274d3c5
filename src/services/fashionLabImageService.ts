import { supabase } from '../components/common/utils/supabase';
import { uploadAssetWithCompression } from '../components/common/utils/assetStorage';
import { v4 as uuidv4 } from 'uuid';
import type { Database } from '../components/common/types/database.types';

type AIGeneratedImage = Database['public']['Tables']['ai_generated_images']['Row'];

interface GenerateImagesOptions {
  prompt: string;
  faceImage: string; // base64
  image2: string; // base64
  image3: string; // base64
  image4: string; // base64
  collectionId: string;
  storeOnCompletion?: boolean;
  metadata?: Record<string, unknown>;
  // Seed values for reproducible generation (optional)
  seed1?: number | null;
  seed2?: number | null;
  seed3?: number | null;
  seed4?: number | null;
  // Number of images to generate (optional, defaults to 1)
  numImages?: number;
}

interface QueueStatusResponse {
  queue_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  images?: string[];
  error?: string;
  stored?: boolean;
  // Seed values returned by API (if available)
  seed1?: number;
  seed2?: number;
  seed3?: number;
  seed4?: number;
}

export class FashionLabImageService {
  /**
   * Generate images using Fashion Lab API V2 (image-based)
   * Uses face image + 3 reference images
   * Returns a queue_id for tracking progress
   */
  static async generateImages(options: GenerateImagesOptions): Promise<{ queue_id: string }> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('No active session');

    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/generate-images`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: options.prompt,
          face_image: options.faceImage,
          image_2: options.image2,
          image_3: options.image3,
          image_4: options.image4,
          collection_id: options.collectionId,
          store_on_completion: options.storeOnCompletion ?? true,
          metadata: options.metadata,
          // Include seed values if provided
          ...(options.seed1 !== undefined && options.seed1 !== null && { seed1: options.seed1 }),
          ...(options.seed2 !== undefined && options.seed2 !== null && { seed2: options.seed2 }),
          ...(options.seed3 !== undefined && options.seed3 !== null && { seed3: options.seed3 }),
          ...(options.seed4 !== undefined && options.seed4 !== null && { seed4: options.seed4 }),
          // Include number of images if provided
          ...(options.numImages !== undefined && options.numImages !== null && { num_images: options.numImages }),
        }),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to generate images: ${error}`);
    }

    const result = await response.json();
    return { queue_id: result.queue_id };
  }

  /**
   * Check generation status and optionally store images
   * If store_images is true, downloads and stores in Supabase Storage
   */
  static async checkQueueStatus(
    queueId: string,
    collectionId?: string,
    storeImages: boolean = true,
    prompt?: string,
    metadata?: Record<string, unknown>
  ): Promise<QueueStatusResponse> {
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('No active session');

    const response = await fetch(
      `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/queue-status`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          queue_id: queueId,
          collection_id: collectionId,
          store_images: storeImages && collectionId ? true : false,
          prompt: prompt,
          metadata: metadata,
        }),
      }
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to check queue status: ${error}`);
    }

    return response.json();
  }

  /**
   * Poll for completion with automatic image storage
   */
  static async waitForCompletion(
    queueId: string,
    collectionId: string,
    options: {
      maxAttempts?: number;
      pollInterval?: number;
      onProgress?: (progress: number) => void;
      prompt?: string;
      metadata?: Record<string, unknown>;
    } = {}
  ): Promise<QueueStatusResponse> {
    const maxAttempts = options.maxAttempts ?? 60; // 5 minutes max
    const pollInterval = options.pollInterval ?? 5000; // 5 seconds
    
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const status = await this.checkQueueStatus(queueId, collectionId, true, options.prompt, options.metadata);
      
      if (options.onProgress) {
        options.onProgress(status.progress);
      }
      
      if (status.status === 'completed' || status.status === 'failed') {
        return status;
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
    
    throw new Error('Timeout waiting for image generation');
  }

  /**
   * Get AI-generated images for a collection (not yet selected)
   */
  static async getGeneratedImages(collectionId: string, selectedOnly: boolean = false) {
    let query = supabase
      .from('ai_generated_images')
      .select('*')
      .eq('collection_id', collectionId);
    
    if (selectedOnly) {
      query = query.eq('selected', true);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  /**
   * Get images by queue ID
   */
  static async getImagesByQueueId(queueId: string) {
    const { data, error } = await supabase
      .from('ai_generated_images')
      .select('*')
      .eq('queue_id', queueId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  }

  /**
   * Download AI-generated image from ai-generated bucket and convert to File
   */
  private static async downloadAIImageAsFile(storagePath: string, fileName: string): Promise<File> {
    const { data, error } = await supabase.storage
      .from('ai-generated')
      .download(storagePath);

    if (error) throw new Error(`Failed to download AI image: ${error.message}`);
    if (!data) throw new Error('No data returned from download');

    // Convert blob to File
    return new File([data], fileName, { type: 'image/png' });
  }

  /**
   * Process AI-generated image through standard compression pipeline
   */
  private static async processAIImageThroughPipeline(
    image: AIGeneratedImage,
    collectionId: string
  ): Promise<{
    assetId: string;
    originalPath: string;
    compressedPath: string;
    thumbnailPath: string;
  }> {
    // Generate new asset ID
    const assetId = uuidv4();

    // Download the AI-generated image as a File
    const fileName = (image.metadata as any)?.file_name || `ai_generated_${image.id}.png`;
    const imageFile = await this.downloadAIImageAsFile(image.storage_path, fileName);

    // Process through compression pipeline
    const compressionResult = await uploadAssetWithCompression(
      imageFile,
      collectionId,
      assetId,
      (stage, progress) => {
        console.log(`Processing AI image ${image.id}: ${stage} - ${progress}%`);
      }
    );

    if (!compressionResult) {
      throw new Error('Compression pipeline returned null');
    }

    return {
      assetId,
      ...compressionResult
    };
  }

  /**
   * Mark AI-generated images as selected and add them to the collection
   * Now processes images through standard compression pipeline
   */
  static async selectGeneratedImages(imageIds: string[], collectionId: string) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('No authenticated user');

    // Update ai_generated_images to mark as selected
    const { error: updateError } = await supabase
      .from('ai_generated_images')
      .update({
        selected: true,
        selected_at: new Date().toISOString(),
        selected_by: user.id,
      })
      .in('id', imageIds)
      .eq('collection_id', collectionId);
    
    if (updateError) throw updateError;

    // Get the selected images
    const { data: selectedImages, error: fetchError } = await supabase
      .from('ai_generated_images')
      .select('*')
      .in('id', imageIds);
    
    if (fetchError) throw fetchError;

    // Process each image through compression pipeline and create asset records
    const assetPromises = selectedImages.map(async (image) => {
      try {
        // Process AI image through standard compression pipeline
        const processedImage = await this.processAIImageThroughPipeline(image, collectionId);

        // Create base asset data using processed paths
        const baseAssetData = {
          id: processedImage.assetId,
          collection_id: image.collection_id,
          file_name: (image.metadata as any)?.file_name || `ai_generated_${image.id}.png`,
          file_type: 'image/png',
          file_path: processedImage.compressedPath, // Use compressed version as main path
          original_path: processedImage.originalPath, // Store original in standard bucket
          compressed_path: processedImage.compressedPath, // Store compressed in standard bucket
          thumbnail_path: processedImage.thumbnailPath, // Store thumbnail in standard bucket
          file_size: (image.metadata as any)?.file_size || 0,
          workflow_stage: 'raw_ai_images' as const,
          metadata: {
            ...(typeof image.metadata === 'object' && image.metadata !== null ? image.metadata : {}),
            ai_generated_image_id: image.id,
            prompt: image.prompt,
            // Add processing info
            processed_from_ai_bucket: true,
            original_ai_storage_path: image.storage_path,
          },
        };

        // Add optional fields that may not exist in all environments
        const optionalFields: Partial<Database['public']['Tables']['assets']['Insert']> = {};
        if (image.user_id) optionalFields.created_by = image.user_id;
        if (image.queue_id) optionalFields.generation_queue_id = image.queue_id;
        if (image.prompt) optionalFields.generation_prompt = image.prompt;
        optionalFields.generation_model = 'fashion-lab-v2';
        if (image.metadata) optionalFields.generation_metadata = image.metadata;
        optionalFields.tags = ['ai-generated', 'fashion-lab'];

        const { error } = await supabase
          .from('assets')
          .insert({
            ...baseAssetData,
            ...optionalFields
          });

        if (error) throw error;

        console.log(`Successfully processed and stored AI image ${image.id} as asset ${processedImage.assetId}`);

      } catch (error) {
        console.error(`Failed to process AI image ${image.id}:`, error);
        throw error;
      }
    });

    await Promise.all(assetPromises);
    
    return selectedImages;
  }
}