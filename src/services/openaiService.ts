interface GarmentAnalysis {
  description: string;
  colors: string[];
  style: string;
  category: string;
}

class OpenAIService {
  private apiKey: string | null = null;
  private maxImageSize = 20 * 1024 * 1024; // 20MB limit for OpenAI

  constructor() {
    // API key will be fetched from environment or Supabase secrets
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || null;
    if (this.apiKey) {
      console.log('OpenAI API key configured');
    } else {
      console.log('OpenAI API key not found');
    }
  }

  async analyzeGarmentImage(imageBase64: string): Promise<GarmentAnalysis> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key is not configured. Please add VITE_OPENAI_API_KEY to your .env.local file');
    }

    // Validate base64 format
    if (!imageBase64.startsWith('data:image/')) {
      throw new Error('Invalid image format. Expected base64 data URL starting with "data:image/"');
    }
    
    // Log image info
    const mimeType = imageBase64.substring(5, imageBase64.indexOf(';'));
    const base64Length = imageBase64.length;
    const estimatedSize = (base64Length * 0.75); // Rough estimate of actual file size
    console.log(`Analyzing image: ${mimeType}, size: ${Math.round(estimatedSize / 1024)}KB`);
    
    // Check size limit
    if (estimatedSize > this.maxImageSize) {
      throw new Error(`Image too large (${Math.round(estimatedSize / 1024 / 1024)}MB). Maximum size is 20MB. Please use a smaller image.`);
    }
    
    // Validate supported formats
    const supportedFormats = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!supportedFormats.some(format => mimeType.includes(format))) {
      throw new Error(`Unsupported image format: ${mimeType}. Please use JPEG, PNG, WebP, or GIF.`);
    }
    
    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // Using gpt-4o-mini which supports vision
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: 'Analyze this garment image and provide a concise description (max 15 words) for use in fashion AI image generation. Focus on: color, material/texture, style/cut, and distinctive features. Format your response as: "[color] [material] [garment type] with [distinctive features]". Example: "black leather biker jacket with silver zippers and cropped fit"'
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: imageBase64,
                    detail: 'high' // Using high detail for better accuracy
                  }
                }
              ]
            }
          ],
          max_tokens: 100,
          temperature: 0.3
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('OpenAI API error:', errorData);
        throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      console.log('OpenAI API response:', JSON.stringify(data, null, 2));
      
      const description = data.choices[0]?.message?.content?.trim() || '';
      console.log('Extracted description:', description);
      
      // Check if OpenAI couldn't analyze the image
      if (description.toLowerCase().includes("can't analyze") || 
          description.toLowerCase().includes("cannot analyze") ||
          description.toLowerCase().includes("unable to analyze") ||
          description.toLowerCase().includes("i'm unable to") ||
          description.toLowerCase().includes("i cannot")) {
        console.error('OpenAI unable to analyze image. Response:', description);
        throw new Error(`OpenAI vision analysis failed: ${description}`);
      }
      
      if (!description) {
        throw new Error('OpenAI returned empty description');
      }

      // Parse the description to extract structured data
      const analysis: GarmentAnalysis = {
        description,
        colors: this.extractColors(description),
        style: this.extractStyle(description),
        category: this.extractCategory(description)
      };

      return analysis;
    } catch (error) {
      console.error('Error analyzing garment with OpenAI:', error);
      throw error;
    }
  }

  private extractColors(description: string): string[] {
    const colorKeywords = [
      'black', 'white', 'red', 'blue', 'green', 'yellow', 'purple', 'pink', 
      'orange', 'brown', 'gray', 'grey', 'beige', 'cream', 'navy', 'teal',
      'burgundy', 'olive', 'coral', 'gold', 'silver', 'denim', 'khaki'
    ];
    
    const foundColors = colorKeywords.filter(color => 
      description.toLowerCase().includes(color)
    );
    
    return foundColors.length > 0 ? foundColors : ['neutral'];
  }

  private extractStyle(description: string): string {
    const styleKeywords = {
      casual: ['casual', 'relaxed', 'comfortable', 'everyday'],
      formal: ['formal', 'elegant', 'sophisticated', 'business'],
      sporty: ['sporty', 'athletic', 'active', 'performance'],
      vintage: ['vintage', 'retro', 'classic', 'antique'],
      modern: ['modern', 'contemporary', 'sleek', 'minimalist'],
      bohemian: ['bohemian', 'boho', 'flowing', 'artistic']
    };

    for (const [style, keywords] of Object.entries(styleKeywords)) {
      if (keywords.some(keyword => description.toLowerCase().includes(keyword))) {
        return style;
      }
    }
    
    return 'contemporary';
  }

  private extractCategory(description: string): string {
    const categories = {
      'jacket': ['jacket', 'blazer', 'coat', 'bomber', 'parka'],
      'shirt': ['shirt', 'blouse', 'top', 'tee', 't-shirt'],
      'pants': ['pants', 'trousers', 'jeans', 'leggings', 'chinos'],
      'dress': ['dress', 'gown', 'frock'],
      'skirt': ['skirt', 'midi', 'mini', 'maxi'],
      'sweater': ['sweater', 'jumper', 'pullover', 'cardigan', 'knit'],
      'accessory': ['scarf', 'belt', 'hat', 'bag', 'shoes']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => description.toLowerCase().includes(keyword))) {
        return category;
      }
    }
    
    return 'garment';
  }

  private getMockAnalysis(): GarmentAnalysis {
    const mockDescriptions = [
      {
        description: 'black leather biker jacket with silver zippers and fitted silhouette',
        colors: ['black'],
        style: 'modern',
        category: 'jacket'
      },
      {
        description: 'flowing silk scarf with vibrant abstract pattern in multicolor',
        colors: ['multicolor'],
        style: 'bohemian',
        category: 'accessory'
      },
      {
        description: 'vintage denim vest with brass buttons and distressed finish',
        colors: ['blue', 'denim'],
        style: 'vintage',
        category: 'jacket'
      },
      {
        description: 'cream cable knit sweater with oversized fit and chunky texture',
        colors: ['cream'],
        style: 'casual',
        category: 'sweater'
      },
      {
        description: 'elegant champagne silk blouse with pearl buttons and flowing sleeves',
        colors: ['champagne', 'beige'],
        style: 'formal',
        category: 'shirt'
      }
    ];

    return mockDescriptions[Math.floor(Math.random() * mockDescriptions.length)];
  }
}

export const openAIService = new OpenAIService();