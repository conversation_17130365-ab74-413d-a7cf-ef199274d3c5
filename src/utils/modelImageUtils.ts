import { supabase } from '../components/common/utils/supabase';

/**
 * Get the best available preview image for a model
 * Priority: face > half-body-front > full-body-front > any available image
 */
export function getModelPreviewImage(
  modelImageMapping: Record<string, string> | undefined,
  modelCode: string
): string {
  if (!modelImageMapping) {
    return getDefaultModelImage(modelCode);
  }

  // Priority order for preview images
  const priorityOrder = [
    'face',
    'half-body-front', 
    'Half-body Front', // Legacy format
    'full-body-front',
    'Full-height Front', // Legacy format
    'half-body-back',
    'Half-body Back', // Legacy format
    'full-body-back',
    'Full-height Back' // Legacy format
  ];

  // Try each priority option
  for (const angleType of priorityOrder) {
    const imageUrl = modelImageMapping[angleType];
    if (imageUrl) {
      return imageUrl;
    }
  }

  // If no priority images found, use any available image
  const availableImages = Object.values(modelImageMapping);
  if (availableImages.length > 0) {
    return availableImages[0];
  }

  // Final fallback to default image
  return getDefaultModelImage(modelCode);
}

/**
 * Get a default placeholder image for a model
 */
function getDefaultModelImage(modelCode: string): string {
  // Use data URLs for placeholder images to avoid external dependencies
  const defaultImages: Record<string, string> = {
    'S': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2UzZjJmZCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiMxOTc2ZDIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIFM8L3RleHQ+PC9zdmc+',
    'M': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2ZjZTRlYyIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNjMjE4NWIiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIE08L3RleHQ+PC9zdmc+',
    'L': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2U4ZjVlOSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiMzODhlM2MiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIEw8L3RleHQ+PC9zdmc+',
    'XL': 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2ZmZjNlMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNlNjUxMDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk1vZGVsIFhMPC90ZXh0Pjwvc3ZnPg=='
  };

  return defaultImages[modelCode] || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiM2NjY2NjYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
}

/**
 * Get model image URL from storage path
 */
export function getModelImageUrl(storagePath: string | null): string | null {
  if (!storagePath) return null;
  
  // Remove duplicate prefix if it exists
  const cleanPath = storagePath.replace(/^model-library\//, '');
  const { data } = supabase.storage.from('model-library').getPublicUrl(cleanPath);
  return data?.publicUrl || null;
}

/**
 * Check if a model has a face image available
 */
export function modelHasFaceImage(
  modelImageMapping: Record<string, string> | undefined
): boolean {
  if (!modelImageMapping) return false;
  
  return !!(modelImageMapping['face'] || modelImageMapping['Face']);
}

/**
 * Get face image URL for a model
 */
export function getModelFaceImage(
  modelImageMapping: Record<string, string> | undefined
): string | null {
  if (!modelImageMapping) return null;
  
  return modelImageMapping['face'] || modelImageMapping['Face'] || null;
}
