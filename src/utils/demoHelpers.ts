import { supabase } from '../components/common/utils/supabase';

/**
 * Get or create a demo collection for testing image generation
 */
export async function getOrCreateDemoCollection(): Promise<string | null> {
  try {
    // Check if user is authenticated
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.error('User not authenticated');
      return null;
    }

    // Check if user is a platform admin first
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('id', user.id)
      .single();
    
    const isPlatformAdmin = userData?.role === 'platform_admin' || userData?.role === 'platform_super';

    // First, try to find an existing demo collection
    let existingCollectionQuery = supabase
      .from('collections')
      .select('id, organization_id')
      .eq('name', 'Image Generation Demo');
    
    // If not a platform admin, filter by user's organizations
    if (!isPlatformAdmin) {
      const { data: userOrgs } = await supabase
        .from('organization_memberships')
        .select('organization_id')
        .eq('user_id', user.id);
      
      if (userOrgs && userOrgs.length > 0) {
        const orgIds = userOrgs.map(om => om.organization_id);
        existingCollectionQuery = existingCollectionQuery.in('organization_id', orgIds);
      }
    }
    
    const { data: existingCollections, error: fetchError } = await existingCollectionQuery.limit(1);

    if (fetchError) {
      console.error('Error fetching collections:', fetchError);
      return null;
    }

    if (existingCollections && existingCollections.length > 0) {
      return existingCollections[0].id;
    }

    // If no demo collection exists, create one
    let organizationId: string;
    
    if (isPlatformAdmin) {
      // Platform admins can use any organization, let's get the first one
      const { data: orgs, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .limit(1);
      
      if (orgError || !orgs || orgs.length === 0) {
        console.error('No organizations found in the system');
        return null;
      }
      
      organizationId = orgs[0].id;
    } else {
      // Regular users need to have an organization membership
      const { data: memberships, error: membershipError } = await supabase
        .from('organization_memberships')
        .select('organization_id')
        .eq('user_id', user.id)
        .limit(1);

      if (membershipError || !memberships || memberships.length === 0) {
        console.error('No organization found for user');
        return null;
      }

      organizationId = memberships[0].organization_id;
    }

    // Create the demo collection
    const { data: newCollection, error: createError } = await supabase
      .from('collections')
      .insert([
        {
          name: 'Image Generation Demo',
          description: 'Demo collection for testing AI image generation',
          organization_id: organizationId,
          status: 'active',
          metadata: {
            isDemo: true,
            createdFrom: 'ImageGeneratorDemo'
          }
        }
      ])
      .select('id')
      .single();

    if (createError) {
      console.error('Error creating demo collection:', createError);
      return null;
    }

    return newCollection.id;
  } catch (error) {
    console.error('Unexpected error in getOrCreateDemoCollection:', error);
    return null;
  }
}