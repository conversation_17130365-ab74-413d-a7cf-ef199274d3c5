/**
 * Utility functions for image optimization before sending to APIs
 */

/**
 * Compress and resize an image to reduce file size
 * @param file - The image file to compress
 * @param maxWidth - Maximum width (default 1024)
 * @param maxHeight - Maximum height (default 1024)
 * @param quality - JPEG quality (0-1, default 0.8)
 * @returns Promise<string> - Base64 encoded compressed image
 */
export async function compressImage(
  file: File,
  maxWidth: number = 1024,
  maxHeight: number = 1024,
  quality: number = 0.8
): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      const img = new Image();
      
      img.onload = () => {
        // Calculate new dimensions
        let width = img.width;
        let height = img.height;
        
        // Scale down if needed
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          
          if (width > height) {
            width = maxWidth;
            height = width / aspectRatio;
          } else {
            height = maxHeight;
            width = height * aspectRatio;
          }
        }
        
        // Create canvas and draw resized image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;
        
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }
        
        // Use better image smoothing
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        
        // Draw the image
        ctx.drawImage(img, 0, 0, width, height);
        
        // Convert to base64 with compression
        const base64 = canvas.toDataURL('image/jpeg', quality);
        
        // Log the size reduction
        const originalSize = file.size / (1024 * 1024); // MB
        const compressedSize = (base64.length * 0.75) / (1024 * 1024); // Approximate MB
        console.log(`Image compressed: ${originalSize.toFixed(2)}MB → ${compressedSize.toFixed(2)}MB (${Math.round((1 - compressedSize/originalSize) * 100)}% reduction)`);
        
        resolve(base64);
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      
      img.src = e.target?.result as string;
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsDataURL(file);
  });
}

/**
 * Check if an image needs compression based on file size
 * @param file - The image file to check
 * @param maxSizeMB - Maximum file size in MB (default 2)
 * @returns boolean - true if compression is needed
 */
export function needsCompression(file: File, maxSizeMB: number = 2): boolean {
  const fileSizeMB = file.size / (1024 * 1024);
  return fileSizeMB > maxSizeMB;
}

/**
 * Process multiple images for upload
 * @param files - Array of image files
 * @param options - Compression options
 * @returns Promise<string[]> - Array of base64 encoded images
 */
export async function processImagesForUpload(
  files: File[],
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    maxSizeMB?: number;
  } = {}
): Promise<string[]> {
  const {
    maxWidth = 1024,
    maxHeight = 1024,
    quality = 0.8,
    maxSizeMB = 2
  } = options;
  
  const processedImages = await Promise.all(
    files.map(async (file) => {
      if (needsCompression(file, maxSizeMB)) {
        console.log(`Compressing ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
        return compressImage(file, maxWidth, maxHeight, quality);
      } else {
        // If no compression needed, just convert to base64
        return new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      }
    })
  );
  
  return processedImages;
}