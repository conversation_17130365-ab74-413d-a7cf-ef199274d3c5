// Global event emitter for cross-component communication
export const ASSET_UPDATE_EVENT = 'asset-update';

export function emitAssetUpdate(collectionId?: string) {
  const event = new CustomEvent(ASSET_UPDATE_EVENT, { 
    detail: { collectionId, timestamp: Date.now() } 
  });
  window.dispatchEvent(event);
}

export function onAssetUpdate(callback: (event: CustomEvent) => void) {
  window.addEventListener(ASSET_UPDATE_EVENT, callback as EventListener);
  return () => window.removeEventListener(ASSET_UPDATE_EVENT, callback as EventListener);
}