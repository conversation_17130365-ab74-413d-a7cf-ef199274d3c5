// Feature flags configuration
export const FEATURES = {
  ASSET_COMPARE_VIEW: {
    staging: true,
    production: false
  },
  ENHANCED_COLLECTION_BRIEF: {
    staging: true,
    production: false
  },
  // Add more features as needed
} as const;

type FeatureName = keyof typeof FEATURES;

export function isFeatureEnabled(feature: FeatureName): boolean {
  const environment = import.meta.env.VITE_ENVIRONMENT || 'development';
  
  if (environment === 'development') {
    // Enable all features in development
    return true;
  }
  
  const featureConfig = FEATURES[feature];
  return featureConfig[environment as keyof typeof featureConfig] ?? false;
}

// Helper for conditional rendering
export function withFeature<T>(
  feature: FeatureName, 
  component: T, 
  fallback?: T
): T | null {
  return isFeatureEnabled(feature) ? component : (fallback ?? null);
}