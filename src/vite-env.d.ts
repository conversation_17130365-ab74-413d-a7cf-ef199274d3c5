/// <reference types="vite/client" />

// Path aliases
declare module '@/*' {
  const value: any;
  export default value;
}

// Environment variables
interface ImportMetaEnv {
  readonly VITE_SUPABASE_URL: string;
  readonly VITE_SUPABASE_ANON_KEY: string;
  readonly VITE_SUPABASE_SERVICE_ROLE_KEY: string;
  readonly VITE_RESEND_API_KEY: string;
  readonly VITE_EMAIL_FROM: string;
  readonly VITE_STORAGE_CLIENT_ASSETS: string;
  readonly VITE_STORAGE_PRODUCT_ASSETS: string;
  readonly VITE_STORAGE_TEMP_UPLOADS: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
