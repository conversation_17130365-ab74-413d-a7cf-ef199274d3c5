# Frontend Development Guide

This file provides guidance specific to frontend development in the FashionLab project.

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **UI Library**: shadcn/ui (built on Radix UI)
- **Styling**: Tailwind CSS
- **State Management**: React Query (Tanstack Query) + Context API
- **Build Tool**: Vite
- **Testing**: Vitest (unit) + Playwright (E2E)

## Component Organization

- `/components/ui/`: shadcn/ui components (Button, Dialog, etc.)
- `/components/layout/`: App-wide layout components
- `/components/auth/`: Authentication wrappers
- `/components/assets/`: Asset management components
- `/components/clients/`: Client-related components
- `/components/collections/`: Collection-related components
- `/components/products/`: Product-related components
- `/pages/`: Route-level components
- `/contexts/`: React Context providers
- `/hooks/`: Custom React hooks
- `/lib/`: Utility functions and Supabase client
- `/types/`: TypeScript type definitions

## Key Architectural Patterns

### Authentication & Authorization

```tsx
// Protected routes use the ProtectedRoute wrapper
<ProtectedRoute>
  <Dashboard />
</ProtectedRoute>

// Role-specific pages use RoleProtectedRoute
<RoleProtectedRoute allowedRole="admin">
  <AdminDashboard />
</RoleProtectedRoute>
```

### Context Providers

The app uses several context providers to manage state:

- `SupabaseContext`: Manages auth state and session
- `UserRoleContext`: Manages user role fetching and caching
- `OrganizationContext`: Manages organization membership and selection
- `FilterContext`: Manages asset filtering state

### Data Fetching with React Query

Use the custom hooks for data fetching:

```tsx
// Using custom hooks for data fetching
const { data: assets, isLoading } = useAssets(collectionId);
const { data: collections } = useCollections(organizationId);
const { data: products } = useProducts(collectionId);
```

### Terminology in UI

Following the terminology guide:
- Use "Brand" in UI instead of "Organization"
- Use "Campaign" in UI instead of "Collection"
- Keep `organization` and `collection` in code and database

```tsx
// Example of correct terminology usage
<h1>Select Your Brand</h1>
<p>You are a Brand Admin for {brandName}</p>
<h2>Create New Campaign</h2>
```

## Common UI Patterns

### Loading States

Always handle loading states in components:

```tsx
if (isLoading) {
  return <Skeleton className="h-32 w-full" />;
}
```

### Error Handling

Handle errors gracefully with alert components:

```tsx
if (error) {
  return <Alert variant="destructive">{error.message}</Alert>;
}
```

### Forms

Use react-hook-form with zod validation:

```tsx
const form = useForm<FormValues>({
  resolver: zodResolver(formSchema),
  defaultValues: {
    name: "",
    description: "",
  },
});
```

### Modals and Dialogs

Use shadcn/ui components for consistency:

```tsx
<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>Dialog description goes here.</DialogDescription>
    </DialogHeader>
    {/* Dialog content */}
  </DialogContent>
</Dialog>
```

## Asset Management

### Asset URL Generation

Use the `getAssetUrl` utility for asset URLs:

```tsx
const url = getAssetUrl(asset, true); // true for thumbnail
```

### Image Processing

- Images are automatically processed into original, compressed, and thumbnail versions
- Use appropriate size for the context (thumbnails for lists, full size for details)
- Handle loading states for images

## Testing

### Unit Tests (Vitest)

```bash
npm test                # Run unit tests
npm run test:ui         # Run with UI
npm run test:coverage   # Generate coverage report
```

Place unit tests next to the files they test:
```
src/
  components/
    Button.tsx
    Button.test.tsx
```

### E2E Tests (Playwright)

```bash
npm run test:e2e       # Run E2E tests
npm run test:e2e:ui    # Run with UI mode
npm run test:e2e:debug # Debug mode
```

E2E tests are located in the `e2e/` directory:
```
e2e/
  auth.spec.ts
  assets.spec.ts
  campaigns.spec.ts
```

## Best Practices

1. **Component Design**
   - Keep components focused on a single responsibility
   - Use composition over complex conditional rendering
   - Extract reusable logic to custom hooks

2. **Performance**
   - Use React Query for data fetching with caching
   - Implement pagination for large data sets
   - Memoize expensive calculations and renders

3. **Accessibility**
   - Use semantic HTML elements
   - Include proper ARIA attributes
   - Ensure keyboard navigation works
   - Test with screen readers

4. **Responsive Design**
   - Use Tailwind's responsive prefixes (sm:, md:, lg:)
   - Test designs across different viewport sizes
   - Implement mobile-first approach

5. **Code Organization**
   - Follow the established file structure
   - Use TypeScript types from `/types/database.types.ts`
   - Keep components small and focused