import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';

export type WorkflowStage =
  | 'upload'
  | 'raw_ai_images'
  | 'selected'
  | 'refined'
  | 'upscale'
  | 'retouch'
  | 'final'
  | 'brief'
  | 'input'
  | 'custom_models'
  | 'library'
  | 'raw';

export type AssetType = 'product-front' | 'product-back' | 'product-detail' | 'model' | 'lighting' | 'pose';
export type TimeFilter = 'today' | 'week' | 'month' | 'quarter' | 'all';

export interface Filters {
  workflowStages: WorkflowStage[];
  assetTypes: AssetType[];
  tagIds: string[];
  timeUpdated: TimeFilter;
  productIds: string[];
  sizes: string[];
  retouchSubstages: string[];
}

interface FilterContextType {
  filters: Filters;
  setWorkflowStageFilter: (stages: WorkflowStage[]) => void;
  setAssetTypeFilter: (types: AssetType[]) => void;
  setTagFilter: (tagIds: string[]) => void;
  setTimeFilter: (time: TimeFilter) => void;
  setProductFilter: (productIds: string[]) => void;
  setSizeFilter: (sizes: string[]) => void;
  setRetouchSubstageFilter: (substages: string[]) => void;
  clearFilters: () => void;
}

const defaultFilters: Filters = {
  workflowStages: [],
  assetTypes: [],
  tagIds: [],
  timeUpdated: 'all',
  productIds: [],
  sizes: [],
  retouchSubstages: [],
};

const FilterContext = createContext<FilterContextType | undefined>(undefined);

// Helper function to get storage key based on current collection
const getStorageKey = (pathname: string): string | null => {
  // Extract collection ID from pathname
  const match = pathname.match(/collections\/([a-f0-9-]+)/);
  if (match && match[1]) {
    return `filters_collection_${match[1]}`;
  }
  return null;
};

// Helper to load filters from sessionStorage
const loadFiltersFromStorage = (storageKey: string): Filters | null => {
  try {
    const stored = sessionStorage.getItem(storageKey);
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Failed to load filters from storage:', error);
  }
  return null;
};

// Helper to save filters to sessionStorage
const saveFiltersToStorage = (storageKey: string, filters: Filters): void => {
  try {
    sessionStorage.setItem(storageKey, JSON.stringify(filters));
  } catch (error) {
    console.error('Failed to save filters to storage:', error);
  }
};

export const FilterProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const location = useLocation();
  const storageKey = getStorageKey(location.pathname);
  
  // Initialize filters from storage if available
  const [filters, setFilters] = useState<Filters>(() => {
    if (storageKey) {
      const storedFilters = loadFiltersFromStorage(storageKey);
      if (storedFilters) {
        return storedFilters;
      }
    }
    return defaultFilters;
  });

  // Save filters to storage whenever they change
  useEffect(() => {
    if (storageKey) {
      saveFiltersToStorage(storageKey, filters);
    }
  }, [filters, storageKey]);

  // Clear storage when navigating away from collection pages
  useEffect(() => {
    return () => {
      // Only clear if we're navigating away from collection pages entirely
      if (storageKey && !location.pathname.includes('/collections/')) {
        sessionStorage.removeItem(storageKey);
      }
    };
  }, [location.pathname, storageKey]);

  const setWorkflowStageFilter = useCallback((stages: WorkflowStage[]) => {
    setFilters(prev => ({ ...prev, workflowStages: stages }));
  }, []);

  const setAssetTypeFilter = useCallback((types: AssetType[]) => {
    setFilters(prev => ({ ...prev, assetTypes: types }));
  }, []);

  const setTagFilter = useCallback((tagIds: string[]) => {
    setFilters(prev => ({ ...prev, tagIds }));
  }, []);

  const setTimeFilter = useCallback((time: TimeFilter) => {
    setFilters(prev => ({ ...prev, timeUpdated: time }));
  }, []);

  const setProductFilter = useCallback((productIds: string[]) => {
    setFilters(prev => ({ ...prev, productIds }));
  }, []);

  const setSizeFilter = useCallback((sizes: string[]) => {
    setFilters(prev => ({ ...prev, sizes }));
  }, []);

  const setRetouchSubstageFilter = useCallback((retouchSubstages: string[]) => {
    setFilters(prev => ({ ...prev, retouchSubstages }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
    if (storageKey) {
      sessionStorage.removeItem(storageKey);
    }
  }, [storageKey]);

  return (
    <FilterContext.Provider value={{
      filters,
      setWorkflowStageFilter,
      setAssetTypeFilter,
      setTagFilter,
      setTimeFilter,
      setProductFilter,
      setSizeFilter,
      setRetouchSubstageFilter,
      clearFilters
    }}>
      {children}
    </FilterContext.Provider>
  );
};

export const useFilters = () => {
  const context = useContext(FilterContext);
  if (context === undefined) {
    throw new Error('useFilters must be used within a FilterProvider');
  }
  return context;
};
