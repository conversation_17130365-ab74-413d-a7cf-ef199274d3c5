import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSupabase } from './SupabaseContext';
import { supabase } from '../components/common/utils/supabase';

// Define the user role type (unified single role system)
export type UserRole = 'platform_super' | 'platform_admin' | 'brand_admin' | 'brand_member' | 'external_retoucher' | 'external_prompter';

// Define the context type
type UserRoleContextType = {
  userRole: UserRole | null;
  isPlatformSuper: boolean;
  isPlatformAdmin: boolean;
  isBrandAdmin: boolean;
  isBrandMember: boolean;
  isExternalRetoucher: boolean;
  isExternalPrompter: boolean;
  isAdmin: boolean; // helper for any admin level (platform_super, platform_admin, brand_admin)
  isPlatformUser: boolean; // helper for platform level users (platform_super, platform_admin)
  isLoadingRole: boolean;
};

// Create the context
const UserRoleContext = createContext<UserRoleContextType | undefined>(undefined);

// Create the provider component
export const UserRoleProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isLoadingUser } = useSupabase();
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isLoadingRole, setIsLoadingRole] = useState<boolean>(true);

  useEffect(() => {
    const fetchUserRole = async () => {
      if (!user) {
        setUserRole(null);
        setIsLoadingRole(false);
        return;
      }

      console.log(`UserRoleContext: Attempting to fetch role for user: ${user?.id}`);
      
      try {
        // Fetch the user's role from the public.users table
        const { data, error } = await supabase
          .from('users')
          .select('role')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('UserRoleContext: Supabase query error:', error); // Log Supabase specific error
          setUserRole(null);
          setIsLoadingRole(false);
        } else {
          console.log(`UserRoleContext: Successfully fetched role data:`, data); // Log fetched data
          
          // Important: Set both states in the same update to ensure consistency
          const roleValue = data.role as UserRole;
          setUserRole(roleValue);
          setIsLoadingRole(false);
          
          // Log after setting the state, using the fetched value directly
          console.log(`UserRoleContext: Set userRole to: ${roleValue}`);
        }
      } catch (error) {
        console.error('UserRoleContext: Error fetching user role:', error); // Log the actual error object
        setUserRole(null);
        setIsLoadingRole(false);
      }
      // Remove the finally block as we're now setting isLoadingRole in each case
    };

    // Only fetch if user data is loaded
    if (!isLoadingUser) {
      console.log(`UserRoleContext: User loaded, fetching role for: ${user?.id || 'no user'}`);
      fetchUserRole();
    }
  }, [user, isLoadingUser]);

  // Add a new effect to track when userRole changes
  useEffect(() => {
    console.log(`UserRoleContext: Role state updated to: ${userRole}, isLoadingRole: ${isLoadingRole}`);
  }, [userRole, isLoadingRole]);

  // Computed properties for role checks
  const isPlatformSuper = userRole === 'platform_super';
  const isPlatformAdmin = userRole === 'platform_admin';
  const isBrandAdmin = userRole === 'brand_admin';
  const isBrandMember = userRole === 'brand_member';
  const isExternalRetoucher = userRole === 'external_retoucher';
  const isExternalPrompter = userRole === 'external_prompter';
  
  // Helper properties
  const isAdmin = userRole === 'platform_super' || userRole === 'platform_admin' || userRole === 'brand_admin';
  const isPlatformUser = userRole === 'platform_super' || userRole === 'platform_admin';

  return (
    <UserRoleContext.Provider
      value={{
        userRole,
        isPlatformSuper,
        isPlatformAdmin,
        isBrandAdmin,
        isBrandMember,
        isExternalRetoucher,
        isExternalPrompter,
        isAdmin,
        isPlatformUser,
        isLoadingRole
      }}
    >
      {children}
    </UserRoleContext.Provider>
  );
};

// Create a hook to use the user role
export const useUserRole = () => {
  const context = useContext(UserRoleContext);
  if (context === undefined) {
    throw new Error('useUserRole must be used within a UserRoleProvider');
  }
  return context;
}; 