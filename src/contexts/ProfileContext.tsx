import React, { createContext, useContext, useState, useEffect } from 'react'
import { useSupabase } from './SupabaseContext'
import { useSecurityLogging } from '../components/common/hooks/useSecurityLogging'
import { toast } from 'sonner'

interface ProfileData {
  id: string
  email: string
  avatar_url?: string
  first_name?: string
  last_name?: string
  is_freelancer?: boolean
}

interface ProfileContextType {
  profile: ProfileData | null
  loading: boolean
  updateProfile: (updates: Partial<ProfileData>) => Promise<void>
  uploadAvatar: (file: File) => Promise<void>
  deleteAvatar: () => Promise<void>
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>
  refreshProfile: () => Promise<void>
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined)

export const ProfileProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { supabase, user } = useSupabase()
  const { logSecurityEvent } = useSecurityLogging()
  const [profile, setProfile] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchProfile = async () => {
    if (!user) {
      setProfile(null)
      setLoading(false)
      return
    }

    try {
      const { data: profileData, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error) throw error

      setProfile({
        id: user.id,
        email: user.email!,
        avatar_url: profileData?.avatar_url,
        first_name: profileData?.first_name,
        last_name: profileData?.last_name,
        is_freelancer: profileData?.is_freelancer,
      })
    } catch (error) {
      console.error('Error fetching profile:', error)
      toast.error('Failed to load profile')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProfile()
  }, [user])

  const updateProfile = async (updates: Partial<ProfileData>) => {
    if (!user) return

    try {
      console.log('Attempting to update profile for user:', user.id)
      console.log('Updates:', updates)
      
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id)
        .select()

      console.log('Update response:', { data, error })

      if (error) throw error

      setProfile(prev => prev ? { ...prev, ...updates } : null)
      toast.success('Profile updated successfully')
    } catch (error) {
      console.error('Error updating profile - Full details:', error)
      
      if (error instanceof Error) {
        console.error('Error message:', error.message)
        // @ts-ignore - Supabase error might have details property
        if (error.details) {
          console.error('Error details:', error.details)
        }
        // @ts-ignore - Supabase error might have hint property
        if (error.hint) {
          console.error('Error hint:', error.hint)
        }
      }
      
      toast.error('Failed to update profile')
      throw error
    }
  }

  const uploadAvatar = async (file: File) => {
    if (!user) return

    try {
      // Validate file type and size
      if (!file.type.startsWith('image/')) {
        throw new Error('Please upload an image file')
      }

      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size must be less than 5MB')
      }

      // Upload to storage
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/avatar.${fileExt}`

      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(fileName, file, { upsert: true })

      if (uploadError) throw uploadError

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(fileName)

      // Update profile with new avatar URL
      await updateProfile({ avatar_url: publicUrl })
    } catch (error) {
      console.error('Error uploading avatar:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to upload avatar')
      throw error
    }
  }

  const deleteAvatar = async () => {
    if (!user || !profile?.avatar_url) return

    try {
      // Extract filename from URL
      const url = new URL(profile.avatar_url)
      const pathParts = url.pathname.split('/')
      const fileName = pathParts[pathParts.length - 1]
      const filePath = `${user.id}/${fileName}`

      // Delete from storage
      const { error: deleteError } = await supabase.storage
        .from('avatars')
        .remove([filePath])

      if (deleteError) throw deleteError

      // Update profile in database with null
      const { error: updateError } = await supabase
        .from('users')
        .update({ avatar_url: null })
        .eq('id', user.id)

      if (updateError) throw updateError

      // Update local state
      setProfile(prev => prev ? { ...prev, avatar_url: undefined } : null)
      toast.success('Avatar deleted successfully')
    } catch (error) {
      console.error('Error deleting avatar:', error)
      toast.error('Failed to delete avatar')
      throw error
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      // First verify the current password by attempting to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: currentPassword
      })

      if (signInError) {
        toast.error('Current password is incorrect')
        throw new Error('Current password is incorrect')
      }

      // If current password is correct, update to new password
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) throw error

      toast.success('Password updated successfully')
      logSecurityEvent('password_change')
    } catch (error) {
      console.error('Error changing password:', error)
      if (error instanceof Error && error.message !== 'Current password is incorrect') {
        toast.error('Failed to change password')
      }
      throw error
    }
  }

  const refreshProfile = async () => {
    await fetchProfile()
  }

  return (
    <ProfileContext.Provider
      value={{
        profile,
        loading,
        updateProfile,
        uploadAvatar,
        deleteAvatar,
        changePassword,
        refreshProfile,
      }}
    >
      {children}
    </ProfileContext.Provider>
  )
}

export const useProfile = () => {
  const context = useContext(ProfileContext)
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider')
  }
  return context
}