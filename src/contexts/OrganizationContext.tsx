import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { useSupabase } from './SupabaseContext'; // Assuming SupabaseContext exists for user info
import { useUserRole } from './UserRoleContext';
import { Database } from '../components/common/types/database.types';

// Define the shape of an organization (minimal for MVP)
export interface Organization {
    id: string;
    name: string;
}

// Define the shape of a membership (no role needed - role is in users table now)
export interface OrganizationMembership {
    id: string; // Membership ID
    organization_id: string;
    user_id: string;
    organizations: { // Nested organization data
        name: string;
        logo_url: string | null; // Add logo_url property
    } | null; // Nullable if join fails or org deleted
}

// Define the context state
interface OrganizationContextType {
    userMemberships: OrganizationMembership[];
    currentOrganization: Organization | null;
    switchOrganization: (organizationId: string) => void;
    isLoading: boolean;
    error: string | null;
}

// Create the context with a default undefined value
const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

// Define the provider component props
interface OrganizationProviderProps {
    children: ReactNode;
}

// Create the provider component
export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({ children }) => {
    const { user, supabase } = useSupabase();
    const { isPlatformUser, isLoadingRole } = useUserRole();
    const [userMemberships, setUserMemberships] = useState<OrganizationMembership[]>([]);
    const [currentOrganizationId, setCurrentOrganizationId] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    // --- Placeholder for fetching memberships (MVP-FE-002) ---
    useEffect(() => {
        const fetchMemberships = async () => {
            if (!user) {
                setUserMemberships([]);
                setCurrentOrganizationId(null);
                setIsLoading(false);
                setError(null);
                return;
            }

            // Wait for UserRoleContext to finish loading before fetching organizations
            if (isLoadingRole) {
                console.log("OrganizationContext: Waiting for UserRoleContext to load...");
                return;
            }

            setIsLoading(true);
            setError(null);

            try {
                console.log(`OrganizationContext: Fetching organizations for user ${user.id} (isPlatformUser: ${isPlatformUser})`);
                
                // First, verify the user exists in the database
                const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('id, role')
                    .eq('id', user.id)
                    .single();
                
                if (userError) {
                    console.error("OrganizationContext: Error verifying user:", userError);
                } else {
                    console.log("OrganizationContext: User verification successful:", userData);
                }
                
                let validMemberships: OrganizationMembership[] = [];
                
                // For platform users, fetch all organizations
                if (isPlatformUser) {
                    console.log("OrganizationContext: Fetching all organizations for platform user");
                    
                    const { data: allOrgs, error: orgsError } = await supabase
                        .from('organizations')
                        .select('id, name, logo_url');
                    
                    if (orgsError) {
                        console.error("OrganizationContext: Error fetching organizations:", orgsError);
                        throw orgsError;
                    }
                    
                    // Convert organizations to membership format for platform users
                    validMemberships = (allOrgs || []).map(org => ({
                        id: `platform-${org.id}`, // Synthetic membership ID for platform access
                        organization_id: org.id,
                        user_id: user.id,
                        organizations: {
                            name: org.name,
                            logo_url: org.logo_url
                        }
                    }));
                } else {
                    // For regular users, fetch actual memberships
                    console.log("OrganizationContext: Fetching memberships for regular user");
                    
                    const { data, error: fetchError } = await supabase
                        .from('organization_memberships')
                        .select(`
                            id,
                            organization_id,
                            user_id,
                            organizations ( name, logo_url )
                        `)
                        .eq('user_id', user.id);

                    if (fetchError) {
                        console.error("OrganizationContext: Error fetching memberships:", fetchError);
                        throw fetchError;
                    }

                    validMemberships = (data || []) as OrganizationMembership[];
                }

                console.log("OrganizationContext: Fetched organizations/memberships:", validMemberships);
                setUserMemberships(validMemberships);

                // If no organization is currently selected AND memberships were found, select the first one by default.
                // Only set if currentOrganizationId is genuinely null, not just temporarily during fetch.
                if (currentOrganizationId === null && validMemberships.length > 0) {
                    console.log(`OrganizationContext: Auto-selecting first organization: ${validMemberships[0].organization_id}`);
                    setCurrentOrganizationId(validMemberships[0].organization_id);
                } else if (validMemberships.length === 0) {
                     // If user has no memberships, clear the current selection
                     setCurrentOrganizationId(null);
                }


            } catch (err: any) {
                console.error("Caught error in fetchMemberships:", err);
                setError(err.message || 'Failed to fetch organization memberships.');
                setUserMemberships([]);
                 setCurrentOrganizationId(null);
            } finally {
                setIsLoading(false);
            }
        };

        fetchMemberships();
    }, [user, supabase, isPlatformUser, isLoadingRole]);
    // --- End Placeholder ---

    // --- Placeholder for switching logic (MVP-FE-004) ---
    const switchOrganization = useCallback((organizationId: string) => {
        // Check if the organizationId is valid (exists in userMemberships)
        const isValidOrg = userMemberships.some(m => m.organization_id === organizationId);
        if (isValidOrg) {
            console.log(`OrganizationContext: Switching current organization to ID: ${organizationId}`);
            setCurrentOrganizationId(organizationId);
            // Optional: Persist the selection to localStorage for better UX on refresh
            // localStorage.setItem('currentOrganizationId', organizationId);
        } else {
            console.warn(`OrganizationContext: Attempted to switch to invalid organization ID: ${organizationId}`);
            setError(`Cannot switch to organization ID ${organizationId} - not a valid membership.`);
        }
    }, [userMemberships]);
    // --- End Placeholder ---

    // Derive currentOrganization from memberships and currentOrganizationId
    const currentOrganization = React.useMemo(() => {
        const membership = userMemberships.find(m => m.organization_id === currentOrganizationId);
        if (membership && membership.organizations) {
             return { id: membership.organization_id, name: membership.organizations.name };
        }
        // If no match (or ID is null), return null
        return null;
    }, [currentOrganizationId, userMemberships]);


    // Value provided by the context
    const contextValue: OrganizationContextType = {
        userMemberships,
        currentOrganization,
        switchOrganization,
        isLoading,
        error,
    };

    return (
        <OrganizationContext.Provider value={contextValue}>
            {children}
        </OrganizationContext.Provider>
    );
};

// Custom hook to use the Organization context
export const useOrganizations = (): OrganizationContextType => {
    const context = useContext(OrganizationContext);
    if (context === undefined) {
        throw new Error('useOrganizations must be used within an OrganizationProvider');
    }
    return context;
};