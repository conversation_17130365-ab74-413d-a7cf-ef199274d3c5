# FashionLab - AI Fashion Platform

AI-powered fashion imagery platform for multi-tenant brand management.

## Project Context

FashionLab enables fashion brands to generate, manage, and distribute AI-powered product imagery at scale. Built with React, TypeScript, and Supabase.

## Quick Reference

### Project IDs
- **Staging**: `qnfmiotatmkoumlymynq`
- **Production**: `cpelxqvcjnbpnphttzsn`

### Critical Notes
- **Fashion Lab API**: Use `jwt` prefix, not `Bearer` for authentication
- **IPv6 Issues**: Use pooler URLs for database connections
- **File Uploads**: Max 10MB, validate types before upload

## Common Commands

```bash
# Development
npm run dev                          # Start local server
npm run supabase:start              # Start local database
npm run lint && npm run typecheck   # Pre-commit checks

# Testing
npm run test                        # Run tests
npm run test:e2e                    # E2E tests

# Deployment
git push origin main                # Auto-deploy to staging
gh pr create --base production      # Deploy to production
```

## Key Patterns

### Authentication Check
```typescript
const user = await requireAuth(request);
if (!user) return new Response('Unauthorized', { status: 401 });
```

### Database Query with RLS
```typescript
const { data, error } = await supabase
  .from('assets')
  .select('*, collections(*)')
  .eq('organization_id', orgId);
```

### File Upload Pattern
```typescript
// Always validate before upload
await validateUpload(file);
const { data } = await supabase.storage
  .from('assets')
  .upload(path, file);
```

## Active Development

### Current Sprint
@docs/10-reference/current-sprint.md

### Architecture
@docs/01-overview/architecture.md

### API Reference
@docs/04-api/README.md

## Database Operations

### Apply Migration
```bash
# Using MCP tools
mcp__supabase__apply_migration --project_id qnfmiotatmkoumlymynq --name migration_name --query "SQL HERE"
```

### Connect to Database (IPv6 Workaround)
```bash
# Staging
psql "postgresql://postgres.qnfmiotatmkoumlymynq:[PASSWORD]@aws-0-eu-central-1.pooler.supabase.com:5432/postgres"
```

## Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature
   ```

2. **Make Changes**
   - Follow coding standards in @docs/RULES.md
   - Write tests for new features
   - Update documentation

3. **Pre-commit Checks**
   ```bash
   npm run lint
   npm run typecheck
   npm run test
   ```

4. **Create PR**
   ```bash
   gh pr create --title "feat: your feature" --body "Description"
   ```

5. **Update Linear**
   ```bash
   # Use Linear MCP tools
   mcp__linear__updateIssue --id ISSUE-ID --stateId done-state-id
   ```

## Troubleshooting

### Common Issues
- **401 on Fashion Lab API**: Check JWT format (`jwt` not `Bearer`)
- **Database connection failed**: Use pooler URL for IPv6 issues
- **Build failures**: Clear cache with `rm -rf node_modules/.cache`
- **Type errors**: Run `npm run types:generate`

### Debug Mode
```typescript
// Add to .env.local
VITE_DEBUG=true

// In code
if (import.meta.env.VITE_DEBUG) {
  console.log('Debug:', data);
}
```

## Security Reminders

1. **Never commit secrets** - Use environment variables
2. **Validate all inputs** - Especially file uploads
3. **Check permissions** - Use RLS and application-level checks
4. **Sanitize outputs** - Prevent XSS attacks

## MCP Tool Usage

### Linear (Issue Tracking)
- `mcp__linear__getIssues` - List recent issues
- `mcp__linear__updateIssue` - Update issue status
- `mcp__linear__createComment` - Add comment

### Supabase (Database)
- `mcp__supabase__execute_sql` - Run queries
- `mcp__supabase__apply_migration` - Apply migrations
- `mcp__supabase__get_logs` - Check function logs

### IDE
- `mcp__ide__getDiagnostics` - Get code diagnostics

## Resources

- [Documentation](./docs/README.md)
- [Quick Start](./docs/00-quick-start/README.md)
- [API Reference](./docs/04-api/README.md)
- [Security Guide](./docs/07-security/README.md)

## Team Contacts

- **Technical Issues**: Create GitHub issue
- **API Access**: <EMAIL>
- **Security**: <EMAIL>