# Logs
logs/
!logs/vector.yml
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


# Environment files
.env
.env.development
.env.production
.env*.local
.env.db
.env.staging
.vercel
supabase/volumes/db/data

# Database backups
*_backup_*.sql
*_schema_*.sql

# Added by <PERSON> Task Master
logs
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.vscode
# OS specific
# Task files
tasks.json
tasks/

 
# Vero Moda test assets (too large for git)
Vero Moda/

# Claude MCP configuration
.claude/.mcp.json

# Reference/documentation files
blackforest-gen-serverless-main/

# Test outputs
test-image.png
*.test.png

# Temporary files
test-*.md
test-*.sql
test-*.html
*-REPORT.md
*-analysis.md

# Demo directories
*-demo/
*-serverless-main/

# Test scripts
scripts/test-*.sh

# Backup files
*.bak

# Analysis and deployment files
STAGING_DEPLOYMENT_REPORT.md
FASHION_LAB_V2_API_ANALYSIS.md
