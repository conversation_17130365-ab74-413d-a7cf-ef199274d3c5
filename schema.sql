

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."comment_status" AS ENUM (
    'open',
    'resolved'
);


ALTER TYPE "public"."comment_status" OWNER TO "postgres";


CREATE TYPE "public"."permission_level" AS ENUM (
    'view',
    'edit',
    'admin'
);


ALTER TYPE "public"."permission_level" OWNER TO "postgres";


CREATE TYPE "public"."tag_category" AS ENUM (
    'view_type',
    'workflow_stage',
    'product_specific',
    'custom'
);


ALTER TYPE "public"."tag_category" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'client',
    'admin',
    'superadmin'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE TYPE "public"."workflow_stage" AS ENUM (
    'upload',
    'draft',
    'upscale',
    'retouch',
    'final'
);


ALTER TYPE "public"."workflow_stage" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."current_user_role"() RETURNS "text"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT COALESCE(
    (SELECT role::text 
     FROM users 
     WHERE id = auth.uid()),
    'user'
  );
$$;


ALTER FUNCTION "public"."current_user_role"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."fix_all_image_paths"() RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  UPDATE assets
  SET 
    file_name = normalize_path(file_name),
    file_path = normalize_path(file_path)
  WHERE file_name IS DISTINCT FROM normalize_path(file_name)
     OR file_path IS DISTINCT FROM normalize_path(file_path);

  -- Update metadata paths individually for clarity
  UPDATE assets
  SET metadata = jsonb_set(
    metadata,
    '{imageProcessing,originalPath}',
    to_jsonb(normalize_path(metadata->'imageProcessing'->>'originalPath'))
  )
  WHERE metadata->'imageProcessing'->>'originalPath' IS DISTINCT FROM normalize_path(metadata->'imageProcessing'->>'originalPath');

  UPDATE assets
  SET metadata = jsonb_set(
    metadata,
    '{imageProcessing,compressedPath}',
    to_jsonb(normalize_path(metadata->'imageProcessing'->>'compressedPath'))
  )
  WHERE metadata->'imageProcessing'->>'compressedPath' IS DISTINCT FROM normalize_path(metadata->'imageProcessing'->>'compressedPath');

  UPDATE assets
  SET metadata = jsonb_set(
    metadata,
    '{imageProcessing,thumbnailPath}',
    to_jsonb(normalize_path(metadata->'imageProcessing'->>'thumbnailPath'))
  )
  WHERE metadata->'imageProcessing'->>'thumbnailPath' IS DISTINCT FROM normalize_path(metadata->'imageProcessing'->>'thumbnailPath');
END;
$$;


ALTER FUNCTION "public"."fix_all_image_paths"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_user_role"("user_id" "uuid") RETURNS "public"."user_role"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN (
        SELECT role FROM public.users 
        WHERE id = user_id
    );
END;
$$;


ALTER FUNCTION "public"."get_user_role"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."has_collection_access"("collection_id" "uuid", "required_permission" "public"."permission_level" DEFAULT 'view'::"public"."permission_level") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users u
        LEFT JOIN user_collection_access uca ON u.id = uca.user_id
        WHERE u.id = auth.uid() 
        AND (
            u.role IN ('superadmin', 'admin')  -- Admins and Superadmins have access to all collections
            OR (
                u.role = 'client' 
                AND uca.collection_id = collection_id 
                AND (
                    CASE 
                        WHEN required_permission = 'view' THEN true
                        WHEN required_permission = 'edit' THEN uca.permission_level IN ('edit', 'admin')
                        WHEN required_permission = 'admin' THEN uca.permission_level = 'admin'
                    END
                )
            )
        )
    );
END;
$$;


ALTER FUNCTION "public"."has_collection_access"("collection_id" "uuid", "required_permission" "public"."permission_level") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_admin_or_above"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id AND role IN ('admin', 'superadmin')
    );
END;
$$;


ALTER FUNCTION "public"."is_admin_or_above"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."is_superadmin"("user_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = user_id AND role = 'superadmin'
    );
END;
$$;


ALTER FUNCTION "public"."is_superadmin"("user_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."normalize_path"("path_text" "text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $_$
BEGIN
  -- 1. Convert to lowercase
  path_text := LOWER(path_text);
  -- 2. Replace all spaces with underscores
  path_text := REGEXP_REPLACE(path_text, ' ', '_', 'g');
  -- 3. Replace multiple underscores with single underscore
  path_text := REGEXP_REPLACE(path_text, '__+', '_', 'g');
  -- 4. Fix trailing underscore before extension (any extension)
  path_text := REGEXP_REPLACE(path_text, '_\\.([^.]+)$', '.\\1');
  RETURN path_text;
END;
$_$;


ALTER FUNCTION "public"."normalize_path"("path_text" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_file_path_from_compressed"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.compressed_path IS NOT NULL THEN
        NEW.file_path = NEW.compressed_path;
    END IF;
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_file_path_from_compressed"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_modified_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_modified_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."asset_tags" (
    "asset_id" "uuid" NOT NULL,
    "tag_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."asset_tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."assets" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "product_id" "uuid",
    "collection_id" "uuid" NOT NULL,
    "file_name" "text" NOT NULL,
    "file_path" "text" NOT NULL,
    "file_type" "text" NOT NULL,
    "file_size" bigint NOT NULL,
    "metadata" "jsonb",
    "workflow_stage" "public"."workflow_stage" DEFAULT 'upload'::"public"."workflow_stage" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "compressed_path" "text",
    "original_path" "text",
    "thumbnail_path" "text"
);


ALTER TABLE "public"."assets" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."clients" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "logo_url" "text",
    "description" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."clients" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."collections" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "client_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "cover_image_url" "text",
    "metadata" "jsonb" DEFAULT '{}'::"jsonb",
    CONSTRAINT "collections_status_check" CHECK (("status" = ANY (ARRAY['active'::"text", 'archived'::"text"])))
);


ALTER TABLE "public"."collections" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."comments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "asset_id" "uuid",
    "user_id" "uuid",
    "parent_id" "uuid",
    "content" "text" NOT NULL,
    "is_annotation" boolean DEFAULT false NOT NULL,
    "coordinates" "jsonb",
    "status" "public"."comment_status" DEFAULT 'open'::"public"."comment_status" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."comments" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "collection_id" "uuid",
    "name" "text" NOT NULL,
    "description" "text",
    "sku" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."products" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."tags" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "category" "public"."tag_category" NOT NULL,
    "color" "text" DEFAULT '#cccccc'::"text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."tags" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_collection_access" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "collection_id" "uuid",
    "permission_level" "public"."permission_level" DEFAULT 'view'::"public"."permission_level" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."user_collection_access" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" NOT NULL,
    "email" "text" NOT NULL,
    "role" "public"."user_role" DEFAULT 'client'::"public"."user_role" NOT NULL,
    "display_name" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."users" OWNER TO "postgres";


ALTER TABLE ONLY "public"."asset_tags"
    ADD CONSTRAINT "asset_tags_pkey" PRIMARY KEY ("asset_id", "tag_id");



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."clients"
    ADD CONSTRAINT "clients_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."collections"
    ADD CONSTRAINT "collections_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tags"
    ADD CONSTRAINT "tags_name_category_key" UNIQUE ("name", "category");



ALTER TABLE ONLY "public"."tags"
    ADD CONSTRAINT "tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_collection_access"
    ADD CONSTRAINT "user_collection_access_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_collection_access"
    ADD CONSTRAINT "user_collection_access_user_id_collection_id_key" UNIQUE ("user_id", "collection_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



CREATE INDEX "idx_assets_compressed_path" ON "public"."assets" USING "btree" ("compressed_path");



CREATE INDEX "idx_assets_original_path" ON "public"."assets" USING "btree" ("original_path");



CREATE INDEX "idx_assets_thumbnail_path" ON "public"."assets" USING "btree" ("thumbnail_path");



CREATE INDEX "idx_collections_client_id" ON "public"."collections" USING "btree" ("client_id");



CREATE INDEX "idx_user_collection_access_collection_id" ON "public"."user_collection_access" USING "btree" ("collection_id");



CREATE INDEX "idx_user_collection_access_user_id" ON "public"."user_collection_access" USING "btree" ("user_id");



CREATE OR REPLACE TRIGGER "update_assets_updated_at" BEFORE UPDATE ON "public"."assets" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_clients_updated_at" BEFORE UPDATE ON "public"."clients" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_collections_updated_at" BEFORE UPDATE ON "public"."collections" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_comments_updated_at" BEFORE UPDATE ON "public"."comments" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_file_path_trigger" BEFORE INSERT OR UPDATE ON "public"."assets" FOR EACH ROW EXECUTE FUNCTION "public"."update_file_path_from_compressed"();



CREATE OR REPLACE TRIGGER "update_products_updated_at" BEFORE UPDATE ON "public"."products" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_tags_updated_at" BEFORE UPDATE ON "public"."tags" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



CREATE OR REPLACE TRIGGER "update_user_collection_access_updated_at" BEFORE UPDATE ON "public"."user_collection_access" FOR EACH ROW EXECUTE FUNCTION "public"."update_modified_column"();



ALTER TABLE ONLY "public"."asset_tags"
    ADD CONSTRAINT "asset_tags_asset_id_fkey" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."asset_tags"
    ADD CONSTRAINT "asset_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."assets"
    ADD CONSTRAINT "assets_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."products"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."collections"
    ADD CONSTRAINT "collections_client_id_fkey" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_asset_id_fkey" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."comments"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."comments"
    ADD CONSTRAINT "comments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."products"
    ADD CONSTRAINT "products_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_collection_access"
    ADD CONSTRAINT "user_collection_access_collection_id_fkey" FOREIGN KEY ("collection_id") REFERENCES "public"."collections"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_collection_access"
    ADD CONSTRAINT "user_collection_access_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_id_fkey" FOREIGN KEY ("id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Admin users can view all profiles" ON "public"."users" FOR SELECT TO "authenticated" USING ((("auth"."uid"() = "id") AND ("role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))));



CREATE POLICY "Admins and Superadmins can create assets" ON "public"."assets" FOR INSERT WITH CHECK ((("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))) OR "public"."has_collection_access"("collection_id", 'edit'::"public"."permission_level")));



CREATE POLICY "Admins and Superadmins can create collections" ON "public"."collections" FOR INSERT WITH CHECK (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))));



CREATE POLICY "Admins and Superadmins can delete assets" ON "public"."assets" FOR DELETE USING ((("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))) OR "public"."has_collection_access"("collection_id", 'admin'::"public"."permission_level")));



CREATE POLICY "Admins and Superadmins can delete collections" ON "public"."collections" FOR DELETE USING (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))));



CREATE POLICY "Admins and Superadmins can manage clients" ON "public"."clients" USING (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))) WITH CHECK (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))));



CREATE POLICY "Admins and Superadmins can update assets" ON "public"."assets" FOR UPDATE USING ((("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))) OR "public"."has_collection_access"("collection_id", 'edit'::"public"."permission_level")));



CREATE POLICY "Admins and Superadmins can update collections" ON "public"."collections" FOR UPDATE USING ((("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))) OR "public"."has_collection_access"("id", 'edit'::"public"."permission_level")));



CREATE POLICY "Admins can create users" ON "public"."users" FOR INSERT WITH CHECK ("public"."is_admin_or_above"("auth"."uid"()));



CREATE POLICY "Admins can update all users" ON "public"."users" FOR UPDATE USING ("public"."is_admin_or_above"("auth"."uid"()));



CREATE POLICY "Admins can view all users" ON "public"."users" FOR SELECT USING ("public"."is_admin_or_above"("auth"."uid"()));



CREATE POLICY "Admins can view, create and update collections." ON "public"."collections" USING ((EXISTS ( SELECT 1
   FROM "auth"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND (("users"."role")::"text" = ANY (ARRAY[('admin'::character varying)::"text", ('superadmin'::character varying)::"text"]))))));



CREATE POLICY "All users can see clients" ON "public"."clients" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."asset_tags" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."assets" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."clients" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."collections" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."comments" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."products" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."tags" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."user_collection_access" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Allow full access to authenticated users" ON "public"."users" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Clients can create collections." ON "public"."collections" FOR INSERT WITH CHECK (("client_id" = "auth"."uid"()));



CREATE POLICY "Clients can update their own collections." ON "public"."collections" FOR UPDATE USING (("client_id" = "auth"."uid"())) WITH CHECK (("client_id" = "auth"."uid"()));



CREATE POLICY "Clients can view their own collections." ON "public"."collections" FOR SELECT USING ((("client_id" = "auth"."uid"()) OR ("client_id" IN ( SELECT "collections"."client_id"
   FROM "public"."user_collection_access"
  WHERE ("user_collection_access"."user_id" = "auth"."uid"())))));



CREATE POLICY "Enable delete for admins" ON "public"."collections" FOR DELETE TO "authenticated" USING (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))));



CREATE POLICY "Enable delete for authenticated users" ON "public"."clients" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "Enable delete for authenticated users" ON "public"."collections" FOR DELETE TO "authenticated" USING (true);



CREATE POLICY "Enable insert for admins" ON "public"."collections" FOR INSERT TO "authenticated" WITH CHECK (("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))));



CREATE POLICY "Enable insert for authenticated users" ON "public"."clients" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable insert for authenticated users" ON "public"."collections" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable read access for authenticated users" ON "public"."clients" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Enable read access for authenticated users" ON "public"."collections" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Enable read access for authenticated users" ON "public"."user_collection_access" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Enable update for admins and editors" ON "public"."collections" FOR UPDATE TO "authenticated" USING ((("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"])))) OR ("auth"."uid"() IN ( SELECT "user_collection_access"."user_id"
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."collection_id" = "collections"."id") AND ("user_collection_access"."permission_level" = ANY (ARRAY['edit'::"public"."permission_level", 'admin'::"public"."permission_level"])))))));



CREATE POLICY "Enable update for authenticated users" ON "public"."clients" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Enable update for authenticated users" ON "public"."collections" FOR UPDATE TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "Enable users to read all users" ON "public"."users" FOR SELECT TO "authenticated" USING (true);



CREATE POLICY "Enable users to read their own record" ON "public"."users" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "Superadmins can manage all users" ON "public"."users" USING ("public"."is_superadmin"("auth"."uid"())) WITH CHECK ("public"."is_superadmin"("auth"."uid"()));



CREATE POLICY "Users can edit collections" ON "public"."collections" TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))) OR (EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."user_id" = "auth"."uid"()) AND ("user_collection_access"."collection_id" = "collections"."id") AND ("user_collection_access"."permission_level" = ANY (ARRAY['edit'::"public"."permission_level", 'admin'::"public"."permission_level"]))))))) WITH CHECK (((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))) OR (EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."user_id" = "auth"."uid"()) AND ("user_collection_access"."collection_id" = "collections"."id") AND ("user_collection_access"."permission_level" = ANY (ARRAY['edit'::"public"."permission_level", 'admin'::"public"."permission_level"])))))));



CREATE POLICY "Users can view assets they have access to" ON "public"."assets" FOR SELECT USING (("public"."has_collection_access"("collection_id", 'view'::"public"."permission_level") OR ("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))));



CREATE POLICY "Users can view collections" ON "public"."collections" FOR SELECT TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."users"
  WHERE (("users"."id" = "auth"."uid"()) AND ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))) OR (EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."user_id" = "auth"."uid"()) AND ("user_collection_access"."collection_id" = "collections"."id"))))));



CREATE POLICY "Users can view collections they have access to" ON "public"."collections" FOR SELECT USING (("public"."has_collection_access"("id", 'view'::"public"."permission_level") OR ("auth"."uid"() IN ( SELECT "users"."id"
   FROM "public"."users"
  WHERE ("users"."role" = ANY (ARRAY['admin'::"public"."user_role", 'superadmin'::"public"."user_role"]))))));



CREATE POLICY "Users can view own profile" ON "public"."users" FOR SELECT USING (("auth"."uid"() = "id"));



CREATE POLICY "Users can view their own profile" ON "public"."users" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "id"));



CREATE POLICY "admin_full_access" ON "public"."users" TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "auth"."users" "users_1"
  WHERE (("users_1"."id" = "auth"."uid"()) AND (("users_1"."role")::"text" = 'service_role'::"text")))) OR (EXISTS ( SELECT 1
   FROM "auth"."users" "users_1"
  WHERE (("users_1"."id" = "auth"."uid"()) AND (("users_1"."raw_user_meta_data" ->> 'role'::"text") = ANY (ARRAY['admin'::"text", 'superadmin'::"text"])))))));



CREATE POLICY "allow_all_access" ON "public"."user_collection_access" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "allow_all_users" ON "public"."users" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."asset_tags" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."assets" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "authenticated_users_assets" ON "public"."assets" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "authenticated_users_clients" ON "public"."clients" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "authenticated_users_collections" ON "public"."collections" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "authenticated_users_user_collection_access" ON "public"."user_collection_access" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "authenticated_users_users" ON "public"."users" TO "authenticated" USING (true) WITH CHECK (true);



ALTER TABLE "public"."clients" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."collections" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "collections_access_policy" ON "public"."collections" TO "authenticated" USING ((((("current_setting"('request.jwt.claims'::"text", true))::"json" ->> 'role'::"text") = 'authenticated'::"text") OR (EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."user_id" = "auth"."uid"()) AND ("user_collection_access"."collection_id" = "user_collection_access"."id")))) OR (NOT (EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE ("user_collection_access"."collection_id" = "user_collection_access"."id"))))));



CREATE POLICY "collections_admin_access" ON "public"."collections" TO "authenticated" USING (("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"]))) WITH CHECK (("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"])));



CREATE POLICY "collections_user_access" ON "public"."collections" FOR SELECT TO "authenticated" USING (((EXISTS ( SELECT 1
   FROM "public"."user_collection_access"
  WHERE (("user_collection_access"."collection_id" = "collections"."id") AND ("user_collection_access"."user_id" = "auth"."uid"())))) OR ("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"]))));



ALTER TABLE "public"."comments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."products" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "read_own_profile" ON "public"."users" FOR SELECT TO "authenticated" USING (("auth"."uid"() = "id"));



ALTER TABLE "public"."tags" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "temp_collections_policy" ON "public"."collections" TO "authenticated" USING (true) WITH CHECK (true);



CREATE POLICY "update_own_profile" ON "public"."users" FOR UPDATE TO "authenticated" USING (("auth"."uid"() = "id")) WITH CHECK (("auth"."uid"() = "id"));



ALTER TABLE "public"."user_collection_access" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "user_collection_access_modify" ON "public"."user_collection_access" TO "authenticated" USING (("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"]))) WITH CHECK (("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"])));



CREATE POLICY "user_collection_access_view" ON "public"."user_collection_access" FOR SELECT TO "authenticated" USING ((("user_id" = "auth"."uid"()) OR ("public"."current_user_role"() = ANY (ARRAY['admin'::"text", 'superadmin'::"text"]))));



ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";





GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";




















































































































































































GRANT ALL ON FUNCTION "public"."current_user_role"() TO "anon";
GRANT ALL ON FUNCTION "public"."current_user_role"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."current_user_role"() TO "service_role";



GRANT ALL ON FUNCTION "public"."fix_all_image_paths"() TO "anon";
GRANT ALL ON FUNCTION "public"."fix_all_image_paths"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."fix_all_image_paths"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_user_role"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."has_collection_access"("collection_id" "uuid", "required_permission" "public"."permission_level") TO "anon";
GRANT ALL ON FUNCTION "public"."has_collection_access"("collection_id" "uuid", "required_permission" "public"."permission_level") TO "authenticated";
GRANT ALL ON FUNCTION "public"."has_collection_access"("collection_id" "uuid", "required_permission" "public"."permission_level") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_admin_or_above"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_admin_or_above"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_admin_or_above"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."is_superadmin"("user_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."is_superadmin"("user_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."is_superadmin"("user_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."normalize_path"("path_text" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."normalize_path"("path_text" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."normalize_path"("path_text" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."update_file_path_from_compressed"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_file_path_from_compressed"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_file_path_from_compressed"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_modified_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."asset_tags" TO "anon";
GRANT ALL ON TABLE "public"."asset_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."asset_tags" TO "service_role";



GRANT ALL ON TABLE "public"."assets" TO "anon";
GRANT ALL ON TABLE "public"."assets" TO "authenticated";
GRANT ALL ON TABLE "public"."assets" TO "service_role";



GRANT ALL ON TABLE "public"."clients" TO "anon";
GRANT ALL ON TABLE "public"."clients" TO "authenticated";
GRANT ALL ON TABLE "public"."clients" TO "service_role";



GRANT ALL ON TABLE "public"."collections" TO "anon";
GRANT ALL ON TABLE "public"."collections" TO "authenticated";
GRANT ALL ON TABLE "public"."collections" TO "service_role";



GRANT ALL ON TABLE "public"."comments" TO "anon";
GRANT ALL ON TABLE "public"."comments" TO "authenticated";
GRANT ALL ON TABLE "public"."comments" TO "service_role";



GRANT ALL ON TABLE "public"."products" TO "anon";
GRANT ALL ON TABLE "public"."products" TO "authenticated";
GRANT ALL ON TABLE "public"."products" TO "service_role";



GRANT ALL ON TABLE "public"."tags" TO "anon";
GRANT ALL ON TABLE "public"."tags" TO "authenticated";
GRANT ALL ON TABLE "public"."tags" TO "service_role";



GRANT ALL ON TABLE "public"."user_collection_access" TO "anon";
GRANT ALL ON TABLE "public"."user_collection_access" TO "authenticated";
GRANT ALL ON TABLE "public"."user_collection_access" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
