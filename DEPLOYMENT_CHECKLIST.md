# Staging Deployment Checklist - ImageGeneratorV2 Updates

## Overview
This deployment includes significant updates to the ImageGeneratorV2 component:
- Fixed TypeScript compilation errors for `ai_generated_images` table
- Added product image selection with a two-step dialog flow
- Implemented OpenAI garment analysis for product images
- Added visual differentiation between main and additional garments
- Fixed the image generation hook destructuring issue

## Pre-Deployment Checklist

### 1. Code Review
- [x] All TypeScript compilation errors in modified files fixed
- [x] OpenAI integration working without mock data
- [x] Product selection flow tested locally
- [x] Image generation working with Edge Functions

### 2. Environment Variables
Ensure these are set in staging (Vercel dashboard):
- [ ] `VITE_ENVIRONMENT=staging`
- [ ] `VITE_SUPABASE_URL=https://qnfmiotatmkoumlymynq.supabase.co`
- [ ] `VITE_SUPABASE_ANON_KEY` - Staging anon key from Supabase dashboard
- [ ] `VITE_OPENAI_API_KEY` - For garment analysis (OpenAI Vision API)
- [ ] `FASHION_LAB_API_URL` - Fashion Lab API endpoint
- [ ] `FASHION_LAB_API_KEY` - Fashion Lab API key for image generation
- [ ] `FASHIONLAB_JWT_SECRET` - JWT secret for Fashion Lab authentication

### 3. Database Requirements
- [x] `ai_generated_images` table exists in staging
- [x] Database types are up to date
- [ ] Run any pending migrations if needed

### 4. Edge Functions
Ensure these Edge Functions are deployed on staging:
- [ ] `generate-images` - For V2 image generation
- [ ] `queue-status` - For polling generation status
- [ ] `fashion-lab-proxy` - For Fashion Lab API integration

### 5. Testing Steps
1. **Product Selection Flow**:
   - Click "Select Product" in Target Garment section
   - Choose a product from the dialog
   - Select a specific image from the product
   - Verify image appears in Target Garment section

2. **OpenAI Garment Analysis**:
   - After selecting a product image, verify it's analyzed
   - Check that a "Main Garment" block appears in prompt builder
   - Upload additional garments and verify they're analyzed
   - Confirm different colors for main vs additional garments

3. **Image Generation**:
   - Build a prompt with models, angles, and garments
   - Click "Generate V2 Images"
   - Verify images are generated and stored

### 6. Known Issues to Monitor
- Large image files may fail OpenAI analysis (>20MB)
- CORS issues if Edge Functions aren't properly configured
- Rate limiting on OpenAI API for garment analysis

## Deployment Steps

1. **Pull latest changes on staging server**:
   ```bash
   git pull origin main
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Build the application**:
   ```bash
   npm run build
   ```

4. **Deploy Edge Functions** (if needed):
   ```bash
   supabase functions deploy generate-images
   supabase functions deploy queue-status
   supabase functions deploy fashion-lab-proxy
   ```

5. **Verify deployment**:
   - Check that the app loads without errors
   - Test the ImageGeneratorV2 component thoroughly
   - Monitor browser console for any errors

## Rollback Plan
If issues arise:
1. Revert to previous commit: `git reset --hard HEAD~10`
2. Rebuild: `npm run build`
3. Investigate issues in development environment

## Post-Deployment Verification
- [ ] All features working as expected
- [ ] No console errors in browser
- [ ] OpenAI analysis successful
- [ ] Image generation completing
- [ ] Performance acceptable

## Notes
- The main changes are in `/src/components/image-generator/ImageGeneratorV2.tsx`
- New dialog component added: `ProductImageSelectorDialog.tsx`
- OpenAI service updated to remove mock data fallbacks
- Database types regenerated to include `ai_generated_images` table