-- Minimal working seed for testing auth
-- This avoids complex seed data that might cause schema conflicts

-- First, disable <PERSON><PERSON> temporarily for seeding
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections DISABLE ROW LEVEL SECURITY;

-- Simple test user in auth.users
INSERT INTO auth.users (
    id, 
    instance_id, 
    aud, 
    role, 
    email, 
    encrypted_password, 
    email_confirmed_at, 
    raw_app_meta_data, 
    raw_user_meta_data,
    created_at,
    updated_at,
    confirmed_at
) VALUES (
    '11111111-1111-1111-1111-111111111111'::uuid,
    '00000000-0000-0000-0000-000000000000'::uuid,
    'authenticated',
    'authenticated',
    '<EMAIL>',
    '$2a$06$9rz3g6e7fQVcI8SJgzjyBe.H1cOh2FPwQMfA6kfgMa7JHWLYGy8sq', -- 'password123'
    now(),
    '{"provider": "email", "providers": ["email"]}',
    '{"display_name": "Test User"}',
    now(),
    now(),
    now()
) ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    encrypted_password = EXCLUDED.encrypted_password;

-- Simple test user in public.users
INSERT INTO public.users (id, email, role, display_name) VALUES (
    '11111111-1111-1111-1111-111111111111'::uuid,
    '<EMAIL>',
    'brand_admin',
    'Test User'
) ON CONFLICT (id) DO UPDATE SET
    role = EXCLUDED.role,
    display_name = EXCLUDED.display_name;

-- Simple organization
INSERT INTO public.organizations (id, name) VALUES (
    '*************-2222-2222-************'::uuid,
    'Test Organization'
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name;

-- Organization membership
INSERT INTO public.organization_memberships (user_id, organization_id) VALUES (
    '11111111-1111-1111-1111-111111111111'::uuid,
    '*************-2222-2222-************'::uuid
) ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Simple collection
INSERT INTO public.collections (id, name, organization_id) VALUES (
    '*************-3333-3333-************'::uuid,
    'Test Collection',
    '*************-2222-2222-************'::uuid
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name;

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;