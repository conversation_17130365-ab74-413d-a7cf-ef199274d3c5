-- Working Seed File for FashionLab (Schema-Compatible)
-- Uses actual database schema with unified role system

-- Disable RLS for seeding
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations DISABLE ROW LEVEL SECURITY;

-- Create auth users
INSERT INTO auth.users (
    id, instance_id, aud, role, email, encrypted_password, email_confirmed_at, 
    raw_app_meta_data, raw_user_meta_data, created_at, updated_at
) VALUES
('11111111-1111-1111-1111-111111111111', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Super Admin"}', now(), now()),
('22222222-2222-2222-2222-222222222222', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Admin"}', now(), now()),
('*************-3333-3333-************', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Marie Jensen"}', now(), now()),
('*************-4444-4444-************', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Lars Andersen"}', now(), now()),
('55555555-5555-5555-5555-************', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Sofia Müller"}', now(), now()),
('66666666-6666-6666-6666-666666666666', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(),
 '{"provider":"email","providers":["email"]}', '{"display_name":"Alex Thompson"}', now(), now())
ON CONFLICT (id) DO NOTHING;

-- Create/update public.users with proper roles
INSERT INTO public.users (id, email, role, first_name, last_name, avatar_url) VALUES
('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'platform_super', 'Platform', 'Super Admin', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'),
('22222222-2222-2222-2222-222222222222', '<EMAIL>', 'platform_admin', 'Platform', 'Admin', 'https://images.unsplash.com/photo-1494790108755-2616b612d3ad?w=150&h=150&fit=crop&crop=face'),
('*************-3333-3333-************', '<EMAIL>', 'brand_admin', 'Marie', 'Jensen', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'),
('*************-4444-4444-************', '<EMAIL>', 'brand_member', 'Lars', 'Andersen', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'),
('55555555-5555-5555-5555-************', '<EMAIL>', 'external_retoucher', 'Sofia', 'Müller', 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'),
('66666666-6666-6666-6666-666666666666', '<EMAIL>', 'external_prompter', 'Alex', 'Thompson', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face')
ON CONFLICT (id) DO UPDATE SET 
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    avatar_url = EXCLUDED.avatar_url;

-- Create organizations
INSERT INTO public.organizations (id, name, description, logo_url) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Vero Moda', 'Danish fashion brand focused on contemporary womens clothing and accessories', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Test Fashion House', 'Additional test brand for multi-organization scenarios', 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=200&fit=crop')
ON CONFLICT (id) DO UPDATE SET 
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    logo_url = EXCLUDED.logo_url;

-- Create organization memberships
INSERT INTO public.organization_memberships (user_id, organization_id) VALUES
('*************-3333-3333-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Marie (brand_admin)
('*************-4444-4444-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Lars (brand_member)
('55555555-5555-5555-5555-************', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Sofia (external_retoucher)
('66666666-6666-6666-6666-666666666666', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Alex (external_prompter)
('22222222-2222-2222-2222-222222222222', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb')  -- Platform admin
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create collections
INSERT INTO public.collections (id, organization_id, name, description) VALUES
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'AW24 Reference Collection', 'Autumn/Winter 2024 reference images for AI model training'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'SS25 Generated Collection', 'Spring/Summer 2025 AI-generated product images'),
('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Test Campaign', 'Test collection for additional scenarios')
ON CONFLICT (id) DO NOTHING;

-- Create products
INSERT INTO public.products (id, collection_id, name, sku, description) VALUES
('11111111-**************-************', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Wool Blend Coat', 'VM-AW24-001', 'Double-breasted wool blend coat in camel. Classic silhouette with tailored fit.'),
('2222**************-5555-666666666666', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 'Chunky Knit Sweater', 'VM-AW24-002', 'Oversized chunky knit sweater in cream. Relaxed fit with ribbed details.'),
('33333333-4444-5555-6666-777777777777', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'Linen Summer Dress', 'VM-SS25-001', 'Flowing linen dress in soft pink. Perfect for summer occasions.'),
('44444444-5555-6666-7777-888888888888', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 'Cotton Blouse', 'VM-SS25-002', 'Classic cotton blouse in white. Versatile piece for work and casual wear.')
ON CONFLICT (id) DO NOTHING;

-- Create assets
INSERT INTO public.assets (id, collection_id, product_id, file_name, file_path, file_type, metadata, file_size, workflow_stage) VALUES
('a1111111-1111-1111-1111-111111111111', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '11111111-**************-************', 'VM-AW24-001-reference-front.jpg', 'client-assets/vero-moda/input-assets/VM-AW24-001/reference-front.jpg', 'image/jpeg', '{"description": "Wool coat front view reference", "tags": ["coat", "wool", "front", "reference"]}', 2048576, 'upload'),
('b2222222-2222-2222-2222-222222222222', 'cccccccc-cccc-cccc-cccc-cccccccccccc', '2222**************-5555-666666666666', 'VM-AW24-002-reference-front.jpg', 'client-assets/vero-moda/input-assets/VM-AW24-002/reference-front.jpg', 'image/jpeg', '{"description": "Chunky knit sweater front view", "tags": ["sweater", "knit", "front", "reference"]}', 1654321, 'upload'),
('c333**************-3333-************', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '33333333-4444-5555-6666-777777777777', 'VM-SS25-001-generated-01.jpg', 'client-assets/vero-moda/final/VM-SS25-001/generated-01.jpg', 'image/jpeg', '{"description": "AI generated linen dress variant 1", "tags": ["dress", "linen", "generated", "ai"]}', 1923456, 'final'),
('d4444444-4444-4444-4444-************', 'dddddddd-dddd-dddd-dddd-dddddddddddd', '44444444-5555-6666-7777-888888888888', 'VM-SS25-002-generated-01.jpg', 'client-assets/vero-moda/final/VM-SS25-002/generated-01.jpg', 'image/jpeg', '{"description": "AI generated cotton blouse", "tags": ["blouse", "cotton", "generated", "ai"]}', 1765432, 'final')
ON CONFLICT (id) DO NOTHING;

-- Create sample comments
INSERT INTO public.comments (id, asset_id, user_id, content, status) VALUES
('ffffffff-ffff-ffff-ffff-ffffffffffff', 'a1111111-1111-1111-1111-111111111111', '*************-3333-3333-************', 'Great reference image! The lighting perfectly captures the wool texture. This will be excellent for training our AI model.', 'open'),
('f1111111-**************-************', 'c333**************-3333-************', '*************-4444-4444-************', 'Love how the AI captured the flowing silhouette! Maybe we can adjust the color slightly for the final version?', 'open')
ON CONFLICT (id) DO NOTHING;

-- Create pending invitations (no role column in the new system)
INSERT INTO public.pending_invitations (id, email, organization_id, invited_by, invited_at, expires_at, token, accepted, message) VALUES
(gen_random_uuid(), '<EMAIL>', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-3333-3333-************', now(), now() + interval '48 hours', 'vero-moda-invitation-token-123', false, 'Welcome to the Vero Moda team! We are excited to have you join our design team.'),
(gen_random_uuid(), '<EMAIL>', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '*************-3333-3333-************', now(), now() + interval '72 hours', 'vero-moda-photographer-token-456', false, 'We would love to work with you on our upcoming photo shoots and retouching work.')
ON CONFLICT (id) DO NOTHING;

-- Re-enable RLS
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;