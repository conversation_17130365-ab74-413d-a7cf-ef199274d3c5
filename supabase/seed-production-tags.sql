-- Seed production-like tag data for testing migration
-- This creates tags similar to what exists in production

-- Clear existing tags and asset_tags for clean test
DELETE FROM asset_tags;
DELETE FROM tags;

-- Insert production-like tags (all starting as 'collection' category to simulate current state)
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at) VALUES
-- High-usage cross-org tags
('Front', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Back', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Detail', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Packshot', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Side', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Side profile', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Styling tags
('Look and feel', 'collection', '#cccccc', NULL, NOW(), NOW()),
('look&feel', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Location', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Poses', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Poses female', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Poses male', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Filter', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Product category tags
('Woman Dress', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Dress', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Jeans front', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Jeans back', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Baby bodysuit', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Babies', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Women', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Model tags
('Eva model', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Jesper model', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Model', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Approved model', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Size tags
('Size XS', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Size M', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Size L', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Size XL', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Image reference tags
('Image 1', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Image 2', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Image 3', 'collection', '#cccccc', NULL, NOW(), NOW()),

-- Unused tags (to be deleted)
('new tag', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Test', 'collection', '#cccccc', NULL, NOW(), NOW()),
('tiff', 'collection', '#cccccc', NULL, NOW(), NOW()),
('TIFF', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Revision 1', 'collection', '#cccccc', NULL, NOW(), NOW()),
('Styling', 'collection', '#cccccc', NULL, NOW(), NOW()),
('look and feel', 'collection', '#cccccc', NULL, NOW(), NOW());

-- Get the collection IDs from seed data
DO $$
DECLARE
  collection_vm_id UUID;
  collection_hm_id UUID;
  collection_bs_id UUID;
  
  tag_front_id UUID;
  tag_back_id UUID;
  tag_detail_id UUID;
  tag_packshot_id UUID;
  tag_side_id UUID;
  tag_look_feel_id UUID;
  tag_dress_id UUID;
  tag_eva_id UUID;
  tag_jesper_id UUID;
  tag_approved_id UUID;
  tag_babies_id UUID;
  tag_women_id UUID;
  
  asset1_id UUID;
  asset2_id UUID;
  asset3_id UUID;
  asset4_id UUID;
  asset5_id UUID;
  asset6_id UUID;
BEGIN
  -- Get collection IDs
  SELECT id INTO collection_vm_id FROM collections WHERE name = 'Summer 2025' LIMIT 1;
  SELECT id INTO collection_hm_id FROM collections WHERE name = 'H&M Conscious 2025' LIMIT 1;
  SELECT id INTO collection_bs_id FROM collections WHERE name = 'Business Casual' LIMIT 1;
  
  -- Get tag IDs
  SELECT id INTO tag_front_id FROM tags WHERE name = 'Front';
  SELECT id INTO tag_back_id FROM tags WHERE name = 'Back';
  SELECT id INTO tag_detail_id FROM tags WHERE name = 'Detail';
  SELECT id INTO tag_packshot_id FROM tags WHERE name = 'Packshot';
  SELECT id INTO tag_side_id FROM tags WHERE name = 'Side';
  SELECT id INTO tag_look_feel_id FROM tags WHERE name = 'Look and feel';
  SELECT id INTO tag_dress_id FROM tags WHERE name = 'Dress';
  SELECT id INTO tag_eva_id FROM tags WHERE name = 'Eva model';
  SELECT id INTO tag_jesper_id FROM tags WHERE name = 'Jesper model';
  SELECT id INTO tag_approved_id FROM tags WHERE name = 'Approved model';
  SELECT id INTO tag_babies_id FROM tags WHERE name = 'Babies';
  SELECT id INTO tag_women_id FROM tags WHERE name = 'Women';
  
  -- Get asset IDs
  SELECT id INTO asset1_id FROM assets WHERE collection_id = collection_vm_id LIMIT 1 OFFSET 0;
  SELECT id INTO asset2_id FROM assets WHERE collection_id = collection_vm_id LIMIT 1 OFFSET 1;
  SELECT id INTO asset3_id FROM assets WHERE collection_id = collection_vm_id LIMIT 1 OFFSET 2;
  SELECT id INTO asset4_id FROM assets WHERE collection_id = collection_hm_id LIMIT 1 OFFSET 0;
  SELECT id INTO asset5_id FROM assets WHERE collection_id = collection_bs_id LIMIT 1 OFFSET 0;
  
  -- Create asset_tags to simulate production usage patterns
  IF asset1_id IS NOT NULL AND tag_front_id IS NOT NULL THEN
    -- Front tag used across multiple orgs
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset1_id, tag_front_id),
      (asset2_id, tag_front_id);
  END IF;
  
  IF asset1_id IS NOT NULL AND tag_back_id IS NOT NULL THEN
    -- Back tag used across multiple orgs
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset1_id, tag_back_id);
  END IF;
  
  IF asset1_id IS NOT NULL AND tag_detail_id IS NOT NULL THEN
    -- Detail tag used across multiple orgs
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset2_id, tag_detail_id);
  END IF;
  
  IF asset1_id IS NOT NULL AND tag_eva_id IS NOT NULL THEN
    -- Model tags specific to one collection
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset1_id, tag_eva_id),
      (asset2_id, tag_eva_id);
  END IF;
  
  IF asset3_id IS NOT NULL AND tag_jesper_id IS NOT NULL THEN
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset3_id, tag_jesper_id);
  END IF;
  
  IF asset1_id IS NOT NULL AND tag_look_feel_id IS NOT NULL THEN
    -- Look and feel used in multiple collections
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset1_id, tag_look_feel_id);
  END IF;
  
  IF asset4_id IS NOT NULL AND tag_approved_id IS NOT NULL THEN
    -- Approved model used in multiple collections
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset4_id, tag_approved_id);
  END IF;
  
  IF asset5_id IS NOT NULL AND tag_approved_id IS NOT NULL THEN
    INSERT INTO asset_tags (asset_id, tag_id) VALUES 
      (asset5_id, tag_approved_id);
  END IF;
  
  RAISE NOTICE 'Production-like tag data seeded successfully';
  RAISE NOTICE 'Total tags created: %', (SELECT COUNT(*) FROM tags);
  RAISE NOTICE 'Total tag associations: %', (SELECT COUNT(*) FROM asset_tags);
END $$;