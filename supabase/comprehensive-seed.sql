-- 🎯 Comprehensive Seed File for FashionLab
-- Features: New Unified Role System + Vero Moda Test Data + Real Asset Structure
-- Updated: 2025-01-23 for unified role system migration

-- ============================================================================
-- 1. DISABLE RLS FOR SEEDING
-- ============================================================================
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations DISABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 2. CREATE AUTH USERS (Comprehensive test users with realistic profiles)
-- ============================================================================
INSERT INTO auth.users (
    id, instance_id, aud, role, email, encrypted_password, email_confirmed_at, 
    invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, 
    email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, 
    raw_app_meta_data, raw_user_meta_data, is_sso_user, created_at, updated_at, 
    phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, 
    email_change_token_current, email_change_confirm_status, banned_until, 
    reauthentication_token, reauthentication_sent_at, is_anonymous, is_super_admin
)
VALUES
-- Platform Super Admin (Full platform access)
('11111111-1111-1111-1111-111111111111', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Super Admin"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),

-- Platform Admin (Platform management)
('22222222-2222-2222-2222-222222222222', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Admin"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),

-- Vero Moda Brand Admin
('*************-3333-3333-************', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Marie Jensen"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),

-- Vero Moda Brand Member (Designer)
('44444444-4444-4444-4444-444444444444', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Lars Andersen"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),

-- External Retoucher
('55555555-5555-5555-5555-555555555555', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Sofia Müller"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),

-- External Prompter
('66666666-6666-6666-6666-666666666666', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', 
 '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, 
 '{"provider":"email","providers":["email"]}', '{"display_name":"Alex Thompson"}', false, now(), now(), 
 NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false);

-- Wait for auth trigger to complete
DO $$ BEGIN PERFORM pg_sleep(1); END $$;

-- ============================================================================
-- 3. UPDATE USERS TABLE WITH NEW UNIFIED ROLES
-- ============================================================================
-- Update the public.users table with the new unified role system
UPDATE public.users SET 
    role = 'platform_super',
    first_name = 'Platform',
    last_name = 'Super Admin',
    avatar_url = 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
WHERE id = '11111111-1111-1111-1111-111111111111';

UPDATE public.users SET 
    role = 'platform_admin',
    first_name = 'Platform',
    last_name = 'Admin',
    avatar_url = 'https://images.unsplash.com/photo-1494790108755-2616b612d3ad?w=150&h=150&fit=crop&crop=face'
WHERE id = '22222222-2222-2222-2222-222222222222';

UPDATE public.users SET 
    role = 'brand_admin',
    first_name = 'Marie',
    last_name = 'Jensen',
    avatar_url = 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
WHERE id = '*************-3333-3333-************';

UPDATE public.users SET 
    role = 'brand_member',
    first_name = 'Lars',
    last_name = 'Andersen',
    avatar_url = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
WHERE id = '44444444-4444-4444-4444-444444444444';

UPDATE public.users SET 
    role = 'external_retoucher',
    first_name = 'Sofia',
    last_name = 'Müller',
    avatar_url = 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face'
WHERE id = '55555555-5555-5555-5555-555555555555';

UPDATE public.users SET 
    role = 'external_prompter',
    first_name = 'Alex',
    last_name = 'Thompson',
    avatar_url = 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face'
WHERE id = '66666666-6666-6666-6666-666666666666';

-- ============================================================================
-- 4. CREATE ORGANIZATIONS
-- ============================================================================
-- Vero Moda - Our main test organization
INSERT INTO public.organizations (id, name, description, logo_url, website_url, contact_email) VALUES
('vero-moda-org-1111-1111-111111111111', 
 'Vero Moda', 
 'Danish fashion brand focused on contemporary womens clothing and accessories',
 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop',
 'https://www.veromoda.com',
 '<EMAIL>')
ON CONFLICT (id) DO UPDATE SET 
    name = EXCLUDED.name, 
    description = EXCLUDED.description, 
    logo_url = EXCLUDED.logo_url,
    website_url = EXCLUDED.website_url,
    contact_email = EXCLUDED.contact_email,
    updated_at = now();

-- Additional test organizations
INSERT INTO public.organizations (id, name, description, logo_url) VALUES
('test-brand-2222-2222-2222-222222222222', 
 'Test Fashion House', 
 'Additional test brand for multi-organization scenarios',
 'https://images.unsplash.com/photo-1445205170230-053b83016050?w=200&h=200&fit=crop')
ON CONFLICT (id) DO UPDATE SET 
    name = EXCLUDED.name, 
    description = EXCLUDED.description, 
    logo_url = EXCLUDED.logo_url,
    updated_at = now();

-- ============================================================================
-- 5. CREATE ORGANIZATION MEMBERSHIPS (No role column - unified system)
-- ============================================================================
-- Vero Moda memberships
INSERT INTO public.organization_memberships (user_id, organization_id) VALUES
('*************-3333-3333-************', 'vero-moda-org-1111-1111-111111111111'), -- Marie (brand_admin)
('44444444-4444-4444-4444-444444444444', 'vero-moda-org-1111-1111-111111111111'), -- Lars (brand_member)
('55555555-5555-5555-5555-555555555555', 'vero-moda-org-1111-1111-111111111111'), -- Sofia (external_retoucher)
('66666666-6666-6666-6666-666666666666', 'vero-moda-org-1111-1111-111111111111')  -- Alex (external_prompter)
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Test brand memberships
INSERT INTO public.organization_memberships (user_id, organization_id) VALUES
('22222222-2222-2222-2222-222222222222', 'test-brand-2222-2222-2222-222222222222') -- Platform admin
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- ============================================================================
-- 6. CREATE COLLECTIONS (Realistic Vero Moda campaigns)
-- ============================================================================
INSERT INTO public.collections (id, organization_id, name, description, brief) VALUES
-- AW24 Reference Collection (Input data for AI training)
('vero-aw24-c011-c011-c011-111111111111', 
 'vero-moda-org-1111-1111-111111111111',
 'AW24 Reference Collection',
 'Autumn/Winter 2024 reference images for AI model training',
 'Reference collection containing high-quality product photography for training our AI models. Includes coats, knitwear, dresses, and accessories in Vero Moda signature style.'),

-- SS25 Generated Collection (AI output)
('vero-ss25-c011-c011-c011-222222222222', 
 'vero-moda-org-1111-1111-111111111111',
 'SS25 Generated Collection',
 'Spring/Summer 2025 AI-generated product images',
 'AI-generated collection showcasing new SS25 designs based on Vero Moda brand aesthetics. Includes dresses, tops, accessories with focus on light fabrics and summer colors.'),

-- Test collection for other scenarios
('test-coll-c011-c011-c011-************',
 'test-brand-2222-2222-2222-222222222222',
 'Test Campaign',
 'Test collection for additional scenarios',
 'Basic test collection for development and testing purposes.')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 7. CREATE PRODUCTS (Realistic SKUs following naming conventions)
-- ============================================================================
INSERT INTO public.products (id, collection_id, name, sku, description) VALUES
-- AW24 Reference Products
('vm-aw24-001-1111-1111-111111111111', 'vero-aw24-c011-c011-c011-111111111111', 
 'Wool Blend Coat', 'VM-AW24-001', 
 'Double-breasted wool blend coat in camel. Classic silhouette with tailored fit.'),
 
('vm-aw24-002-2222-2222-222222222222', 'vero-aw24-c011-c011-c011-111111111111', 
 'Chunky Knit Sweater', 'VM-AW24-002', 
 'Oversized chunky knit sweater in cream. Relaxed fit with ribbed details.'),
 
('vm-aw24-003-3333-3333-************', 'vero-aw24-c011-c011-c011-111111111111', 
 'Midi Wrap Dress', 'VM-AW24-003', 
 'Printed midi wrap dress in navy. Feminine silhouette with long sleeves.'),

-- SS25 Generated Products  
('vm-ss25-001-4444-4444-444444444444', 'vero-ss25-c011-c011-c011-222222222222', 
 'Linen Summer Dress', 'VM-SS25-001', 
 'Flowing linen dress in soft pink. Perfect for summer occasions.'),
 
('vm-ss25-002-5555-5555-555555555555', 'vero-ss25-c011-c011-c011-222222222222', 
 'Cotton Blouse', 'VM-SS25-002', 
 'Classic cotton blouse in white. Versatile piece for work and casual wear.'),
 
('vm-ss25-003-6666-6666-666666666666', 'vero-ss25-c011-c011-c011-222222222222', 
 'Beach Kimono', 'VM-SS25-003', 
 'Lightweight kimono in tropical print. Perfect beach cover-up.')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 8. CREATE ASSETS (Organized by collection and product with realistic file paths)
-- ============================================================================
INSERT INTO public.assets (id, collection_id, product_id, file_name, file_path, file_type, metadata, file_size, workflow_stage) VALUES
-- AW24 Reference Assets (Input for AI training)
('asset-vm-aw24-001-ref-front-111111', 'vero-aw24-c011-c011-c011-111111111111', 'vm-aw24-001-1111-1111-111111111111',
 'VM-AW24-001-reference-front.jpg', 'client-assets/vero-moda/input-assets/VM-AW24-001/reference-front.jpg', 'image/jpeg',
 '{"description": "Wool coat front view reference", "tags": ["coat", "wool", "front", "reference"], "camera_settings": {"iso": 100, "aperture": "f/8", "shutter": "1/125"}}',
 2048576, 'upload'),

('asset-vm-aw24-001-ref-back-222222', 'vero-aw24-c011-c011-c011-111111111111', 'vm-aw24-001-1111-1111-111111111111',
 'VM-AW24-001-reference-back.jpg', 'client-assets/vero-moda/input-assets/VM-AW24-001/reference-back.jpg', 'image/jpeg',
 '{"description": "Wool coat back view reference", "tags": ["coat", "wool", "back", "reference"]}',
 1875432, 'upload'),

('asset-vm-aw24-002-ref-front-333333', 'vero-aw24-c011-c011-c011-111111111111', 'vm-aw24-002-2222-2222-222222222222',
 'VM-AW24-002-reference-front.jpg', 'client-assets/vero-moda/input-assets/VM-AW24-002/reference-front.jpg', 'image/jpeg',
 '{"description": "Chunky knit sweater front view", "tags": ["sweater", "knit", "front", "reference"]}',
 1654321, 'upload'),

-- SS25 Generated Assets (AI output)
('asset-vm-ss25-001-gen-01-444444', 'vero-ss25-c011-c011-c011-222222222222', 'vm-ss25-001-4444-4444-444444444444',
 'VM-SS25-001-generated-01.jpg', 'client-assets/vero-moda/final/VM-SS25-001/generated-01.jpg', 'image/jpeg',
 '{"description": "AI generated linen dress variant 1", "tags": ["dress", "linen", "generated", "ai"], "ai_model": "stable-diffusion-xl", "prompt": "elegant linen summer dress, soft pink, flowing silhouette, natural lighting"}',
 1923456, 'final'),

('asset-vm-ss25-001-gen-02-555555', 'vero-ss25-c011-c011-c011-222222222222', 'vm-ss25-001-4444-4444-444444444444',
 'VM-SS25-001-generated-02.jpg', 'client-assets/vero-moda/final/VM-SS25-001/generated-02.jpg', 'image/jpeg',
 '{"description": "AI generated linen dress variant 2", "tags": ["dress", "linen", "generated", "ai"], "ai_model": "stable-diffusion-xl"}',
 1854321, 'final'),

('asset-vm-ss25-002-gen-01-666666', 'vero-ss25-c011-c011-c011-222222222222', 'vm-ss25-002-5555-5555-555555555555',
 'VM-SS25-002-generated-01.jpg', 'client-assets/vero-moda/final/VM-SS25-002/generated-01.jpg', 'image/jpeg',
 '{"description": "AI generated cotton blouse", "tags": ["blouse", "cotton", "generated", "ai"]}',
 1765432, 'final')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 9. CREATE TAGS FOR BETTER ORGANIZATION
-- ============================================================================
INSERT INTO public.tags (id, name, category, color) VALUES
-- Product type tags
('tag-coat-111111111111', 'coat', 'product_type', '#8B4513'),
('tag-sweater-222222222222', 'sweater', 'product_type', '#FF6347'),
('tag-dress-************', 'dress', 'product_type', '#FF69B4'),
('tag-blouse-444444444444', 'blouse', 'product_type', '#87CEEB'),

-- Material tags
('tag-wool-555555555555', 'wool', 'material', '#CD853F'),
('tag-cotton-666666666666', 'cotton', 'material', '#F5F5DC'),
('tag-linen-777777777777', 'linen', 'material', '#E6E6FA'),

-- Season tags
('tag-aw24-888888888888', 'AW24', 'season', '#8B0000'),
('tag-ss25-999999999999', 'SS25', 'season', '#32CD32'),

-- Process tags
('tag-reference-101010101010', 'reference', 'process', '#4169E1'),
('tag-generated-111111111111', 'ai-generated', 'process', '#9370DB')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 10. CREATE ASSET-TAG RELATIONSHIPS
-- ============================================================================
INSERT INTO public.asset_tags (asset_id, tag_id) VALUES
-- AW24 Reference assets
('asset-vm-aw24-001-ref-front-111111', 'tag-coat-111111111111'),
('asset-vm-aw24-001-ref-front-111111', 'tag-wool-555555555555'),
('asset-vm-aw24-001-ref-front-111111', 'tag-aw24-888888888888'),
('asset-vm-aw24-001-ref-front-111111', 'tag-reference-101010101010'),

('asset-vm-aw24-002-ref-front-333333', 'tag-sweater-222222222222'),
('asset-vm-aw24-002-ref-front-333333', 'tag-wool-555555555555'),
('asset-vm-aw24-002-ref-front-333333', 'tag-aw24-888888888888'),
('asset-vm-aw24-002-ref-front-333333', 'tag-reference-101010101010'),

-- SS25 Generated assets
('asset-vm-ss25-001-gen-01-444444', 'tag-dress-************'),
('asset-vm-ss25-001-gen-01-444444', 'tag-linen-777777777777'),
('asset-vm-ss25-001-gen-01-444444', 'tag-ss25-999999999999'),
('asset-vm-ss25-001-gen-01-444444', 'tag-generated-111111111111'),

('asset-vm-ss25-002-gen-01-666666', 'tag-blouse-444444444444'),
('asset-vm-ss25-002-gen-01-666666', 'tag-cotton-666666666666'),
('asset-vm-ss25-002-gen-01-666666', 'tag-ss25-999999999999'),
('asset-vm-ss25-002-gen-01-666666', 'tag-generated-111111111111')
ON CONFLICT (asset_id, tag_id) DO NOTHING;

-- ============================================================================
-- 11. CREATE SAMPLE COMMENTS FOR COLLABORATION TESTING
-- ============================================================================
INSERT INTO public.comments (id, asset_id, user_id, content, status) VALUES
('comment-001-111111111111', 'asset-vm-aw24-001-ref-front-111111', '*************-3333-3333-************',
 'Great reference image! The lighting perfectly captures the wool texture. This will be excellent for training our AI model.', 'active'),
 
('comment-002-222222222222', 'asset-vm-ss25-001-gen-01-444444', '44444444-4444-4444-4444-444444444444',
 'Love how the AI captured the flowing silhouette! Maybe we can adjust the color slightly for the final version?', 'active'),
 
('comment-003-************', 'asset-vm-ss25-001-gen-01-444444', '55555555-5555-5555-5555-555555555555',
 'The image quality is excellent. Ready for final retouching if needed.', 'active')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 12. CREATE PENDING INVITATIONS FOR TESTING
-- ============================================================================
INSERT INTO public.pending_invitations (
  id, email, organization_id, role, invited_by, invited_at, expires_at, token, accepted, message
) VALUES
-- Invitation for new Vero Moda team member
(gen_random_uuid(), '<EMAIL>', 'vero-moda-org-1111-1111-111111111111', 
 'brand_member', '*************-3333-3333-************', now(), now() + interval '48 hours', 
 'vero-moda-invitation-token-123', false,
 'Welcome to the Vero Moda team! We are excited to have you join our design team.'),

-- Invitation for external contractor
(gen_random_uuid(), '<EMAIL>', 'vero-moda-org-1111-1111-111111111111', 
 'external_retoucher', '*************-3333-3333-************', now(), now() + interval '72 hours', 
 'vero-moda-photographer-token-456', false,
 'We would love to work with you on our upcoming photo shoots and retouching work.')
ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 13. RE-ENABLE RLS
-- ============================================================================
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 14. VERIFICATION QUERIES (For testing)
-- ============================================================================
-- These queries help verify the seed data was created correctly

-- Verify users and roles
-- SELECT id, email, role, first_name, last_name FROM public.users ORDER BY role;

-- Verify organizations and memberships
-- SELECT o.name, u.email, u.role 
-- FROM public.organizations o 
-- JOIN public.organization_memberships om ON o.id = om.organization_id 
-- JOIN public.users u ON om.user_id = u.id 
-- ORDER BY o.name, u.role;

-- Verify collections and products
-- SELECT o.name as org_name, c.name as collection_name, p.name as product_name, p.sku 
-- FROM public.organizations o 
-- JOIN public.collections c ON o.id = c.organization_id 
-- JOIN public.products p ON c.id = p.collection_id 
-- ORDER BY o.name, c.name, p.sku;

-- Verify assets and their organization
-- SELECT o.name as org_name, c.name as collection_name, p.sku, a.file_name, a.workflow_stage
-- FROM public.organizations o 
-- JOIN public.collections c ON o.id = c.organization_id 
-- JOIN public.assets a ON c.id = a.collection_id 
-- LEFT JOIN public.products p ON a.product_id = p.id 
-- ORDER BY o.name, c.name, p.sku, a.file_name;

-- ============================================================================
-- SEED COMPLETE ✅
-- ============================================================================
-- Test Users Created:
-- - <EMAIL> (platform_super) - password123
-- - <EMAIL> (platform_admin) - password123  
-- - <EMAIL> (brand_admin) - password123
-- - <EMAIL> (brand_member) - password123
-- - <EMAIL> (external_retoucher) - password123
-- - <EMAIL> (external_prompter) - password123
--
-- Organizations: Vero Moda + Test Fashion House
-- Collections: AW24 Reference + SS25 Generated + Test Campaign
-- Products: 6 realistic Vero Moda products with proper SKUs
-- Assets: Reference images + AI-generated images with proper file paths
-- Tags: Comprehensive tagging system for organization
-- Comments: Sample collaboration comments
-- Invitations: Test pending invitations
-- ============================================================================