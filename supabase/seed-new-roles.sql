-- Seed file for Fashionlab MVP with New Single Role System
-- This seed file uses the new unified role system where roles are stored in public.users.role

-- Disable <PERSON><PERSON> temporarily for seeding
ALTER TABLE public.organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.products DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations DISABLE ROW LEVEL SECURITY;

-- Clear existing data (development only - BE CAREFUL in production!)
DELETE FROM public.asset_tags;
DELETE FROM public.tags;
DELETE FROM public.comments;
DELETE FROM public.assets;
DELETE FROM public.products;
DELETE FROM public.collections;
DELETE FROM public.pending_invitations;
DELETE FROM public.organization_memberships;
DELETE FROM public.users;
DELETE FROM public.organizations;
DELETE FROM auth.users;

-- 1. Create sample users in auth.users
-- Use the SAME comprehensive column list for ALL inserts
INSERT INTO auth.users (
    id, instance_id, aud, role, email, encrypted_password, email_confirmed_at, invited_at, confirmation_token, confirmation_sent_at, recovery_token, recovery_sent_at, email_change_token_new, email_change, email_change_sent_at, last_sign_in_at, raw_app_meta_data, raw_user_meta_data, is_sso_user, created_at, updated_at, phone, phone_confirmed_at, phone_change, phone_change_token, phone_change_sent_at, email_change_token_current, email_change_confirm_status, banned_until, reauthentication_token, reauthentication_sent_at, is_anonymous, is_super_admin
)
VALUES
-- Platform Super User
( 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Super User"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
-- Platform Admin User
( 'b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Platform Admin User"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
-- Brand Admin Users
( 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Brand Admin 1"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
( 'd4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Brand Admin 2"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
-- Brand Member Users
( 'e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Brand Member 1"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
( 'f6f6f6f6-f6f6-f6f6-f6f6-f6f6f6f6f6f6', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"Brand Member 2"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
-- External Retoucher
( 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"External Retoucher"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false),
-- External Prompter
( 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), now(), NULL, '', NULL, '', NULL, '', '', NULL, NULL, '{"provider":"email","providers":["email"]}', '{"display_name":"External Prompter"}', false, now(), now(), NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, false);

-- Wait for the trigger to complete before continuing
DO $$ BEGIN PERFORM pg_sleep(1); END $$;

-- 2. Update users table with new role system
UPDATE public.users SET role = 'platform_super' WHERE id = 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1';
UPDATE public.users SET role = 'platform_admin' WHERE id = 'b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2';
UPDATE public.users SET role = 'brand_admin' WHERE id = 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3';
UPDATE public.users SET role = 'brand_admin' WHERE id = 'd4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4';
UPDATE public.users SET role = 'brand_member' WHERE id = 'e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5';
UPDATE public.users SET role = 'brand_member' WHERE id = 'f6f6f6f6-f6f6-f6f6-f6f6-f6f6f6f6f6f6';
UPDATE public.users SET role = 'external_retoucher' WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';
UPDATE public.users SET role = 'external_prompter' WHERE id = 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb';

-- 3. Create sample organizations
INSERT INTO public.organizations (id, name, description) VALUES
('c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1', 'Brand A', 'Premium fashion brand'),
('d2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2', 'Brand B', 'Luxury evening wear'),
('e3e3e3e3-e3e3-e3e3-e3e3-e3e3e3e3e3e3', 'Studio Z', 'Experimental design studio')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, description = EXCLUDED.description, updated_at = now();

-- Create test organizations for member management testing
INSERT INTO public.organizations (id, name, description) VALUES
('11111111-1111-1111-1111-111111111111', 'TestBrand1', 'Test brand for multi-user access'),
('*************-2222-2222-************', 'TestBrand2', 'Test brand for limited access')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, description = EXCLUDED.description, updated_at = now();

-- 4. Create organization memberships (NO ROLE COLUMN - roles are in users table)
INSERT INTO public.organization_memberships (user_id, organization_id) VALUES
-- Brand A memberships
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1'), -- Brand Admin 1
('e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', 'c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1'), -- Brand Member 1
-- Brand B memberships 
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'd2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2'), -- Brand Admin 2
('f6f6f6f6-f6f6-f6f6-f6f6-f6f6f6f6f6f6', 'd2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2'), -- Brand Member 2
-- Studio Z memberships
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'e3e3e3e3-e3e3-e3e3-e3e3-e3e3e3e3e3e3'), -- Brand Admin 1 (cross-brand)
-- TestBrand1 memberships (diverse roles for testing)
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', '11111111-1111-1111-1111-111111111111'), -- Brand Admin 1
('e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', '11111111-1111-1111-1111-111111111111'), -- Brand Member 1
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '11111111-1111-1111-1111-111111111111'), -- External Retoucher
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '11111111-1111-1111-1111-111111111111'), -- External Prompter
-- TestBrand2 memberships (limited for access control testing)
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', '*************-2222-2222-************') -- Brand Admin 2
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- 5. Create sample collections linked to organizations
-- NOTE: Collections use a special UUID format with 'c011-c011-c011' in the middle
INSERT INTO public.collections (id, organization_id, name, description) VALUES
('a1a1a1a1-c011-c011-c011-a1a1a1a1a1a1', 'c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1', 'Brand A - Summer 24', 'Swimwear and beach accessories'),
('b2b2b2b2-c011-c011-c011-b2b2b2b2b2b2', 'c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1', 'Brand A - Fall 24', 'Outerwear and knit essentials'),
('c3c3c3c3-c011-c011-c011-c3c3c3c3c3c3', 'd2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2', 'Brand B - Evening Wear', 'Gowns and formal attire'),
('d4d4d4d4-c011-c011-c011-d4d4d4d4d4d4', 'e3e3e3e3-e3e3-e3e3-e3e3-e3e3e3e3e3e3', 'Studio Z - Concept X', 'Experimental designs')
ON CONFLICT (id) DO NOTHING;

-- Create test collections for test organizations
INSERT INTO public.collections (id, organization_id, name, description) VALUES
('11111111-c011-c011-c011-111111111111', '11111111-1111-1111-1111-111111111111', 'TestBrand1 - Campaign 1', 'Test campaign for TestBrand1'),
('22222222-c011-c011-c011-************', '*************-2222-2222-************', 'TestBrand2 - Campaign 1', 'Test campaign for TestBrand2')
ON CONFLICT (id) DO NOTHING;

-- 6. Create sample products
INSERT INTO public.products (id, collection_id, name, description) VALUES
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'a1a1a1a1-c011-c011-c011-a1a1a1a1a1a1', 'Bikini Set A', 'Two-piece bikini in navy blue'),
('b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'a1a1a1a1-c011-c011-c011-a1a1a1a1a1a1', 'Beach Cover-up B', 'Flowy beach cover-up'),
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'b2b2b2b2-c011-c011-c011-b2b2b2b2b2b2', 'Wool Coat C', 'Long wool winter coat'),
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'c3c3c3c3-c011-c011-c011-c3c3c3c3c3c3', 'Evening Gown D', 'Black tie evening gown')
ON CONFLICT (id) DO NOTHING;

-- 7. Create sample assets
INSERT INTO public.assets (id, product_id, filename, original_filename, file_size, mime_type, workflow_stage) VALUES
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'bikini_set_a_01.jpg', 'bikini_set_a_01.jpg', 1024000, 'image/jpeg', 'draft'),
('b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'bikini_set_a_02.jpg', 'bikini_set_a_02.jpg', 1048576, 'image/jpeg', 'upscale'),
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'cover_up_b_01.jpg', 'cover_up_b_01.jpg', 950000, 'image/jpeg', 'retouch'),
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'wool_coat_c_01.jpg', 'wool_coat_c_01.jpg', 2100000, 'image/jpeg', 'final'),
('e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', 'd4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'evening_gown_d_01.jpg', 'evening_gown_d_01.jpg', 1800000, 'image/jpeg', 'draft')
ON CONFLICT (id) DO NOTHING;

-- 8. Create sample tags
INSERT INTO public.tags (id, name, color) VALUES
('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Summer', '#FF6B6B'),
('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Beach', '#4ECDC4'),
('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Formal', '#45B7D1'),
('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Winter', '#96CEB4')
ON CONFLICT (id) DO NOTHING;

-- 9. Create sample asset tags (many-to-many relationship)
INSERT INTO public.asset_tags (asset_id, tag_id) VALUES
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Bikini Set A + Summer
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'), -- Bikini Set A + Beach
('b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'), -- Bikini Set A variant + Summer
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'), -- Cover-up B + Beach
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'dddddddd-dddd-dddd-dddd-dddddddddddd'), -- Wool Coat C + Winter
('e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', 'cccccccc-cccc-cccc-cccc-cccccccccccc') -- Evening Gown D + Formal
ON CONFLICT (asset_id, tag_id) DO NOTHING;

-- 10. Create sample comments
INSERT INTO public.comments (id, asset_id, user_id, content, x_coordinate, y_coordinate) VALUES
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'Love the color scheme here!', 0.3, 0.7),
('b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', 'e5e5e5e5-e5e5-e5e5-e5e5-e5e5e5e5e5e5', 'The lighting could be adjusted in this area', 0.8, 0.2),
('c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Retouching needed on the fabric texture', 0.5, 0.4),
('d4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'd4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Ready for final approval', NULL, NULL) -- General comment, no coordinates
ON CONFLICT (id) DO NOTHING;

-- 11. Create sample pending invitations (with old organization role format for migration testing)
INSERT INTO public.pending_invitations (id, email, organization_id, role, invited_by, token, expires_at) VALUES
('a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1', '<EMAIL>', 'c1c1c1c1-c1c1-c1c1-c1c1-c1c1c1c1c1c1', 'org_member', 'c3c3c3c3-c3c3-c3c3-c3c3-c3c3c3c3c3c3', 'token123', now() + interval '24 hours'),
('b2b2b2b2-b2b2-b2b2-b2b2-b2b2b2b2b2b2', '<EMAIL>', 'd2d2d2d2-d2d2-d2d2-d2d2-d2d2d2d2d2d2', 'org_retoucher', 'd4d4d4d4-d4d4-d4d4-d4d4-d4d4d4d4d4d4', 'token456', now() + interval '36 hours')
ON CONFLICT (id) DO NOTHING;

-- Re-enable RLS
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.asset_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Display summary of seeded data with new role system
SELECT 
    'Users Created' as summary_type,
    COUNT(*) as count,
    string_agg(DISTINCT role::text, ', ') as roles
FROM public.users
UNION ALL
SELECT 
    'Organizations Created' as summary_type,
    COUNT(*) as count,
    string_agg(name, ', ') as names
FROM public.organizations
UNION ALL
SELECT 
    'Collections Created' as summary_type,
    COUNT(*) as count,
    string_agg(name, ', ') as names
FROM public.collections
UNION ALL
SELECT 
    'Memberships Created' as summary_type,
    COUNT(*) as count,
    'No roles - stored in users table' as note
FROM public.organization_memberships;

-- Show role distribution
SELECT 
    'Role Distribution' as type,
    role,
    COUNT(*) as user_count
FROM public.users 
GROUP BY role
ORDER BY user_count DESC;