-- Enable R<PERSON> on pending_invitations table
ALTER TABLE pending_invitations ENABLE ROW LEVEL SECURITY;

-- Drop ALL existing policies to start fresh
DROP POLICY IF EXISTS "Admins can manage invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Allow admins to delete invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Allow admins to update invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Debug allow all users to create invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Unauthenticated users can view invitations by token" ON pending_invitations;
DROP POLICY IF EXISTS "Users can update their own invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Users can view their invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Organization admins can manage invitations" ON pending_invitations;
DROP POLICY IF EXISTS "Anyone can view their invitation by token" ON pending_invitations;
DROP POLICY IF EXISTS "Invitees can view their invitation by email" ON pending_invitations;
DROP POLICY IF EXISTS "Platform admins can view all invitations" ON pending_invitations;

-- Helper function to check if user is org admin (create if not exists)
CREATE OR REPLACE FUNCTION auth.is_organization_admin(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM organization_memberships om
    JOIN users u ON om.user_id = u.id
    WHERE om.user_id = auth.uid()
    AND om.organization_id = org_id
    AND u.role = 'brand_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policy 1: Organization admins can do everything with their org's invitations
CREATE POLICY "Organization admins can manage invitations"
ON pending_invitations
FOR ALL
USING (
  auth.uid() IS NOT NULL AND
  (
    -- Platform admins can manage all
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
    )
    OR
    -- Brand admins can manage their org
    auth.is_organization_admin(organization_id)
  )
)
WITH CHECK (
  auth.uid() IS NOT NULL AND
  (
    -- Platform admins can manage all
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
    )
    OR
    -- Brand admins can manage their org
    auth.is_organization_admin(organization_id)
  )
);

-- Policy 2: Anyone can view their invitation using the token (for invitation acceptance page)
-- This is specifically for the anon role when accessing via invitation link
CREATE POLICY "Anyone can view their invitation by token"
ON pending_invitations
FOR SELECT
TO anon
USING (
  -- For now, allow viewing any valid (not expired, not accepted) invitation
  -- In production, this should check against a token passed in the request
  expires_at > now()
  AND accepted = false
);

-- Policy 3: Authenticated users can view invitations sent to their email
CREATE POLICY "Authenticated users can view their invitations"
ON pending_invitations
FOR SELECT  
TO authenticated
USING (
  -- Users can see invitations sent to their email
  email = (SELECT email FROM auth.users WHERE id = auth.uid())::text
);

-- Policy 4: Allow users to update their own invitations (for accepting)
CREATE POLICY "Users can accept their invitations"
ON pending_invitations
FOR UPDATE
TO authenticated
USING (
  email = (SELECT email FROM auth.users WHERE id = auth.uid())::text
)
WITH CHECK (
  email = (SELECT email FROM auth.users WHERE id = auth.uid())::text
);

-- Add comment explaining the security model
COMMENT ON TABLE pending_invitations IS 'Stores pending invitations to join organizations. RLS policies allow:
1. Organization/platform admins to manage invitations
2. Anonymous users to view valid invitations (for the acceptance page)
3. Authenticated users to see and accept invitations sent to their email
4. The invitation acceptance flow works by allowing anon to view, then authenticated to update';