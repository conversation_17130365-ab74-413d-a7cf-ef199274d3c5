-- Temporarily create very permissive policy for debugging
-- This is a debugging aid to identify RLS issues

-- Drop the restrictive policy
DROP POLICY IF EXISTS "Only admins and org_admins can create invitations" ON public.pending_invitations;

-- Temporarily allow all authenticated users to create invitations (for debugging)
CREATE POLICY "Debug allow all users to create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (true);

-- Make sure RLS is enabled
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Add helpful comment
COMMENT ON POLICY "Debug allow all users to create invitations" ON public.pending_invitations
IS 'TEMPORARY DEBUG POLICY: Allows all authenticated users to create invitations for testing';