-- Create model_library table for storing model metadata
CREATE TABLE IF NOT EXISTS public.model_library (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  code TEXT NOT NULL UNIQUE, -- S, M, L, XL, etc.
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES public.users(id)
);

-- Create model_images table for storing image references
CREATE TABLE IF NOT EXISTS public.model_images (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  model_id UUID NOT NULL REFERENCES public.model_library(id) ON DELETE CASCADE,
  angle_type TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  file_size INTEGER,
  mime_type TEXT DEFAULT 'image/webp',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(model_id, angle_type)
);

-- <PERSON><PERSON> indexes for better performance
CREATE INDEX IF NOT EXISTS idx_model_library_code ON public.model_library(code);
CREATE INDEX IF NOT EXISTS idx_model_library_is_active ON public.model_library(is_active);
CREATE INDEX IF NOT EXISTS idx_model_images_model_id ON public.model_images(model_id);
CREATE INDEX IF NOT EXISTS idx_model_images_angle_type ON public.model_images(angle_type);

-- Add RLS policies for model_library
ALTER TABLE public.model_library ENABLE ROW LEVEL SECURITY;

-- Everyone can read active models
CREATE POLICY "model_library_read_active" ON public.model_library
  FOR SELECT USING (is_active = true);

-- Platform admins can read all models
CREATE POLICY "model_library_read_all_platform_admin" ON public.model_library
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Platform admins can insert models
CREATE POLICY "model_library_insert_platform_admin" ON public.model_library
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Platform admins can update models
CREATE POLICY "model_library_update_platform_admin" ON public.model_library
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Platform admins can delete models
CREATE POLICY "model_library_delete_platform_admin" ON public.model_library
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Add RLS policies for model_images
ALTER TABLE public.model_images ENABLE ROW LEVEL SECURITY;

-- Everyone can read images for active models
CREATE POLICY "model_images_read_active" ON public.model_images
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.model_library
      WHERE model_library.id = model_images.model_id
      AND model_library.is_active = true
    )
  );

-- Platform admins can read all images
CREATE POLICY "model_images_read_all_platform_admin" ON public.model_images
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Platform admins can manage images
CREATE POLICY "model_images_insert_platform_admin" ON public.model_images
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "model_images_update_platform_admin" ON public.model_images
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "model_images_delete_platform_admin" ON public.model_images
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_model_library_updated_at
  BEFORE UPDATE ON public.model_library
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_model_images_updated_at
  BEFORE UPDATE ON public.model_images
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert seed data for existing models
INSERT INTO public.model_library (name, code, description, display_order, created_by)
VALUES 
  ('Small Model', 'S', 'Small size model for product visualization', 1, NULL),
  ('Medium Model', 'M', 'Medium size model for product visualization', 2, NULL),
  ('Large Model', 'L', 'Large size model for product visualization', 3, NULL),
  ('Extra Large Model', 'XL', 'Extra large size model for product visualization', 4, NULL)
ON CONFLICT (code) DO NOTHING;

-- Create storage bucket for model library
INSERT INTO storage.buckets (id, name, public)
VALUES ('model-library', 'model-library', true)
ON CONFLICT (id) DO NOTHING;

-- Add storage policies for model-library bucket
CREATE POLICY "model_library_bucket_read" ON storage.objects
  FOR SELECT USING (bucket_id = 'model-library');

CREATE POLICY "model_library_bucket_insert_platform_admin" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'model-library' AND
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "model_library_bucket_update_platform_admin" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'model-library' AND
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "model_library_bucket_delete_platform_admin" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'model-library' AND
    EXISTS (
      SELECT 1 FROM public.users
      WHERE users.id = auth.uid()
      AND users.role IN ('platform_super', 'platform_admin')
    )
  );