-- Migration to add TEMPORARY permissive DELETE policy for assets
-- This follows the MVP pattern established in previous migrations
-- WARNING: This policy is NOT secure for production.
-- Proper, fine-grained RLS must be implemented post-MVP.

-- Drop existing policy if it exists (idempotent)
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to delete assets in their orgs" ON public.assets;

-- Create TEMPORARY MVP DELETE policy for assets
-- Allow users to DELETE assets belonging to collections linked via client_id to organizations they are members of
CREATE POLICY "TEMP_MVP_Allow members to delete assets in their orgs"
ON public.assets FOR DELETE
USING (
   EXISTS (
     SELECT 1
     FROM public.collections c
     JOIN public.organization_memberships om ON c.client_id = om.organization_id
     WHERE c.id = assets.collection_id AND om.user_id = auth.uid()
   )
);

-- Also add the INSERT policy that exists in the current database but wasn't in the original migration
DROP POLICY IF EXISTS "Allow members to insert assets in their org collections" ON public.assets;

CREATE POLICY "Allow members to insert assets in their org collections"
ON public.assets FOR INSERT
WITH CHECK (
   EXISTS (
     SELECT 1
     FROM public.collections c
     JOIN public.organization_memberships om ON c.client_id = om.organization_id
     WHERE c.id = assets.collection_id AND om.user_id = auth.uid()
   )
);

-- Optionally, add the production-ready policy from schema.sql (commented out for MVP)
-- This would replace the permissive policy above when moving to production
/*
DROP POLICY IF EXISTS "Admins and Superadmins can delete assets" ON public.assets;

CREATE POLICY "Admins and Superadmins can delete assets" 
ON public.assets FOR DELETE 
USING (
  (
    auth.uid() IN (
      SELECT users.id
      FROM public.users
      WHERE users.role = ANY (ARRAY['admin'::public.user_role, 'superadmin'::public.user_role])
    )
  ) 
  OR 
  public.has_collection_access(collection_id, 'admin'::public.permission_level)
);
*/