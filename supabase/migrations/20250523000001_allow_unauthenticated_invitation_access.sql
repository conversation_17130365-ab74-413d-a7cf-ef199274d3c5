-- Migration: Allow unauthenticated access to pending invitations by token
-- Date: 2025-05-23
-- Description: Add policy to allow unauthenticated users to read invitations by token for acceptance flow

-- Add policy for unauthenticated users to read invitations by token
-- This is needed for the invitation acceptance page to work
CREATE POLICY "Unauthenticated users can view invitations by token"
ON public.pending_invitations FOR SELECT
TO anon
USING (
  -- Allow access only if a specific token is being queried
  -- This is secure because tokens are UUIDs that are hard to guess
  token IS NOT NULL
);

-- Add comment explaining the security model
COMMENT ON POLICY "Unauthenticated users can view invitations by token" ON public.pending_invitations IS 
'Allows unauthenticated users to read invitation details by token for the invitation acceptance flow. This is secure because tokens are UUIDs that cannot be easily guessed.';