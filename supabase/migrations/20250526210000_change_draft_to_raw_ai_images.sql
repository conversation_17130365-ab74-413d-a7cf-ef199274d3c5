-- Migration: Change workflow stage 'draft' to 'raw_ai_images'
-- This migration updates the workflow_stage enum and any existing data

-- Create a new enum with 'raw_ai_images' instead of 'draft'
CREATE TYPE public.workflow_stage_new AS ENUM (
    'upload',
    'raw_ai_images',
    'upscale',
    'retouch',
    'final'
);

-- Update any existing assets that use 'draft' to use 'raw_ai_images'
-- First remove the default constraint and convert to text
ALTER TABLE public.assets
ALTER COLUMN workflow_stage DROP DEFAULT;

ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE text;

UPDATE public.assets
SET workflow_stage = 'raw_ai_images'
WHERE workflow_stage = 'draft';

-- Now convert to the new enum type
ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE public.workflow_stage_new
USING workflow_stage::public.workflow_stage_new;

-- Restore the default value with the new enum
ALTER TABLE public.assets
ALTER COLUMN workflow_stage SET DEFAULT 'upload';

-- Drop the old enum and rename the new one
DROP TYPE public.workflow_stage;
ALTER TYPE public.workflow_stage_new RENAME TO workflow_stage;

-- Add comment for documentation
COMMENT ON TYPE public.workflow_stage IS 'Workflow stages for asset processing: upload (input assets), raw_ai_images (AI generated drafts), upscale, retouch, final';
