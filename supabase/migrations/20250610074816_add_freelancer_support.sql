-- Add is_freelancer column to users table
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS is_freelancer BOOLEAN DEFAULT false;

-- Add comment for documentation
COMMENT ON COLUMN public.users.is_freelancer IS 'Indicates if the user is an external freelancer hired by Fashionlab';

-- Create index for efficient querying of freelancers
CREATE INDEX IF NOT EXISTS idx_users_is_freelancer ON public.users(is_freelancer) WHERE is_freelancer = true;

-- Create invitation_organizations table for multi-organization invitations
CREATE TABLE IF NOT EXISTS public.invitation_organizations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invitation_id UUID NOT NULL REFERENCES public.pending_invitations(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(invitation_id, organization_id)
);

-- Enable RLS on invitation_organizations
ALTER TABLE public.invitation_organizations ENABLE ROW LEVEL SECURITY;

-- Add role column to pending_invitations if it doesn't exist
ALTER TABLE public.pending_invitations 
ADD COLUMN IF NOT EXISTS role public.user_role;

-- Add comment for documentation
COMMENT ON COLUMN public.pending_invitations.role IS 'The role to assign to the invited user. If NULL, defaults to brand_member';

-- RLS Policies for invitation_organizations
-- Platform admins can view all invitation organizations
CREATE POLICY "Platform admins can view invitation organizations"
ON public.invitation_organizations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Platform admins can create invitation organizations
CREATE POLICY "Platform admins can create invitation organizations"
ON public.invitation_organizations FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Platform admins can delete invitation organizations
CREATE POLICY "Platform admins can delete invitation organizations"
ON public.invitation_organizations FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Fix pending_invitations RLS policies to be more restrictive
-- Drop existing overly permissive policies
DROP POLICY IF EXISTS "Allow authenticated users to view invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Anyone can select pending invitations using token" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow org members to create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow org admins to create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow platform admins to create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Token holders can update their invitation" ON public.pending_invitations;
DROP POLICY IF EXISTS "Org admins can delete their invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Platform admins can delete any invitation" ON public.pending_invitations;

-- Create new restrictive policies
-- Anyone can view their own invitation by token (for accept flow)
CREATE POLICY "Users can view invitation by token"
ON public.pending_invitations FOR SELECT
TO anon, authenticated
USING (token IS NOT NULL);

-- Platform admins can view all invitations
CREATE POLICY "Platform admins can view all invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Brand admins can view invitations for their organization
CREATE POLICY "Brand admins can view org invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = pending_invitations.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
);

-- Platform admins can create any invitation
CREATE POLICY "Platform admins can create any invitation"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Brand admins can create invitations for their organization (only brand_member role)
CREATE POLICY "Brand admins can create member invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = pending_invitations.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
  AND (pending_invitations.role IS NULL OR pending_invitations.role = 'brand_member')
);

-- Users can update their own invitation (for acceptance)
CREATE POLICY "Users can update own invitation by token"
ON public.pending_invitations FOR UPDATE
TO anon, authenticated
USING (token IS NOT NULL)
WITH CHECK (token IS NOT NULL);

-- Platform admins can delete any invitation
CREATE POLICY "Platform admins can delete any invitation"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_admin', 'platform_super')
  )
);

-- Brand admins can delete invitations they created
CREATE POLICY "Brand admins can delete own invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  invited_by = auth.uid()
  AND EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role = 'brand_admin'
  )
);

-- Update handle_new_user function to support role assignment from invitations
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user was invited and get the invitation details
  IF NEW.email_confirmed_at IS NOT NULL THEN
    -- Look for pending invitation with role
    DECLARE
      invitation_record RECORD;
      org_record RECORD;
    BEGIN
      -- Get the invitation record
      SELECT pi.*, u.role as inviter_role 
      INTO invitation_record
      FROM public.pending_invitations pi
      LEFT JOIN public.users u ON u.id = pi.invited_by
      WHERE LOWER(pi.email) = LOWER(NEW.email)
        AND pi.expires_at > NOW()
        AND pi.accepted_at IS NULL
      ORDER BY pi.created_at DESC
      LIMIT 1;

      IF invitation_record.id IS NOT NULL THEN
        -- Create user with appropriate role
        INSERT INTO public.users (id, email, first_name, last_name, role, is_freelancer)
        VALUES (
          NEW.id,
          NEW.email,
          COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
          COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
          COALESCE(invitation_record.role, 'brand_member'),
          CASE 
            WHEN invitation_record.role = 'brand_admin' 
              AND invitation_record.inviter_role IN ('platform_admin', 'platform_super')
            THEN COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false)
            ELSE false
          END
        );

        -- Create organization membership for the primary organization
        INSERT INTO public.organization_memberships (user_id, organization_id)
        VALUES (NEW.id, invitation_record.organization_id);

        -- Create memberships for any additional organizations (for freelancers)
        INSERT INTO public.organization_memberships (user_id, organization_id)
        SELECT NEW.id, io.organization_id
        FROM public.invitation_organizations io
        WHERE io.invitation_id = invitation_record.id
          AND io.organization_id != invitation_record.organization_id;

        -- Mark invitation as accepted
        UPDATE public.pending_invitations
        SET accepted_at = NOW()
        WHERE id = invitation_record.id;

        -- Log the activity
        INSERT INTO public.security_activity (user_id, event_type, ip_address, metadata)
        VALUES (
          NEW.id,
          'invitation_accepted',
          COALESCE(NEW.raw_user_meta_data->>'ip_address', 'unknown'),
          jsonb_build_object(
            'invitation_id', invitation_record.id,
            'organization_id', invitation_record.organization_id,
            'role', COALESCE(invitation_record.role, 'brand_member'),
            'is_freelancer', CASE 
              WHEN invitation_record.role = 'brand_admin' 
                AND invitation_record.inviter_role IN ('platform_admin', 'platform_super')
              THEN COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false)
              ELSE false
            END
          )
        );
      ELSE
        -- No invitation found, create regular user
        INSERT INTO public.users (id, email, first_name, last_name, role, is_freelancer)
        VALUES (
          NEW.id,
          NEW.email,
          COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
          COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
          'platform_admin',
          false
        );
      END IF;
    END;
  ELSE
    -- Email not confirmed yet, just create basic user record
    INSERT INTO public.users (id, email, role, is_freelancer)
    VALUES (NEW.id, NEW.email, 'platform_admin', false);
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add helper function to check if user is a freelancer
CREATE OR REPLACE FUNCTION public.is_user_freelancer(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.users
    WHERE id = user_id
    AND is_freelancer = true
    AND role = 'brand_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add helper function to get freelancer's organizations
CREATE OR REPLACE FUNCTION public.get_freelancer_organizations(freelancer_id UUID)
RETURNS TABLE (
  organization_id UUID,
  organization_name TEXT,
  joined_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    om.organization_id,
    o.name as organization_name,
    om.created_at as joined_at
  FROM public.organization_memberships om
  JOIN public.organizations o ON o.id = om.organization_id
  WHERE om.user_id = freelancer_id
    AND EXISTS (
      SELECT 1 FROM public.users u
      WHERE u.id = freelancer_id
      AND u.is_freelancer = true
      AND u.role = 'brand_admin'
    )
  ORDER BY om.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;