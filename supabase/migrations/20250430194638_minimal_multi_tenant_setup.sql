-- Note: Base tables and enums are created in 20250430194637_create_base_tables.sql

-- 1. Ensure 'user' value exists in user_role enum (migration-specific addition)
DO $$
BEGIN
    -- If enum exists, check if 'user' value exists and add it if not
    IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumtypid = 'public.user_role'::regtype AND enumlabel = 'user') THEN
        ALTER TYPE public.user_role ADD VALUE IF NOT EXISTS 'user';
    END IF;
END $$;

-- 2. Create the organization_role enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'organization_role' AND typtype = 'e') THEN
        CREATE TYPE public.organization_role AS ENUM (
            'org_admin',
            'org_member'
        );
    END IF;
END $$;

-- 3. Create the organizations table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Enable RLS for organizations table (Good practice even if policies are deferred)
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- Trigger to update 'updated_at' timestamp
CREATE TRIGGER handle_updated_at
BEFORE UPDATE ON public.organizations
FOR EACH ROW
EXECUTE FUNCTION public.update_modified_column(); -- Ensure this function exists from the dump

-- 4. Create the organization_memberships table
CREATE TABLE public.organization_memberships (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    role public.organization_role NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    -- Ensure a user can only have one role per organization
    CONSTRAINT unique_user_organization UNIQUE (user_id, organization_id)
);

-- Enable RLS for organization_memberships table
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;

-- Add indexes for faster lookups
CREATE INDEX idx_organization_memberships_user_id ON public.organization_memberships(user_id);
CREATE INDEX idx_organization_memberships_organization_id ON public.organization_memberships(organization_id);

-- 5. Create the users table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.users (
    id UUID NOT NULL PRIMARY KEY,
    email TEXT NOT NULL,
    role public.user_role DEFAULT 'client'::public.user_role NOT NULL,
    display_name TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS for users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Add foreign key constraint to link with auth.users
ALTER TABLE public.users 
  ADD CONSTRAINT users_id_fkey 
  FOREIGN KEY (id) 
  REFERENCES auth.users(id) 
  ON DELETE CASCADE;

-- 6. Create function and trigger to handle new user signup
-- This function copies the new user from auth.users to public.users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER -- Important: Allows the function to write to public.users
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.users (id, email, role, display_name)
  VALUES (
    NEW.id,
    NEW.email,
    'user', -- Default role for new users
    -- Attempt to get name from raw_user_meta_data, fallback to email part
    COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
  );
  RETURN NEW;
END;
$$;

-- Create the trigger on the auth.users table
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();