-- Fix permission for pending_invitations to restrict to admins and org_admins only
-- This corrects the overly permissive policy we created earlier

-- Drop the overly permissive policy
DROP POLICY IF EXISTS "Allow authenticated users to create invitations" ON public.pending_invitations;

-- Create properly restricted policy for INSERT
CREATE POLICY "Only admins and org_admins can create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  -- User is a global admin/superadmin
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  -- User is an org_admin for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- Add helpful comment
COMMENT ON POLICY "Only admins and org_admins can create invitations" ON public.pending_invitations
IS 'Ensures only admins, superadmins, and organization admins can create invitations';