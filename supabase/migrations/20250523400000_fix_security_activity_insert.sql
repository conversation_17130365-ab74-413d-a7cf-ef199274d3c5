-- Migration: Fix security activity table to allow inserts
-- Date: 2025-05-23
-- Description: Add INSERT policy for security_activity table to allow users to log their own security events

-- Add INSERT policy for security_activity table
CREATE POLICY "Users can insert their own security activity"
ON public.security_activity FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Add UPDATE policy in case we need it later
CREATE POLICY "Users can update their own security activity"
ON public.security_activity FOR UPDATE
TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Add comment explaining the policy
COMMENT ON POLICY "Users can insert their own security activity" ON public.security_activity IS 
'Allows authenticated users to insert security activity records for themselves. Used for logging invitation cancellations, password changes, etc.';