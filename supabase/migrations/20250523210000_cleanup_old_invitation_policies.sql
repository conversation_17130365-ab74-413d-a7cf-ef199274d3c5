-- Clean up any remaining old invitation policies that weren't removed
-- Keep only the new "Standard" policies that use public.users.role exclusively

-- Remove any remaining old policies
DROP POLICY IF EXISTS "Admins and invitees can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Ad<PERSON> can create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Ad<PERSON> can delete invitations" ON public.pending_invitations;

-- Verify that only our Standard policies remain
-- These policies use public.users.role exclusively:
-- - "Standard: View invitations"
-- - "Standard: Create invitations" 
-- - "Standard: Update invitations"
-- - "Standard: Delete invitations"

-- Add final comment to confirm single role system is complete
COMMENT ON TABLE public.pending_invitations
IS 'Single role system complete: All policies use public.users.role only. Auth metadata sync removed.';