-- Create base tables that are referenced in subsequent migrations
-- This migration ensures all necessary tables exist before policies are applied

-- 1. Create enums first

-- User role enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role' AND typtype = 'e') THEN
        CREATE TYPE public.user_role AS ENUM ('client', 'admin', 'superadmin', 'user');
    END IF;
END $$;

-- Workflow stage enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'workflow_stage' AND typtype = 'e') THEN
        CREATE TYPE public.workflow_stage AS ENUM ('upload', 'draft', 'upscale', 'retouch', 'final');
    END IF;
END $$;

-- Tag category enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tag_category' AND typtype = 'e') THEN
        CREATE TYPE public.tag_category AS ENUM (
            'workflow_stage',
            'descriptive',
            'campaign',
            'season',
            'usage',
            'view_type',
            'product_specific',
            'custom'
        );
    END IF;
END $$;

-- Comment status enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'comment_status' AND typtype = 'e') THEN
        CREATE TYPE public.comment_status AS ENUM ('open', 'resolved', 'archived');
    END IF;
END $$;

-- Permission level enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permission_level' AND typtype = 'e') THEN
        CREATE TYPE public.permission_level AS ENUM ('view', 'edit', 'admin');
    END IF;
END $$;

-- Asset type enum
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'asset_type' AND typtype = 'e') THEN
        CREATE TYPE public.asset_type AS ENUM (
            'product-front',
            'product-back',
            'product-detail',
            'product-lifestyle',
            'marketing',
            'social-media',
            'catalog',
            'website-banner',
            'email-campaign',
            'lookbook',
            'advertisement',
            'packaging',
            'other'
        );
    END IF;
END $$;

-- 2. Create base helper functions

-- Update modified column function
CREATE OR REPLACE FUNCTION public.update_modified_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- 3. Create tables in dependency order

-- Clients table
CREATE TABLE IF NOT EXISTS public.clients (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    name TEXT NOT NULL,
    logo_url TEXT,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Collections table
CREATE TABLE IF NOT EXISTS public.collections (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'active' NOT NULL CHECK (status IN ('active', 'archived')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    cover_image_url TEXT,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    collection_id UUID REFERENCES public.collections(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Assets table
CREATE TABLE IF NOT EXISTS public.assets (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
    collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    metadata JSONB,
    workflow_stage public.workflow_stage DEFAULT 'upload' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    compressed_path TEXT,
    original_path TEXT,
    thumbnail_path TEXT
);

-- Comments table
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    asset_id UUID REFERENCES public.assets(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_annotation BOOLEAN DEFAULT false NOT NULL,
    coordinates JSONB,
    status public.comment_status DEFAULT 'open' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Tags table
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    name TEXT NOT NULL,
    category public.tag_category NOT NULL,
    color TEXT DEFAULT '#cccccc' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT tags_name_category_key UNIQUE (name, category)
);

-- Asset tags junction table
CREATE TABLE IF NOT EXISTS public.asset_tags (
    asset_id UUID NOT NULL REFERENCES public.assets(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES public.tags(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT now(),
    PRIMARY KEY (asset_id, tag_id)
);

-- User collection access table
CREATE TABLE IF NOT EXISTS public.user_collection_access (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    collection_id UUID REFERENCES public.collections(id) ON DELETE CASCADE,
    permission_level public.permission_level DEFAULT 'view' NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT user_collection_access_user_id_collection_id_key UNIQUE (user_id, collection_id)
);

-- 4. Create indexes
CREATE INDEX IF NOT EXISTS idx_collections_client_id ON public.collections(client_id);
CREATE INDEX IF NOT EXISTS idx_assets_collection_id ON public.assets(collection_id);
CREATE INDEX IF NOT EXISTS idx_assets_product_id ON public.assets(product_id);
CREATE INDEX IF NOT EXISTS idx_comments_asset_id ON public.comments(asset_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_asset_tags_asset_id ON public.asset_tags(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_tags_tag_id ON public.asset_tags(tag_id);
CREATE INDEX IF NOT EXISTS idx_user_collection_access_user_id ON public.user_collection_access(user_id);
CREATE INDEX IF NOT EXISTS idx_user_collection_access_collection_id ON public.user_collection_access(collection_id);

-- 5. Create update triggers (idempotent - check if trigger exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_clients_updated_at') THEN
        CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_collections_updated_at') THEN
        CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON public.collections
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_products_updated_at') THEN
        CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_assets_updated_at') THEN
        CREATE TRIGGER update_assets_updated_at BEFORE UPDATE ON public.assets
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_comments_updated_at') THEN
        CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON public.comments
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_tags_updated_at') THEN
        CREATE TRIGGER update_tags_updated_at BEFORE UPDATE ON public.tags
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_collection_access_updated_at') THEN
        CREATE TRIGGER update_user_collection_access_updated_at BEFORE UPDATE ON public.user_collection_access
            FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;