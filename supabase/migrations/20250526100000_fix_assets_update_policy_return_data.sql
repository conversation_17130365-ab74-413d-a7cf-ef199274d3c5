-- Fix assets update policy to properly return updated data
-- The issue might be that the WITH CHECK clause is too restrictive

-- First, let's drop the existing update policy
DROP POLICY IF EXISTS "Members can update assets in their organizations" ON public.assets;

-- Create a new update policy that should work properly
CREATE POLICY "Members can update assets in their organizations"
ON public.assets FOR UPDATE
USING (
  -- Allow if user is a member of the organization that owns the collection
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  -- Allow platform users to update any asset
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (
  -- For WITH CHECK, we need to check against the NEW row values
  -- But for collection_id, it should remain the same, so we can use the same check
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = collection_id  -- Note: no table prefix needed in WITH CHECK
      AND om.user_id = auth.uid()
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
);

-- Add a simpler policy specifically for platform users to ensure they can always update
DROP POLICY IF EXISTS "Platform users can update any asset" ON public.assets;

CREATE POLICY "Platform users can update any asset"
ON public.assets FOR UPDATE
USING (
  auth.uid() IN (
    SELECT id FROM public.users 
    WHERE role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (true);  -- Platform users can update to any values

-- Ensure the policies are in the correct order (platform policy should be checked first)
-- PostgreSQL evaluates permissive policies with OR logic, so both will be checked

-- Let's also create a debug function to test updates directly
CREATE OR REPLACE FUNCTION debug_update_asset_workflow_stage(
  p_asset_id UUID,
  p_new_stage TEXT
)
RETURNS TABLE(
  success BOOLEAN,
  updated_id UUID,
  old_stage TEXT,
  new_stage TEXT,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER  -- This runs with the permissions of the function owner
AS $$
DECLARE
  v_old_stage TEXT;
  v_user_role TEXT;
  v_error TEXT;
BEGIN
  -- Get current user role
  SELECT role INTO v_user_role
  FROM public.users
  WHERE id = auth.uid();

  -- Get current workflow stage
  SELECT workflow_stage INTO v_old_stage
  FROM public.assets
  WHERE id = p_asset_id;

  -- Try to update
  BEGIN
    UPDATE public.assets
    SET workflow_stage = p_new_stage
    WHERE id = p_asset_id
    RETURNING id INTO updated_id;

    -- If we get here, update succeeded
    RETURN QUERY
    SELECT 
      true,
      p_asset_id,
      v_old_stage,
      p_new_stage,
      NULL::TEXT;
  EXCEPTION 
    WHEN OTHERS THEN
      -- Capture the error
      GET STACKED DIAGNOSTICS v_error = MESSAGE_TEXT;
      
      RETURN QUERY
      SELECT 
        false,
        p_asset_id,
        v_old_stage,
        p_new_stage,
        v_error || ' (User role: ' || COALESCE(v_user_role, 'unknown') || ')';
  END;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION debug_update_asset_workflow_stage TO authenticated;

-- Add comment explaining the issue and solution
COMMENT ON POLICY "Platform users can update any asset" ON public.assets IS 
'Separate policy for platform users with simplified WITH CHECK to ensure updates return data properly. 
This fixes the issue where updates were succeeding but returning empty arrays due to WITH CHECK constraints.';