-- Migration: Fix Auth redirect URLs for staging environment
-- Date: 2025-05-23
-- Description: Update Auth configuration to use correct site URLs

-- Update Auth configuration (this needs to be done via dashboard or CLI, but document here)
-- The main issue is that site_url in auth.email configuration needs to be updated

-- This migration serves as documentation of the fix needed:
-- 1. Go to Supabase Dashboard > Authentication > Settings
-- 2. Update Site URL to match your staging environment
-- 3. Update Redirect URLs to include staging domain

-- For now, let's create a simple function to help debug Auth issues
CREATE OR REPLACE FUNCTION public.debug_auth_config()
RETURNS json
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT json_build_object(
    'current_user', auth.uid(),
    'user_role', (SELECT role FROM public.users WHERE id = auth.uid()),
    'timestamp', now()
  );
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.debug_auth_config() TO authenticated;

COMMENT ON FUNCTION public.debug_auth_config() IS 'Helper function to debug authentication state during signup/invitation flow.';