-- Comprehensive fix for profile functionality on staging

-- 1. Ensure RLS is enabled on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- 2. Drop all existing policies on users table to start fresh
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read their own profile" ON public.users;
DROP POLICY IF EXISTS "Allow users to read their own user record" ON public.users;
DROP POLICY IF EXISTS "Allow authenticated users to read all user records" ON public.users;
DROP POLICY IF EXISTS "TEMP_MVP_Allow authenticated read access to users" ON public.users;

-- 3. Create comprehensive policies for users table
-- Allow users to read their own record
CREATE POLICY "Users can read own record"
ON public.users FOR SELECT
TO authenticated
USING (id = auth.uid());

-- Allow users to update their own record
CREATE POLICY "Users can update own record"
ON public.users FOR UPDATE
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- Drop the existing admin read policy if it exists
DROP POLICY IF EXISTS "Ad<PERSON> can read all users" ON public.users;

-- Allow admins to read all users
CREATE POLICY "Ad<PERSON> can read all users"
ON public.users FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('admin', 'superadmin')
  )
);

-- Drop the existing admin update policy if it exists
DROP POLICY IF EXISTS "Admins can update all users" ON public.users;

-- Allow admins to update all users
CREATE POLICY "Admins can update all users"
ON public.users FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('admin', 'superadmin')
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('admin', 'superadmin')
  )
);

-- 4. Ensure avatars bucket exists with correct configuration
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars', 
  'avatars', 
  true, 
  5242880, -- 5MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- 5. Drop existing storage policies to avoid conflicts
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view avatars" ON storage.objects;

-- 6. Create fresh storage policies
CREATE POLICY "Avatar upload policy"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'avatars' 
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Avatar update policy"
ON storage.objects FOR UPDATE
TO authenticated
USING (
  bucket_id = 'avatars' 
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Avatar delete policy"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'avatars' 
  AND (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Avatar view policy"
ON storage.objects FOR SELECT
TO public
USING (bucket_id = 'avatars');

-- 7. Ensure security_activity table has correct structure and policies
-- Drop existing policies
DROP POLICY IF EXISTS "Users can read their own security activity" ON public.security_activity;

-- Create fresh policy
CREATE POLICY "Users can view own security activity"
ON public.security_activity FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.security_activity TO authenticated;

-- 9. Ensure the log_security_event function exists
CREATE OR REPLACE FUNCTION public.log_security_event(
    p_user_id UUID,
    p_event_type TEXT,
    p_ip_address TEXT DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_location TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_activity_id UUID;
BEGIN
    INSERT INTO public.security_activity (user_id, event_type, ip_address, user_agent, location)
    VALUES (p_user_id, p_event_type, p_ip_address, p_user_agent, p_location)
    RETURNING id INTO v_activity_id;
    
    RETURN v_activity_id;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION public.log_security_event TO authenticated;