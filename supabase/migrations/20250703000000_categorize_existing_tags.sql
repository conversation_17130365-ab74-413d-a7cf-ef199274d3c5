-- Migration to add standard global and styling tags
-- This migration should be run after the tag_system_redesign migration

-- Add standard angle tags (these are global)
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
VALUES 
  ('Three Quarter', 'angles', '#3B82F6', NULL, NOW(), NOW()),
  ('Close Up', 'angles', '#8B5CF6', NULL, NOW(), NOW()),
  ('Full Body', 'angles', '#10B981', NULL, NOW(), NOW()),
  ('Profile', 'angles', '#F59E0B', NULL, NOW(), NOW()),
  ('Overhead', 'angles', '#EF4444', NULL, NOW(), NOW())
ON CONFLICT DO NOTHING;

-- Add common styling tags
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
VALUES 
  ('Casual', 'styling', '#EC4899', NULL, NOW(), NOW()),
  ('Formal', 'styling', '#F59E0B', NULL, NOW(), NOW()),
  ('Street Style', 'styling', '#6366F1', NULL, NOW(), NOW()),
  ('Editorial', 'styling', '#84CC16', NULL, NOW(), NOW()),
  ('Minimalist', 'styling', '#06B6D4', NULL, NOW(), NOW()),
  ('Vintage', 'styling', '#A855F7', NULL, NOW(), NOW()),
  ('Modern', 'styling', '#3B82F6', NULL, NOW(), NOW()),
  ('Classic', 'styling', '#10B981', NULL, NOW(), NOW())
ON CONFLICT DO NOTHING;