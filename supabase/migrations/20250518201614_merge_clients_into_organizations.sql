-- Merge clients table into organizations table
-- This migration consolidates the multi-tenant architecture

-- 1. Add client-specific fields to organizations table
ALTER TABLE public.organizations 
ADD COLUMN IF NOT EXISTS logo_url TEXT,
ADD COLUMN IF NOT EXISTS description TEXT;

-- 2. Copy data from clients to organizations
UPDATE public.organizations o
SET 
    logo_url = c.logo_url,
    description = c.description,
    updated_at = NOW()
FROM public.clients c
WHERE o.id = c.id;

-- 3. Update collections table to use organization_id instead of client_id
ALTER TABLE public.collections 
ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE;

-- Copy client_id values to organization_id
UPDATE public.collections
SET organization_id = client_id
WHERE client_id IS NOT NULL;

-- 4. Update user_collection_access to reference organizations
-- Note: This table doesn't directly reference clients, so no changes needed

-- 5. Before dropping client_id, we need to update all policies that reference it
-- Drop ALL policies that might reference client_id
-- First, get the policies from all migrations
DROP POLICY IF EXISTS "Allow members to read their collections" ON public.collections;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to insert collections in their orgs" ON public.collections;
DROP POLICY IF EXISTS "Allow org_admin to update collections" ON public.collections;
DROP POLICY IF EXISTS "Allow org_admin to delete collections" ON public.collections;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read assets in their orgs" ON public.assets;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to delete assets in their orgs" ON public.assets;
DROP POLICY IF EXISTS "Allow members to insert assets in their org collections" ON public.assets;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read comments for accessible assets" ON public.comments;

-- Also drop the policies from the restrict_collection_edit migration that will be created afterwards
-- We'll need to drop ALL existing policies on collections to be safe
DO $$
DECLARE
    policy_rec RECORD;
BEGIN
    -- Drop all existing policies on collections table
    FOR policy_rec IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'collections'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.collections', policy_rec.policyname);
    END LOOP;
    
    -- Drop all existing policies on assets table that might reference client_id through collections
    FOR policy_rec IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'assets'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.assets', policy_rec.policyname);
    END LOOP;
    
    -- Drop all existing policies on comments table that might reference client_id through collections
    FOR policy_rec IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'comments'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON public.comments', policy_rec.policyname);
    END LOOP;
END $$;

-- 6. Now safe to drop the client_id column (after ensuring data is migrated)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM public.collections 
        WHERE organization_id IS NULL AND client_id IS NOT NULL
    ) THEN
        -- Drop the foreign key constraint first
        ALTER TABLE public.collections 
        DROP CONSTRAINT IF EXISTS collections_client_id_fkey;
        
        -- Now drop the column
        ALTER TABLE public.collections 
        DROP COLUMN IF EXISTS client_id;
    ELSE
        RAISE EXCEPTION 'Migration failed: Some collections still have null organization_id';
    END IF;
END $$;

-- 7. Update storage policies to use organizations instead of clients
-- Note: Storage policies were already using organization membership checks, so no changes needed

-- 8. Drop clients table (CASCADE will handle any remaining dependencies)
DROP TABLE IF EXISTS public.clients CASCADE;

-- 9. Add NOT NULL constraint to organization_id in collections
ALTER TABLE public.collections 
ALTER COLUMN organization_id SET NOT NULL;

-- 10. Update indexes
DROP INDEX IF EXISTS idx_collections_client_id;
CREATE INDEX IF NOT EXISTS idx_collections_organization_id ON public.collections(organization_id);

-- 11. Update or create RLS policies for collections to use organization_id
-- Note: Some policies were already dropped in step 5
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read collections in their orgs" ON public.collections;

-- Create new policies using organization_id
CREATE POLICY "Organization members can view collections"
ON public.collections FOR SELECT
TO authenticated
USING (
    -- Check if user is a member of the organization
    EXISTS (
        SELECT 1 FROM public.organization_memberships om
        WHERE om.organization_id = collections.organization_id
        AND om.user_id = auth.uid()
    )
    OR
    -- Or if user is admin/superadmin
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Only org_admin can create collections (and platform admin/superadmin)
CREATE POLICY "Organization admins can create collections"
ON public.collections FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.organization_memberships om
        WHERE om.organization_id = collections.organization_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Only org_admin can update collections (and platform admin/superadmin)
CREATE POLICY "Organization admins can update collections"
ON public.collections FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.organization_memberships om
        WHERE om.organization_id = collections.organization_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Only org_admin can delete collections (and platform admin/superadmin)
CREATE POLICY "Organization admins can delete collections"
ON public.collections FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.organization_memberships om
        WHERE om.organization_id = collections.organization_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Update assets policies to use organization_id through collections
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read assets in their orgs" ON public.assets;

CREATE POLICY "Organization members can view assets"
ON public.assets FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1
        FROM public.collections c
        JOIN public.organization_memberships om ON c.organization_id = om.organization_id
        WHERE c.id = assets.collection_id 
        AND om.user_id = auth.uid()
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Create INSERT policy for assets
CREATE POLICY "Organization members can insert assets"
ON public.assets FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1
        FROM public.collections c
        JOIN public.organization_memberships om ON c.organization_id = om.organization_id
        WHERE c.id = assets.collection_id 
        AND om.user_id = auth.uid()
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Create DELETE policy for assets
CREATE POLICY "Organization members can delete assets"
ON public.assets FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1
        FROM public.collections c
        JOIN public.organization_memberships om ON c.organization_id = om.organization_id
        WHERE c.id = assets.collection_id 
        AND om.user_id = auth.uid()
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Update comments policies to use the new structure
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read comments for accessible assets" ON public.comments;

CREATE POLICY "Organization members can view comments"
ON public.comments FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1
        FROM public.assets a
        JOIN public.collections c ON a.collection_id = c.id
        JOIN public.organization_memberships om ON c.organization_id = om.organization_id
        WHERE a.id = comments.asset_id
        AND om.user_id = auth.uid()
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Add comment about migration completion
COMMENT ON TABLE public.organizations IS 'Multi-tenant organizations (brands) - merged with former clients table';
COMMENT ON COLUMN public.organizations.logo_url IS 'Organization logo URL (migrated from clients table)';
COMMENT ON COLUMN public.organizations.description IS 'Organization description (migrated from clients table)';
COMMENT ON COLUMN public.collections.organization_id IS 'References the organization that owns this collection (formerly client_id)';