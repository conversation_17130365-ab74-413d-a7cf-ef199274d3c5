-- Fix recursive RLS policy for users table
-- First, drop the problematic recursive policy
DROP POLICY IF EXISTS "Organization admins can view user profiles of members and inviters" ON public.users;

-- Create a simpler policy for admins to view all users
-- This is much simpler than the previous policy and avoids recursion
CREATE POLICY "Admins can view all users" 
ON public.users
FOR SELECT
TO authenticated
USING (
  -- Current user is viewing their own profile
  auth.uid() = id
  OR
  -- Current user is an admin/superadmin (can view all users)
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
);

-- Add a permissive policy to allow all authenticated users to view basic user info
-- This allows the app to function even without other complex permissions
CREATE POLICY "Allow authenticated users to read basic user info"
ON public.users
FOR SELECT
TO authenticated
USING (true);
