-- Fix all insert policies to properly allow platform users to create records

-- Assets: Platform users should be able to upload to any collection
DROP POLICY IF EXISTS "assets_insert_policy" ON assets;
CREATE POLICY "assets_insert_policy"
    ON assets FOR INSERT
    WITH CHECK (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = assets.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- Products: Platform users should be able to create in any collection
DROP POLICY IF EXISTS "products_insert_policy" ON products;
CREATE POLICY "products_insert_policy"
    ON products FOR INSERT
    WITH CHECK (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- Asset tags: Platform users should be able to tag any asset
DROP POLICY IF EXISTS "asset_tags_insert_policy" ON asset_tags;
CREATE POLICY "asset_tags_insert_policy"
    ON asset_tags FOR INSERT
    WITH CHECK (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = asset_tags.asset_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- Comments: Platform users should be able to comment on any asset
DROP POLICY IF EXISTS "comments_insert_policy" ON comments;
CREATE POLICY "comments_insert_policy"
    ON comments FOR INSERT
    WITH CHECK (
        auth.uid() = user_id AND (
            auth.is_platform_user() OR
            EXISTS (
                SELECT 1 FROM assets a
                JOIN collections c ON a.collection_id = c.id
                WHERE a.id = comments.asset_id
                AND auth.user_in_organization(c.organization_id)
            )
        )
    );

-- Bulk uploads: Platform users should be able to bulk upload to any collection
DROP POLICY IF EXISTS "bulk_uploads_insert_policy" ON bulk_uploads;
CREATE POLICY "bulk_uploads_insert_policy"
    ON bulk_uploads FOR INSERT
    WITH CHECK (
        auth.uid() = user_id AND (
            auth.is_platform_user() OR
            EXISTS (
                SELECT 1 FROM collections c
                WHERE c.id = bulk_uploads.collection_id
                AND auth.user_in_organization(c.organization_id)
            )
        )
    );

-- Update helper text
COMMENT ON POLICY "collections_insert_policy" ON collections IS 'Platform users can create collections in any org, members can create in their own org';
COMMENT ON POLICY "assets_insert_policy" ON assets IS 'Platform users can upload to any collection, members can upload to their org collections';
COMMENT ON POLICY "products_insert_policy" ON products IS 'Platform users can create products in any collection, members can create in their org collections';
COMMENT ON POLICY "asset_tags_insert_policy" ON asset_tags IS 'Platform users can tag any asset, members can tag assets in their org';
COMMENT ON POLICY "comments_insert_policy" ON comments IS 'Platform users can comment on any asset, members can comment on their org assets';
COMMENT ON POLICY "bulk_uploads_insert_policy" ON bulk_uploads IS 'Platform users can bulk upload to any collection, members can bulk upload to their org collections';