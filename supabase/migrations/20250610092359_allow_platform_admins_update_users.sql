-- Allow platform admins to update user details
-- This enables the user management edit functionality

-- Drop existing update policy to replace it
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;

-- Create a more comprehensive update policy
CREATE POLICY "Users can update profiles" ON public.users
FOR UPDATE
TO authenticated
USING (
  -- Users can update their own profile
  id = auth.uid()
  OR
  -- Platform admins can update any user
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('platform_admin', 'platform_super')
  )
)
WITH CHECK (
  -- For self-updates, allow all changes except role
  (id = auth.uid() AND (
    -- Either the role hasn't changed
    role = (SELECT role FROM users WHERE id = auth.uid())
    -- Or the user is a platform admin (can change their own role)
    OR EXISTS (
      SELECT 1 FROM users
      WHERE id = auth.uid()
      AND role IN ('platform_admin', 'platform_super')
    )
  ))
  OR
  -- Platform admins can update any user (including role changes)
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('platform_admin', 'platform_super')
  )
);

-- Also ensure platform admins can view all users (if not already exists)
-- This is needed for the user management table
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'users' 
    AND policyname = 'Platform admins can view all users'
  ) THEN
    CREATE POLICY "Platform admins can view all users" ON public.users
    FOR SELECT
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE id = auth.uid()
        AND role IN ('platform_admin', 'platform_super')
      )
    );
  END IF;
END $$;