-- Create products table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  sku TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT,
  sizes TEXT[] DEFAULT '{}',
  colors TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  CONSTRAINT products_collection_sku_unique UNIQUE (collection_id, sku)
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_products_collection_id ON public.products(collection_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON public.products(sku);

-- Enable RLS
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "products_select_policy" ON public.products;
DROP POLICY IF EXISTS "products_insert_policy" ON public.products;
DROP POLICY IF EXISTS "products_update_policy" ON public.products;
DROP POLICY IF EXISTS "products_delete_policy" ON public.products;

-- Create RLS policies - inherit from collection permissions
CREATE POLICY "products_select_policy" ON public.products
FOR SELECT TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = products.collection_id
    AND (
      -- Platform admins
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Organization members can view
      c.organization_id IN (
        SELECT om.organization_id
        FROM public.organization_memberships om
        WHERE om.user_id = auth.uid()
      )
    )
  )
);

CREATE POLICY "products_insert_policy" ON public.products
FOR INSERT TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = products.collection_id
    AND (
      -- Platform admins
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can insert
      EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        JOIN public.users u ON u.id = om.user_id
        WHERE om.organization_id = c.organization_id
        AND om.user_id = auth.uid()
        AND u.role = 'brand_admin'
      )
    )
  )
);

CREATE POLICY "products_update_policy" ON public.products
FOR UPDATE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = products.collection_id
    AND (
      -- Platform admins
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can update
      EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        JOIN public.users u ON u.id = om.user_id
        WHERE om.organization_id = c.organization_id
        AND om.user_id = auth.uid()
        AND u.role = 'brand_admin'
      )
    )
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = products.collection_id
    AND (
      -- Platform admins
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can update
      EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        JOIN public.users u ON u.id = om.user_id
        WHERE om.organization_id = c.organization_id
        AND om.user_id = auth.uid()
        AND u.role = 'brand_admin'
      )
    )
  )
);

CREATE POLICY "products_delete_policy" ON public.products
FOR DELETE TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = products.collection_id
    AND (
      -- Platform admins
      EXISTS (
        SELECT 1 FROM public.users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can delete
      EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        JOIN public.users u ON u.id = om.user_id
        WHERE om.organization_id = c.organization_id
        AND om.user_id = auth.uid()
        AND u.role = 'brand_admin'
      )
    )
  )
);

-- Add update trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_products_updated_at') THEN
        CREATE TRIGGER update_products_updated_at 
          BEFORE UPDATE ON public.products
          FOR EACH ROW 
          EXECUTE FUNCTION public.update_modified_column();
    END IF;
END $$;