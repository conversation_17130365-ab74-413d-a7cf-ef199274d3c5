-- Restrict collection UPDATE and DELETE operations to org_admin users

-- Drop existing UPDATE policy if it exists
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to update collections" ON public.collections;
DROP POLICY IF EXISTS "Allow members to update collections" ON public.collections;

-- Create UPDATE policy that only allows org_admin users to update collections in their organization
CREATE POLICY "Allow org_admin to update collections"
ON public.collections FOR UPDATE
TO authenticated
USING (
    -- User must be org_admin of the organization that owns this collection
    EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        WHERE om.organization_id = collections.client_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    -- Or user must be platform admin/superadmin
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
)
WITH CHECK (
    -- Same check for the new data
    EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        WHERE om.organization_id = collections.client_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Drop existing DELETE policy if it exists
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to delete collections" ON public.collections;
DROP POLICY IF EXISTS "Allow members to delete collections" ON public.collections;

-- Create DELETE policy that only allows org_admin users to delete collections in their organization
CREATE POLICY "Allow org_admin to delete collections"
ON public.collections FOR DELETE
TO authenticated
USING (
    -- User must be org_admin of the organization that owns this collection
    EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        WHERE om.organization_id = collections.client_id
        AND om.user_id = auth.uid()
        AND om.role = 'org_admin'
    )
    OR
    -- Or user must be platform admin/superadmin
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);