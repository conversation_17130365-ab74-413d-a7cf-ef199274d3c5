-- Add custom prompt text and metadata fields to model_library
ALTER TABLE public.model_library
ADD COLUMN IF NOT EXISTS prompt_text TEXT,
ADD COLUMN IF NOT EXISTS model_persona_name TEXT,
ADD COLUMN IF NOT EXISTS model_backstory TEXT,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Add custom prompt text for angles in model_images
ALTER TABLE public.model_images
ADD COLUMN IF NOT EXISTS angle_prompt_text TEXT,
ADD COLUMN IF NOT EXISTS is_white_background BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS client_view_path TEXT;

-- Add comment to explain the fields
COMMENT ON COLUMN public.model_library.prompt_text IS 'Custom prompt text for this model provided by Fashion Lab team';
COMMENT ON COLUMN public.model_library.model_persona_name IS 'Display name/persona for the model (e.g., "<PERSON>", "<PERSON>")';
COMMENT ON COLUMN public.model_library.model_backstory IS 'Backstory or description for storytelling purposes';
COMMENT ON COLUMN public.model_images.angle_prompt_text IS 'Custom prompt text specific to this angle';
COMMENT ON COLUMN public.model_images.is_white_background IS 'Whether this image has a white background suitable for client viewing';
COMMENT ON COLUMN public.model_images.client_view_path IS 'Optional separate image path for client-facing white background version';