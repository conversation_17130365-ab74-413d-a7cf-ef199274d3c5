-- Fix products table RLS policies with correct schema reference
-- The previous migration may have created policies on 'products' without schema qualifier

-- First, drop any existing policies on both possible table references
DROP POLICY IF EXISTS "Users can view products in their collections" ON products;
DROP POLICY IF EXISTS "Users can create products in their collections" ON products;
DROP POLICY IF EXISTS "Ad<PERSON> can update products" ON products;
DROP POLICY IF EXISTS "Admins can delete products" ON products;

DROP POLICY IF EXISTS "Users can view products in their collections" ON public.products;
DROP POLICY IF EXISTS "Users can create products in their collections" ON public.products;
DROP POLICY IF EXISTS "Admins can update products" ON public.products;
DROP POLICY IF EXISTS "Admins can delete products" ON public.products;

-- Ensure RLS is enabled on the products table
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Create new policies with explicit schema reference

-- SELECT: Users can view products in collections they have access to
CREATE POLICY "Users can view products in their collections"
ON public.products FOR SELECT
TO authenticated
USING (
  -- Check if user has access to the collection
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = public.products.collection_id
    AND (
      -- Platform users can see all
      auth.uid() IN (
        SELECT id FROM public.users 
        WHERE role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Organization members can see their org's collections
      c.organization_id IN (
        SELECT organization_id 
        FROM public.organization_memberships 
        WHERE user_id = auth.uid()
      )
    )
  )
);

-- INSERT: Users can create products if they can access the collection
CREATE POLICY "Users can create products in their collections"
ON public.products FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = public.products.collection_id
    AND (
      -- Platform users can create in all collections
      auth.uid() IN (
        SELECT id FROM public.users 
        WHERE role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can create in their org's collections
      EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.organization_memberships om ON om.user_id = u.id
        WHERE u.id = auth.uid()
        AND u.role = 'brand_admin'
        AND om.organization_id = c.organization_id
      )
    )
  )
);

-- UPDATE: Only platform users and brand admins can update
CREATE POLICY "Admins can update products"
ON public.products FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = public.products.collection_id
    AND (
      -- Platform users can update all
      auth.uid() IN (
        SELECT id FROM public.users 
        WHERE role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can update their org's products
      EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.organization_memberships om ON om.user_id = u.id
        WHERE u.id = auth.uid()
        AND u.role = 'brand_admin'
        AND om.organization_id = c.organization_id
      )
    )
  )
);

-- DELETE: Only platform users and brand admins can delete
CREATE POLICY "Admins can delete products"
ON public.products FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.collections c
    WHERE c.id = public.products.collection_id
    AND (
      -- Platform users can delete all
      auth.uid() IN (
        SELECT id FROM public.users 
        WHERE role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Brand admins can delete their org's products
      EXISTS (
        SELECT 1 FROM public.users u
        JOIN public.organization_memberships om ON om.user_id = u.id
        WHERE u.id = auth.uid()
        AND u.role = 'brand_admin'
        AND om.organization_id = c.organization_id
      )
    )
  )
);