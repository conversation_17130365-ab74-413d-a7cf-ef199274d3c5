-- Migration: Simplify to Single Role System
-- Description: Replace dual role system (user_role + organization_role) with unified single role system
-- Date: 2025-01-22

-- Step 1: Create new unified user_role enum with clear naming
CREATE TYPE user_role_new AS ENUM (
  'platform_super',
  'platform_admin', 
  'brand_admin',
  'brand_member',
  'external_retoucher',
  'external_prompter'
);

-- Step 2: Add temporary column to users table for new roles
ALTER TABLE public.users 
ADD COLUMN role_new user_role_new;

-- Step 3: Migrate existing data based on current roles and organization memberships
-- This is a complex migration that needs to handle all current user/org combinations

-- First, handle platform-level roles (users not dependent on organization membership)
UPDATE public.users 
SET role_new = 'platform_super'
WHERE role = 'superadmin';

UPDATE public.users 
SET role_new = 'platform_admin'
WHERE role = 'admin';

-- Handle users with organization roles
-- For users who are org_admins, make them brand_admins
UPDATE public.users 
SET role_new = 'brand_admin'
WHERE role = 'user' 
AND id IN (
  SELECT DISTINCT user_id 
  FROM public.organization_memberships 
  WHERE role = 'org_admin'
);

-- For users who are org_members (and not already set as brand_admin), make them brand_members
UPDATE public.users 
SET role_new = 'brand_member'
WHERE role = 'user' 
AND role_new IS NULL
AND id IN (
  SELECT DISTINCT user_id 
  FROM public.organization_memberships 
  WHERE role = 'org_member'
);

-- Handle external collaborators
UPDATE public.users 
SET role_new = 'external_retoucher'
WHERE role = 'user' 
AND id IN (
  SELECT DISTINCT user_id 
  FROM public.organization_memberships 
  WHERE role = 'org_retoucher'
);

UPDATE public.users 
SET role_new = 'external_prompter'
WHERE role = 'user' 
AND id IN (
  SELECT DISTINCT user_id 
  FROM public.organization_memberships 
  WHERE role = 'org_prompter'
);

-- Handle any remaining users (fallback to brand_member)
UPDATE public.users 
SET role_new = 'brand_member'
WHERE role_new IS NULL;

-- Step 4: Drop dependencies on the old role column first
-- Check for any functions or policies that reference the old role column
DROP FUNCTION IF EXISTS auth.is_admin_user() CASCADE;
DROP FUNCTION IF EXISTS auth.is_platform_user() CASCADE;

-- Drop any constraints and triggers that might reference the role column
ALTER TABLE public.users ALTER COLUMN role DROP DEFAULT;

-- Now drop the old role column and rename the new one
ALTER TABLE public.users DROP COLUMN role CASCADE;
ALTER TABLE public.users RENAME COLUMN role_new TO role;

-- Step 5: Set not null constraint and default
ALTER TABLE public.users 
ALTER COLUMN role SET NOT NULL,
ALTER COLUMN role SET DEFAULT 'brand_member';

-- Step 6: Drop the old user_role enum and rename the new one
DROP TYPE user_role;
ALTER TYPE user_role_new RENAME TO user_role;

-- Step 7: Simplify organization_memberships table
-- Remove the role column since role is now stored in users table
-- First drop any dependencies
ALTER TABLE public.organization_memberships 
DROP COLUMN role CASCADE;

-- Step 8: Drop the organization_role enum as it's no longer needed
DROP TYPE organization_role CASCADE;

-- Step 9: Add helpful comments
COMMENT ON TYPE user_role IS 'Unified role system: platform_super, platform_admin, brand_admin, brand_member, external_retoucher, external_prompter';
COMMENT ON COLUMN users.role IS 'User role in the unified single role system';
COMMENT ON TABLE organization_memberships IS 'Simple membership table - role is now stored in users.role';