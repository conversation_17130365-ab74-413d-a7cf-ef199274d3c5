-- Add retouch substage enum type
CREATE TYPE retouch_substage AS ENUM (
  'internal_review',    -- Only visible to platform users and freelancers
  'revision_1',         -- Visible to all
  'revision_2',         -- Visible to all  
  'extra_revisions'     -- Visible to all
);

-- Add retouch_substage column to assets table
ALTER TABLE public.assets 
ADD COLUMN retouch_substage retouch_substage DEFAULT NULL;

-- Add index for performance when filtering by retouch substage
CREATE INDEX idx_assets_retouch_substage ON public.assets(retouch_substage) 
WHERE workflow_stage = 'retouch';

-- Update existing retouch assets to have appropriate default substage
-- Since we don't have created_by info, set all existing retouch assets to revision_1
-- This is safe as it's the most visible substage (brands can see it)
UPDATE public.assets
SET retouch_substage = 'revision_1'::retouch_substage
WHERE workflow_stage = 'retouch'
AND retouch_substage IS NULL;

-- Add comment to explain the column
COMMENT ON COLUMN public.assets.retouch_substage IS 'Substage within the retouch workflow stage. internal_review is only visible to platform users and freelancers.';