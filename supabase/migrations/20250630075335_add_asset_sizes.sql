-- Add size metadata to all assets that don't have it yet
-- This migration ensures all assets have a size from the standard set: XS, S, M, L, XL

DO $$
DECLARE
    sizes text[] := ARRAY['XS', 'S', 'M', 'L', 'XL'];
    asset_record RECORD;
    random_size text;
    current_metadata jsonb;
    updated_count integer := 0;
BEGIN
    -- Loop through all assets that don't have a size yet
    FOR asset_record IN 
        SELECT id, metadata 
        FROM assets 
        WHERE metadata IS NULL 
           OR metadata->>'size' IS NULL
           OR metadata->>'size' = ''
    LOOP
        -- Select a random size
        random_size := sizes[1 + floor(random() * array_length(sizes, 1))];
        
        -- Get current metadata or create empty object if null
        current_metadata := COALESCE(asset_record.metadata, '{}'::jsonb);
        
        -- Add or update the size field
        current_metadata := jsonb_set(current_metadata, '{size}', to_jsonb(random_size), true);
        
        -- Update the asset
        UPDATE assets 
        SET metadata = current_metadata,
            updated_at = NOW()
        WHERE id = asset_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RAISE NOTICE 'Updated % assets with size metadata', updated_count;
END $$;

-- Add comment to document the standard sizes
COMMENT ON COLUMN assets.metadata IS 'JSONB metadata for the asset. Standard sizes: XS, S, M, L, XL stored in metadata.size field';