-- Migration: Update RLS Policies for Single Role System
-- Description: Replace all RLS policies to work with unified role system
-- Date: 2025-01-22

-- ==================================================================
-- STEP 1: Drop all existing RLS policies
-- ==================================================================

-- Drop all existing policies on users table
DROP POLICY IF EXISTS "Only superadmins can delete users" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read user profiles" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;

-- Drop all existing policies on organizations table
DROP POLICY IF EXISTS "Only platform admins can create organizations" ON public.organizations;
DROP POLICY IF EXISTS "Org admins and platform admins can update organizations" ON public.organizations;
DROP POLICY IF EXISTS "Organization admins can update their organizations" ON public.organizations;
DROP POLICY IF EXISTS "TEMP_MVP_Allow authenticated read access to organizations" ON public.organizations;
DROP POLICY IF EXISTS "Users can view their organizations" ON public.organizations;

-- Drop all existing policies on organization_memberships table
DROP POLICY IF EXISTS "Allow users to read their own memberships" ON public.organization_memberships;
DROP POLICY IF EXISTS "Org admins can manage memberships" ON public.organization_memberships;
DROP POLICY IF EXISTS "Simple auth read for organization_memberships" ON public.organization_memberships;
DROP POLICY IF EXISTS "Users can view their own memberships" ON public.organization_memberships;

-- Drop all existing policies on collections table
DROP POLICY IF EXISTS "Org admins can delete collections" ON public.collections;
DROP POLICY IF EXISTS "Org admins can modify collections" ON public.collections;
DROP POLICY IF EXISTS "Org members can create collections" ON public.collections;
DROP POLICY IF EXISTS "Users can view collections in their organizations" ON public.collections;

-- Drop all existing policies on assets table
DROP POLICY IF EXISTS "Org admins can delete assets" ON public.assets;
DROP POLICY IF EXISTS "Users can modify assets in accessible collections" ON public.assets;
DROP POLICY IF EXISTS "Users can upload assets to accessible collections" ON public.assets;
DROP POLICY IF EXISTS "Users can view assets in accessible collections" ON public.assets;

-- ==================================================================
-- STEP 2: Create helper functions for cleaner policies
-- ==================================================================

-- Function to check if user has platform-level access
CREATE OR REPLACE FUNCTION auth.is_platform_user()
RETURNS boolean AS $$
BEGIN
  RETURN (
    SELECT role FROM public.users WHERE id = auth.uid()
  ) IN ('platform_super', 'platform_admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has admin privileges (platform or brand admin)
CREATE OR REPLACE FUNCTION auth.is_admin_user()
RETURNS boolean AS $$
BEGIN
  RETURN (
    SELECT role FROM public.users WHERE id = auth.uid()
  ) IN ('platform_super', 'platform_admin', 'brand_admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is member of organization
CREATE OR REPLACE FUNCTION auth.is_organization_member(org_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.organization_memberships 
    WHERE user_id = auth.uid() AND organization_id = org_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==================================================================
-- STEP 3: Create new RLS policies for users table
-- ==================================================================

-- Users can insert their own profile during signup
CREATE POLICY "Users can insert own profile" ON public.users
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = id);

-- All authenticated users can read user profiles
CREATE POLICY "Users can read user profiles" ON public.users
  FOR SELECT TO authenticated
  USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Only platform supers can delete users
CREATE POLICY "Only platform supers can delete users" ON public.users
  FOR DELETE TO authenticated
  USING ((SELECT role FROM public.users WHERE id = auth.uid()) = 'platform_super');

-- ==================================================================
-- STEP 4: Create new RLS policies for organizations table
-- ==================================================================

-- Only platform admins can create organizations
CREATE POLICY "Only platform admins can create organizations" ON public.organizations
  FOR INSERT TO authenticated
  WITH CHECK (auth.is_platform_user());

-- Users can view organizations they're members of, platform users can see all
CREATE POLICY "Users can view accessible organizations" ON public.organizations
  FOR SELECT TO authenticated
  USING (
    auth.is_platform_user() OR 
    auth.is_organization_member(id)
  );

-- Platform admins and brand admins can update their organizations
CREATE POLICY "Admins can update organizations" ON public.organizations
  FOR UPDATE TO authenticated
  USING (
    auth.is_platform_user() OR 
    (auth.is_organization_member(id) AND auth.is_admin_user())
  )
  WITH CHECK (
    auth.is_platform_user() OR 
    (auth.is_organization_member(id) AND auth.is_admin_user())
  );

-- ==================================================================
-- STEP 5: Create new RLS policies for organization_memberships table
-- ==================================================================

-- Users can view their own memberships, platform users can see all
CREATE POLICY "Users can view memberships" ON public.organization_memberships
  FOR SELECT TO authenticated
  USING (
    auth.is_platform_user() OR 
    user_id = auth.uid()
  );

-- Platform admins and brand admins can manage memberships in their organizations
CREATE POLICY "Admins can manage organization memberships" ON public.organization_memberships
  FOR ALL TO authenticated
  USING (
    auth.is_platform_user() OR 
    (auth.is_organization_member(organization_id) AND auth.is_admin_user())
  )
  WITH CHECK (
    auth.is_platform_user() OR 
    (auth.is_organization_member(organization_id) AND auth.is_admin_user())
  );

-- ==================================================================
-- STEP 6: Create new RLS policies for collections table
-- ==================================================================

-- Users can view collections in organizations they're members of
CREATE POLICY "Users can view accessible collections" ON public.collections
  FOR SELECT TO authenticated
  USING (
    auth.is_platform_user() OR 
    auth.is_organization_member(organization_id)
  );

-- Organization members can create collections in their organizations
CREATE POLICY "Members can create collections" ON public.collections
  FOR INSERT TO authenticated
  WITH CHECK (
    auth.is_platform_user() OR 
    auth.is_organization_member(organization_id)
  );

-- Platform admins and brand admins can modify collections
CREATE POLICY "Admins can modify collections" ON public.collections
  FOR UPDATE TO authenticated
  USING (
    auth.is_platform_user() OR 
    (auth.is_organization_member(organization_id) AND auth.is_admin_user())
  )
  WITH CHECK (
    auth.is_platform_user() OR 
    (auth.is_organization_member(organization_id) AND auth.is_admin_user())
  );

-- Platform admins and brand admins can delete collections
CREATE POLICY "Admins can delete collections" ON public.collections
  FOR DELETE TO authenticated
  USING (
    auth.is_platform_user() OR 
    (auth.is_organization_member(organization_id) AND auth.is_admin_user())
  );

-- ==================================================================
-- STEP 7: Create new RLS policies for assets table
-- ==================================================================

-- Users can view assets in collections from organizations they're members of
CREATE POLICY "Users can view accessible assets" ON public.assets
  FOR SELECT TO authenticated
  USING (
    auth.is_platform_user() OR 
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE auth.is_organization_member(organization_id)
    )
  );

-- Members can upload assets to accessible collections
CREATE POLICY "Members can upload assets" ON public.assets
  FOR INSERT TO authenticated
  WITH CHECK (
    auth.is_platform_user() OR 
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE auth.is_organization_member(organization_id)
    )
  );

-- Members can modify assets (for workflow, tags, etc.)
-- External retouchers can also edit assets
CREATE POLICY "Members can modify assets" ON public.assets
  FOR UPDATE TO authenticated
  USING (
    auth.is_platform_user() OR 
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE auth.is_organization_member(organization_id)
    )
  )
  WITH CHECK (
    auth.is_platform_user() OR 
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE auth.is_organization_member(organization_id)
    )
  );

-- Platform admins and brand admins can delete assets
CREATE POLICY "Admins can delete assets" ON public.assets
  FOR DELETE TO authenticated
  USING (
    auth.is_platform_user() OR 
    (collection_id IN (
      SELECT id FROM public.collections 
      WHERE auth.is_organization_member(organization_id)
    ) AND auth.is_admin_user())
  );

-- ==================================================================
-- STEP 8: Update any other relevant tables (if they exist)
-- ==================================================================

-- Update pending_invitations policies if the table exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'pending_invitations') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "Admins can manage invitations" ON public.pending_invitations;
    DROP POLICY IF EXISTS "Users can view their invitations" ON public.pending_invitations;
    
    -- Create new policies
    EXECUTE 'CREATE POLICY "Admins can manage invitations" ON public.pending_invitations
      FOR ALL TO authenticated
      USING (
        auth.is_platform_user() OR 
        (auth.is_organization_member(organization_id) AND auth.is_admin_user())
      )
      WITH CHECK (
        auth.is_platform_user() OR 
        (auth.is_organization_member(organization_id) AND auth.is_admin_user())
      )';
      
    EXECUTE 'CREATE POLICY "Users can view their invitations" ON public.pending_invitations
      FOR SELECT TO authenticated
      USING (
        auth.is_platform_user() OR 
        email = (SELECT email FROM auth.users WHERE id = auth.uid())
      )';
  END IF;
END;
$$;

-- ==================================================================
-- STEP 9: Add helpful comments
-- ==================================================================

COMMENT ON FUNCTION auth.is_platform_user() IS 'Returns true if current user has platform-level access (platform_super or platform_admin)';
COMMENT ON FUNCTION auth.is_admin_user() IS 'Returns true if current user has admin privileges (platform_super, platform_admin, or brand_admin)';
COMMENT ON FUNCTION auth.is_organization_member(uuid) IS 'Returns true if current user is member of specified organization';