-- Fix RLS policy for pending invitations view
-- This migration adds a policy to allow organization admins to view user profiles
-- of members in their organization and users who have sent invitations to their organization.

-- Create new policy to allow viewing user data for organization admins
CREATE POLICY "Organization admins can view user profiles of members and inviters"
ON public.users FOR SELECT
TO authenticated
USING (
  -- Current user is viewing their own profile
  auth.uid() = id
  OR
  -- Current user is an admin/superadmin (can view all users)
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR
  -- Current user is org_admin and the viewed user is a member of their organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships as admin_membership
    WHERE admin_membership.user_id = auth.uid()
    AND admin_membership.role = 'org_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships as target_membership
      WHERE target_membership.user_id = users.id
      AND target_membership.organization_id = admin_membership.organization_id
    )
  )
  OR
  -- Current user is org_admin and the viewed user has invited someone to their organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships as admin_membership
    WHERE admin_membership.user_id = auth.uid()
    AND admin_membership.role = 'org_admin'
    AND EXISTS (
      SELECT 1 FROM public.pending_invitations
      WHERE invited_by = users.id
      AND organization_id = admin_membership.organization_id
    )
  )
);

-- Add helpful comment explaining the policy
COMMENT ON POLICY "Organization admins can view user profiles of members and inviters" ON public.users
IS 'Allows organization admins to view the profiles of members in their organization and users who have sent invitations to their organization.';