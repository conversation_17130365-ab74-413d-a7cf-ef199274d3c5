-- Setup admin auth metadata synchronization
-- This ensures that when users have admin roles in public.users,
-- their auth.users.raw_app_meta_data is also updated accordingly

-- Create a function to sync admin metadata when user roles change
CREATE OR REPLACE FUNCTION sync_admin_auth_metadata()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update if the role changed to or from admin/superadmin
  IF (NEW.role IN ('admin', 'superadmin')) AND (OLD.role IS NULL OR OLD.role NOT IN ('admin', 'superadmin'))
  OR (OLD.role IN ('admin', 'superadmin')) AND (NEW.role NOT IN ('admin', 'superadmin'))
  OR (OLD.role IS NULL AND NEW.role IN ('admin', 'superadmin')) THEN
    
    -- Update the auth.users.raw_app_meta_data based on the new role
    UPDATE auth.users 
    SET raw_app_meta_data = 
      CASE 
        WHEN NEW.role = 'admin' THEN 
          raw_app_meta_data || '{"role": "admin", "is_admin": true}'::jsonb
        WHEN NEW.role = 'superadmin' THEN 
          raw_app_meta_data || '{"role": "superadmin", "is_admin": true}'::jsonb
        ELSE 
          raw_app_meta_data - 'role' - 'is_admin'
      END
    WHERE id = NEW.id;
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically sync admin metadata
DROP TRIGGER IF EXISTS sync_admin_auth_metadata_trigger ON public.users;
CREATE TRIGGER sync_admin_auth_metadata_trigger
  AFTER UPDATE OF role ON public.users
  FOR EACH ROW
  EXECUTE FUNCTION sync_admin_auth_metadata();

-- Also create trigger for INSERT to handle new admin users
DROP TRIGGER IF EXISTS sync_admin_auth_metadata_insert_trigger ON public.users;
CREATE TRIGGER sync_admin_auth_metadata_insert_trigger
  AFTER INSERT ON public.users
  FOR EACH ROW
  WHEN (NEW.role IN ('admin', 'superadmin'))
  EXECUTE FUNCTION sync_admin_auth_metadata();

-- Now sync existing admin users (this handles the current seed data)
UPDATE auth.users 
SET raw_app_meta_data = raw_app_meta_data || 
  CASE 
    WHEN (SELECT role FROM public.users WHERE id = auth.users.id) = 'admin' 
    THEN '{"role": "admin", "is_admin": true}'::jsonb
    WHEN (SELECT role FROM public.users WHERE id = auth.users.id) = 'superadmin' 
    THEN '{"role": "superadmin", "is_admin": true}'::jsonb
    ELSE '{}'::jsonb
  END
WHERE id IN (
  SELECT id FROM public.users WHERE role IN ('admin', 'superadmin')
);

-- Add helpful comments
COMMENT ON FUNCTION sync_admin_auth_metadata() 
IS 'Automatically syncs admin role information from public.users.role to auth.users.raw_app_meta_data';

COMMENT ON TRIGGER sync_admin_auth_metadata_trigger ON public.users
IS 'Syncs admin metadata when user roles are updated';

COMMENT ON TRIGGER sync_admin_auth_metadata_insert_trigger ON public.users  
IS 'Syncs admin metadata when new admin users are created';