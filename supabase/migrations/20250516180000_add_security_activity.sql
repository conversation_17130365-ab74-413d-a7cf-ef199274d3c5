-- Create security activity table
CREATE TABLE IF NOT EXISTS public.security_activity (
    id UUID DEFAULT extensions.uuid_generate_v4() NOT NULL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL CHECK (event_type IN ('login', 'logout', 'password_change', 'email_change', 'account_deletion')),
    ip_address TEXT,
    user_agent TEXT,
    location TEXT,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Enable RLS
ALTER TABLE public.security_activity ENABLE ROW LEVEL SECURITY;

-- Users can read their own security activity
CREATE POLICY "Users can read their own security activity"
ON public.security_activity FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- <PERSON>reate function to log security events
CREATE OR REPLACE FUNCTION public.log_security_event(
    p_user_id UUID,
    p_event_type TEXT,
    p_ip_address TEXT DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_location TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_activity_id UUID;
BEGIN
    INSERT INTO public.security_activity (user_id, event_type, ip_address, user_agent, location)
    VALUES (p_user_id, p_event_type, p_ip_address, p_user_agent, p_location)
    RETURNING id INTO v_activity_id;
    
    RETURN v_activity_id;
END;
$$;

-- Create indexes for performance
CREATE INDEX idx_security_activity_user_id ON public.security_activity(user_id);
CREATE INDEX idx_security_activity_created_at ON public.security_activity(created_at DESC);
CREATE INDEX idx_security_activity_event_type ON public.security_activity(event_type);