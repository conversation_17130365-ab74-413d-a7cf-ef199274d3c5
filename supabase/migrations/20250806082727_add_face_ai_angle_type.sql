-- Add face-ai angle type for AI generation (white background face images)
-- This is separate from the display face image which can have a cool background

-- Insert face-ai angle type for all existing models that don't have it yet
INSERT INTO model_images (id, model_id, angle_type, storage_path, created_at, updated_at)
SELECT 
  gen_random_uuid(),
  ml.id,
  'face-ai',
  'placeholder', -- Will be updated when image is uploaded
  NOW(),
  NOW()
FROM model_library ml
WHERE NOT EXISTS (
  SELECT 1 
  FROM model_images mi
  WHERE mi.model_id = ml.id 
  AND mi.angle_type = 'face-ai'
);

-- Add a comment to document the purpose of this angle type
COMMENT ON COLUMN model_images.angle_type IS 'Angle type for the model image. face-ai is specifically for AI generation with white background, while face is for display purposes with styled background';