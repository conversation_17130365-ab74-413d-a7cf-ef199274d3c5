-- Fix RLS policies for ai_generated_images to properly handle platform admins

-- Drop the incorrect policy that references om.role
DROP POLICY IF EXISTS "Organization admins can select AI-generated images" ON public.ai_generated_images;

-- Create corrected policy that checks user role properly
CREATE POLICY "Organization admins can select AI-generated images"
ON public.ai_generated_images FOR UPDATE
USING (
  -- Platform admins can update any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can update images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
)
WITH CHECK (
  -- Platform admins can update any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can update images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
);

-- Also update the SELECT policy to ensure platform admins can view all images
DROP POLICY IF EXISTS "Users can view their organization's AI-generated images" ON public.ai_generated_images;

CREATE POLICY "Users can view their organization's AI-generated images"
ON public.ai_generated_images FOR SELECT
USING (
  -- Platform admins can view all images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Organization members can view their organization's images
  organization_id IN (
    SELECT organization_id 
    FROM public.organization_memberships 
    WHERE user_id = auth.uid()
  )
);

-- Update INSERT policy to allow platform admins
DROP POLICY IF EXISTS "Users can insert their own AI-generated images" ON public.ai_generated_images;

CREATE POLICY "Users can insert their own AI-generated images"
ON public.ai_generated_images FOR INSERT
WITH CHECK (
  user_id = auth.uid() AND
  (
    -- Platform admins can insert into any collection
    EXISTS (
      SELECT 1
      FROM public.users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
    OR
    -- Regular users must be members of the organization
    EXISTS (
      SELECT 1
      FROM public.collections c
      JOIN public.organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = collection_id
      AND om.user_id = auth.uid()
    )
  )
);