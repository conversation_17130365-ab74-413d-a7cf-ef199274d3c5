-- Add new columns to organizations table for expanded settings
ALTER TABLE public.organizations
ADD COLUMN IF NOT EXISTS company_domain TEXT,
ADD COLUMN IF NOT EXISTS primary_color TEXT,
ADD COLUMN IF NOT EXISTS is_private BOOLEAN DEFAULT false;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN public.organizations.company_domain IS 'Domain used for email validation and SSO integration';
COMMENT ON COLUMN public.organizations.primary_color IS 'Brand primary color in hex format';
COMMENT ON COLUMN public.organizations.is_private IS 'Controls whether the organization is private or visible to all users';

-- Update the logo_url comment
COMMENT ON COLUMN public.organizations.logo_url IS 'URL to the organization logo image';
COMMENT ON COLUMN public.organizations.description IS 'Text description of the organization';