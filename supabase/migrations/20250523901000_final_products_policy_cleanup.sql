-- Final cleanup of products table policies to ensure no duplicates

-- Drop ALL existing policies on products table using dynamic SQL
DO $$
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'products'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON products', pol.policyname);
    END LOOP;
END $$;

-- Create clean, consistent policies matching the pattern used for other tables

-- SELECT: Platform users can see all, org members can see their organization's products
CREATE POLICY "products_select_policy"
    ON products FOR SELECT
    USING (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- INSERT: Platform users can create anywhere, org members can create in their collections
CREATE POLICY "products_insert_policy"
    ON products FOR INSERT
    WITH CHECK (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- UPDATE: Platform users can update all, org members can update their organization's products
CREATE POLICY "products_update_policy"
    ON products FOR UPDATE
    USING (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    )
    WITH CHECK (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- DELETE: Platform users and brand admins can delete products
CREATE POLICY "products_delete_policy"
    ON products FOR DELETE
    USING (
        auth.is_platform_user() OR
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.is_organization_admin(c.organization_id)
        )
    );