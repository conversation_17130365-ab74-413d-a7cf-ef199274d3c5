-- Enable RLS on all tables and add proper policies
-- This migration transitions from demo mode (<PERSON><PERSON> disabled) to production mode (RLS enabled)

-- First, ensure all helper functions exist
CREATE OR REPLACE FUNCTION auth.user_in_organization(org_id UUID)
RETURNS BOOLEAN AS $$
    SELECT EXISTS (
        SELECT 1 FROM public.organization_memberships
        WHERE user_id = auth.uid() 
        AND organization_id = org_id
    );
$$ LANGUAGE sql SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION auth.user_role()
RETURNS user_role AS $$
    SELECT role FROM public.users WHERE id = auth.uid();
$$ LANGUAGE sql SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION auth.is_organization_admin(org_id UUID)
RETURNS BOOLEAN AS $$
    SELECT EXISTS (
        SELECT 1 
        FROM public.organization_memberships om
        JOIN public.users u ON om.user_id = u.id
        WHERE om.user_id = auth.uid()
        AND om.organization_id = org_id
        AND u.role = 'brand_admin'
    );
$$ LANGUAGE sql SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION auth.is_platform_user()
RETURNS BOOLEAN AS $$
    SELECT EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('platform_super', 'platform_admin')
    );
$$ LANGUAGE sql SECURITY DEFINER STABLE;

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE pending_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_uploads ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DO $$ 
DECLARE
    pol RECORD;
BEGIN
    FOR pol IN 
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
            pol.policyname, pol.schemaname, pol.tablename);
    END LOOP;
END $$;

-- ORGANIZATIONS POLICIES
CREATE POLICY "orgs_select_policy"
    ON organizations FOR SELECT
    USING (
        auth.is_platform_user() OR
        auth.user_in_organization(id)
    );

CREATE POLICY "orgs_insert_policy"
    ON organizations FOR INSERT
    WITH CHECK (
        auth.user_role() IN ('platform_super', 'platform_admin')
    );

CREATE POLICY "orgs_update_policy"
    ON organizations FOR UPDATE
    USING (
        auth.is_platform_user() OR
        auth.is_organization_admin(id)
    )
    WITH CHECK (
        auth.is_platform_user() OR
        auth.is_organization_admin(id)
    );

CREATE POLICY "orgs_delete_policy"
    ON organizations FOR DELETE
    USING (
        auth.user_role() = 'platform_super'
    );

-- USERS POLICIES
CREATE POLICY "users_select_policy"
    ON users FOR SELECT
    USING (true); -- All users can view profiles

CREATE POLICY "users_update_policy"
    ON users FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        -- Prevent role changes except by platform admins
        (role = (SELECT role FROM users WHERE id = auth.uid()) OR
         auth.user_role() IN ('platform_super', 'platform_admin'))
    );

-- ORGANIZATION MEMBERSHIPS POLICIES
CREATE POLICY "memberships_select_policy"
    ON organization_memberships FOR SELECT
    USING (
        auth.is_platform_user() OR
        user_id = auth.uid() OR
        auth.user_in_organization(organization_id)
    );

CREATE POLICY "memberships_insert_policy"
    ON organization_memberships FOR INSERT
    WITH CHECK (
        auth.is_platform_user() OR
        auth.is_organization_admin(organization_id)
    );

CREATE POLICY "memberships_update_policy"
    ON organization_memberships FOR UPDATE
    USING (
        auth.is_platform_user() OR
        auth.is_organization_admin(organization_id)
    );

CREATE POLICY "memberships_delete_policy"
    ON organization_memberships FOR DELETE
    USING (
        auth.is_platform_user() OR
        auth.is_organization_admin(organization_id)
    );

-- COLLECTIONS POLICIES
CREATE POLICY "collections_select_policy"
    ON collections FOR SELECT
    USING (
        auth.is_platform_user() OR
        auth.user_in_organization(organization_id)
    );

CREATE POLICY "collections_insert_policy"
    ON collections FOR INSERT
    WITH CHECK (
        auth.user_in_organization(organization_id)
    );

CREATE POLICY "collections_update_policy"
    ON collections FOR UPDATE
    USING (
        auth.is_platform_user() OR
        auth.is_organization_admin(organization_id)
    );

CREATE POLICY "collections_delete_policy"
    ON collections FOR DELETE
    USING (
        auth.is_platform_user() OR
        auth.is_organization_admin(organization_id)
    );

-- PRODUCTS POLICIES
CREATE POLICY "products_select_policy"
    ON products FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND (auth.is_platform_user() OR auth.user_in_organization(c.organization_id))
        )
    );

CREATE POLICY "products_insert_policy"
    ON products FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "products_update_policy"
    ON products FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "products_delete_policy"
    ON products FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = products.collection_id
            AND (auth.is_platform_user() OR auth.is_organization_admin(c.organization_id))
        )
    );

-- ASSETS POLICIES
CREATE POLICY "assets_select_policy"
    ON assets FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = assets.collection_id
            AND (auth.is_platform_user() OR auth.user_in_organization(c.organization_id))
        )
    );

CREATE POLICY "assets_insert_policy"
    ON assets FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = assets.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "assets_update_policy"
    ON assets FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = assets.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "assets_delete_policy"
    ON assets FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = assets.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- TAGS POLICIES
-- Note: Tags are global and don't have organization_id
CREATE POLICY "tags_select_policy"
    ON tags FOR SELECT
    USING (true); -- All authenticated users can view tags

CREATE POLICY "tags_insert_policy"
    ON tags FOR INSERT
    WITH CHECK (
        auth.uid() IS NOT NULL -- Any authenticated user can create tags
    );

CREATE POLICY "tags_update_policy"
    ON tags FOR UPDATE
    USING (
        auth.is_platform_user() -- Only platform admins can update tags
    );

CREATE POLICY "tags_delete_policy"
    ON tags FOR DELETE
    USING (
        auth.is_platform_user() -- Only platform admins can delete tags
    );

-- ASSET TAGS POLICIES
CREATE POLICY "asset_tags_select_policy"
    ON asset_tags FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = asset_tags.asset_id
            AND (auth.is_platform_user() OR auth.user_in_organization(c.organization_id))
        )
    );

CREATE POLICY "asset_tags_insert_policy"
    ON asset_tags FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = asset_tags.asset_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "asset_tags_delete_policy"
    ON asset_tags FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = asset_tags.asset_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- COMMENTS POLICIES
CREATE POLICY "comments_select_policy"
    ON comments FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = comments.asset_id
            AND (auth.is_platform_user() OR auth.user_in_organization(c.organization_id))
        )
    );

CREATE POLICY "comments_insert_policy"
    ON comments FOR INSERT
    WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = comments.asset_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

CREATE POLICY "comments_update_policy"
    ON comments FOR UPDATE
    USING (auth.uid() = user_id);

CREATE POLICY "comments_delete_policy"
    ON comments FOR DELETE
    USING (auth.uid() = user_id);

-- COMMENT MENTIONS POLICIES
CREATE POLICY "mentions_select_policy"
    ON comment_mentions FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM comments c
            WHERE c.id = comment_mentions.comment_id
        )
    );

CREATE POLICY "mentions_insert_policy"
    ON comment_mentions FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM comments c
            WHERE c.id = comment_mentions.comment_id
            AND c.user_id = auth.uid()
        )
    );

-- PENDING INVITATIONS POLICIES (already done in previous migration)
-- Keep existing policies from 20250523203919_enable_pending_invitations_rls.sql

-- SECURITY ACTIVITY POLICIES
CREATE POLICY "security_select_policy"
    ON security_activity FOR SELECT
    USING (
        auth.uid() = user_id OR
        auth.is_platform_user()
    );

CREATE POLICY "security_insert_policy"
    ON security_activity FOR INSERT
    WITH CHECK (true); -- System can always insert

-- BULK UPLOADS POLICIES
CREATE POLICY "bulk_uploads_select_policy"
    ON bulk_uploads FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = bulk_uploads.collection_id
            AND (auth.is_platform_user() OR auth.user_in_organization(c.organization_id))
        )
    );

CREATE POLICY "bulk_uploads_insert_policy"
    ON bulk_uploads FOR INSERT
    WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM collections c
            WHERE c.id = bulk_uploads.collection_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- Add comment to track this migration
COMMENT ON SCHEMA public IS 'FashionLab platform - RLS enabled for production security';