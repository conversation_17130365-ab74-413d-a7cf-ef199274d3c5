-- Add policy to allow org_admin users to update their organizations

-- First, check if the policy already exists and drop it if so
DROP POLICY IF EXISTS "Organization admins can update their organizations" ON public.organizations;

-- Create policy allowing org_admin users to update their organizations
CREATE POLICY "Organization admins can update their organizations"
ON public.organizations FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.organization_memberships
        WHERE organization_id = organizations.id
        AND user_id = auth.uid()
        AND role = 'org_admin'
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.organization_memberships
        WHERE organization_id = organizations.id
        AND user_id = auth.uid()
        AND role = 'org_admin'
    )
);

-- Add comment about migration
COMMENT ON TABLE public.organizations IS 'Multi-tenant organizations (brands)';