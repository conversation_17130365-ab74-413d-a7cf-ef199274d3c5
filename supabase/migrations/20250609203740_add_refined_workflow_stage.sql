-- Migration: Add 'refined' workflow stage between 'selected' and 'upscale'
-- This migration adds the 'refined' stage for FashionLab staff workflow

-- Create a new enum with 'refined' stage added between 'selected' and 'upscale'
CREATE TYPE public.workflow_stage_with_refined AS ENUM (
    'upload',
    'raw_ai_images',
    'selected',
    'refined',
    'upscale',
    'retouch',
    'final'
);

-- Update the assets table to use the new enum
-- First remove the default constraint and convert to text
ALTER TABLE public.assets
ALTER COLUMN workflow_stage DROP DEFAULT;

ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE text;

-- Convert to the new enum type
ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE public.workflow_stage_with_refined
USING workflow_stage::public.workflow_stage_with_refined;

-- Restore the default value with the new enum
ALTER TABLE public.assets
ALTER COLUMN workflow_stage SET DEFAULT 'upload';

-- Drop the old enum and rename the new one
DROP TYPE public.workflow_stage;
ALTER TYPE public.workflow_stage_with_refined RENAME TO workflow_stage;

-- Add comment for documentation
COMMENT ON TYPE public.workflow_stage IS 'Workflow stages for asset processing: upload (input assets), raw_ai_images (AI generated drafts), selected (client selected images), refined (refined by staff), upscale, retouch, final';