-- Handle invitation acceptance flow after email confirmation
-- This migration extends the email confirmation handler to:
-- 1. Create organization memberships for accepted invitations
-- 2. Mark invitations as accepted
-- 3. Log security events

-- Update the handle_email_confirmation function to handle invitation acceptance
CREATE OR REPLACE FUNCTION public.handle_email_confirmation()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
DECLARE
  invitation_id_value uuid;
  invitation_record record;
  is_platform_invitation boolean;
  invitation_role text;
  user_exists boolean;
BEGIN
  -- Only process if email was just confirmed (transition from NULL to timestamp)
  IF OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL THEN
    -- Check if user already exists in public.users
    SELECT EXISTS(SELECT 1 FROM public.users WHERE id = NEW.id) INTO user_exists;
    
    -- If user doesn't exist, check for invitation and create user
    IF NOT user_exists THEN
      invitation_id_value := (NEW.raw_user_meta_data->>'invitation_id')::uuid;
      
      IF invitation_id_value IS NOT NULL THEN
        -- Get the full invitation record
        SELECT * INTO invitation_record
        FROM public.pending_invitations
        WHERE id = invitation_id_value;
        
        IF invitation_record IS NOT NULL THEN
          -- Check if it's a platform admin invitation
          is_platform_invitation := (invitation_record.organization_id IS NULL);
          
          -- Determine the role based on invitation type
          IF is_platform_invitation THEN
            invitation_role := 'platform_admin';
          ELSE
            invitation_role := 'brand_member';  -- Default role for organization invitations
          END IF;
          
          -- Create the public.users record
          INSERT INTO public.users (
            id,
            email,
            full_name,
            avatar_url,
            role,
            created_at,
            updated_at
          ) VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
            COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
            invitation_role,
            NOW(),
            NOW()
          );
          
          -- If this is an organization invitation, create the membership
          IF NOT is_platform_invitation AND invitation_record.organization_id IS NOT NULL THEN
            INSERT INTO public.organization_memberships (
              user_id,
              organization_id,
              created_at
            ) VALUES (
              NEW.id,
              invitation_record.organization_id,
              NOW()
            )
            ON CONFLICT (user_id, organization_id) DO NOTHING;
          END IF;
          
          -- Mark the invitation as accepted
          UPDATE public.pending_invitations
          SET 
            accepted_at = NOW(),
            updated_at = NOW()
          WHERE id = invitation_id_value;
          
          -- Log security event for invitation acceptance
          INSERT INTO public.security_activity (
            user_id,
            event_type,
            ip_address,
            user_agent,
            metadata
          ) VALUES (
            NEW.id,
            'invitation_accepted',
            COALESCE(NEW.raw_app_meta_data->>'ip_address', 'system'),
            COALESCE(NEW.raw_app_meta_data->>'user_agent', 'system'),
            jsonb_build_object(
              'invitation_id', invitation_id_value,
              'organization_id', invitation_record.organization_id,
              'invited_by', invitation_record.invited_by,
              'role', invitation_role
            )
          );
        END IF;
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment explaining the updated function
COMMENT ON FUNCTION public.handle_email_confirmation() IS 
'Creates public.users record for invited users after email confirmation and handles invitation acceptance flow including organization membership creation.';

-- Create an index on pending_invitations.accepted_at for performance
CREATE INDEX IF NOT EXISTS idx_pending_invitations_accepted_at 
ON public.pending_invitations(accepted_at) 
WHERE accepted_at IS NOT NULL;