-- Create general-uploads bucket for collection covers, briefing files, and other non-asset uploads
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('general-uploads', 'general-uploads', true, 10485760, 
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml', 'application/pdf', 'application/zip'])
ON CONFLICT (id) DO UPDATE SET
  public = EXCLUDED.public,
  file_size_limit = EXCLUDED.file_size_limit,
  allowed_mime_types = EXCLUDED.allowed_mime_types;

-- Add storage policies for general-uploads bucket
-- Note: These policies are temporarily disabled for demo as per previous migrations

-- Allow authenticated users to view all files
-- CREATE POLICY "Authenticated users can view general uploads" 
-- ON storage.objects FOR SELECT 
-- USING (bucket_id = 'general-uploads' AND auth.role() = 'authenticated');

-- Allow organization members to upload/update/delete their own files
-- CREATE POLICY "Organization members can manage their general uploads" 
-- ON storage.objects FOR INSERT 
-- USING (
--   bucket_id = 'general-uploads' AND 
--   auth.role() = 'authenticated' AND
--   (storage.foldername(name))[1] = 'organizations' AND
--   EXISTS (
--     SELECT 1 FROM organization_members 
--     WHERE user_id = auth.uid() 
--     AND organization_id = (storage.foldername(name))[2]::uuid
--   )
-- );

-- CREATE POLICY "Organization members can update their general uploads" 
-- ON storage.objects FOR UPDATE 
-- USING (
--   bucket_id = 'general-uploads' AND 
--   auth.role() = 'authenticated' AND
--   (storage.foldername(name))[1] = 'organizations' AND
--   EXISTS (
--     SELECT 1 FROM organization_members 
--     WHERE user_id = auth.uid() 
--     AND organization_id = (storage.foldername(name))[2]::uuid
--   )
-- );

-- CREATE POLICY "Organization members can delete their general uploads" 
-- ON storage.objects FOR DELETE 
-- USING (
--   bucket_id = 'general-uploads' AND 
--   auth.role() = 'authenticated' AND
--   (storage.foldername(name))[1] = 'organizations' AND
--   EXISTS (
--     SELECT 1 FROM organization_members 
--     WHERE user_id = auth.uid() 
--     AND organization_id = (storage.foldername(name))[2]::uuid
--   )
-- );