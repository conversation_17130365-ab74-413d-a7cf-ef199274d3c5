-- TEMPORARY: Disable <PERSON><PERSON> on pending_invitations to debug 403 errors
-- This should be removed once the issue is resolved

ALTER TABLE public.pending_invitations DISABLE ROW LEVEL SECURITY;

COMMENT ON TABLE public.pending_invitations IS 'TEMPORARY: <PERSON><PERSON> disabled for debugging 403 errors';

-- Also check if <PERSON><PERSON> is enabled on the table
DO $$
BEGIN
  RAISE NOTICE 'RLS Status for pending_invitations: %', 
    (SELECT relrowsecurity FROM pg_class WHERE relname = 'pending_invitations' AND relnamespace = 'public'::regnamespace);
END $$;