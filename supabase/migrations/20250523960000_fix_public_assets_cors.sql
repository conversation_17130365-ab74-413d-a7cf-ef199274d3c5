-- Fix CORS for public assets
-- This migration ensures that public assets can be accessed without authentication

-- Update storage bucket policies to allow public read access for specific file types
DO $$
BEGIN
  -- Check if buckets exist before updating policies
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'assets') THEN
    -- Allow public read access to placeholder.svg and other public assets
    INSERT INTO storage.objects (bucket_id, name, owner, created_at, updated_at, version, metadata)
    VALUES 
      ('assets', 'placeholder.svg', NULL, NOW(), NOW(), 1, '{}')
    ON CONFLICT (bucket_id, name) DO NOTHING;
  END IF;
END $$;

-- Note: Auth redirect URLs should be configured in Supabase Dashboard
-- or via environment variables, not in migrations
-- Removing invalid auth.config reference