-- Remove all existing policies that might be causing recursion
DROP POLICY IF EXISTS "Admins can view all users" ON public.users;
DROP POLICY IF EXISTS "Allow admins and superadmins to read all memberships" ON organization_memberships;
DROP POLICY IF EXISTS "Allow org admins to see members in their orgs" ON organization_memberships;

-- Replace with extremely simple policies for development/testing
-- These policies make the tables accessible to all authenticated users
-- They don't rely on checking roles or memberships, avoiding any recursion

-- Simple policy for users table - all authenticated users can read
CREATE POLICY "Simple auth read for users" 
ON public.users
FOR SELECT
TO authenticated
USING (true);

-- Simple policy for organization_memberships - all authenticated users can read
CREATE POLICY "Simple auth read for organization_memberships" 
ON organization_memberships
FOR SELECT
TO authenticated
USING (true);

-- Comment to explain this is for development/testing
COMMENT ON POLICY "Simple auth read for users" ON public.users
IS 'Temporary development policy to allow all authenticated users to read users data without recursion.';

COMMENT ON POLICY "Simple auth read for organization_memberships" ON organization_memberships
IS 'Temporary development policy to allow all authenticated users to read all memberships without recursion.';
