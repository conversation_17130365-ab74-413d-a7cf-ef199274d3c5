-- Fix handle_new_user trigger to properly handle invitation flow
-- Only create public.users record after email confirmation for invited users

-- Drop existing trigger first
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Recreate the handle_new_user function with invitation flow handling
CREATE OR REPLACE FUNCTION public.handle_new_user()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
DECLARE
  invitation_id_value uuid;
  is_platform_invitation boolean;
  invitation_role text;
BEGIN
  -- Check if this is an invitation signup
  invitation_id_value := (NEW.raw_user_meta_data->>'invitation_id')::uuid;
  
  -- If user is signing up via invitation and email is NOT confirmed, skip creating public.users record
  IF invitation_id_value IS NOT NULL AND NEW.email_confirmed_at IS NULL THEN
    -- Don't create user record yet - wait for email confirmation
    RETURN NEW;
  END IF;
  
  -- For regular signups or confirmed invited users, create the public.users record
  -- Check if there's a pending invitation for this email
  IF invitation_id_value IS NOT NULL THEN
    -- User signed up via invitation and email is confirmed
    -- Check if it's a platform admin invitation
    SELECT organization_id IS NULL INTO is_platform_invitation
    FROM public.pending_invitations
    WHERE id = invitation_id_value;
    
    -- Determine the role based on invitation type
    IF is_platform_invitation THEN
      invitation_role := 'platform_admin';
    ELSE
      invitation_role := 'brand_member';  -- Default role for organization invitations
    END IF;
  ELSE
    -- Regular signup without invitation
    invitation_role := 'brand_member';  -- Default role
  END IF;
  
  -- Create the public.users record
  INSERT INTO public.users (
    id,
    email,
    full_name,
    avatar_url,
    role,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
    invitation_role,
    NOW(),
    NOW()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Add a new function to handle email confirmation for invited users
CREATE OR REPLACE FUNCTION public.handle_email_confirmation()
RETURNS trigger AS $$
DECLARE
  invitation_id_value uuid;
  is_platform_invitation boolean;
  invitation_role text;
  user_exists boolean;
BEGIN
  -- Only process if email was just confirmed (transition from NULL to timestamp)
  IF OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL THEN
    -- Check if user already exists in public.users
    SELECT EXISTS(SELECT 1 FROM public.users WHERE id = NEW.id) INTO user_exists;
    
    -- If user doesn't exist, check for invitation and create user
    IF NOT user_exists THEN
      invitation_id_value := (NEW.raw_user_meta_data->>'invitation_id')::uuid;
      
      IF invitation_id_value IS NOT NULL THEN
        -- Check if it's a platform admin invitation
        SELECT organization_id IS NULL INTO is_platform_invitation
        FROM public.pending_invitations
        WHERE id = invitation_id_value;
        
        -- Determine the role based on invitation type
        IF is_platform_invitation THEN
          invitation_role := 'platform_admin';
        ELSE
          invitation_role := 'brand_member';
        END IF;
        
        -- Create the public.users record
        INSERT INTO public.users (
          id,
          email,
          full_name,
          avatar_url,
          role,
          created_at,
          updated_at
        ) VALUES (
          NEW.id,
          NEW.email,
          COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
          COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
          invitation_role,
          NOW(),
          NOW()
        );
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for email confirmation
DROP TRIGGER IF EXISTS on_auth_user_email_confirmed ON auth.users;
CREATE TRIGGER on_auth_user_email_confirmed
  AFTER UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_email_confirmation();

-- Add comment explaining the invitation flow
COMMENT ON FUNCTION public.handle_new_user() IS 
'Handles new user creation. For invited users, waits for email confirmation before creating public.users record.';

COMMENT ON FUNCTION public.handle_email_confirmation() IS 
'Creates public.users record for invited users after email confirmation.';