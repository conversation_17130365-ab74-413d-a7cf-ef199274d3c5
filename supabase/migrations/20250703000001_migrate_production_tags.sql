-- Production tag migration based on analysis
-- This migration categorizes existing tags and handles duplicates

-- First, update angle tags (used across multiple organizations for standard views)
UPDATE tags 
SET category = 'angles'
WHERE LOWER(name) IN ('front', 'back', 'detail', 'packshot', 'side', 'side profile')
  AND category != 'angles';

-- Update styling tags
UPDATE tags 
SET category = 'styling'
WHERE LOWER(name) IN ('look and feel', 'look&feel', 'location', 'poses', 'poses female', 'poses male', 'styling', 'filter')
  AND category != 'styling';

-- Handle "Look and feel" duplicates - merge into single tag
-- First, find the most used version
WITH look_feel_tags AS (
  SELECT 
    t.id,
    t.name,
    COUNT(at.asset_id) as usage_count
  FROM tags t
  LEFT JOIN asset_tags at ON t.id = at.tag_id
  WHERE LOWER(REPLACE(t.name, '&', ' and ')) = 'look and feel'
  GROUP BY t.id, t.name
  ORDER BY usage_count DESC
  LIMIT 1
)
-- Update all asset_tags to point to the main tag
UPDATE asset_tags
SET tag_id = (SELECT id FROM look_feel_tags)
WHERE tag_id IN (
  SELECT t.id 
  FROM tags t 
  WHERE LOWER(REPLACE(t.name, '&', ' and ')) = 'look and feel'
    AND t.id != (SELECT id FROM look_feel_tags)
);

-- Delete duplicate look and feel tags
DELETE FROM tags
WHERE LOWER(REPLACE(name, '&', ' and ')) = 'look and feel'
  AND id NOT IN (
    SELECT t.id
    FROM tags t
    LEFT JOIN asset_tags at ON t.id = at.tag_id
    WHERE LOWER(REPLACE(t.name, '&', ' and ')) = 'look and feel'
    GROUP BY t.id
    ORDER BY COUNT(at.asset_id) DESC
    LIMIT 1
  );

-- Set collection_id for single-collection tags
WITH single_collection_tags AS (
  SELECT 
    t.id as tag_id,
    (ARRAY_AGG(DISTINCT a.collection_id))[1] as collection_id,
    COUNT(DISTINCT a.collection_id) as collection_count
  FROM tags t
  JOIN asset_tags at ON t.id = at.tag_id
  JOIN assets a ON at.asset_id = a.id
  WHERE t.category NOT IN ('angles', 'styling', 'global')
    AND t.collection_id IS NULL
  GROUP BY t.id
  HAVING COUNT(DISTINCT a.collection_id) = 1
)
UPDATE tags 
SET 
  category = 'collection',
  collection_id = sct.collection_id
FROM single_collection_tags sct
WHERE tags.id = sct.tag_id;

-- Handle multi-collection tags within same organization
-- These need to be duplicated per collection
WITH multi_collection_tags AS (
  SELECT DISTINCT
    t.id as original_tag_id,
    t.name,
    t.color,
    a.collection_id,
    c.organization_id
  FROM tags t
  JOIN asset_tags at ON t.id = at.tag_id
  JOIN assets a ON at.asset_id = a.id
  JOIN collections c ON a.collection_id = c.id
  WHERE t.category NOT IN ('angles', 'styling', 'global')
    AND t.collection_id IS NULL
),
tags_to_duplicate AS (
  SELECT 
    original_tag_id,
    name,
    color,
    COUNT(DISTINCT collection_id) as collection_count,
    ARRAY_AGG(DISTINCT collection_id) as collection_ids
  FROM multi_collection_tags
  GROUP BY original_tag_id, name, color
  HAVING COUNT(DISTINCT collection_id) > 1
)
-- Insert new collection-specific copies
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
SELECT 
  ttd.name,
  'collection',
  COALESCE(ttd.color, '#9333EA'), -- Default purple for collection tags
  unnest(ttd.collection_ids) as collection_id,
  NOW(),
  NOW()
FROM tags_to_duplicate ttd
ON CONFLICT (name, category, collection_id) DO NOTHING;

-- Update asset_tags to point to the new collection-specific tags
WITH tag_mappings AS (
  SELECT 
    old_tag.id as old_tag_id,
    new_tag.id as new_tag_id,
    new_tag.collection_id
  FROM tags old_tag
  JOIN tags new_tag ON old_tag.name = new_tag.name 
    AND COALESCE(old_tag.color, '') = COALESCE(new_tag.color, '')
    AND new_tag.category = 'collection'
    AND new_tag.collection_id IS NOT NULL
  WHERE old_tag.category NOT IN ('angles', 'styling', 'global')
    AND old_tag.collection_id IS NULL
)
UPDATE asset_tags
SET tag_id = tm.new_tag_id
FROM tag_mappings tm, assets a
WHERE asset_tags.asset_id = a.id
  AND asset_tags.tag_id = tm.old_tag_id
  AND a.collection_id = tm.collection_id;

-- Update remaining tags to collection if not already categorized
UPDATE tags 
SET category = 'collection'
WHERE category NOT IN ('angles', 'styling', 'global', 'collection');

-- Clean up old multi-collection tags that have been replaced
DELETE FROM tags
WHERE category = 'collection'
  AND collection_id IS NULL
  AND id NOT IN (
    SELECT DISTINCT tag_id FROM asset_tags
  );

-- Delete truly unused tags (never used)
DELETE FROM tags
WHERE id NOT IN (SELECT DISTINCT tag_id FROM asset_tags)
  AND LOWER(name) IN (
    'new tag', 
    'test', 
    'tiff', 
    'tiff image',
    'revision 1',
    '5715677190187_retouched_m_front_v4',
    'body 134/140 -9-10 years',
    'body size 38 (s',
    'body size 38 (s)',
    'body size 50 (xl)',
    'casting age reference',
    'casting reference',
    'high res 122/128',
    'high res garment image 110/116 (5-6)',
    'high res garment image 146/152 (11-12',
    'reference body for 122/128 (6-8)',
    'reference body for 134/140 (8-10)',
    'styling garment for 122/128 (7-8)',
    'styling garment for 134/140 (9-10)'
  );

-- Add any missing standard angle tags
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
VALUES 
  ('Three Quarter', 'angles', '#3B82F6', NULL, NOW(), NOW()),
  ('Close Up', 'angles', '#8B5CF6', NULL, NOW(), NOW()),
  ('Full Body', 'angles', '#10B981', NULL, NOW(), NOW()),
  ('Profile', 'angles', '#F59E0B', NULL, NOW(), NOW()),
  ('Overhead', 'angles', '#EF4444', NULL, NOW(), NOW())
ON CONFLICT (name, category, collection_id) DO NOTHING;

-- Add common styling tags if they don't exist
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
VALUES 
  ('Casual', 'styling', '#EC4899', NULL, NOW(), NOW()),
  ('Formal', 'styling', '#F59E0B', NULL, NOW(), NOW()),
  ('Street Style', 'styling', '#6366F1', NULL, NOW(), NOW()),
  ('Editorial', 'styling', '#84CC16', NULL, NOW(), NOW()),
  ('Minimalist', 'styling', '#06B6D4', NULL, NOW(), NOW()),
  ('Vintage', 'styling', '#A855F7', NULL, NOW(), NOW()),
  ('Modern', 'styling', '#3B82F6', NULL, NOW(), NOW()),
  ('Classic', 'styling', '#10B981', NULL, NOW(), NOW())
ON CONFLICT (name, category, collection_id) DO NOTHING;

-- Log summary of changes
DO $$
DECLARE
  angle_count INTEGER;
  styling_count INTEGER;
  collection_count INTEGER;
  deleted_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO angle_count FROM tags WHERE category = 'angles';
  SELECT COUNT(*) INTO styling_count FROM tags WHERE category = 'styling';
  SELECT COUNT(*) INTO collection_count FROM tags WHERE category = 'collection';
  
  RAISE NOTICE 'Tag migration complete:';
  RAISE NOTICE '  Angle tags: %', angle_count;
  RAISE NOTICE '  Styling tags: %', styling_count;
  RAISE NOTICE '  Collection tags: %', collection_count;
END $$;