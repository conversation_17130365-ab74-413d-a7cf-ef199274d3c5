-- RLS policies for optimized storage buckets
-- These policies maintain security while optimizing for performance

-- <PERSON><PERSON> helper function to check if user can access a collection
CREATE OR REPLACE FUNCTION auth.is_collection_accessible(collection_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = collection_id
    AND om.user_id = auth.uid()
  )
  OR auth.is_platform_user();
$$;

-- =====================================
-- PROFILES BUCKET POLICIES
-- =====================================

-- Allow authenticated users to view all profile images (public bucket)
CREATE POLICY "Allow public read access to profiles" ON storage.objects
  FOR SELECT USING (bucket_id = 'profiles');

-- Allow users to upload their own avatar
CREATE POLICY "Allow users to upload own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profiles' 
    AND auth.uid()::text = (storage.foldername(name))[2]  -- matches users/{user_id}/
  );

-- Allow organization admins to upload org logos
CREATE POLICY "Allow admins to upload org logos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profiles' 
    AND (storage.foldername(name))[1] = 'organizations'
    AND auth.is_admin_user()
    AND auth.is_organization_member((storage.foldername(name))[2]::uuid)
  );

-- Allow users to update their own avatar
CREATE POLICY "Allow users to update own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profiles' 
    AND auth.uid()::text = (storage.foldername(name))[2]
  );

-- Allow organization admins to update org logos
CREATE POLICY "Allow admins to update org logos" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profiles' 
    AND (storage.foldername(name))[1] = 'organizations'
    AND auth.is_admin_user()
    AND auth.is_organization_member((storage.foldername(name))[2]::uuid)
  );

-- =====================================
-- MEDIA BUCKETS POLICIES (thumbnails, compressed, originals)
-- =====================================

-- Allow organization members to view media assets
CREATE POLICY "Allow members to view media thumbnails" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-thumbnails'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow members to view media compressed" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-compressed'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow members to view media originals" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-originals'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

-- Allow organization admins to upload/manage media assets
CREATE POLICY "Allow admins to upload media thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-thumbnails'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to upload media compressed" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-compressed'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to upload media originals" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-originals'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

-- Allow admins to update media assets
CREATE POLICY "Allow admins to update media thumbnails" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-thumbnails'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to update media compressed" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-compressed'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to update media originals" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-originals'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

-- Allow admins to delete media assets
CREATE POLICY "Allow admins to delete media thumbnails" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-thumbnails'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to delete media compressed" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-compressed'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow admins to delete media originals" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-originals'
    AND auth.is_admin_user()
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );