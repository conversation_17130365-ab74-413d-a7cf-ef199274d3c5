-- Fix consistent admin role checking across all policies
-- The issue is that some policies check auth.users.raw_app_meta_data while others check public.users.role
-- We need to standardize on public.users.role which is what the frontend uses

-- First, let's ensure admin users have the role set in BOTH places for compatibility
-- Update auth metadata for admin users to match their users.role
UPDATE auth.users 
SET raw_app_meta_data = raw_app_meta_data || 
    CASE 
        WHEN (SELECT role FROM public.users WHERE id = auth.users.id) = 'admin' 
        THEN '{"role": "admin", "is_admin": true}'::jsonb
        WHEN (SELECT role FROM public.users WHERE id = auth.users.id) = 'superadmin' 
        THEN '{"role": "superadmin", "is_admin": true}'::jsonb
        ELSE '{}'::jsonb
    END
WHERE id IN (
    SELECT id FROM public.users WHERE role IN ('admin', 'superadmin')
);

-- Now fix the invitation policies to use public.users.role consistently
-- Drop the debug policy first
DROP POLICY IF EXISTS "Temp debug: allow all authenticated users to read invitations" ON public.pending_invitations;

-- Create the correct SELECT policy using public.users.role
CREATE POLICY "Admins and org members can view invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- Organization admins can see their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins can see all invitations (using public.users.role)
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Users can see invitations sent to their email
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- Ensure all admin role checks in RLS policies use public.users.role consistently
-- This creates a standard pattern for admin checking that the frontend can rely on

-- Add a helpful comment
COMMENT ON POLICY "Admins and org members can view invitations" ON public.pending_invitations
IS 'Allows global admins (public.users.role), org admins, and invitees to view invitations';