-- Fix search path issues in user creation functions
-- Ensures functions can find the user_role enum type

-- Update handle_new_user with explicit search path
CREATE OR REPLACE FUNCTION public.handle_new_user()
RET<PERSON>NS trigger 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
  invitation_id_value uuid;
  is_platform_invitation boolean;
  invitation_role text;
  full_name_value text;
  first_name_value text;
  last_name_value text;
BEGIN
  -- Check if this is an invitation signup
  invitation_id_value := (NEW.raw_user_meta_data->>'invitation_id')::uuid;
  
  -- If user is signing up via invitation and email is NOT confirmed, skip creating public.users record
  IF invitation_id_value IS NOT NULL AND NEW.email_confirmed_at IS NULL THEN
    -- Don't create user record yet - wait for email confirmation
    RETURN NEW;
  END IF;
  
  -- For regular signups or confirmed invited users, create the public.users record
  -- Check if there's a pending invitation for this email
  IF invitation_id_value IS NOT NULL THEN
    -- User signed up via invitation and email is confirmed
    -- Check if it's a platform admin invitation
    SELECT organization_id IS NULL INTO is_platform_invitation
    FROM public.pending_invitations
    WHERE id = invitation_id_value;
    
    -- Determine the role based on invitation type
    IF is_platform_invitation THEN
      invitation_role := 'platform_admin';
    ELSE
      invitation_role := 'brand_member';  -- Default role for organization invitations
    END IF;
  ELSE
    -- Regular signup without invitation
    invitation_role := 'brand_member';  -- Default role
  END IF;
  
  -- Extract name information from metadata
  full_name_value := COALESCE(NEW.raw_user_meta_data->>'full_name', '');
  
  -- Split full_name into first and last name if not provided separately
  IF NEW.raw_user_meta_data->>'first_name' IS NOT NULL THEN
    first_name_value := NEW.raw_user_meta_data->>'first_name';
    last_name_value := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
  ELSE
    -- If only full_name is provided, split it
    IF full_name_value != '' THEN
      first_name_value := split_part(full_name_value, ' ', 1);
      -- Get everything after the first space as last name
      IF position(' ' in full_name_value) > 0 THEN
        last_name_value := substring(full_name_value from position(' ' in full_name_value) + 1);
      ELSE
        last_name_value := '';
      END IF;
    ELSE
      -- Default to email username if no name provided
      first_name_value := split_part(NEW.email, '@', 1);
      last_name_value := '';
    END IF;
  END IF;
  
  -- Create the public.users record with explicit schema reference
  INSERT INTO public.users (
    id,
    email,
    first_name,
    last_name,
    display_name,
    avatar_url,
    role,
    created_at,
    updated_at
  ) VALUES (
    NEW.id,
    NEW.email,
    first_name_value,
    last_name_value,
    COALESCE(NEW.raw_user_meta_data->>'display_name', first_name_value || ' ' || last_name_value),
    COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
    invitation_role::public.user_role,  -- Explicit schema reference
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING; -- Prevent duplicate errors
  
  RETURN NEW;
END;
$$;

-- Update handle_email_confirmation with explicit search path
CREATE OR REPLACE FUNCTION public.handle_email_confirmation()
RETURNS trigger 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, auth
AS $$
DECLARE
  invitation_id_value uuid;
  invitation_record record;
  is_platform_invitation boolean;
  invitation_role text;
  user_exists boolean;
  full_name_value text;
  first_name_value text;
  last_name_value text;
BEGIN
  -- Only process if email was just confirmed (transition from NULL to timestamp)
  IF OLD.email_confirmed_at IS NULL AND NEW.email_confirmed_at IS NOT NULL THEN
    -- Check if user already exists in public.users
    SELECT EXISTS(SELECT 1 FROM public.users WHERE id = NEW.id) INTO user_exists;
    
    -- If user doesn't exist, check for invitation and create user
    IF NOT user_exists THEN
      invitation_id_value := (NEW.raw_user_meta_data->>'invitation_id')::uuid;
      
      IF invitation_id_value IS NOT NULL THEN
        -- Get the full invitation record
        SELECT * INTO invitation_record
        FROM public.pending_invitations
        WHERE id = invitation_id_value;
        
        IF invitation_record IS NOT NULL THEN
          -- Check if it's a platform admin invitation
          is_platform_invitation := (invitation_record.organization_id IS NULL);
          
          -- Determine the role based on invitation type
          IF is_platform_invitation THEN
            invitation_role := 'platform_admin';
          ELSE
            invitation_role := 'brand_member';  -- Default role for organization invitations
          END IF;
          
          -- Extract name information from metadata
          full_name_value := COALESCE(NEW.raw_user_meta_data->>'full_name', '');
          
          -- Split full_name into first and last name if not provided separately
          IF NEW.raw_user_meta_data->>'first_name' IS NOT NULL THEN
            first_name_value := NEW.raw_user_meta_data->>'first_name';
            last_name_value := COALESCE(NEW.raw_user_meta_data->>'last_name', '');
          ELSE
            -- If only full_name is provided, split it
            IF full_name_value != '' THEN
              first_name_value := split_part(full_name_value, ' ', 1);
              -- Get everything after the first space as last name
              IF position(' ' in full_name_value) > 0 THEN
                last_name_value := substring(full_name_value from position(' ' in full_name_value) + 1);
              ELSE
                last_name_value := '';
              END IF;
            ELSE
              -- Default to email username if no name provided
              first_name_value := split_part(NEW.email, '@', 1);
              last_name_value := '';
            END IF;
          END IF;
          
          -- Create the public.users record with explicit schema reference
          INSERT INTO public.users (
            id,
            email,
            first_name,
            last_name,
            display_name,
            avatar_url,
            role,
            created_at,
            updated_at
          ) VALUES (
            NEW.id,
            NEW.email,
            first_name_value,
            last_name_value,
            COALESCE(NEW.raw_user_meta_data->>'display_name', first_name_value || ' ' || last_name_value),
            COALESCE(NEW.raw_user_meta_data->>'avatar_url', ''),
            invitation_role::public.user_role,  -- Explicit schema reference
            NOW(),
            NOW()
          );
          
          -- If this is an organization invitation, create the membership
          IF NOT is_platform_invitation AND invitation_record.organization_id IS NOT NULL THEN
            INSERT INTO public.organization_memberships (
              user_id,
              organization_id,
              created_at
            ) VALUES (
              NEW.id,
              invitation_record.organization_id,
              NOW()
            )
            ON CONFLICT (user_id, organization_id) DO NOTHING;
          END IF;
          
          -- Mark the invitation as accepted
          UPDATE public.pending_invitations
          SET 
            accepted_at = NOW(),
            updated_at = NOW()
          WHERE id = invitation_id_value;
          
          -- Log security event for invitation acceptance
          INSERT INTO public.security_activity (
            user_id,
            event_type,
            ip_address,
            user_agent,
            metadata
          ) VALUES (
            NEW.id,
            'invitation_accepted',
            COALESCE(NEW.raw_app_meta_data->>'ip_address', 'system'),
            COALESCE(NEW.raw_app_meta_data->>'user_agent', 'system'),
            jsonb_build_object(
              'invitation_id', invitation_id_value,
              'organization_id', invitation_record.organization_id,
              'invited_by', invitation_record.invited_by,
              'role', invitation_role
            )
          );
        END IF;
      END IF;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$;