-- Tag System Redesign Migration
-- This migration implements organization/collection-scoped tags with specific categories

-- Step 1: Update tag_category enum to include new categories
ALTER TYPE tag_category RENAME TO tag_category_old;

CREATE TYPE tag_category AS ENUM (
  'global',           -- Platform-wide tags (managed by FashionLab)
  'angles',           -- View angles (Front, Back, Detail, etc.)
  'styling',          -- Styling-related tags
  'collection'        -- Customer-created tags for specific collections
);

-- Migrate existing categories to new enum values
ALTER TABLE tags 
  ALTER COLUMN category TYPE tag_category 
  USING CASE 
    WHEN category::text = 'view_type' THEN 'angles'::tag_category
    WHEN category::text = 'workflow_stage' THEN 'global'::tag_category
    WHEN category::text = 'product_specific' THEN 'collection'::tag_category
    WHEN category::text = 'custom' THEN 'collection'::tag_category
    ELSE 'collection'::tag_category
  END;

DROP TYPE tag_category_old;

-- Step 2: Add collection_id column to tags table
ALTER TABLE tags 
ADD COLUMN collection_id UUID REFERENCES collections(id) ON DELETE CASCADE;

-- Step 3: Update unique constraint to allow same tag names in different collections
ALTER TABLE tags DROP CONSTRAINT IF EXISTS tags_name_category_key;
ALTER TABLE tags ADD CONSTRAINT tags_name_category_collection_key 
  UNIQUE NULLS NOT DISTINCT (name, category, collection_id);

-- Step 4: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tags_collection_id 
  ON tags(collection_id) 
  WHERE collection_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_tags_category 
  ON tags(category);

CREATE INDEX IF NOT EXISTS idx_tags_global 
  ON tags(category) 
  WHERE category IN ('global', 'angles', 'styling') AND collection_id IS NULL;

-- Step 5: Migrate existing tags to new structure

-- Mark common workflow stages as global tags
UPDATE tags 
SET collection_id = NULL
WHERE category = 'global'
  AND name IN ('upload', 'draft', 'upscale', 'retouch', 'final', 'brief', 'input', 'custom_models', 'library', 'raw');

-- Mark common view types as angles
UPDATE tags
SET category = 'angles', collection_id = NULL
WHERE name IN ('front', 'back', 'detail', 'packshot', 'lifestyle', 'side', 'close-up', '45-degree', 'top-down')
  AND category = 'angles';

-- For other tags, assign them to collections based on asset usage
WITH tag_collections AS (
  SELECT DISTINCT
    t.id as tag_id,
    a.collection_id
  FROM tags t
  JOIN asset_tags at ON t.id = at.tag_id
  JOIN assets a ON at.asset_id = a.id
  WHERE t.collection_id IS NULL
    AND t.category NOT IN ('global', 'angles', 'styling')
    AND a.collection_id IS NOT NULL
)
UPDATE tags t
SET collection_id = tc.collection_id
FROM tag_collections tc
WHERE t.id = tc.tag_id
  AND t.collection_id IS NULL
  AND t.category = 'collection';

-- For tags used across multiple collections, we'll need to create duplicates
INSERT INTO tags (name, category, color, collection_id, created_at, updated_at)
SELECT DISTINCT
  t.name,
  t.category,
  t.color,
  tc.collection_id,
  t.created_at,
  NOW()
FROM tags t
JOIN (
  SELECT 
    t.id as tag_id,
    a.collection_id,
    ROW_NUMBER() OVER (PARTITION BY t.id ORDER BY a.collection_id) as rn
  FROM tags t
  JOIN asset_tags at ON t.id = at.tag_id
  JOIN assets a ON at.asset_id = a.id
  WHERE t.collection_id IS NULL
    AND t.category = 'collection'
    AND a.collection_id IS NOT NULL
  GROUP BY t.id, a.collection_id
) tc ON t.id = tc.tag_id
WHERE tc.rn > 1  -- Skip the first collection (already updated above)
ON CONFLICT (name, category, collection_id) DO NOTHING;

-- Update asset_tags to point to collection-specific tags
UPDATE asset_tags at
SET tag_id = (
  SELECT t2.id 
  FROM tags t2
  JOIN assets a ON a.id = at.asset_id
  WHERE 
    t2.name = (SELECT name FROM tags WHERE id = at.tag_id)
    AND t2.category = (SELECT category FROM tags WHERE id = at.tag_id)
    AND t2.collection_id = a.collection_id
  LIMIT 1
)
WHERE EXISTS (
  SELECT 1 
  FROM tags t
  JOIN assets a ON a.id = at.asset_id
  WHERE t.id = at.tag_id 
    AND t.collection_id IS NULL
    AND t.category = 'collection'
    AND a.collection_id IS NOT NULL
);

-- Clean up orphaned collection tags
DELETE FROM tags 
WHERE category = 'collection'
  AND collection_id IS NULL
  AND id NOT IN (SELECT DISTINCT tag_id FROM asset_tags);

-- Step 6: Update RLS policies

-- Drop existing policies
DROP POLICY IF EXISTS "Tags are viewable by everyone" ON tags;
DROP POLICY IF EXISTS "Only platform admins can create tags" ON tags;
DROP POLICY IF EXISTS "Only platform admins can update tags" ON tags;
DROP POLICY IF EXISTS "Only platform admins can delete tags" ON tags;

-- New SELECT policy: Users can see global tags + tags from their collections
CREATE POLICY "Users can view relevant tags" ON tags
FOR SELECT TO authenticated
USING (
  -- Global, angles, and styling tags are visible to all
  (category IN ('global', 'angles', 'styling') AND collection_id IS NULL)
  OR 
  -- Collection tags are visible to organization members
  collection_id IN (
    SELECT c.id 
    FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- INSERT policy: Users can only create collection-specific tags
CREATE POLICY "Users can create collection tags" ON tags
FOR INSERT TO authenticated
WITH CHECK (
  -- Must be a collection tag
  category = 'collection'
  AND 
  -- Must specify a collection
  collection_id IS NOT NULL
  AND
  -- Must be a member of the organization
  collection_id IN (
    SELECT c.id 
    FROM collections c
    JOIN organization_memberships om ON c.organization_id = om.organization_id
    WHERE om.user_id = auth.uid()
  )
);

-- UPDATE policy: Platform admins can update any tag, brand admins can update collection tags
CREATE POLICY "Admins can update tags" ON tags
FOR UPDATE TO authenticated
USING (
  -- Platform admins can update any tag
  (SELECT role FROM users WHERE id = auth.uid()) IN ('platform_admin', 'platform_super')
  OR
  -- Brand admins can update collection tags in their organization
  (
    category = 'collection'
    AND collection_id IN (
      SELECT c.id 
      FROM collections c
      JOIN organization_memberships om ON c.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
        AND (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
    )
  )
)
WITH CHECK (
  -- Same conditions for the updated row
  (SELECT role FROM users WHERE id = auth.uid()) IN ('platform_admin', 'platform_super')
  OR
  (
    category = 'collection'
    AND collection_id IN (
      SELECT c.id 
      FROM collections c
      JOIN organization_memberships om ON c.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
        AND (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
    )
  )
);

-- DELETE policy: Same as update
CREATE POLICY "Admins can delete tags" ON tags
FOR DELETE TO authenticated
USING (
  -- Platform admins can delete any tag
  (SELECT role FROM users WHERE id = auth.uid()) IN ('platform_admin', 'platform_super')
  OR
  -- Brand admins can delete collection tags in their organization
  (
    category = 'collection'
    AND collection_id IN (
      SELECT c.id 
      FROM collections c
      JOIN organization_memberships om ON c.organization_id = om.organization_id
      WHERE om.user_id = auth.uid()
        AND (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
    )
  )
);

-- Step 7: Create some default global and angle tags if they don't exist
INSERT INTO tags (name, category, color, collection_id)
VALUES 
  -- Global workflow tags
  ('upload', 'global', '#6B7280', NULL),
  ('draft', 'global', '#8B5CF6', NULL),
  ('upscale', 'global', '#3B82F6', NULL),
  ('retouch', 'global', '#10B981', NULL),
  ('final', 'global', '#059669', NULL),
  -- Angle tags
  ('front', 'angles', '#EC4899', NULL),
  ('back', 'angles', '#EC4899', NULL),
  ('side', 'angles', '#EC4899', NULL),
  ('detail', 'angles', '#EC4899', NULL),
  ('packshot', 'angles', '#EC4899', NULL),
  ('lifestyle', 'angles', '#EC4899', NULL),
  ('close-up', 'angles', '#EC4899', NULL),
  ('45-degree', 'angles', '#EC4899', NULL),
  ('top-down', 'angles', '#EC4899', NULL)
ON CONFLICT (name, category, collection_id) DO NOTHING;

-- Step 8: Add helpful comments
COMMENT ON COLUMN tags.category IS 'Tag category: global (platform-wide), angles (view types), styling (style-related), collection (customer-created)';
COMMENT ON COLUMN tags.collection_id IS 'NULL for global/angles/styling tags, UUID for collection-specific tags';