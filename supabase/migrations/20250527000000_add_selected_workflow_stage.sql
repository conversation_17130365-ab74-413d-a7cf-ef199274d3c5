-- Migration: Add 'selected' workflow stage
-- This migration adds the 'selected' stage between 'raw_ai_images' and 'retouch'

-- Create a new enum with 'selected' stage added
CREATE TYPE public.workflow_stage_with_selected AS ENUM (
    'upload',
    'raw_ai_images', 
    'selected',
    'upscale',
    'retouch',
    'final'
);

-- Update the assets table to use the new enum
-- First remove the default constraint and convert to text
ALTER TABLE public.assets
ALTER COLUMN workflow_stage DROP DEFAULT;

ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE text;

-- Convert to the new enum type
ALTER TABLE public.assets
ALTER COLUMN workflow_stage TYPE public.workflow_stage_with_selected
USING workflow_stage::public.workflow_stage_with_selected;

-- Restore the default value with the new enum
ALTER TABLE public.assets
ALTER COLUMN workflow_stage SET DEFAULT 'upload';

-- Drop the old enum and rename the new one
DROP TYPE public.workflow_stage;
ALTER TYPE public.workflow_stage_with_selected RENAME TO workflow_stage;

-- Add comment for documentation
COMMENT ON TYPE public.workflow_stage IS 'Workflow stages for asset processing: upload (input assets), raw_ai_images (AI generated drafts), selected (client selected images), upscale, retouch, final';
