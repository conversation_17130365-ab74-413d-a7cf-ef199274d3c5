-- Fix the pending_invitations table RLS policies for UPDATE and DELETE
-- This migration addresses the 403 Forbidden errors when trying to resend or cancel invitations

-- First, drop any existing policies for UPDATE and DELETE
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can delete invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow admins to update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow admins to delete invitations" ON public.pending_invitations;

-- Create more permissive policies for UPDATE operations
CREATE POLICY "Allow admins to update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Platform admin check using new role system
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  -- OR brand admin check
  OR (
    (SELECT role FROM public.users WHERE id = auth.uid()) = 'brand_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships
      WHERE user_id = auth.uid()
      AND organization_id = pending_invitations.organization_id
    )
  )
);

-- Create more permissive policies for DELETE operations
CREATE POLICY "Allow admins to delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  -- Platform admin check using new role system
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  -- OR brand admin check
  OR (
    (SELECT role FROM public.users WHERE id = auth.uid()) = 'brand_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships
      WHERE user_id = auth.uid()
      AND organization_id = pending_invitations.organization_id
    )
  )
);

-- Make sure RLS is enabled
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Add helpful comments
COMMENT ON POLICY "Allow admins to update invitations" ON public.pending_invitations
IS 'Allows platform admins and organization admins to update invitation records';

COMMENT ON POLICY "Allow admins to delete invitations" ON public.pending_invitations
IS 'Allows platform admins and organization admins to delete invitation records';