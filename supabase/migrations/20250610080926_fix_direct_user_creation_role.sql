-- Fix handle_new_user function to properly handle direct user creation with role assignment
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user was invited and get the invitation details
  IF NEW.email_confirmed_at IS NOT NULL THEN
    -- Look for pending invitation with role
    DE<PERSON>AR<PERSON>
      invitation_record RECORD;
      org_record RECORD;
      user_role public.user_role;
      user_is_freelancer BOOLEAN;
    BEGIN
      -- Get the invitation record
      SELECT pi.*, u.role as inviter_role 
      INTO invitation_record
      FROM public.pending_invitations pi
      LEFT JOIN public.users u ON u.id = pi.invited_by
      WHERE LOWER(pi.email) = LOWER(NEW.email)
        AND pi.expires_at > NOW()
        AND pi.accepted_at IS NULL
      ORDER BY pi.created_at DESC
      LIMIT 1;

      IF invitation_record.id IS NOT NULL THEN
        -- User was invited - use invitation settings
        INSERT INTO public.users (id, email, first_name, last_name, role, is_freelancer)
        VALUES (
          NEW.id,
          NEW.email,
          COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
          COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
          COALESCE(invitation_record.role, 'brand_member'),
          CASE 
            WHEN invitation_record.role = 'brand_admin' 
              AND invitation_record.inviter_role IN ('platform_admin', 'platform_super')
            THEN COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false)
            ELSE false
          END
        );

        -- Create organization membership for the primary organization
        INSERT INTO public.organization_memberships (user_id, organization_id)
        VALUES (NEW.id, invitation_record.organization_id);

        -- Create memberships for any additional organizations (for freelancers)
        INSERT INTO public.organization_memberships (user_id, organization_id)
        SELECT NEW.id, io.organization_id
        FROM public.invitation_organizations io
        WHERE io.invitation_id = invitation_record.id
          AND io.organization_id != invitation_record.organization_id;

        -- Mark invitation as accepted
        UPDATE public.pending_invitations
        SET accepted_at = NOW()
        WHERE id = invitation_record.id;

        -- Log the activity
        INSERT INTO public.security_activity (user_id, event_type, ip_address, metadata)
        VALUES (
          NEW.id,
          'invitation_accepted',
          COALESCE(NEW.raw_user_meta_data->>'ip_address', 'unknown'),
          jsonb_build_object(
            'invitation_id', invitation_record.id,
            'organization_id', invitation_record.organization_id,
            'role', COALESCE(invitation_record.role, 'brand_member'),
            'is_freelancer', CASE 
              WHEN invitation_record.role = 'brand_admin' 
                AND invitation_record.inviter_role IN ('platform_admin', 'platform_super')
              THEN COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false)
              ELSE false
            END
          )
        );
      ELSE
        -- No invitation found - check if this is direct creation
        -- Get role and freelancer status from user metadata
        user_role := COALESCE((NEW.raw_user_meta_data->>'role')::public.user_role, 'platform_admin');
        user_is_freelancer := COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false);
        
        INSERT INTO public.users (id, email, first_name, last_name, role, is_freelancer)
        VALUES (
          NEW.id,
          NEW.email,
          COALESCE(NEW.raw_user_meta_data->>'first_name', ''),
          COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
          user_role,
          user_is_freelancer
        );

        -- Handle organization memberships for direct creation
        IF NEW.raw_user_meta_data->>'organization_ids' IS NOT NULL THEN
          -- Parse organization IDs from metadata and create memberships
          INSERT INTO public.organization_memberships (user_id, organization_id)
          SELECT NEW.id, org_id::uuid
          FROM jsonb_array_elements_text((NEW.raw_user_meta_data->>'organization_ids')::jsonb) AS org_id
          WHERE org_id::uuid IN (SELECT id FROM public.organizations);
        END IF;

        -- Log direct creation activity if created by another user
        IF NEW.raw_user_meta_data->>'created_by' IS NOT NULL THEN
          INSERT INTO public.security_activity (user_id, event_type, ip_address, metadata)
          VALUES (
            (NEW.raw_user_meta_data->>'created_by')::uuid,
            'user_created',
            COALESCE(NEW.raw_user_meta_data->>'ip_address', 'unknown'),
            jsonb_build_object(
              'created_user_id', NEW.id,
              'created_user_email', NEW.email,
              'created_user_role', user_role,
              'is_freelancer', user_is_freelancer,
              'organization_ids', NEW.raw_user_meta_data->'organization_ids'
            )
          );
        END IF;
      END IF;
    END;
  ELSE
    -- Email not confirmed yet, just create basic user record
    -- Check metadata for role assignment even without email confirmation
    INSERT INTO public.users (id, email, role, is_freelancer)
    VALUES (
      NEW.id, 
      NEW.email, 
      COALESCE((NEW.raw_user_meta_data->>'role')::public.user_role, 'platform_admin'),
      COALESCE((NEW.raw_user_meta_data->>'is_freelancer')::boolean, false)
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also update the send-email function to work in local development
-- by bypassing Resend and using Supabase's built-in email testing
CREATE OR REPLACE FUNCTION public.send_email_local(
  recipient_email TEXT,
  subject TEXT,
  html_content TEXT,
  text_content TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  is_local BOOLEAN;
BEGIN
  -- Check if we're in local development
  SELECT current_setting('app.settings.site_url', true) LIKE '%localhost%' 
      OR current_setting('app.settings.site_url', true) LIKE '%127.0.0.1%'
  INTO is_local;

  IF is_local THEN
    -- In local development, emails are automatically captured by Inbucket
    -- We just need to log that an email was sent
    RAISE NOTICE 'Email sent to % with subject: %', recipient_email, subject;
    RETURN true;
  ELSE
    -- In production, use the Edge Function
    RETURN false;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;