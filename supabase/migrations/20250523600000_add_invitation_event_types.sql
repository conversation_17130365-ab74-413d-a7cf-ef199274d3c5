-- Migration: Add invitation-related event types to security_activity
-- Date: 2025-05-23
-- Description: Add invitation_cancelled and other invitation events to allowed event types

-- Drop the existing constraint
ALTER TABLE public.security_activity 
DROP CONSTRAINT IF EXISTS security_activity_event_type_check;

-- Add the new constraint with invitation event types
ALTER TABLE public.security_activity 
ADD CONSTRAINT security_activity_event_type_check 
CHECK (event_type IN (
    'login', 
    'logout', 
    'password_change', 
    'email_change', 
    'account_deletion',
    'invitation_created',
    'invitation_cancelled',
    'invitation_resent',
    'invitation_accepted',
    'invitation_rejected'
));

-- Add comment explaining the allowed event types
COMMENT ON CONSTRAINT security_activity_event_type_check ON public.security_activity IS 
'Allowed event types: login, logout, password_change, email_change, account_deletion, invitation_created, invitation_cancelled, invitation_resent, invitation_accepted, invitation_rejected';