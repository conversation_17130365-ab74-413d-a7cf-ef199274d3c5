-- Restrict storage bucket access to platform admins only
-- This replaces the overly permissive policy that allowed all authenticated users

-- Drop the existing permissive policy
DROP POLICY IF EXISTS "Allow authenticated users to list buckets" ON storage.buckets;

-- Create a more restrictive policy - only platform admins can list buckets
CREATE POLICY "Only platform admins can list buckets"
ON storage.buckets FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('platform_admin', 'platform_super')
  )
);