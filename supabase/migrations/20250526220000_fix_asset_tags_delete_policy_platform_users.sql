-- Fix asset_tags delete policy to allow platform users (superadmins/platform admins)
-- to delete tags from any asset, following the same pattern as other recent policy fixes

-- Drop the existing policy
DROP POLICY IF EXISTS "asset_tags_delete_policy" ON asset_tags;

-- Create new policy that allows both platform users and organization members
CREATE POLICY "asset_tags_delete_policy"
    ON asset_tags FOR DELETE
    USING (
        -- Allow platform users (superadmins/platform admins) to delete any asset tag
        auth.is_platform_user() 
        OR
        -- Allow organization members to delete tags from assets in their organization
        EXISTS (
            SELECT 1 FROM assets a
            JOIN collections c ON a.collection_id = c.id
            WHERE a.id = asset_tags.asset_id
            AND auth.user_in_organization(c.organization_id)
        )
    );

-- Add comment for documentation
COMMENT ON POLICY "asset_tags_delete_policy" ON asset_tags IS 
'Platform users can delete any asset tag, organization members can delete tags from their org assets. Fixes FAS-30: Superadmin cannot remove tags from assets.';
