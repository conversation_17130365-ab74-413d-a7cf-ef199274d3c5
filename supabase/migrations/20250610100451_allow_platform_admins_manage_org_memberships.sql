-- Allow platform admins to manage organization memberships
-- This enables the user edit dialog to update organization assignments

-- Create policy for platform admins to insert organization memberships
CREATE POLICY "Platform admins can create org memberships" ON public.organization_memberships
FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('platform_admin', 'platform_super')
  )
);

-- Create policy for platform admins to delete organization memberships
CREATE POLICY "Platform admins can delete org memberships" ON public.organization_memberships
FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND role IN ('platform_admin', 'platform_super')
  )
);

-- Also ensure platform admins can view all memberships (if not already exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'organization_memberships' 
    AND policyname = 'Platform admins can view all memberships'
  ) THEN
    CREATE POLICY "Platform admins can view all memberships" ON public.organization_memberships
    FOR SELECT
    TO authenticated
    USING (
      EXISTS (
        SELECT 1 FROM users
        WHERE id = auth.uid()
        AND role IN ('platform_admin', 'platform_super')
      )
    );
  END IF;
END $$;