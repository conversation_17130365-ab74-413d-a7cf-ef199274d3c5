-- Temporary debug: Create a very simple policy to test if the basic permissions work
-- Then we can iterate to find the exact issue

-- Drop current problematic policy
DROP POLICY IF EXISTS "Users can view relevant invitations" ON public.pending_invitations;

-- Create a simple test policy that allows all authenticated users to read
-- This will help us isolate if the issue is with the policy logic or something else
CREATE POLICY "Temp debug: allow all authenticated users to read invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (true);

-- Add a comment to remember this is temporary
COMMENT ON POLICY "Temp debug: allow all authenticated users to read invitations" ON public.pending_invitations
IS 'TEMPORARY DEBUG POLICY - allows all authenticated users to read invitations for testing';