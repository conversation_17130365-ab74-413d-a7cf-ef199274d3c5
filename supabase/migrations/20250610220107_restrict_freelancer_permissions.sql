-- Restrict freelancer permissions
-- Freelancers (brand_admin with is_freelancer=true) should not be able to:
-- 1. Create new collections
-- 2. Delete organizations
-- 3. Delete collections

-- Add helper function to check if user is a freelancer
CREATE OR REPLACE FUNCTION auth.is_freelancer_user()
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() 
    AND role = 'brand_admin' 
    AND is_freelancer = true
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update collections INSERT policy to exclude freelancers
DROP POLICY IF EXISTS "collections_insert_policy" ON public.collections;

CREATE POLICY "collections_insert_policy"
ON public.collections FOR INSERT
WITH CHECK (
    -- Platform users can always create
    auth.is_platform_user() 
    OR 
    -- Organization members who are NOT freelancers can create
    (auth.user_in_organization(organization_id) AND NOT auth.is_freelancer_user())
);

-- Update collections DELETE policy to exclude freelancers
DROP POLICY IF EXISTS "Admins can delete collections" ON public.collections;

CREATE POLICY "Admins can delete collections"
ON public.collections FOR DELETE
USING (
    -- Platform users can always delete
    auth.is_platform_user() 
    OR 
    -- Brand admins who are NOT freelancers can delete in their org
    (
        auth.is_organization_member(organization_id) 
        AND auth.is_admin_user()
        AND NOT auth.is_freelancer_user()
    )
);

-- Ensure organizations cannot be deleted by freelancers
-- First check if there's an existing DELETE policy on organizations
DROP POLICY IF EXISTS "Only platform admins can delete organizations" ON public.organizations;
DROP POLICY IF EXISTS "Admins can delete organizations" ON public.organizations;

-- Create new restrictive DELETE policy for organizations
CREATE POLICY "Only platform admins can delete organizations"
ON public.organizations FOR DELETE
USING (auth.is_platform_user());

-- Update products table policies to prevent freelancers from creating products
DROP POLICY IF EXISTS "Org members can create products" ON public.products;
DROP POLICY IF EXISTS "products_insert_policy" ON public.products;

CREATE POLICY "products_insert_policy"
ON public.products FOR INSERT
WITH CHECK (
    -- Platform users can always create
    auth.is_platform_user()
    OR
    -- Non-freelancer members can create products
    EXISTS (
        SELECT 1 FROM public.collections c
        WHERE c.id = products.collection_id
        AND auth.is_organization_member(c.organization_id)
        AND NOT auth.is_freelancer_user()
    )
);

-- Add comment to document freelancer restrictions
COMMENT ON FUNCTION auth.is_freelancer_user() IS 'Returns true if the current user is a freelancer (brand_admin with is_freelancer flag). Freelancers have restricted permissions compared to regular brand_admins.';