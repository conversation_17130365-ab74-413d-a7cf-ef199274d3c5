-- Add model scoping functionality to model_library table
-- This allows models to be scoped to specific organizations or collections
-- while maintaining backward compatibility for global models

-- Add scoping columns to model_library table
ALTER TABLE public.model_library 
ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS collection_id UUID REFERENCES public.collections(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS scope_type TEXT DEFAULT 'global' CHECK (scope_type IN ('global', 'organization', 'collection'));

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_model_library_organization_id ON public.model_library(organization_id);
CREATE INDEX IF NOT EXISTS idx_model_library_collection_id ON public.model_library(collection_id);
CREATE INDEX IF NOT EXISTS idx_model_library_scope_type ON public.model_library(scope_type);

-- Add constraint to ensure proper scoping relationships
ALTER TABLE public.model_library 
ADD CONSTRAINT model_library_scope_consistency CHECK (
  (scope_type = 'global' AND organization_id IS NULL AND collection_id IS NULL) OR
  (scope_type = 'organization' AND organization_id IS NOT NULL AND collection_id IS NULL) OR
  (scope_type = 'collection' AND collection_id IS NOT NULL)
);

-- Update existing RLS policies
-- Drop the existing simple policy
DROP POLICY IF EXISTS "model_library_read_active" ON public.model_library;

-- Create new comprehensive RLS policy for reading models
CREATE POLICY "model_library_read_with_scope" ON public.model_library
FOR SELECT
TO authenticated
USING (
  is_active = true AND (
    -- Global models are visible to everyone
    scope_type = 'global' OR
    
    -- Organization models are visible to organization members
    (scope_type = 'organization' AND EXISTS (
      SELECT 1 FROM public.organization_memberships om
      WHERE om.organization_id = model_library.organization_id
      AND om.user_id = auth.uid()
    )) OR
    
    -- Collection models are visible to users with collection access
    (scope_type = 'collection' AND EXISTS (
      SELECT 1 FROM public.collections c
      JOIN public.organization_memberships om ON c.organization_id = om.organization_id
      WHERE c.id = model_library.collection_id
      AND om.user_id = auth.uid()
    )) OR
    
    -- Platform admins can see all models
    EXISTS (
      SELECT 1 FROM public.users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
  )
);

-- Update insert policy to allow scoped model creation
DROP POLICY IF EXISTS "model_library_insert_platform_admin" ON public.model_library;

CREATE POLICY "model_library_insert_with_scope" ON public.model_library
FOR INSERT
TO authenticated
WITH CHECK (
  -- Platform admins can create any model
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  ) OR

  -- Brand admins can create organization-scoped models for their org
  (scope_type = 'organization' AND EXISTS (
    SELECT 1 FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = model_library.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )) OR

  -- Brand admins can create collection-scoped models for collections in their org
  (scope_type = 'collection' AND EXISTS (
    SELECT 1 FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    JOIN public.users u ON u.id = om.user_id
    WHERE c.id = model_library.collection_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  ))
);

-- Update policy for model updates
DROP POLICY IF EXISTS "model_library_update_platform_admin" ON public.model_library;

CREATE POLICY "model_library_update_with_scope" ON public.model_library
FOR UPDATE
TO authenticated
USING (
  -- Platform admins can update any model
  EXISTS (
    SELECT 1 FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  ) OR

  -- Brand admins can update their organization's models
  (scope_type = 'organization' AND EXISTS (
    SELECT 1 FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = model_library.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )) OR

  -- Brand admins can update their collection's models
  (scope_type = 'collection' AND EXISTS (
    SELECT 1 FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    JOIN public.users u ON u.id = om.user_id
    WHERE c.id = model_library.collection_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  ))
);

-- Add comments for documentation
COMMENT ON COLUMN public.model_library.organization_id IS 'Organization that owns this model (for organization-scoped models)';
COMMENT ON COLUMN public.model_library.collection_id IS 'Collection that owns this model (for collection-scoped models)';
COMMENT ON COLUMN public.model_library.scope_type IS 'Scope of model visibility: global, organization, or collection';

-- Update existing global models to have explicit scope_type
UPDATE public.model_library 
SET scope_type = 'global' 
WHERE scope_type IS NULL OR scope_type = 'global';
