-- Fix infinite recursion in users table policies
-- The issue is that policies are checking the users table to determine if someone is an admin,
-- which creates a circular dependency

-- Drop the problematic policies
DROP POLICY IF EXISTS "Users can update profiles" ON public.users;
DROP POLICY IF EXISTS "Platform admins can view all users" ON public.users;

-- Create a secure way to check if current user is platform admin without recursion
-- This uses auth.uid() directly without querying the users table
CREATE OR REPLACE FUNCTION auth.is_platform_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.users 
    WHERE id = auth.uid() 
    AND role IN ('platform_admin', 'platform_super')
  );
$$;

-- Now create policies using the function
-- Users can view their own profile or platform admins can view all
CREATE POLICY "Users can view profiles" ON public.users
FOR SELECT
TO authenticated
USING (
  id = auth.uid() 
  OR 
  auth.is_platform_admin()
);

-- Users can update their own profile or platform admins can update any
CREATE POLICY "Users can update profiles" ON public.users
FOR UPDATE
TO authenticated
USING (
  id = auth.uid() 
  OR 
  auth.is_platform_admin()
)
WITH CHECK (
  -- For self-updates, check role hasn't changed (unless they're admin)
  (id = auth.uid() AND (
    role = (SELECT role FROM users WHERE id = auth.uid())
    OR auth.is_platform_admin()
  ))
  OR
  -- Platform admins can update anyone
  auth.is_platform_admin()
);