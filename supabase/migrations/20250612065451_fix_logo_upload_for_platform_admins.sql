-- Fix logo upload permissions for platform admins
-- Platform admins should be able to upload logos for any organization without being a member

-- Drop existing policies for logo uploads
DROP POLICY IF EXISTS "Allow admins to upload org logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to update org logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to delete org logos" ON storage.objects;

-- Create new policies that properly handle platform admin access

-- Platform admins can upload logos for any organization
CREATE POLICY "Platform admins can upload any org logo" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);

-- Brand admins can upload their own organization's logo
CREATE POLICY "Brand admins can upload own org logo" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    JOIN organization_memberships om ON om.user_id = u.id
    WHERE u.id = auth.uid()
    AND u.role = 'brand_admin'
    AND om.organization_id = (storage.foldername(name))[2]::uuid
  )
);

-- Platform admins can update logos for any organization
CREATE POLICY "Platform admins can update any org logo" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);

-- Brand admins can update their own organization's logo
CREATE POLICY "Brand admins can update own org logo" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    JOIN organization_memberships om ON om.user_id = u.id
    WHERE u.id = auth.uid()
    AND u.role = 'brand_admin'
    AND om.organization_id = (storage.foldername(name))[2]::uuid
  )
);

-- Platform admins can delete logos for any organization
CREATE POLICY "Platform admins can delete any org logo" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);

-- Brand admins can delete their own organization's logo
CREATE POLICY "Brand admins can delete own org logo" ON storage.objects
FOR DELETE USING (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'organizations'
  AND EXISTS (
    SELECT 1 FROM users u
    JOIN organization_memberships om ON om.user_id = u.id
    WHERE u.id = auth.uid()
    AND u.role = 'brand_admin'
    AND om.organization_id = (storage.foldername(name))[2]::uuid
  )
);

-- Also ensure platform admins can upload user avatars (in case they need to)
CREATE POLICY "Platform admins can manage avatars" ON storage.objects
FOR ALL USING (
  bucket_id = 'profiles' 
  AND (storage.foldername(name))[1] = 'avatars'
  AND EXISTS (
    SELECT 1 FROM users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
);