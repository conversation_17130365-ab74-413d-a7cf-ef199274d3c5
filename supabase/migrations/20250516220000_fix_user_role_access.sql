-- Fix user access issues while maintaining profile functionality

-- 1. Drop all existing policies on users table
DROP POLICY IF EXISTS "Users can read own record" ON public.users;
DROP POLICY IF EXISTS "Users can update own record" ON public.users;
DROP POLICY IF EXISTS "Admins can read all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can update all users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can read their own profile" ON public.users;
DROP POLICY IF EXISTS "Allow users to read their own user record" ON public.users;
DROP POLICY IF EXISTS "Allow authenticated users to read all user records" ON public.users;

-- 2. Create working policies that allow proper access
-- Allow authenticated users to read all users (needed for role checks)
CREATE POLICY "Allow authenticated users to read users"
ON public.users FOR SELECT
TO authenticated
USING (true);

-- Allow users to update their own record
CREATE POLICY "Allow users to update own record"
ON public.users FOR UPDATE
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- 3. Ensure security_activity table has correct policy
DROP POLICY IF EXISTS "Users can view own security activity" ON public.security_activity;
DROP POLICY IF EXISTS "Users can read their own security activity" ON public.security_activity;

CREATE POLICY "Users can view own security activity"
ON public.security_activity FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- 4. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, UPDATE ON public.users TO authenticated;
GRANT SELECT ON public.security_activity TO authenticated;

-- 5. Ensure we can still log security events
GRANT EXECUTE ON FUNCTION public.log_security_event TO authenticated;