-- Clean up old policies that reference outdated role names
-- This must run conditionally only if tables exist

DO $$
BEGIN
    -- Only clean up if the table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' AND table_name = 'pending_invitations') THEN
        
        -- Drop specific policies that might use old role names
        DROP POLICY IF EXISTS "Simple: Update invitations" ON public.pending_invitations;
        DROP POLICY IF EXISTS "Simple: Delete invitations" ON public.pending_invitations;
        DROP POLICY IF EXISTS "Standard: View invitations" ON public.pending_invitations;
        DROP POLICY IF EXISTS "Standard: Update invitations" ON public.pending_invitations;
        DROP POLICY IF EXISTS "Debug: All authenticated can select" ON public.pending_invitations;
        
    END IF;
END $$;