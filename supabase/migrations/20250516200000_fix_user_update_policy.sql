-- Drop existing policy if it exists
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;

-- Create policy to allow users to update their own profile
CREATE POLICY "Users can update their own profile"
ON public.users FOR UPDATE
TO authenticated
USING (id = auth.uid())
WITH CHECK (id = auth.uid());

-- Also ensure users can read their own profile
DROP POLICY IF EXISTS "Users can read their own profile" ON public.users;

CREATE POLICY "Users can read their own profile"
ON public.users FOR SELECT
TO authenticated
USING (id = auth.uid());