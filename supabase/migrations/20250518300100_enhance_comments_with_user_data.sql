-- Enhance comments table to include author information
-- First add author field for storing user name
ALTER TABLE public.comments ADD COLUMN IF NOT EXISTS author TEXT;

-- Drop existing comments policies to replace with new ones
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read comments for accessible assets" ON public.comments;
DROP POLICY IF EXISTS "Users can insert their own comments" ON public.comments;

-- Create new policies for comments table

-- Policy 1: Users can view comments on assets they have access to
CREATE POLICY "Users can view comments on accessible assets"
ON public.comments FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM public.assets a
    JOIN public.collections c ON a.collection_id = c.id
    WHERE a.id = comments.asset_id
      AND (
        -- User is a member of the organization
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
        )
        OR
        -- User is an admin or superadmin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);

-- Policy 2: Users can create comments on assets they have access to
CREATE POLICY "Users can create comments on accessible assets"
ON public.comments FOR INSERT
WITH CHECK (
  -- User must be authenticated
  auth.uid() = user_id
  AND
  -- User must have access to the asset
  EXISTS (
    SELECT 1
    FROM public.assets a
    JOIN public.collections c ON a.collection_id = c.id
    WHERE a.id = asset_id
      AND (
        -- User is a member of the organization
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
        )
        OR
        -- User is an admin or superadmin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);

-- Policy 3: Users can update their own comments
CREATE POLICY "Users can update their own comments"
ON public.comments FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Policy 4: Users can delete their own comments
CREATE POLICY "Users can delete their own comments"
ON public.comments FOR DELETE
USING (auth.uid() = user_id);

-- Add mentions table for tagging users in comments
CREATE TABLE IF NOT EXISTS public.comment_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    mentioned_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on mentions table
ALTER TABLE public.comment_mentions ENABLE ROW LEVEL SECURITY;

-- Policy for mentions: users can see mentions on comments they can access
CREATE POLICY "Users can view mentions on accessible comments"
ON public.comment_mentions FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM public.comments c
    WHERE c.id = comment_mentions.comment_id
      -- Reuse the same access logic as comments
      AND EXISTS (
        SELECT 1
        FROM public.assets a
        JOIN public.collections col ON a.collection_id = col.id
        WHERE a.id = c.asset_id
          AND (
            EXISTS (
              SELECT 1
              FROM public.organization_memberships om
              WHERE om.organization_id = col.organization_id
                AND om.user_id = auth.uid()
            )
            OR
            EXISTS (
              SELECT 1
              FROM public.users u
              WHERE u.id = auth.uid()
                AND u.role IN ('admin', 'superadmin')
            )
          )
      )
  )
);

-- Policy for mentions: users can create mentions when they create comments
CREATE POLICY "Users can create mentions with their comments"
ON public.comment_mentions FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.comments c
    WHERE c.id = comment_mentions.comment_id
      AND c.user_id = auth.uid()
  )
);

-- Create an index for performance
CREATE INDEX IF NOT EXISTS idx_comments_asset_id ON public.comments(asset_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comment_mentions_comment_id ON public.comment_mentions(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_mentions_user_id ON public.comment_mentions(mentioned_user_id);

-- Update trigger for comments updated_at
CREATE OR REPLACE FUNCTION update_comments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop if exists and recreate to avoid conflicts
DROP TRIGGER IF EXISTS comments_updated_at_trigger ON public.comments;
CREATE TRIGGER comments_updated_at_trigger
BEFORE UPDATE ON public.comments
FOR EACH ROW
EXECUTE FUNCTION update_comments_updated_at();