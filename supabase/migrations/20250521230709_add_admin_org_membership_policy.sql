-- Add policy for admins and superadmins to see all memberships
CREATE POLICY "Allow admins and superadmins to read all memberships" 
ON organization_memberships
FOR SELECT
TO public
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE id = auth.uid()
    AND users.role IN ('admin', 'superadmin')
  )
);

-- Also add a policy for org admins to see members in their organizations
-- This is a non-recursive implementation 
CREATE POLICY "Allow org admins to see members in their orgs" 
ON organization_memberships
FOR SELECT
TO public
USING (
  EXISTS (
    -- Check if the user is an org_admin for this organization
    SELECT 1 FROM organization_memberships om
    WHERE om.user_id = auth.uid()
    AND om.organization_id = organization_memberships.organization_id
    AND om.role = 'org_admin'
  )
);
