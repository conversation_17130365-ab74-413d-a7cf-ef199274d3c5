-- Fix asset DELETE policy to use organization_id instead of client_id
-- This updates the policy to match the current schema

-- Drop old policies that reference client_id
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to delete assets in their orgs" ON public.assets;
DROP POLICY IF EXISTS "Allow members to insert assets in their org collections" ON public.assets;

-- Create new DELETE policy using organization_id
CREATE POLICY "Members can delete assets in their organizations"
ON public.assets FOR DELETE
USING (
  -- Allow if user is a member of the organization that owns the collection
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  -- Allow platform users to delete any asset
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
);

-- Also update INSERT policy to use organization_id
CREATE POLICY "Members can insert assets in their organizations"
ON public.assets FOR INSERT
WITH CHECK (
  -- Allow if user is a member of the organization that owns the collection
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  -- Allow platform users to insert into any collection
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
);

-- Update UPDATE policy as well for consistency
DROP POLICY IF EXISTS "Members can update assets in their organizations" ON public.assets;

CREATE POLICY "Members can update assets in their organizations"
ON public.assets FOR UPDATE
USING (
  -- Allow if user is a member of the organization that owns the collection
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  -- Allow platform users to update any asset
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
)
WITH CHECK (
  -- Same check for the new values
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON c.organization_id = om.organization_id
    WHERE c.id = assets.collection_id 
      AND om.user_id = auth.uid()
  )
  OR
  EXISTS (
    SELECT 1
    FROM public.users
    WHERE id = auth.uid() 
      AND role IN ('platform_super', 'platform_admin')
  )
);