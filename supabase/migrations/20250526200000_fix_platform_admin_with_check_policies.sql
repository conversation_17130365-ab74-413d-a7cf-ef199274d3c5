-- Fix platform admin WITH CHECK policies for UPDATE operations
-- Problem: Platform admins couldn't see returned data after updates due to restrictive WITH CHECK clauses
-- Solution: Create separate policies for platform admins with WITH CHECK (true)

-- ============================================
-- Organizations table
-- ============================================

-- Check if a platform-specific policy already exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'organizations' 
    AND policyname = 'Platform users can update any organization'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any organization"
    ON public.organizations
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any organization" ON public.organizations IS 
    'Platform administrators have unrestricted update access to all organizations. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Products table
-- ============================================

-- Check if a platform-specific policy already exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'products' 
    AND policyname = 'Platform users can update any product'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any product"
    ON public.products
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any product" ON public.products IS 
    'Platform administrators have unrestricted update access to all products. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Collections table
-- ============================================

-- The collections_update_policy currently doesn't have WITH CHECK, but let's add a platform-specific one for consistency
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'collections' 
    AND policyname = 'Platform users can update any collection'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any collection"
    ON public.collections
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any collection" ON public.collections IS 
    'Platform administrators have unrestricted update access to all collections. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Organization Memberships table
-- ============================================

-- The memberships_update_policy currently doesn't have WITH CHECK, but let's add a platform-specific one for consistency
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'organization_memberships' 
    AND policyname = 'Platform users can update any membership'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any membership"
    ON public.organization_memberships
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any membership" ON public.organization_memberships IS 
    'Platform administrators have unrestricted update access to all organization memberships. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Comments table (for completeness)
-- ============================================

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'comments' 
    AND policyname = 'Platform users can update any comment'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any comment"
    ON public.comments
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any comment" ON public.comments IS 
    'Platform administrators have unrestricted update access to all comments. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Tags table (for completeness)
-- ============================================

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'tags' 
    AND policyname = 'Platform users can update any tag'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any tag"
    ON public.tags
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any tag" ON public.tags IS 
    'Platform administrators have unrestricted update access to all tags. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Bulk uploads table (for completeness)
-- ============================================

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'bulk_uploads' 
    AND policyname = 'Platform users can update any bulk upload'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any bulk upload"
    ON public.bulk_uploads
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any bulk upload" ON public.bulk_uploads IS 
    'Platform administrators have unrestricted update access to all bulk uploads. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Pending invitations table (important!)
-- ============================================

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'pending_invitations' 
    AND policyname = 'Platform users can update any invitation'
  ) THEN
    -- Create new policy for platform users with WITH CHECK (true)
    CREATE POLICY "Platform users can update any invitation"
    ON public.pending_invitations
    FOR UPDATE
    USING (auth.is_platform_user())
    WITH CHECK (true);

    COMMENT ON POLICY "Platform users can update any invitation" ON public.pending_invitations IS 
    'Platform administrators have unrestricted update access to all pending invitations. 
    WITH CHECK (true) ensures updates return data properly.';
  END IF;
END $$;

-- ============================================
-- Verification query
-- ============================================

-- This query helps verify that all platform UPDATE policies have WITH CHECK (true)
-- Run after migration to confirm:
/*
SELECT 
  tablename,
  policyname,
  cmd,
  CASE 
    WHEN with_check = 'true' THEN '✅ WITH CHECK (true)'
    WHEN with_check IS NULL THEN '❌ NO WITH CHECK'
    ELSE '⚠️  RESTRICTIVE WITH CHECK'
  END as with_check_status
FROM pg_policies 
WHERE schemaname = 'public' 
AND cmd = 'UPDATE'
AND policyname LIKE '%Platform users can update%'
ORDER BY tablename;
*/