-- Fix RLS policy for pending_invitations table
-- This migration modifies the RLS policies for pending_invitations table
-- to ensure org_admin users can view pending invitations for their organization

-- First, drop existing policies if they're causing issues
DROP POLICY IF EXISTS "Organization admins can create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Users can view pending invitations for their organizations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can delete invitations" ON public.pending_invitations;

-- Create comprehensive policies for pending_invitations

-- Allow org_admins to view pending invitations for their organization
CREATE POLICY "Organization admins can view pending invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- User is an admin/superadmin
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  -- User is org_admin for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR
  -- User is being invited (can see their own invitations)
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- Allow org_admins to create invitations for their organization
CREATE POLICY "Organization admins can create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  -- User is an admin/superadmin
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  -- User is org_admin for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- Allow org_admins to update invitations for their organization
CREATE POLICY "Organization admins can update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- User is an admin/superadmin
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  -- User is org_admin for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- Allow org_admins to delete invitations for their organization
CREATE POLICY "Organization admins can delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  -- User is an admin/superadmin
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  -- User is org_admin for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- Add helpful comments explaining the policies
COMMENT ON POLICY "Organization admins can view pending invitations" ON public.pending_invitations
IS 'Allows organization admins to view pending invitations for their organization';

COMMENT ON POLICY "Organization admins can create invitations" ON public.pending_invitations
IS 'Allows organization admins to create invitations for their organization';

COMMENT ON POLICY "Organization admins can update invitations" ON public.pending_invitations
IS 'Allows organization admins to update invitations for their organization';

COMMENT ON POLICY "Organization admins can delete invitations" ON public.pending_invitations
IS 'Allows organization admins to delete invitations for their organization';