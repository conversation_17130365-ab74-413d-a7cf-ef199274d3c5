-- Add policies to allow admins to create and manage collections for clients

-- Drop existing SELECT policy if it exists
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to read collections" ON public.collections;

-- Create TEMPORARY MVP SELECT policy for admins
-- Allow admin and superadmin users to read all collections
CREATE POLICY "TEMP_MVP_Allow admins to read collections"
ON public.collections FOR SELECT TO authenticated
USING (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

-- Drop existing INSERT policy if it exists
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to insert collections" ON public.collections;

-- Create TEMPORARY MVP INSERT policy for admins
-- Allow admin and superadmin users to create collections for any client
CREATE POLICY "TEMP_MVP_Allow admins to insert collections"
ON public.collections FOR INSERT TO authenticated
WITH CHECK (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

-- Also update the DELETE policy to allow admins to delete any collection
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to delete collections" ON public.collections;

CREATE POLICY "TEMP_MVP_Allow admins to delete collections"
ON public.collections FOR DELETE TO authenticated
USING (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

-- And UPDATE policy for admins
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to update collections" ON public.collections;

CREATE POLICY "TEMP_MVP_Allow admins to update collections"
ON public.collections FOR UPDATE TO authenticated
USING (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
)
WITH CHECK (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

-- Also add policies for admins to manage assets in collections
DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to read assets" ON public.assets;

CREATE POLICY "TEMP_MVP_Allow admins to read assets"
ON public.assets FOR SELECT TO authenticated
USING (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

DROP POLICY IF EXISTS "TEMP_MVP_Allow admins to insert assets" ON public.assets;

CREATE POLICY "TEMP_MVP_Allow admins to insert assets"
ON public.assets FOR INSERT TO authenticated
WITH CHECK (
    auth.uid() IN (
        SELECT id FROM public.users
        WHERE role IN ('admin', 'superadmin')
    )
);

-- Note: These are TEMPORARY MVP policies. In production, you'd want:
-- 1. More granular permissions (e.g., admin can only manage collections for assigned clients)
-- 2. Audit logging for admin actions
-- 3. Proper role-based access control with organization context