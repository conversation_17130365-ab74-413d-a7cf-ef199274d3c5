-- Debug and fix the invitation SELECT policy
-- The current policy is too complex and causing 403 errors for SuperA<PERSON>mins

-- Drop the existing SELECT policy
DROP POLICY IF EXISTS "Standard: View invitations" ON public.pending_invitations;

-- Create a simpler, more direct SELECT policy
CREATE POLICY "Simple: View invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- Platform supers and admins can see everything (direct role check)
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  OR
  -- Brand admins can see their organization's invitations
  (
    (SELECT role FROM public.users WHERE id = auth.uid()) = 'brand_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships
      WHERE user_id = auth.uid()
      AND organization_id = pending_invitations.organization_id
    )
  )
  OR
  -- Users can see invitations sent to their email
  email = (SELECT email FROM auth.users WHERE id = auth.uid())
);

-- Add comment for tracking
COMMENT ON POLICY "Simple: View invitations" ON public.pending_invitations
IS 'Simplified policy: Direct role lookup for admins, then org admin check, then email match';