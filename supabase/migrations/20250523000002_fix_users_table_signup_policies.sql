-- Migration: Fix users table RLS policies for signup flow
-- Date: 2025-05-23
-- Description: Allow authenticated users to insert their own profile and read user data

-- Drop existing policies that might be causing issues
DROP POLICY IF EXISTS "Simple auth read for users" ON public.users;
DROP POLICY IF EXISTS "Users can read own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Only platform supers can delete users" ON public.users;

-- Create comprehensive RLS policies for users table
-- 1. SELECT Policy: Authenticated users can read all user profiles (needed for inviter info, etc.)
CREATE POLICY "Authenticated users can read users"
ON public.users FOR SELECT
TO authenticated
USING (true);

-- 2. INSERT Policy: Users can only insert their own profile
CREATE POLICY "Users can insert own profile"
ON public.users FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = id);

-- 3. UPDATE Policy: Users can only update their own profile
CREATE POLICY "Users can update own profile"
ON public.users FOR UPDATE
TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- 4. DELETE Policy: Only platform supers can delete users (safety measure)
CREATE POLICY "Only platform supers can delete users"
ON public.users FOR DELETE
TO authenticated
USING (
  role = 'platform_super'
);

-- Add comments explaining the policies
COMMENT ON POLICY "Authenticated users can read users" ON public.users IS 
'Allows all authenticated users to read user profiles. This is needed for displaying inviter information, member lists, etc.';

COMMENT ON POLICY "Users can insert own profile" ON public.users IS 
'Allows users to create their own profile during signup. Users can only insert records with their own auth.uid().';

COMMENT ON POLICY "Users can update own profile" ON public.users IS 
'Allows users to update only their own profile information.';

COMMENT ON POLICY "Only platform supers can delete users" ON public.users IS 
'Safety measure - only platform super administrators can delete user accounts.';