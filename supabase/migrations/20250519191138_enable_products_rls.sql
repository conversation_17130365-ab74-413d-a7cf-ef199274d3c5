-- Enable RLS on products table and create policies

-- Enable RLS on products table
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view products in collections they have access to
CREATE POLICY "Users can view products in accessible collections"
ON public.products FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM public.collections c
    WHERE c.id = products.collection_id
      AND (
        -- User is a member of the organization
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
        )
        OR
        -- User is an admin or superadmin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);

-- Policy: Users can insert products in collections they have access to
CREATE POLICY "Users can insert products in accessible collections"
ON public.products FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.collections c
    WHERE c.id = products.collection_id
      AND (
        -- User is a member of the organization
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
        )
        OR
        -- User is an admin or superadmin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);

-- Policy: Only org_admin can update products
CREATE POLICY "Organization admins can update products"
ON public.products FOR UPDATE
USING (
  EXISTS (
    SELECT 1
    FROM public.collections c
    WHERE c.id = products.collection_id
      AND (
        -- User is org_admin
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
            AND om.role = 'org_admin'
        )
        OR
        -- User is platform admin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);

-- Policy: Only org_admin can delete products
CREATE POLICY "Organization admins can delete products"
ON public.products FOR DELETE
USING (
  EXISTS (
    SELECT 1
    FROM public.collections c
    WHERE c.id = products.collection_id
      AND (
        -- User is org_admin
        EXISTS (
          SELECT 1
          FROM public.organization_memberships om
          WHERE om.organization_id = c.organization_id
            AND om.user_id = auth.uid()
            AND om.role = 'org_admin'
        )
        OR
        -- User is platform admin
        EXISTS (
          SELECT 1
          FROM public.users u
          WHERE u.id = auth.uid()
            AND u.role IN ('admin', 'superadmin')
        )
      )
  )
);