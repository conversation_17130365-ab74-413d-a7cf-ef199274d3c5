-- Fix the UPDATE policy for pending invitations using the same simplified approach
DROP POLICY IF EXISTS "Standard: Update invitations" ON public.pending_invitations;

-- Create a simpler UPDATE policy using unified role system
CREATE POLICY "Simple: Update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Platform users can update everything
  auth.is_platform_user()
  OR
  -- Organization admins can update their organization's invitations  
  (auth.is_admin_user() AND auth.is_organization_member(organization_id))
  OR
  -- Users can update invitations sent to their email (for accepting)
  email = (SELECT email FROM auth.users WHERE id = auth.uid())
)
WITH CHECK (
  -- Same permissions for updates
  auth.is_platform_user()
  OR
  (auth.is_admin_user() AND auth.is_organization_member(organization_id))
  OR
  email = (SELECT email FROM auth.users WHERE id = auth.uid())
);

-- Add comment for tracking
COMMENT ON POLICY "Simple: Update invitations" ON public.pending_invitations
IS 'Simplified UPDATE policy: Direct role lookup for admins, then org admin check, then email match';