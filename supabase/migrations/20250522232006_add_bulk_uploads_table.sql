-- Create bulk uploads tracking table for ZIP file uploads
-- This table tracks the progress and results of bulk upload operations

CREATE TABLE public.bulk_uploads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  status VARCHAR(50) NOT NULL DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed', 'cancelled')),
  total_files INTEGER NOT NULL DEFAULT 0,
  processed_files INTEGER NOT NULL DEFAULT 0,
  success_count INTEGER NOT NULL DEFAULT 0,
  error_count INTEGER NOT NULL DEFAULT 0,
  error_log JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Add indexes for performance
CREATE INDEX idx_bulk_uploads_collection_id ON public.bulk_uploads(collection_id);
CREATE INDEX idx_bulk_uploads_user_id ON public.bulk_uploads(user_id);
CREATE INDEX idx_bulk_uploads_status ON public.bulk_uploads(status);
CREATE INDEX idx_bulk_uploads_created_at ON public.bulk_uploads(created_at);

-- Add updated_at trigger
CREATE TRIGGER update_bulk_uploads_updated_at
  BEFORE UPDATE ON public.bulk_uploads
  FOR EACH ROW
  EXECUTE FUNCTION update_modified_column();

-- Enable RLS
ALTER TABLE public.bulk_uploads ENABLE ROW LEVEL SECURITY;

-- RLS Policies for bulk_uploads table

-- Users can view bulk uploads for collections they have access to
CREATE POLICY "Users can view bulk uploads for accessible collections"
ON public.bulk_uploads FOR SELECT
TO authenticated
USING (
  auth.is_platform_user()
  OR 
  auth.is_collection_accessible(collection_id)
);

-- Users can create bulk uploads for collections they can edit
CREATE POLICY "Users can create bulk uploads for editable collections"
ON public.bulk_uploads FOR INSERT
TO authenticated
WITH CHECK (
  auth.is_platform_user()
  OR
  (auth.is_admin_user() AND auth.is_collection_accessible(collection_id))
);

-- Users can update their own bulk uploads or admins can update any
CREATE POLICY "Users can update own bulk uploads"
ON public.bulk_uploads FOR UPDATE
TO authenticated
USING (
  auth.is_platform_user()
  OR
  user_id = auth.uid()
  OR
  (auth.is_admin_user() AND auth.is_collection_accessible(collection_id))
);

-- Only platform users and upload creators can delete
CREATE POLICY "Limited delete access for bulk uploads"
ON public.bulk_uploads FOR DELETE
TO authenticated
USING (
  auth.is_platform_user()
  OR
  user_id = auth.uid()
);

-- Add comments for documentation
COMMENT ON TABLE public.bulk_uploads IS 'Tracks bulk upload operations from ZIP files, including progress and error reporting';
COMMENT ON COLUMN public.bulk_uploads.status IS 'Current status: processing, completed, failed, or cancelled';
COMMENT ON COLUMN public.bulk_uploads.error_log IS 'JSON array of error objects with file and error details';

-- Add workflow_stage enum to assets table if it doesn't exist
DO $$ 
BEGIN
    -- Check if the enum type exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'workflow_stage_enum') THEN
        CREATE TYPE workflow_stage_enum AS ENUM ('input_assets', 'raw', 'upscaled', 'retouched', 'final');
    END IF;
END $$;

-- Add workflow_stage column to assets table if it doesn't exist
DO $$
BEGIN
    -- Check if the column exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assets' 
                   AND column_name = 'workflow_stage' 
                   AND table_schema = 'public') THEN
        ALTER TABLE public.assets 
        ADD COLUMN workflow_stage workflow_stage_enum DEFAULT 'input_assets';
        
        -- Add index for performance
        CREATE INDEX idx_assets_workflow_stage ON public.assets(workflow_stage);
        
        -- Add comment
        COMMENT ON COLUMN public.assets.workflow_stage IS 'Workflow stage for asset processing pipeline';
    END IF;
END $$;