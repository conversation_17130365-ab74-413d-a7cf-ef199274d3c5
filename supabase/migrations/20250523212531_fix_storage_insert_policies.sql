-- Fix storage insert policies to allow authenticated users to upload

-- Drop existing empty insert policies
DROP POLICY IF EXISTS "Allow admins to upload media originals" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload org logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow users to upload own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Avatar upload policy" ON storage.objects;

-- Create proper insert policies for media buckets
-- These allow authenticated users to upload to collection folders they have access to

CREATE POLICY "Allow media originals upload"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'media-originals' AND
    -- Allow platform users to upload anywhere
    (EXISTS (
        SELECT 1 FROM users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
    ) OR
    -- Or check if user has access to the collection
    EXISTS (
        SELECT 1 FROM collections c
        JOIN organization_memberships om ON om.organization_id = c.organization_id
        WHERE om.user_id = auth.uid()
        AND c.id = (storage.foldername(name))[2]::uuid
    ))
);

CREATE POLICY "Allow media compressed upload"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'media-compressed' AND
    -- Allow platform users to upload anywhere
    (EXISTS (
        SELECT 1 FROM users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
    ) OR
    -- Or check if user has access to the collection
    EXISTS (
        SELECT 1 FROM collections c
        JOIN organization_memberships om ON om.organization_id = c.organization_id
        WHERE om.user_id = auth.uid()
        AND c.id = (storage.foldername(name))[2]::uuid
    ))
);

CREATE POLICY "Allow media thumbnails upload"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'media-thumbnails' AND
    -- Allow platform users to upload anywhere
    (EXISTS (
        SELECT 1 FROM users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
    ) OR
    -- Or check if user has access to the collection
    EXISTS (
        SELECT 1 FROM collections c
        JOIN organization_memberships om ON om.organization_id = c.organization_id
        WHERE om.user_id = auth.uid()
        AND c.id = (storage.foldername(name))[2]::uuid
    ))
);

-- Profile uploads (avatars and org logos)
CREATE POLICY "Allow profile uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'profiles' AND
    (
        -- User avatars: users/{user_id}/avatar.*
        ((storage.foldername(name))[1] = 'users' AND (storage.foldername(name))[2] = auth.uid()::text)
        OR
        -- Organization logos: organizations/{org_id}/logo.*
        ((storage.foldername(name))[1] = 'organizations' AND EXISTS (
            SELECT 1 FROM users u
            JOIN organization_memberships om ON om.user_id = u.id
            WHERE u.id = auth.uid()
            AND (u.role IN ('platform_super', 'platform_admin', 'brand_admin'))
            AND om.organization_id = (storage.foldername(name))[2]::uuid
        ))
    )
);

-- General uploads bucket
CREATE POLICY "Allow general uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'general-uploads' AND
    -- Allow platform users to upload anywhere
    (EXISTS (
        SELECT 1 FROM users u
        WHERE u.id = auth.uid()
        AND u.role IN ('platform_super', 'platform_admin')
    ) OR
    -- Or check if user has access to the organization
    EXISTS (
        SELECT 1 FROM organization_memberships om
        WHERE om.user_id = auth.uid()
        AND om.organization_id = (storage.foldername(name))[2]::uuid
    ))
);

-- Avatar bucket (if still used)
CREATE POLICY "Allow avatar uploads"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
    bucket_id = 'avatars' AND
    (storage.foldername(name))[1] = auth.uid()::text
);