-- Create separate bucket for AI-generated images
INSERT INTO storage.buckets (id, name, public)
VALUES ('ai-generated', 'ai-generated', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for ai-generated bucket
-- Allow authenticated users to view images from their organization's collections
CREATE POLICY "Users can view AI-generated images from their collections"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'ai-generated' AND
  auth.role() = 'authenticated' AND
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON om.organization_id = c.organization_id
    WHERE om.user_id = auth.uid()
    AND (storage.foldername(name))[2] = c.id::text
  )
);

-- Allow authenticated users to upload AI-generated images to their collections
CREATE POLICY "Users can upload AI-generated images to their collections"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'ai-generated' AND
  auth.role() = 'authenticated' AND
  EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON om.organization_id = c.organization_id
    WHERE om.user_id = auth.uid()
    AND (storage.foldername(name))[2] = c.id::text
  )
);

-- Allow service role full access
CREATE POLICY "Service role has full access to AI-generated images"
ON storage.objects
USING (bucket_id = 'ai-generated' AND auth.role() = 'service_role')
WITH CHECK (bucket_id = 'ai-generated' AND auth.role() = 'service_role');

-- Create table to track AI-generated images before they're added to collections
CREATE TABLE IF NOT EXISTS public.ai_generated_images (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  queue_id TEXT NOT NULL,
  collection_id UUID NOT NULL REFERENCES public.collections(id),
  organization_id UUID NOT NULL REFERENCES public.organizations(id),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  image_url TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  prompt TEXT NOT NULL,
  metadata JSONB,
  selected BOOLEAN DEFAULT FALSE,
  selected_at TIMESTAMPTZ,
  selected_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes
CREATE INDEX idx_ai_generated_images_queue_id ON public.ai_generated_images(queue_id);
CREATE INDEX idx_ai_generated_images_collection_id ON public.ai_generated_images(collection_id);
CREATE INDEX idx_ai_generated_images_user_id ON public.ai_generated_images(user_id);
CREATE INDEX idx_ai_generated_images_selected ON public.ai_generated_images(selected) WHERE selected = true;

-- RLS policies for ai_generated_images table
ALTER TABLE public.ai_generated_images ENABLE ROW LEVEL SECURITY;

-- Users can view AI-generated images from their organization
CREATE POLICY "Users can view their organization's AI-generated images"
ON public.ai_generated_images FOR SELECT
USING (
  -- Platform admins can view all images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Organization members can view their organization's images
  organization_id IN (
    SELECT organization_id
    FROM public.organization_memberships
    WHERE user_id = auth.uid()
  )
);

-- Users can insert their own AI-generated images
CREATE POLICY "Users can insert their own AI-generated images"
ON public.ai_generated_images FOR INSERT
WITH CHECK (
  user_id = auth.uid() AND
  (
    -- Platform admins can insert into any collection
    EXISTS (
      SELECT 1
      FROM public.users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
    OR
    -- Regular users must be members of the organization
    EXISTS (
      SELECT 1
      FROM public.collections c
      JOIN public.organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = collection_id
      AND om.user_id = auth.uid()
    )
  )
);

-- Organization admins can update selection status
CREATE POLICY "Organization admins can select AI-generated images"
ON public.ai_generated_images FOR UPDATE
USING (
  -- Platform admins can update any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can update images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
)
WITH CHECK (
  -- Platform admins can update any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can update images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
);

-- Add trigger for updated_at
CREATE TRIGGER handle_ai_generated_images_updated_at BEFORE UPDATE ON public.ai_generated_images
  FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();