-- Fix permission for pending_invitations INSERT operations
-- This addresses the 403 Forbidden error when creating invitations

-- First, check if we need to re-enable <PERSON><PERSON> as it might have been accidentally left disabled
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Drop existing INSERT policy
DROP POLICY IF EXISTS "Allow organization admins to create invitations" ON public.pending_invitations;

-- Create completely new policy just for INSERT with more permissive approach
CREATE POLICY "Allow authenticated users to create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (TRUE);

-- Add helpful comment
COMMENT ON POLICY "Allow authenticated users to create invitations" ON public.pending_invitations
IS 'Temporarily allow authenticated users to create invitations to diagnose RLS issues';