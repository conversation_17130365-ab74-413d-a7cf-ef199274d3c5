-- Add DELETE policy for ai_generated_images table
-- This allows users to delete AI-generated images based on their role and organization membership

CREATE POLICY "Users can delete their own AI-generated images"
ON public.ai_generated_images FOR DELETE
USING (
  -- User who created the image can delete it
  user_id = auth.uid()
  OR
  -- Platform admins can delete any images
  EXISTS (
    SELECT 1
    FROM public.users u
    WHERE u.id = auth.uid()
    AND u.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can delete images in their organization
  EXISTS (
    SELECT 1
    FROM public.organization_memberships om
    JOIN public.users u ON u.id = om.user_id
    WHERE om.organization_id = ai_generated_images.organization_id
    AND om.user_id = auth.uid()
    AND u.role = 'brand_admin'
  )
);