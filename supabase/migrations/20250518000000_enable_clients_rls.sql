-- Enable RLS on clients table and add policies based on organization membership
-- Since clients.id = organizations.id, we need to check organization membership

-- Enable RLS on clients table
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;

-- Allow users to see clients for organizations they are members of, or all clients if admin/superadmin
CREATE POLICY "Allow users to read clients they have access to"
ON public.clients FOR SELECT
TO authenticated
USING (
    -- Check if user is a member of the organization (client.id = organization.id)
    EXISTS (
        SELECT 1 FROM public.organization_memberships
        WHERE organization_id = clients.id
        AND user_id = auth.uid()
    )
    OR
    -- Or if user is admin/superadmin
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Allow authenticated users with admin or superadmin role to insert clients
CREATE POLICY "Allow admins to create clients"
ON public.clients FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Allow authenticated users with admin or superadmin role to update clients
CREATE POLICY "Allow admins to update clients"
ON public.clients FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Allow authenticated users with admin or superadmin role to delete clients
CREATE POLICY "Allow admins to delete clients"
ON public.clients FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);