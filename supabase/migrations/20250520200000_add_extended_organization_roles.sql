-- Add new roles to organization_role enum
ALTER TYPE public.organization_role ADD VALUE IF NOT EXISTS 'org_retoucher';
ALTER TYPE public.organization_role ADD VALUE IF NOT EXISTS 'org_prompter';

-- Create pending_invitations table
CREATE TABLE IF NOT EXISTS public.pending_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
  role public.organization_role NOT NULL DEFAULT 'org_member',
  invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  invited_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  expires_at TIMESTAMPTZ NOT NULL DEFAULT (now() + interval '48 hours'),
  token TEXT NOT NULL UNIQUE,
  accepted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_pending_invitations_email ON public.pending_invitations(email);
CREATE INDEX IF NOT EXISTS idx_pending_invitations_organization_id ON public.pending_invitations(organization_id);
CREATE INDEX IF NOT EXISTS idx_pending_invitations_token ON public.pending_invitations(token);
CREATE INDEX IF NOT EXISTS idx_pending_invitations_expires_at ON public.pending_invitations(expires_at);

-- Enable RLS on the pending_invitations table
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for pending_invitations

-- Organization admins can insert invitations for their organization
CREATE POLICY "Organization admins can create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
);

-- Users can see pending invitations for organizations they admin
CREATE POLICY "Users can view pending invitations for their organizations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR
  -- Users can see their own invitations by email
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- Organization admins can update invitations for their organization
CREATE POLICY "Organization admins can update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
);

-- Organization admins can delete invitations for their organization
CREATE POLICY "Organization admins can delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
);

-- Add trigger to update updated_at column
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for pending_invitations table
DROP TRIGGER IF EXISTS set_pending_invitations_updated_at ON public.pending_invitations;
CREATE TRIGGER set_pending_invitations_updated_at
BEFORE UPDATE ON public.pending_invitations
FOR EACH ROW
EXECUTE FUNCTION public.handle_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.pending_invitations IS 'Tracks pending invitations to organizations';
COMMENT ON COLUMN public.pending_invitations.email IS 'Email address of the invited user';
COMMENT ON COLUMN public.pending_invitations.organization_id IS 'Organization the user is invited to';
COMMENT ON COLUMN public.pending_invitations.role IS 'Role the user will receive upon accepting';
COMMENT ON COLUMN public.pending_invitations.invited_by IS 'User who created the invitation';
COMMENT ON COLUMN public.pending_invitations.invited_at IS 'When the invitation was created';
COMMENT ON COLUMN public.pending_invitations.expires_at IS 'When the invitation expires (48 hours after creation)';
COMMENT ON COLUMN public.pending_invitations.token IS 'Unique token for invitation acceptance';
COMMENT ON COLUMN public.pending_invitations.accepted IS 'Whether the invitation has been accepted'; 