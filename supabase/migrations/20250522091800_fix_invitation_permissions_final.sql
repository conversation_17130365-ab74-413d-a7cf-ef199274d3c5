-- Fix invitation permissions by using correct user role checking
-- The issue is that policies were checking auth.users.raw_app_meta_data for roles,
-- but our system uses the users.role column instead

-- Drop all existing pending_invitations policies
DROP POLICY IF EXISTS "Allow admins to delete invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow admins to update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow all authenticated users to view pending invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow organization admins to delete invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow organization admins to update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Debug allow all users to create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Users can update their own invitations" ON public.pending_invitations;

-- CREATE CLEAN, CONSISTENT POLICIES

-- INSERT: Only org admins and global admins can create invitations
CREATE POLICY "Admins can create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins (using users.role column)
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
);

-- SELECT: Organization members can view their org's invitations, users can view invitations sent to their email
CREATE POLICY "Users can view relevant invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- Organization members can see their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins can see all invitations
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Users can see invitations sent to their email
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- UPDATE: Org admins can update invitations, users can update their own (for accepting)
CREATE POLICY "Admins and invitees can update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Users can update invitations sent to their email (for accepting)
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
)
WITH CHECK (
  -- Same conditions for WITH CHECK
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- DELETE: Only org admins and global admins can delete invitations
CREATE POLICY "Admins can delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
);