-- Drop and recreate the update policy with WITH CHECK clause
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;

CREATE POLICY "Organization admins can update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Platform admin check using new role system
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  -- OR brand admin check
  OR (
    (SELECT role FROM public.users WHERE id = auth.uid()) = 'brand_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships
      WHERE user_id = auth.uid()
      AND organization_id = pending_invitations.organization_id
    )
  )
)
WITH CHECK (
  -- Platform admin check using new role system
  (SELECT role FROM public.users WHERE id = auth.uid()) IN ('platform_super', 'platform_admin')
  -- OR brand admin check
  OR (
    (SELECT role FROM public.users WHERE id = auth.uid()) = 'brand_admin'
    AND EXISTS (
      SELECT 1 FROM public.organization_memberships
      WHERE user_id = auth.uid()
      AND organization_id = pending_invitations.organization_id
    )
  )
);

-- Add policy for users to update their own invitations (for accepting them)
CREATE POLICY "Users can update their own invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
)
WITH CHECK (
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);