-- Fix the infinitely recursive policy issue
-- First, drop only the problematic recursive policy
DROP POLICY IF EXISTS "Allow org admins to read their organization memberships" ON organization_memberships;

-- We're not creating any new policies in this migration to avoid conflicts
-- The existing "Allow users to read their own memberships" policy should be sufficient
-- along with the "Allow admins to read all memberships" policy for basic functionality
-- We'll implement a better org_admin policy in a later migration if needed
