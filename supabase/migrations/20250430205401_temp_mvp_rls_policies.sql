-- Migration to add TEMPORARY permissive RLS policies for UI MVP Demo (Corrected)
-- WARNING: These policies are NOT secure for production.
-- Proper, fine-grained RLS must be implemented post-MVP.

-- Enable RLS if not already enabled (idempotent)
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;

-- Drop potentially existing policies we are defining (idempotent)
DROP POLICY IF EXISTS "Allow users to read their own memberships" ON public.organization_memberships;
DROP POLICY IF EXISTS "TEMP_MVP_Allow authenticated read access to organizations" ON public.organizations;
DROP POLICY IF EXISTS "Allow users to read their own user record" ON public.users;
DROP POLICY IF EXISTS "Allow authenticated users to read all user records" ON public.users;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read assets in their orgs" ON public.assets;
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to read comments for accessible assets" ON public.comments;
DROP POLICY IF EXISTS "Users can insert their own comments" ON public.comments;
DROP POLICY IF EXISTS "Allow members to read their collections" ON public.collections;


-- POLICY 1: Allow logged-in users to read their OWN memberships.
CREATE POLICY "Allow users to read their own memberships"
ON public.organization_memberships FOR SELECT
USING (auth.uid() = user_id);

-- POLICY 2: TEMP MVP - Allow logged-in users to read ALL organizations (needed for name join).
CREATE POLICY "TEMP_MVP_Allow authenticated read access to organizations"
ON public.organizations FOR SELECT
TO authenticated
USING (true);

-- POLICY 3: Allow users to read their own user record in public.users
CREATE POLICY "Allow users to read their own user record"
ON public.users FOR SELECT
USING (auth.uid() = id);

-- POLICY 4: TEMP MVP - Allow logged-in users to read ALL user records.
CREATE POLICY "Allow authenticated users to read all user records"
ON public.users FOR SELECT
TO authenticated
USING (true);

-- POLICY 5: TEMP MVP - Allow users to SELECT assets belonging to collections linked via client_id to organizations they are members of.
CREATE POLICY "TEMP_MVP_Allow members to read assets in their orgs"
ON public.assets FOR SELECT
USING (
   EXISTS (
     SELECT 1
     FROM public.collections c
     JOIN public.organization_memberships om ON c.client_id = om.organization_id -- Join via client_id->org_id
     WHERE c.id = assets.collection_id AND om.user_id = auth.uid()
   )
);

-- POLICY 6: TEMP MVP - Allow users to SELECT comments for assets they can see (using the corrected asset visibility logic).
CREATE POLICY "TEMP_MVP_Allow members to read comments for accessible assets"
ON public.comments FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM public.assets a
    WHERE a.id = comments.asset_id
      -- Check if the user can see the asset based on Policy 5 logic
      AND EXISTS (
         SELECT 1
         FROM public.collections c
         JOIN public.organization_memberships om ON c.client_id = om.organization_id -- Join via client_id->org_id
         WHERE c.id = a.collection_id AND om.user_id = auth.uid()
       )
  )
);

-- POLICY 7: Allow logged-in users to INSERT their own comments
CREATE POLICY "Users can insert their own comments"
ON public.comments FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- POLICY 8: TEMP MVP - Allow authenticated users to SELECT collections linked via client_id to organizations they are members of
CREATE POLICY "Allow members to read their collections"
ON public.collections FOR SELECT
USING (
  EXISTS (
     SELECT 1
     FROM public.organization_memberships om
     WHERE om.organization_id = collections.client_id -- Join via client_id->org_id
       AND om.user_id = auth.uid()
   )
);

-- POLICY 9: TEMP MVP - Allow users to INSERT collections if they are members of the org (linked via client_id)
-- WARNING: This doesn't check for 'org_admin' role, which would be needed in production.
DROP POLICY IF EXISTS "TEMP_MVP_Allow members to insert collections in their orgs" ON public.collections;

CREATE POLICY "TEMP_MVP_Allow members to insert collections in their orgs"
ON public.collections FOR INSERT TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1
        FROM public.organization_memberships om
        WHERE om.organization_id = collections.client_id -- Check based on the client_id holding the orgId
          AND om.user_id = auth.uid()
    )
);

-- Drop policies for client-assets if they exist
DROP POLICY IF EXISTS "Allow authenticated insert on client-assets" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated select on client-assets" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated update on own client-assets" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated delete on own client-assets" ON storage.objects;

-- Create permissive INSERT policy for client-assets bucket
CREATE POLICY "Allow authenticated insert on client-assets"
ON storage.objects FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'client-assets');

-- Create permissive SELECT policy for client-assets bucket
CREATE POLICY "Allow authenticated select on client-assets"
ON storage.objects FOR SELECT TO authenticated
USING (bucket_id = 'client-assets');

-- Create UPDATE policy for client-assets bucket (allow updating own objects)
CREATE POLICY "Allow authenticated update on own client-assets"
ON storage.objects FOR UPDATE TO authenticated
USING (bucket_id = 'client-assets' AND auth.uid() = owner);

-- Create DELETE policy for client-assets bucket (allow deleting own objects)
CREATE POLICY "Allow authenticated delete on own client-assets"
ON storage.objects FOR DELETE TO authenticated
USING (bucket_id = 'client-assets' AND auth.uid() = owner);