-- Create comment_mentions table that was missing from the original migration
-- This uses organization_id since it runs after the merge migration

-- Create comment_mentions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.comment_mentions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    mentioned_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on mentions table
ALTER TABLE public.comment_mentions ENABLE ROW LEVEL SECURITY;

-- Drop and recreate mention policies using organization_id
DROP POLICY IF EXISTS "Users can view mentions on accessible comments" ON public.comment_mentions;
CREATE POLICY "Users can view mentions on accessible comments"
ON public.comment_mentions FOR SELECT
USING (
  EXISTS (
    SELECT 1
    FROM public.comments c
    WHERE c.id = comment_mentions.comment_id
      AND EXISTS (
        SELECT 1
        FROM public.assets a
        JOIN public.collections col ON a.collection_id = col.id
        WHERE a.id = c.asset_id
          AND (
            EXISTS (
              SELECT 1
              FROM public.organization_memberships om
              WHERE om.organization_id = col.organization_id
                AND om.user_id = auth.uid()
            )
            OR
            EXISTS (
              SELECT 1
              FROM public.users u
              WHERE u.id = auth.uid()
                AND u.role IN ('admin', 'superadmin')
            )
          )
      )
  )
);

-- Drop and recreate insert policy
DROP POLICY IF EXISTS "Users can create mentions with their comments" ON public.comment_mentions;
CREATE POLICY "Users can create mentions with their comments"
ON public.comment_mentions FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM public.comments c
    WHERE c.id = comment_mentions.comment_id
      AND c.user_id = auth.uid()
  )
);

-- Create indices for performance
CREATE INDEX IF NOT EXISTS idx_comment_mentions_comment_id ON public.comment_mentions(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_mentions_user_id ON public.comment_mentions(mentioned_user_id);