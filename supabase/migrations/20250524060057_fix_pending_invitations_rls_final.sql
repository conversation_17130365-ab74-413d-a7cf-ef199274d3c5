-- Fix pending_invitations RLS policies with simplified approach
-- First, re-enable RLS
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "can_view_invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "can_create_invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "can_update_invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "can_delete_invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Simple: View invitations" ON public.pending_invitations;

-- Create simplified policies that avoid complex joins

-- 1. SELECT: Allow platform users to see all, others see their own org
CREATE POLICY "view_invitations" ON public.pending_invitations
FOR SELECT
USING (
  -- Platform users can see all invitations
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Organization admins can see their org's invitations
  (
    pending_invitations.organization_id IN (
      SELECT om.organization_id 
      FROM public.organization_memberships om
      JOIN public.users u ON u.id = om.user_id
      WHERE om.user_id = auth.uid()
      AND u.role = 'brand_admin'
    )
  )
  OR
  -- Users can see invitations for their email
  (
    auth.uid() IS NOT NULL 
    AND pending_invitations.email = (
      SELECT email FROM auth.users WHERE id = auth.uid()
    )
  )
  OR
  -- Allow unauthenticated access by token
  (auth.uid() IS NULL AND token IS NOT NULL)
);

-- 2. INSERT: Only admins can create
CREATE POLICY "create_invitations" ON public.pending_invitations
FOR INSERT
WITH CHECK (
  -- Platform users can create any invitation
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can create for their orgs
  (
    pending_invitations.organization_id IN (
      SELECT om.organization_id 
      FROM public.organization_memberships om
      JOIN public.users u ON u.id = om.user_id
      WHERE om.user_id = auth.uid()
      AND u.role = 'brand_admin'
    )
  )
);

-- 3. UPDATE: Admins and invitees
CREATE POLICY "update_invitations" ON public.pending_invitations
FOR UPDATE
USING (
  -- Platform users can update any
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can update their org's invitations
  (
    pending_invitations.organization_id IN (
      SELECT om.organization_id 
      FROM public.organization_memberships om
      JOIN public.users u ON u.id = om.user_id
      WHERE om.user_id = auth.uid()
      AND u.role = 'brand_admin'
    )
  )
  OR
  -- Users can update invitations for their email
  (
    auth.uid() IS NOT NULL 
    AND pending_invitations.email = (
      SELECT email FROM auth.users WHERE id = auth.uid()
    )
  )
);

-- 4. DELETE: Only admins
CREATE POLICY "delete_invitations" ON public.pending_invitations
FOR DELETE
USING (
  -- Platform users can delete any
  EXISTS (
    SELECT 1 FROM public.users
    WHERE users.id = auth.uid()
    AND users.role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Brand admins can delete their org's invitations
  (
    pending_invitations.organization_id IN (
      SELECT om.organization_id 
      FROM public.organization_memberships om
      JOIN public.users u ON u.id = om.user_id
      WHERE om.user_id = auth.uid()
      AND u.role = 'brand_admin'
    )
  )
);

-- Update table comment
COMMENT ON TABLE public.pending_invitations IS 'Stores pending invitations with RLS enabled';