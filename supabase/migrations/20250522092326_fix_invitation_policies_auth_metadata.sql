-- Fix invitation permissions using auth metadata (the correct approach)
-- The system is designed to use auth.users.raw_app_meta_data, not public.users.role

-- Drop the problematic policies from the previous migration
DROP POLICY IF EXISTS "Admins can create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Users can view relevant invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Admins and invitees can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Ad<PERSON> can delete invitations" ON public.pending_invitations;

-- CREATE CORRECT POLICIES USING AUTH METADATA

-- INSERT: Only org admins and global admins can create invitations
CREATE POLICY "Admins can create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins (using auth metadata)
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
);

-- SELECT: Organization admins can view their org's invitations, global admins can view all, users can view invitations sent to their email
CREATE POLICY "Users can view relevant invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- Organization admins can see their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins can see all invitations
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR
  -- Users can see invitations sent to their email
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- UPDATE: Org admins can update invitations, global admins can update all, users can update their own (for accepting)
CREATE POLICY "Admins and invitees can update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR
  -- Users can update invitations sent to their email (for accepting)
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
)
WITH CHECK (
  -- Same conditions for WITH CHECK
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- DELETE: Only org admins and global admins can delete invitations
CREATE POLICY "Admins can delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  -- Organization admins for this organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR 
  -- Global admins
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
);