-- Migration: Add sizes field to products table for Asset Compare View
-- Feature: FAS-79 - Asset Compare View Phase 1
-- Date: 2025-01-07

-- Add sizes field to products table
-- This field will store an array of size strings (e.g., ["XS", "S", "M", "L", "XL"])
-- Used for filtering products in the Asset Compare View

ALTER TABLE public.products 
ADD COLUMN IF NOT EXISTS sizes JSONB DEFAULT '[]'::jsonb;

-- Create GIN index for efficient size filtering
-- This allows fast queries like: WHERE sizes @> '["M"]'
CREATE INDEX IF NOT EXISTS idx_products_sizes ON public.products USING GIN (sizes);

-- Add comment for documentation
COMMENT ON COLUMN public.products.sizes IS 'Array of size strings for product filtering in Asset Compare View (e.g., ["XS", "S", "M", "L", "XL"])';

-- Update the updated_at trigger to include the new column
-- (The trigger should already exist from the base table creation)

-- Example data for testing (commented out for production)
-- UPDATE public.products SET sizes = '["S", "M", "L"]'::jsonb WHERE sku LIKE 'TEST-%';
