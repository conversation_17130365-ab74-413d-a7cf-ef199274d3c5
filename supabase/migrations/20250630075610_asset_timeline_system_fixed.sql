-- Migration: Asset Timeline System
-- Description: Implements asset lineage tracking and variant grouping for timeline views

-- 1. Create asset variant groups table
CREATE TABLE IF NOT EXISTS asset_variant_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  collection_id UUID NOT NULL REFERENCES collections(id) ON DELETE CASCADE,
  product_id UUID REFERENCES products(id) ON DELETE SET NULL,
  size TEXT,
  view_type TEXT,
  group_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Unique constraint for grouping logic
  CONSTRAINT unique_variant_group UNIQUE(collection_id, product_id, size, view_type)
);

-- 2. Add variant group reference to assets table
ALTER TABLE assets 
ADD COLUMN IF NOT EXISTS variant_group_id UUID REFERENCES asset_variant_groups(id) ON DELETE SET NULL;

-- 3. Create asset lineage table
CREATE TABLE IF NOT EXISTS asset_lineage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lineage_id UUID NOT NULL,
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  parent_asset_id UUID REFERENCES assets(id) ON DELETE SET NULL,
  workflow_stage workflow_stage NOT NULL,
  lineage_metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique asset per lineage
  CONSTRAINT unique_asset_lineage UNIQUE(lineage_id, asset_id),
  
  -- Ensure parent is in same lineage (will be checked via trigger)
  CONSTRAINT check_valid_parent CHECK (
    parent_asset_id IS NULL OR lineage_id IS NOT NULL
  )
);

-- 4. Create timeline links table for retroactive grouping
CREATE TABLE IF NOT EXISTS asset_timeline_links (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  primary_lineage_id UUID NOT NULL,
  linked_lineage_id UUID NOT NULL,
  link_type TEXT NOT NULL CHECK (link_type IN ('retroactive_grouping', 'reference', 'derivative')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  created_by UUID REFERENCES users(id),
  
  -- Prevent duplicate links
  CONSTRAINT unique_timeline_link UNIQUE(primary_lineage_id, linked_lineage_id)
);

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_asset_lineage_lookup ON asset_lineage(lineage_id, created_at);
CREATE INDEX IF NOT EXISTS idx_asset_lineage_asset ON asset_lineage(asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_lineage_parent ON asset_lineage(parent_asset_id);
CREATE INDEX IF NOT EXISTS idx_asset_variant_group ON assets(variant_group_id);
CREATE INDEX IF NOT EXISTS idx_variant_group_lookup ON asset_variant_groups(collection_id, product_id, size, view_type);

-- 6. Create function to validate lineage consistency
CREATE OR REPLACE FUNCTION validate_lineage_parent()
RETURNS TRIGGER AS $$
BEGIN
  -- Skip validation if no parent
  IF NEW.parent_asset_id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Check if parent exists in same lineage
  IF NOT EXISTS (
    SELECT 1 FROM asset_lineage 
    WHERE asset_id = NEW.parent_asset_id 
    AND lineage_id = NEW.lineage_id
  ) THEN
    RAISE EXCEPTION 'Parent asset must be in the same lineage';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. Create trigger for lineage validation
DROP TRIGGER IF EXISTS validate_lineage_parent_trigger ON asset_lineage;
CREATE TRIGGER validate_lineage_parent_trigger
  BEFORE INSERT OR UPDATE ON asset_lineage
  FOR EACH ROW
  EXECUTE FUNCTION validate_lineage_parent();

-- 8. Create function to automatically create variant groups
CREATE OR REPLACE FUNCTION auto_create_variant_group()
RETURNS TRIGGER AS $$
DECLARE
  v_size TEXT;
  v_view_type TEXT;
  v_variant_group_id UUID;
BEGIN
  -- Extract size and view_type from metadata if present
  v_size := NEW.metadata->>'size';
  v_view_type := NEW.metadata->>'view_type';
  
  -- Only create variant group if we have the necessary data
  IF NEW.product_id IS NOT NULL OR v_size IS NOT NULL OR v_view_type IS NOT NULL THEN
    -- Try to find existing variant group
    SELECT id INTO v_variant_group_id
    FROM asset_variant_groups
    WHERE collection_id = NEW.collection_id
      AND (product_id = NEW.product_id OR (product_id IS NULL AND NEW.product_id IS NULL))
      AND (size = v_size OR (size IS NULL AND v_size IS NULL))
      AND (view_type = v_view_type OR (view_type IS NULL AND v_view_type IS NULL));
    
    -- Create new variant group if not found
    IF v_variant_group_id IS NULL THEN
      INSERT INTO asset_variant_groups (collection_id, product_id, size, view_type)
      VALUES (NEW.collection_id, NEW.product_id, v_size, v_view_type)
      RETURNING id INTO v_variant_group_id;
    END IF;
    
    -- Set the variant group ID
    NEW.variant_group_id := v_variant_group_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Create trigger for auto variant grouping
DROP TRIGGER IF EXISTS auto_create_variant_group_trigger ON assets;
CREATE TRIGGER auto_create_variant_group_trigger
  BEFORE INSERT OR UPDATE ON assets
  FOR EACH ROW
  EXECUTE FUNCTION auto_create_variant_group();

-- 10. Create view for timeline comments
CREATE OR REPLACE VIEW timeline_comments AS
SELECT 
  c.*,
  l.lineage_id,
  a.workflow_stage,
  u.display_name as author_name,
  u.avatar_url as author_avatar,
  a.variant_group_id,
  vg.product_id,
  vg.size,
  vg.view_type
FROM comments c
JOIN assets a ON c.asset_id = a.id
LEFT JOIN asset_lineage l ON a.id = l.asset_id
JOIN users u ON c.user_id = u.id
LEFT JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
ORDER BY c.created_at;

-- 11. Create view for timeline overview
CREATE OR REPLACE VIEW timeline_overview AS
WITH asset_counts AS (
  SELECT 
    l.lineage_id,
    COUNT(DISTINCT l.asset_id) as total_assets,
    COUNT(DISTINCT a.workflow_stage) as total_stages,
    MIN(a.created_at) as timeline_started,
    MAX(a.created_at) as timeline_updated
  FROM asset_lineage l
  JOIN assets a ON l.asset_id = a.id
  GROUP BY l.lineage_id
),
comment_counts AS (
  SELECT 
    l.lineage_id,
    COUNT(DISTINCT c.id) as total_comments,
    COUNT(DISTINCT CASE WHEN c.status = 'open' THEN c.id END) as open_comments
  FROM asset_lineage l
  JOIN comments c ON l.asset_id = c.asset_id
  GROUP BY l.lineage_id
)
SELECT 
  ac.lineage_id,
  ac.total_assets,
  ac.total_stages,
  ac.timeline_started,
  ac.timeline_updated,
  COALESCE(cc.total_comments, 0) as total_comments,
  COALESCE(cc.open_comments, 0) as open_comments,
  vg.product_id,
  vg.size,
  vg.view_type,
  p.name as product_name,
  col.id as collection_id,
  col.name as collection_name
FROM asset_counts ac
LEFT JOIN comment_counts cc ON ac.lineage_id = cc.lineage_id
LEFT JOIN asset_lineage l ON ac.lineage_id = l.lineage_id
LEFT JOIN assets a ON l.asset_id = a.id
LEFT JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
LEFT JOIN products p ON vg.product_id = p.id
LEFT JOIN collections col ON a.collection_id = col.id
WHERE l.id = (
  SELECT id FROM asset_lineage 
  WHERE lineage_id = ac.lineage_id 
  LIMIT 1
);

-- 12. Add RLS policies for new tables

-- Enable RLS
ALTER TABLE asset_variant_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_lineage ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_timeline_links ENABLE ROW LEVEL SECURITY;

-- Variant groups policies (inherit from collection permissions)
CREATE POLICY "Users can view variant groups in their collections"
  ON asset_variant_groups FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM collections c
      WHERE c.id = collection_id
      AND (
        (SELECT auth.is_platform_user())
        OR
        c.organization_id IN (
          SELECT organization_id FROM organization_memberships
          WHERE user_id = auth.uid()
        )
      )
    )
  );

CREATE POLICY "Users can create variant groups in their collections"
  ON asset_variant_groups FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM collections c
      WHERE c.id = collection_id
      AND (
        (SELECT auth.is_platform_user())
        OR
        (
          c.organization_id IN (
            SELECT organization_id FROM organization_memberships
            WHERE user_id = auth.uid()
          )
          AND
          (SELECT role FROM users WHERE id = auth.uid()) IN ('brand_admin', 'brand_member')
        )
      )
    )
  );

-- Asset lineage policies (inherit from asset permissions)
CREATE POLICY "Users can view lineage for accessible assets"
  ON asset_lineage FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM assets a
      JOIN collections c ON a.collection_id = c.id
      WHERE a.id = asset_id
      AND (
        (SELECT auth.is_platform_user())
        OR
        c.organization_id IN (
          SELECT organization_id FROM organization_memberships
          WHERE user_id = auth.uid()
        )
      )
    )
  );

CREATE POLICY "Users can create lineage for their assets"
  ON asset_lineage FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM assets a
      JOIN collections c ON a.collection_id = c.id
      WHERE a.id = asset_id
      AND (
        (SELECT auth.is_platform_user())
        OR
        (
          c.organization_id IN (
            SELECT organization_id FROM organization_memberships
            WHERE user_id = auth.uid()
          )
          AND
          (SELECT role FROM users WHERE id = auth.uid()) 
          IN ('brand_admin', 'brand_member', 'external_retoucher', 'external_prompter')
        )
      )
    )
  );

-- Timeline links policies
CREATE POLICY "Users can view timeline links for their assets"
  ON asset_timeline_links FOR SELECT
  USING (
    (SELECT auth.is_platform_user())
    OR
    EXISTS (
      SELECT 1 FROM asset_lineage al
      JOIN assets a ON al.asset_id = a.id
      JOIN collections c ON a.collection_id = c.id
      WHERE (al.lineage_id = primary_lineage_id OR al.lineage_id = linked_lineage_id)
      AND c.organization_id IN (
        SELECT organization_id FROM organization_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Admins can create timeline links"
  ON asset_timeline_links FOR INSERT
  WITH CHECK (
    (SELECT auth.is_platform_user())
    OR
    (SELECT role FROM users WHERE id = auth.uid()) = 'brand_admin'
  );

-- 13. Create helper function to get full timeline
CREATE OR REPLACE FUNCTION get_asset_timeline(
  p_collection_id UUID,
  p_product_id UUID DEFAULT NULL,
  p_size TEXT DEFAULT NULL,
  p_view_type TEXT DEFAULT NULL
)
RETURNS TABLE (
  lineage_id UUID,
  asset_id UUID,
  parent_asset_id UUID,
  workflow_stage workflow_stage,
  file_path TEXT,
  thumbnail_path TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  created_by_id UUID,
  created_by_name TEXT,
  comment_count BIGINT,
  variant_group_id UUID,
  product_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  WITH filtered_lineages AS (
    SELECT DISTINCT l.lineage_id
    FROM asset_lineage l
    JOIN assets a ON l.asset_id = a.id
    LEFT JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
    WHERE a.collection_id = p_collection_id
      AND (p_product_id IS NULL OR vg.product_id = p_product_id)
      AND (p_size IS NULL OR vg.size = p_size)
      AND (p_view_type IS NULL OR vg.view_type = p_view_type)
  )
  SELECT 
    l.lineage_id,
    l.asset_id,
    l.parent_asset_id,
    a.workflow_stage,
    a.file_path,
    a.thumbnail_path,
    a.metadata,
    a.created_at,
    a.created_by,
    u.display_name,
    COUNT(c.id),
    a.variant_group_id,
    p.name
  FROM asset_lineage l
  JOIN assets a ON l.asset_id = a.id
  LEFT JOIN users u ON a.created_by = u.id
  LEFT JOIN comments c ON a.id = c.asset_id
  LEFT JOIN asset_variant_groups vg ON a.variant_group_id = vg.id
  LEFT JOIN products p ON vg.product_id = p.id
  WHERE l.lineage_id IN (SELECT lineage_id FROM filtered_lineages)
  GROUP BY 
    l.lineage_id, l.asset_id, l.parent_asset_id,
    a.workflow_stage, a.file_path, a.thumbnail_path,
    a.metadata, a.created_at, a.created_by,
    u.display_name, a.variant_group_id, p.name
  ORDER BY l.lineage_id, a.workflow_stage, a.created_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_asset_timeline TO authenticated;

-- 14. Update existing assets to create lineage records (one-time migration)
DO $$
BEGIN
  -- Only run if asset_lineage table is empty
  IF NOT EXISTS (SELECT 1 FROM asset_lineage LIMIT 1) THEN
    -- Create lineage entries for existing assets
    INSERT INTO asset_lineage (lineage_id, asset_id, parent_asset_id, workflow_stage)
    SELECT 
      gen_random_uuid() as lineage_id, -- Each asset gets its own lineage for now
      id as asset_id,
      NULL as parent_asset_id, -- No parent relationships yet
      workflow_stage
    FROM assets
    ON CONFLICT (lineage_id, asset_id) DO NOTHING;
  END IF;
END $$;