-- Fix storage policies to work with single role system
-- The optimized storage policies were using old helper functions that no longer exist

-- First, drop the existing policies that use the old functions
DROP POLICY IF EXISTS "Allow admins to upload org logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to update org logos" ON storage.objects;
DROP POLICY IF EXISTS "Allow members to view media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow members to view media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow members to view media originals" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media originals" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to update media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to update media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to update media originals" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to delete media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to delete media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to delete media originals" ON storage.objects;

-- Drop the old function that uses auth.is_platform_user() with CASCADE to handle dependencies
DROP FUNCTION IF EXISTS auth.is_collection_accessible(uuid) CASCADE;

-- Create new helper function using single role system
CREATE OR REPLACE FUNCTION auth.is_collection_accessible(collection_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM collections c
    WHERE c.id = collection_id
    AND (
      -- Platform users can access all
      auth.uid() IN (
        SELECT id FROM users 
        WHERE role IN ('platform_super', 'platform_admin')
      )
      OR
      -- Organization members can access their org's collections
      c.organization_id IN (
        SELECT organization_id 
        FROM organization_memberships 
        WHERE user_id = auth.uid()
      )
    )
  );
$$;

-- Recreate policies for profiles bucket using single role system
-- Allow organization admins to upload org logos
CREATE POLICY "Allow admins to upload org logos" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'profiles' 
    AND (storage.foldername(name))[1] = 'organizations'
    AND EXISTS (
      SELECT 1 FROM users u
      JOIN organization_memberships om ON om.user_id = u.id
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin', 'brand_admin')
      AND om.organization_id = (storage.foldername(name))[2]::uuid
    )
  );

-- Allow organization admins to update org logos
CREATE POLICY "Allow admins to update org logos" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'profiles' 
    AND (storage.foldername(name))[1] = 'organizations'
    AND EXISTS (
      SELECT 1 FROM users u
      JOIN organization_memberships om ON om.user_id = u.id
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin', 'brand_admin')
      AND om.organization_id = (storage.foldername(name))[2]::uuid
    )
  );

-- Recreate policies for media buckets using new helper function
-- Allow organization members to view media assets
CREATE POLICY "Allow members to view media thumbnails" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-thumbnails'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow members to view media compressed" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-compressed'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

CREATE POLICY "Allow members to view media originals" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'media-originals'
    AND auth.is_collection_accessible((storage.foldername(name))[2]::uuid)
  );

-- Allow admins to upload media assets
CREATE POLICY "Allow admins to upload media thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to upload media compressed" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to upload media originals" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

-- Allow admins to update media assets
CREATE POLICY "Allow admins to update media thumbnails" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to update media compressed" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to update media originals" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

-- Allow admins to delete media assets
CREATE POLICY "Allow admins to delete media thumbnails" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to delete media compressed" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );

CREATE POLICY "Allow admins to delete media originals" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN users u ON u.id = auth.uid()
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND (
        u.role IN ('platform_super', 'platform_admin')
        OR EXISTS (
          SELECT 1 FROM organization_memberships om
          WHERE om.user_id = u.id
          AND om.organization_id = c.organization_id
          AND u.role = 'brand_admin'
        )
      )
    )
  );