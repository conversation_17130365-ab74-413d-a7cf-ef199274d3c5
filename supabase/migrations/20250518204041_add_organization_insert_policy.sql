-- Add policy to allow admin and superadmin users to create organizations

-- First, check if the policy already exists and drop it if so
DROP POLICY IF EXISTS "Admins can create organizations" ON public.organizations;

-- Create policy allowing admin and superadmin users to insert organizations
CREATE POLICY "Admins can create organizations"
ON public.organizations FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Also add update and delete policies if they don't exist
DROP POLICY IF EXISTS "Admins can update organizations" ON public.organizations;
DROP POLICY IF EXISTS "Admins can delete organizations" ON public.organizations;

-- Allow admins to update organizations
CREATE POLICY "Admins can update organizations"
ON public.organizations FOR UPDATE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
)
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Allow admins to delete organizations
CREATE POLICY "Admins can delete organizations"
ON public.organizations FOR DELETE
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);

-- Also add a read policy for organizations if it doesn't exist
DROP POLICY IF EXISTS "Users can read organizations they are members of" ON public.organizations;

-- Allow users to see organizations they are members of
CREATE POLICY "Users can read organizations they are members of"
ON public.organizations FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.organization_memberships
        WHERE organization_id = organizations.id
        AND user_id = auth.uid()
    )
    OR
    EXISTS (
        SELECT 1 FROM public.users
        WHERE id = auth.uid()
        AND role IN ('admin', 'superadmin')
    )
);