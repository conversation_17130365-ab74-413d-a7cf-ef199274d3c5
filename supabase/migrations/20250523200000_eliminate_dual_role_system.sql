-- Eliminate Dual Role System - Use public.users.role as Single Source of Truth
-- This migration removes the bidirectional sync between public.users.role and auth.users.raw_app_meta_data
-- and standardizes ALL RLS policies to use public.users.role consistently
-- NOTE: This migration is superseded by the single role system migration and is now a no-op

-- Exit early as this is superseded by newer single role system migrations
-- The rest of this migration is commented out since it's superseded by the single role system migration

/*
-- ====================================================================
-- PHASE 1: Remove Bidirectional Sync System
-- ====================================================================

-- Drop the sync triggers first
DROP TRIGGER IF EXISTS sync_admin_auth_metadata_trigger ON public.users;
DROP TRIGGER IF EXISTS sync_admin_auth_metadata_insert_trigger ON public.users;

-- Drop the sync function
DROP FUNCTION IF EXISTS sync_admin_auth_metadata();

-- ====================================================================
-- PHASE 2: Clean Up Duplicate/Conflicting Invitation Policies  
-- ====================================================================

-- Remove ALL existing invitation policies to start clean
DROP POLICY IF EXISTS "Organization admins can create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Users can view pending invitations for their organizations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can delete invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow admins to update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Allow admins to delete invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Debug allow all users to create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Users can update their own invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Admins and org members can view invitations" ON public.pending_invitations;

-- ====================================================================
-- PHASE 3: Create New Standardized Invitation Policies (public.users.role ONLY)
-- ====================================================================

-- SELECT Policy: Admins, org admins, and invitees can view invitations
CREATE POLICY "Standard: View invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (
  -- Platform admins can see all invitations (using new role system)
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('platform_super', 'platform_admin')
  )
  OR
  -- Organization admins can see their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR
  -- Users can see invitations sent to their email
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- INSERT Policy: Global admins and org admins can create invitations
CREATE POLICY "Standard: Create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  -- Global admins can create invitations for any organization
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Organization admins can create invitations for their organization
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- UPDATE Policy: Global admins, org admins, and invitees can update invitations
CREATE POLICY "Standard: Update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  -- Global admins can update any invitation
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Organization admins can update their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR
  -- Users can update invitations sent to their email (for accepting)
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
)
WITH CHECK (
  -- Same permissions for updates
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
  OR
  email = (
    SELECT email FROM auth.users WHERE id = auth.uid()
  )
);

-- DELETE Policy: Global admins and org admins can delete invitations
CREATE POLICY "Standard: Delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  -- Global admins can delete any invitation
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
  OR
  -- Organization admins can delete their organization's invitations
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- ====================================================================
-- PHASE 4: Verification and Documentation
-- ====================================================================

-- Add helpful comments explaining the single role system
COMMENT ON POLICY "Standard: View invitations" ON public.pending_invitations
IS 'Single role system: Uses only public.users.role for admin checking. Allows global admins, org admins, and invitees to view invitations.';

COMMENT ON POLICY "Standard: Create invitations" ON public.pending_invitations
IS 'Single role system: Uses only public.users.role for admin checking. Allows global admins and org admins to create invitations.';

COMMENT ON POLICY "Standard: Update invitations" ON public.pending_invitations
IS 'Single role system: Uses only public.users.role for admin checking. Allows global admins, org admins, and invitees to update invitations.';

COMMENT ON POLICY "Standard: Delete invitations" ON public.pending_invitations
IS 'Single role system: Uses only public.users.role for admin checking. Allows global admins and org admins to delete invitations.';

-- Add a migration comment for tracking
COMMENT ON TABLE public.pending_invitations
IS 'Invitation system now uses single role source: public.users.role only. No auth metadata checking.';

-- ====================================================================
-- PHASE 5: Clean Up Auth Metadata (Optional - keeping for backward compatibility)
-- ====================================================================

-- We could clear the auth metadata role fields, but keeping them for potential future use
-- This ensures no breaking changes if any external integrations rely on them

-- Note: auth.users.raw_app_meta_data role fields will remain but are NOT used by RLS policies
-- Only public.users.role is the authoritative source for role checking
*/