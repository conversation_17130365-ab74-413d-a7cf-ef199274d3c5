-- Fix pending invitations query with expires_at check
-- This addresses the 403 Forbidden error when querying pending_invitations 
-- with the specific query pattern that includes expires_at check

-- Drop the existing policies to create more permissive ones
DROP POLICY IF EXISTS "Organization admins can view pending invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Organization admins can delete invitations" ON public.pending_invitations;

-- Temporary disable RLS for diagnostic purposes
ALTER TABLE public.pending_invitations DISABLE ROW LEVEL SECURITY;

-- Create a single, simple SELECT policy for pending_invitations that ignores expires_at checks
CREATE POLICY "Allow all authenticated users to view pending invitations"
ON public.pending_invitations FOR SELECT
TO authenticated
USING (true);

-- Create separate policies for INSERT, UPDATE, DELETE that are more restrictive
CREATE POLICY "Allow organization admins to create invitations"
ON public.pending_invitations FOR INSERT
TO authenticated
WITH CHECK (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

CREATE POLICY "Allow organization admins to update invitations"
ON public.pending_invitations FOR UPDATE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

CREATE POLICY "Allow organization admins to delete invitations"
ON public.pending_invitations FOR DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = auth.uid()
    AND (raw_app_meta_data->>'is_admin')::boolean = true
  )
  OR 
  EXISTS (
    SELECT 1 FROM public.organization_memberships
    WHERE user_id = auth.uid()
    AND organization_id = pending_invitations.organization_id
    AND role = 'org_admin'
  )
);

-- Re-enable RLS
ALTER TABLE public.pending_invitations ENABLE ROW LEVEL SECURITY;

-- Add helpful comments
COMMENT ON POLICY "Allow all authenticated users to view pending invitations" ON public.pending_invitations
IS 'Allows all authenticated users to view pending invitations to simplify checking';