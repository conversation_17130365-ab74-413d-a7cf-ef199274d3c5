-- Allow brand members to upload assets to media buckets
-- Brand members should be able to contribute assets to collections they have access to

-- Drop existing upload policies for media buckets
DROP POLICY IF EXISTS "Allow admins to upload media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow admins to upload media originals" ON storage.objects;

-- Create new policies that include brand_member role for uploads
-- Allow organization members (including brand_member) to upload media thumbnails
CREATE POLICY "Allow org members to upload media thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Allow organization members (including brand_member) to upload media compressed
CREATE POLICY "Allow org members to upload media compressed" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Allow organization members (including brand_member) to upload media originals
CREATE POLICY "Allow org members to upload media originals" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Also update the assets table insert policy to ensure brand members can create asset records
-- First check if the existing policy needs updating
DROP POLICY IF EXISTS "Members can upload assets" ON public.assets;
DROP POLICY IF EXISTS "Organization members can upload assets" ON public.assets;

-- Create a comprehensive policy for asset uploads that includes all organization members
CREATE POLICY "Organization members can upload assets" ON public.assets
  FOR INSERT WITH CHECK (
    -- User must be a member of the organization that owns the collection
    EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = assets.collection_id
      AND om.user_id = auth.uid()
    )
  );

-- Update the asset update policy to allow members to update their uploaded assets
DROP POLICY IF EXISTS "Members can update assets in their organizations" ON public.assets;

CREATE POLICY "Members can update assets in their organizations" ON public.assets
  FOR UPDATE USING (
    -- User must be a member of the organization that owns the collection
    EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = assets.collection_id
      AND om.user_id = auth.uid()
    )
  )
  WITH CHECK (
    -- Same check for the new row
    EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = assets.collection_id
      AND om.user_id = auth.uid()
    )
  );

-- Note: We keep the update and delete policies for media storage objects restricted to admins
-- This ensures that while brand members can upload, only admins can modify or delete existing files