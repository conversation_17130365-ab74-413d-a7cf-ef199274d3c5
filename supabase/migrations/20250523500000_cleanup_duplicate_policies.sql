-- Migration: Clean up duplicate RLS policies
-- Date: 2025-05-23
-- Description: Remove duplicate policies that were created during the debugging process

-- Drop the old-style policies (these use EXISTS clauses)
DROP POLICY IF EXISTS "Standard: Create invitations" ON public.pending_invitations;
DROP POLICY IF EXISTS "Standard: Delete invitations" ON public.pending_invitations;

-- The new simplified policies should remain:
-- - "Users can view organization invitations" (SELECT)
-- - "Organization admins can create invitations" (INSERT) 
-- - "Users can update organization invitations" (UPDATE)
-- - "Users can delete organization invitations" (DELETE)

-- Verify we have exactly 4 policies after cleanup
COMMENT ON TABLE public.pending_invitations IS 
'Invitation table with 4 RLS policies: SELECT, INSERT, UPDATE, DELETE. All use simplified query patterns to avoid permission denied errors.';