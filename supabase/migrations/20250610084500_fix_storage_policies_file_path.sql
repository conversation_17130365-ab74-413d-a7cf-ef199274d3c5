-- Fix storage policies to use correct file path reference
-- The policies were incorrectly using storage.foldername(c.name) instead of storage.foldername(name)
-- where 'name' refers to the file path being uploaded

-- Drop the incorrectly defined policies
DROP POLICY IF EXISTS "Allow org members to upload media thumbnails" ON storage.objects;
DROP POLICY IF EXISTS "Allow org members to upload media compressed" ON storage.objects;
DROP POLICY IF EXISTS "Allow org members to upload media originals" ON storage.objects;

-- Also drop the other media upload policies that have the same issue
DROP POLICY IF EXISTS "Allow media thumbnails upload" ON storage.objects;
DROP POLICY IF EXISTS "Allow media compressed upload" ON storage.objects;
DROP POLICY IF EXISTS "Allow media originals upload" ON storage.objects;

-- Recreate policies with correct file path reference
-- Allow organization members (including brand_member) to upload media thumbnails
CREATE POLICY "Organization members can upload media thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(objects.name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Allow organization members (including brand_member) to upload media compressed
CREATE POLICY "Organization members can upload media compressed" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(objects.name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Allow organization members (including brand_member) to upload media originals
CREATE POLICY "Organization members can upload media originals" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM collections c
      JOIN organization_memberships om ON om.organization_id = c.organization_id
      WHERE c.id = (storage.foldername(objects.name))[2]::uuid
      AND om.user_id = auth.uid()
    )
  );

-- Also ensure platform admins can upload
CREATE POLICY "Platform admins can upload media thumbnails" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-thumbnails'
    AND EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "Platform admins can upload media compressed" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-compressed'
    AND EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
  );

CREATE POLICY "Platform admins can upload media originals" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'media-originals'
    AND EXISTS (
      SELECT 1 FROM users u
      WHERE u.id = auth.uid()
      AND u.role IN ('platform_super', 'platform_admin')
    )
  );