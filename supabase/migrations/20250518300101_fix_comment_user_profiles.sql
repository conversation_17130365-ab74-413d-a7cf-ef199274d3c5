-- Update comments query to properly join with users
-- This creates a view to simplify the query
CREATE OR REPLACE VIEW comments_with_users AS
SELECT 
    c.*,
    u.email as user_email,
    COALESCE(u.first_name || ' ' || u.last_name, u.first_name, u.last_name, c.author) as user_name,
    u.avatar_url as user_avatar_url
FROM comments c
LEFT JOIN users u ON c.user_id = u.id;

-- Grant access to the view
GRANT SELECT ON comments_with_users TO authenticated;