-- Add fields to assets table for AI generation tracking
ALTER TABLE public.assets
ADD COLUMN IF NOT EXISTS generation_queue_id TEXT,
ADD COLUMN IF NOT EXISTS generation_prompt TEXT,
ADD COLUMN IF NOT EXISTS generation_model TEXT,
ADD COLUMN IF NOT EXISTS generation_metadata JSONB;

-- Create index for queue_id lookups
CREATE INDEX IF NOT EXISTS idx_assets_generation_queue_id 
ON public.assets(generation_queue_id) 
WHERE generation_queue_id IS NOT NULL;

-- RLS policy for generated images will be handled by existing asset policies
-- No need to create a separate policy here as assets already have RLS policies

-- Function to validate collection access
CREATE OR REPLACE FUNCTION public.user_has_collection_access(
  p_user_id UUID,
  p_collection_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.collections c
    JOIN public.organization_memberships om ON om.organization_id = c.organization_id
    WHERE c.id = p_collection_id
    AND om.user_id = p_user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;