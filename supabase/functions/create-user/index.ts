import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface CreateUserPayload {
  email: string;
  firstName: string;
  lastName: string;
  role: 'platform_admin' | 'brand_admin' | 'brand_member';
  organizationIds: string[];
  isFreelancer?: boolean;
  sendPasswordReset?: boolean;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create Supabase client with service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, serviceRoleKey)

    // Verify the requesting user is a platform admin
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get the requesting user
    const token = authHeader.replace('Bearer ', '')
    const { data: { user: requestingUser }, error: authError } = await supabase.auth.getUser(token)
    
    if (authError || !requestingUser) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if requesting user is platform admin
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', requestingUser.id)
      .single()

    if (userError || !userData || !['platform_admin', 'platform_super'].includes(userData.role)) {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions. Only platform admins can create users.' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Parse payload
    const payload: CreateUserPayload = await req.json()

    // Validate payload
    if (!payload.email || !payload.firstName || !payload.lastName || !payload.role || !payload.organizationIds?.length) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(payload.email)) {
      return new Response(
        JSON.stringify({ error: 'Invalid email format' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Freelancers must have brand_admin role
    if (payload.isFreelancer && payload.role !== 'brand_admin') {
      return new Response(
        JSON.stringify({ error: 'Freelancers must have brand_admin role' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Platform admins can only be created without organization
    if (['platform_admin', 'platform_super'].includes(payload.role) && payload.organizationIds.length > 0) {
      return new Response(
        JSON.stringify({ error: 'Platform admins cannot be assigned to organizations' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('id, email')
      .eq('email', payload.email.toLowerCase())
      .single()

    if (existingUser) {
      return new Response(
        JSON.stringify({ error: 'User with this email already exists' }),
        { status: 409, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Verify all organizations exist
    const { data: organizations, error: orgError } = await supabase
      .from('organizations')
      .select('id')
      .in('id', payload.organizationIds)

    if (orgError || organizations?.length !== payload.organizationIds.length) {
      return new Response(
        JSON.stringify({ error: 'One or more organizations not found' }),
        { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create the user in Supabase Auth
    // If sendPasswordReset is true, create without password to force reset flow
    // Otherwise create with random password
    const createUserPayload: any = {
      email: payload.email,
      email_confirm: true,
      user_metadata: {
        first_name: payload.firstName,
        last_name: payload.lastName,
        is_freelancer: payload.isFreelancer || false,
        created_by: requestingUser.id,
        role: payload.role,
        organization_ids: payload.organizationIds
      }
    }

    // If not sending password reset, set a random password
    if (!payload.sendPasswordReset) {
      createUserPayload.password = crypto.randomUUID()
    }

    const { data: authData, error: createError } = await supabase.auth.admin.createUser(createUserPayload)

    if (createError || !authData.user) {
      console.error('Error creating auth user:', createError)
      return new Response(
        JSON.stringify({ error: 'Failed to create user' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // The user profile will be created by the handle_new_user trigger
    // We need to wait a moment for the trigger to complete
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Create organization memberships
    if (payload.organizationIds.length > 0) {
      const memberships = payload.organizationIds.map(orgId => ({
        user_id: authData.user.id,
        organization_id: orgId
      }))

      const { error: membershipError } = await supabase
        .from('organization_memberships')
        .insert(memberships)

      if (membershipError) {
        console.error('Error creating memberships:', membershipError)
        // Don't fail the whole operation, user was created successfully
      }
    }

    // Send password reset email if requested
    if (payload.sendPasswordReset) {
      const siteUrl = Deno.env.get('SITE_URL') || 'http://localhost:8080'
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(payload.email, {
        redirectTo: `${siteUrl}/update-password`
      })

      if (resetError) {
        console.error('Error sending password reset:', resetError)
        // Don't fail the whole operation
      }
    }

    // Log the activity
    await supabase
      .from('security_activity')
      .insert({
        user_id: requestingUser.id,
        event_type: 'user_created',
        ip_address: req.headers.get('x-forwarded-for') || 'unknown',
        metadata: {
          created_user_id: authData.user.id,
          created_user_email: payload.email,
          created_user_role: payload.role,
          is_freelancer: payload.isFreelancer || false,
          organization_ids: payload.organizationIds,
          password_reset_sent: payload.sendPasswordReset || false
        }
      })

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        user: {
          id: authData.user.id,
          email: authData.user.email,
          firstName: payload.firstName,
          lastName: payload.lastName,
          role: payload.role,
          isFreelancer: payload.isFreelancer || false,
          organizationIds: payload.organizationIds,
          passwordResetSent: payload.sendPasswordReset || false
        }
      }),
      { status: 201, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error: unknown) {
    console.error('Error in create-user function:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Internal server error'
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
}

serve(handler)