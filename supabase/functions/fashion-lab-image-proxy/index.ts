import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get image URL from query parameter
    const url = new URL(req.url)
    const imageUrl = url.searchParams.get('url')
    
    if (!imageUrl) {
      throw new Error('Image URL is required')
    }
    
    // Validate it's a Fashion Lab URL
    if (!imageUrl.includes('fashionlab.notfirst.rodeo')) {
      throw new Error('Only Fashion Lab images are supported')
    }
    
    // Get Fashion Lab API token
    const fashionLabToken = Deno.env.get('FASHIONLAB_API_TOKEN')
    
    if (!fashionLabToken) {
      console.error('FASHIONLAB_API_TOKEN environment variable is not set')
      throw new Error('Fashion Lab API not configured')
    }
    
    console.log('Proxying image:', imageUrl)
    
    // Try different authentication methods
    let response: Response
    let lastError: string = ''
    
    // Method 1: Try with JWT token (correct Fashion Lab format)
    console.log('Trying with JWT token...')
    response = await fetch(imageUrl, {
      method: 'GET',
      headers: {
        'Authorization': `jwt ${fashionLabToken}`,
        'Accept': 'image/*',
      },
    })
    
    console.log('API token response:', response.status, response.statusText)
    
    if (!response.ok) {
      lastError = `API token failed: ${response.status} ${response.statusText}`
      
      // Method 2: Try without authentication (in case images are public)
      console.log('Trying without authentication...')
      response = await fetch(imageUrl, {
        method: 'GET',
        headers: {
          'Accept': 'image/*',
        },
      })
      
      console.log('No auth response:', response.status, response.statusText)
      
      if (!response.ok) {
        lastError += `, No auth failed: ${response.status} ${response.statusText}`
        
        // Method 3: Try with API key in query parameter
        const urlWithKey = new URL(imageUrl)
        urlWithKey.searchParams.set('token', fashionLabToken)
        console.log('Trying with token in query parameter...')
        
        response = await fetch(urlWithKey.toString(), {
          method: 'GET',
          headers: {
            'Accept': 'image/*',
          },
        })
        
        console.log('Query param response:', response.status, response.statusText)
        
        if (!response.ok) {
          lastError += `, Query param failed: ${response.status} ${response.statusText}`
          const errorText = await response.text()
          console.error('All authentication methods failed:', lastError)
          console.error('Last response body:', errorText)
          throw new Error(`Failed to fetch image after trying all methods: ${lastError}`)
        }
      }
    }
    
    // Get the image data
    const imageData = await response.arrayBuffer()
    const contentType = response.headers.get('content-type') || 'image/png'
    
    // Return the image with CORS headers
    return new Response(imageData, {
      headers: {
        ...corsHeaders,
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    })
  } catch (error: unknown) {
    console.error('Error in fashion-lab-image-proxy:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to proxy image'
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { 
        status: 500,
        headers: { 
          ...corsHeaders,
          'Content-Type': 'application/json'
        } 
      }
    )
  }
})