// Enhanced CORS headers setup with support for localhost domains
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // Allow all origins for now (can be restricted in production)
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-requested-with',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Access-Control-Max-Age': '86400', // 24 hours cache for preflight requests
}; 