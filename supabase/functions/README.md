# Supabase Edge Functions

This directory contains Edge Functions for the Fashionlab application.

## Functions

- `send-email`: Sends emails using Resend API

## Local Development

To run the functions locally:

```bash
supabase functions serve
```

## Deployment

To deploy to your Supabase project:

```bash
# Deploy the send-email function
supabase functions deploy send-email --project-ref YOUR_PROJECT_REF

# Set environment variables for the function
supabase secrets set RESEND_API_KEY=re_YOUR_RESEND_API_KEY EMAIL_FROM=<EMAIL> --project-ref YOUR_PROJECT_REF
```

## Required Environment Variables

The following environment variables must be set for the functions to work properly:

- `RESEND_API_KEY`: Your Resend API key
- `EMAIL_FROM`: The email address to send from (must be verified in Resend)

## Testing

You can test the functions using the Supabase CLI:

```bash
# Test send-email function locally
curl -X POST -H "Content-Type: application/json" -d '{"type":"password_reset","email":"<EMAIL>","resetLink":"http://localhost:8080/update-password"}' http://localhost:54321/functions/v1/send-email
``` 