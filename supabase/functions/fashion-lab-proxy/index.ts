import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import * as jose from 'https://deno.land/x/jose@v4.13.1/index.ts'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get JWT from Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      throw new Error('No JWT token provided')
    }
    
    const jwtToken = authHeader.substring(7)
    
    // Get JWT secret to verify tokens
    const jwtSecret = Deno.env.get('FASHIONLAB_JWT_SECRET') || '2ms4LQBtkbvJ8RwFmBht'
    console.log('JWT Secret loaded:', jwtSecret ? `${jwtSecret.substring(0, 10)}...` : 'NOT FOUND')
    if (!jwtSecret) {
      throw new Error('JWT verification not configured')
    }
    
    // Verify the JWT
    const secret = new TextEncoder().encode(jwtSecret)
    console.log('Verifying JWT token:', jwtToken.substring(0, 50) + '...')
    try {
      const { payload } = await jose.jwtVerify(jwtToken, secret)
      console.log('JWT verified successfully:', { iss: payload.iss, scope: payload.scope })
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError)
      console.error('JWT Secret used for verification:', jwtSecret ? `${jwtSecret.substring(0, 10)}...` : 'NOT FOUND')
      throw new Error('Invalid or expired JWT token')
    }
    
    // Get the master Fashion Lab API token
    const fashionLabToken = Deno.env.get('FASHIONLAB_API_TOKEN') || '2ms4LQBtkbvJ8RwFmBht'
    if (!fashionLabToken) {
      throw new Error('Fashion Lab API not configured')
    }
    
    // Extract the path and query from the request
    const url = new URL(req.url)
    const fashionLabPath = url.pathname.replace('/functions/v1/fashion-lab-proxy', '')
    const fashionLabUrl = `https://fashionlab.notfirst.rodeo${fashionLabPath}${url.search}`
    
    console.log('Proxying request to:', fashionLabUrl)
    
    // Forward the request to Fashion Lab API
    const fashionLabHeaders = new Headers(req.headers)
    fashionLabHeaders.set('Authorization', `jwt ${fashionLabToken}`)
    fashionLabHeaders.delete('host') // Remove host header
    
    const proxyResponse = await fetch(fashionLabUrl, {
      method: req.method,
      headers: fashionLabHeaders,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? await req.blob() : undefined,
    })
    
    // Forward the response back to the client
    const responseHeaders = new Headers(proxyResponse.headers)
    // Add CORS headers
    Object.entries(corsHeaders).forEach(([key, value]) => {
      responseHeaders.set(key, value)
    })
    
    return new Response(await proxyResponse.blob(), {
      status: proxyResponse.status,
      statusText: proxyResponse.statusText,
      headers: responseHeaders,
    })
  } catch (error: unknown) {
    console.error('Error in fashion-lab-proxy:', error)
    
    const errorMessage = error instanceof Error ? error.message : 'Proxy error'
    const status = errorMessage.includes('JWT') ? 401 : 500
    
    return new Response(
      JSON.stringify({ error: errorMessage }),
      {
        status,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})

/* 
Fashion Lab API Proxy

This edge function acts as a proxy to the Fashion Lab API, providing:
1. JWT-based authentication (clients use JWTs instead of the master API key)
2. Request forwarding to Fashion Lab API
3. Response forwarding back to clients

Usage:

1. Client gets JWT from /functions/v1/fashionlab-jwt
2. Client calls this proxy with the JWT
3. Proxy validates JWT and forwards to Fashion Lab API

Example:
  curl -X POST http://localhost:54321/functions/v1/fashion-lab-proxy/generate-image-v2 \
    -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    -F "model=@model.jpg" \
    -F "garment=@garment.jpg" \
    -F "pose=@pose.jpg" \
    -F "background=@background.jpg" \
    -F "prompt=your prompt"

This will be forwarded to:
  https://fashionlab.notfirst.rodeo/generate-image-v2
  with the master API token
*/