import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the logged-in user
    const authHeader = req.headers.get('Authorization')!
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )

    // Create admin client for user deletion
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { auth: { persistSession: false } }
    )

    // Get the JWT from the Authorization header
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token)

    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Log the deletion event
    await supabaseClient
      .from('security_activity')
      .insert({
        user_id: user.id,
        event_type: 'account_deletion',
        ip_address: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip'),
        user_agent: req.headers.get('user-agent'),
      })

    // Delete user data in correct order (respecting foreign key constraints)
    
    // 1. Delete from tables that reference the user
    await supabaseClient.from('security_activity').delete().eq('user_id', user.id)
    await supabaseClient.from('organization_memberships').delete().eq('user_id', user.id)
    await supabaseClient.from('comments').delete().eq('user_id', user.id)
    await supabaseClient.from('user_collection_access').delete().eq('user_id', user.id)
    
    // 2. Delete from users table
    await supabaseClient.from('users').delete().eq('id', user.id)
    
    // 3. Delete avatar from storage if exists
    const { data: files } = await supabaseClient.storage
      .from('avatars')
      .list(user.id)
    
    if (files && files.length > 0) {
      const filePaths = files.map(file => `${user.id}/${file.name}`)
      await supabaseClient.storage
        .from('avatars')
        .remove(filePaths)
    }
    
    // 4. Finally, delete the auth user using admin client
    const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(user.id)
    
    if (deleteError) {
      throw deleteError
    }

    return new Response(
      JSON.stringify({ 
        message: 'Account deleted successfully',
        userId: user.id 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})