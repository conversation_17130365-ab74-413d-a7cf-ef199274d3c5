# Database Development Guide

This file provides guidance specific to database development and Supabase operations.

## Quick Database Access

```bash
# Local database
psql -h localhost -p 54322 -d postgres -U postgres
# Password: postgres

# Staging database
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres
# Password: Get from Supabase dashboard

# Quick query example
psql -h db.qnfmiotatmkoumlymynq.supabase.co -p 5432 -d postgres -U postgres -c "SELECT * FROM users LIMIT 5;"
```

## Migration Development

### Creating Migrations

```bash
# Create new migration
supabase migration new epic_name_description

# Test locally
supabase db reset

# Verify changes
supabase db diff
```

### Migration Template

Use this template when creating new tables:

```sql
-- Create table with standard fields
CREATE TABLE public.<table_name> (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  -- your fields here
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Enable RLS
ALTER TABLE public.<table_name> ENABLE ROW LEVEL SECURITY;

-- Add update trigger
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.<table_name>
  FOR EACH ROW EXECUTE FUNCTION public.update_modified_column();

-- Basic RLS policies
CREATE POLICY "Users can view own records"
ON public.<table_name> FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own records"
ON public.<table_name> FOR INSERT
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own records"
ON public.<table_name> FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own records"
ON public.<table_name> FOR DELETE
USING (auth.uid() = user_id);
```

## RLS Policy Patterns

### Organization-based Access

```sql
-- Members can view organization data
CREATE POLICY "Organization members can view"
ON public.<table_name> FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships om
    WHERE om.organization_id = <table_name>.organization_id
    AND om.user_id = auth.uid()
  )
);

-- Only org_admin can modify
CREATE POLICY "Organization admins can modify"
ON public.<table_name> FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.organization_memberships om
    WHERE om.organization_id = <table_name>.organization_id
    AND om.user_id = auth.uid()
    AND om.role = 'org_admin'
  )
);
```

### Public Read Access

```sql
-- Anyone can read
CREATE POLICY "Public read access"
ON public.<table_name> FOR SELECT
TO authenticated
USING (true);

-- Only admins can modify
CREATE POLICY "Admin write access"
ON public.<table_name> FOR INSERT, UPDATE, DELETE
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
    AND role IN ('admin', 'superadmin')
  )
);
```

## Database Best Practices

1. **Migration Order**
   - Always maintain backwards compatibility
   - Add new columns as nullable first
   - Use separate migrations for dropping columns

2. **Testing**
   - Always test on local first
   - Check RLS policies with different roles
   - Verify foreign key constraints
   - Test rollback scenarios

3. **Deployment**
   - Link to staging first: `supabase link --project-ref qnfmiotatmkoumlymynq`
   - Push to staging: `supabase db push`
   - Test on staging thoroughly
   - Only then push to production

4. **Type Generation**
   - After migrations, regenerate types:
   ```bash
   supabase gen types typescript --local > src/types/database.types.ts
   ```

## Common Problems & Solutions

### Foreign Key Constraint Errors

When migrations fail due to foreign key constraints:

```sql
-- Check for orphaned records
SELECT c.id, c.name, c.client_id 
FROM collections c 
LEFT JOIN organizations o ON c.client_id = o.id 
WHERE c.client_id IS NOT NULL 
  AND o.id IS NULL;

-- Create missing organizations
INSERT INTO organizations (id, name, created_at, updated_at)
SELECT DISTINCT 
  c.client_id,
  'Organization for ' || c.client_id,
  NOW(),
  NOW()
FROM collections c
LEFT JOIN organizations o ON c.client_id = o.id
WHERE c.client_id IS NOT NULL
  AND o.id IS NULL
ON CONFLICT (id) DO NOTHING;
```

### Debugging Policies

```bash
# Check which policies apply to a table
staging-query "SELECT * FROM pg_policies WHERE tablename = 'collections';"

# Check if a user has access to a specific record
staging-query "SELECT * FROM auth.role(auth.uid(), 'authenticated') WHERE role = 'authenticated';"
```

## Multi-Tenant Data Isolation

Remember that this is a multi-tenant application:
- Each organization's data must be isolated
- RLS policies must check organization membership
- Use the organization_memberships table to verify access
- Test with users from different organizations

For detailed database information, see:
- @docs/database-guide.md - Complete database reference
- @docs/development/guides/rls-policy-patterns.md - RLS best practices