# Deploy to Production: $ARGUMENTS

I'll help you deploy to production with message: "$ARGUMENTS"

## 1. Pre-Production Checklist

### Verify Staging
- Confirm feature tested on staging
- Check staging deployment health
- Review any reported issues

### Code Review
- Ensure PR is approved
- All review comments addressed
- CI/CD checks passing

### Linear Status
- Related issues marked as "Ready for Production"
- Stakeholder approval documented

### Feature Flag Review
- List features to be enabled in production
- Confirm feature flags updated if needed
- Check for any staging-only features that should remain hidden

## 2. Production Deployment Process

### Create Production PR
Using gh CLI:
```
gh pr create --base production --head main --title "Deploy: $ARGUMENTS"
```

### PR Description Template
- Changes included
- Testing performed
- Rollback plan
- Linear issues included
- Feature flags being enabled
- Features remaining in staging-only

### Merge Strategy
- Squash and merge for clean history
- Ensure PR description is comprehensive
- Tag the release appropriately

## 3. Database Migrations

### Review Migrations
- List pending migrations
- Review SQL for production safety
- Check for breaking changes

### Apply Migrations
After PR merge:
- Use `/project:database:migrate-production` command
- This uses Supabase MCP instead of CLI
- More reliable than `npm run migrate:production`
- Requires explicit confirmation for safety

### Verify Migration
- Check migration status
- Verify schema changes
- Test affected features

## 4. Post-Deployment Verification

### Production Health
- URL: https://app.fashionlab.tech
- Monitor error rates
- Check performance metrics
- Verify critical paths

### Monitoring
- Check Supabase logs: mcp__supabase__get_logs
- Monitor for errors
- Watch user activity

## 5. Communication

### Update Linear
- Update issue status to "Done"
- Add deployment confirmation
- Link to production

### Team Notification
- Announce deployment completion
- Share any important notes
- Document any issues encountered

## 6. Rollback Plan

If issues arise:
1. Revert PR on production branch
2. Re-deploy previous version
3. Revert database migrations if needed
4. Communicate status to team

Starting production deployment...