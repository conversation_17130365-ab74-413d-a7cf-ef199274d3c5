# Create Hotfix for Production

I'll help you create an urgent hotfix that bypasses staging and goes directly to production.

## 1. Assess the Situation

First, let me understand the urgency:
- What is broken in production?
- How many users are affected?
- Is this data-critical or security-related?
- Can it wait for normal deployment?

## 2. Create Hotfix Branch

Following the hotfix workflow:
```bash
git checkout production
git pull origin production
git checkout -b hotfix/[issue-description]
```

## 3. Implement the Fix

Guidelines for hotfixes:
- Make minimal changes only
- Focus on fixing the immediate issue
- Don't include unrelated improvements
- Add tests if possible
- Document the fix clearly

## 4. Test Locally

Before pushing:
- Test the specific fix thoroughly
- Verify no side effects
- Run critical test suites
- Check for console errors

## 5. Create Direct PR to Production

Skip staging for urgent fixes:
```bash
gh pr create --base production --title "HOTFIX: [description]"
```

PR Description should include:
- What was broken
- What the fix does
- How it was tested
- Potential risks
- Rollback plan

## 6. Fast-Track Review

For critical hotfixes:
- Tag senior developers for immediate review
- Provide clear testing instructions
- Be available for questions
- Monitor PR closely

## 7. Deploy Immediately

After merge:
- Deploy database migrations if needed
- Monitor production closely
- Test the fix in production
- Watch error logs

## 8. Sync Back to Main

Critical step after hotfix:
```bash
git checkout main
git pull origin main
git merge production
git push origin main
```

This ensures staging gets the fix too.

## 9. Update Linear

Document the hotfix:
- Create or update Linear issue
- Add "hotfix" label
- Document what happened
- Link to production PR

## 10. Post-Mortem

After the crisis:
- Schedule a post-mortem meeting
- Document lessons learned
- Update processes if needed
- Consider adding tests

Let me help you create this hotfix...