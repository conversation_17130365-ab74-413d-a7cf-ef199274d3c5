# Deploy to Staging: $ARGUMENTS

I'll deploy the current branch to staging with message: "$ARGUMENTS"

## 1. Pre-Deployment Checks

### Git Status
- Check current branch
- Ensure no uncommitted changes
- Verify branch is pushed to remote

### Code Quality
- Run tests: `npm test`
- Run linting: `npm run lint`
- Check TypeScript: `npm run typecheck`

### Linear Integration
- Find related Linear issues from branch name
- Check if issues are ready for staging

## 2. Deployment Process

### Push to Main
If not on main:
- Show diff between current branch and main
- Confirm merge is safe
- Push current branch

### Trigger Deployment
- Push triggers automatic Vercel deployment
- Monitor deployment progress
- Wait for deployment to complete

## 3. Database Migrations

Check for pending migrations:
- List files in `supabase/migrations/`
- Compare with staging database
- If migrations exist:
  - Use `/project:database:migrate-staging` command
  - This uses Supabase MCP instead of CLI
  - More reliable than `npm run migrate:staging`

## 4. Post-Deployment Verification

### Staging Health Check
- URL: https://staging.fashionlab.tech
- Check deployment status
- Verify key features are working
- List features with staging-only flags

### Feature Flag Status
- Show all features enabled in staging
- Highlight new features just deployed
- Note which features are ready for production

### Update Linear
- Add comment to related issues: "Deployed to staging: $ARGUMENTS"
- Update issue status if appropriate
- Include staging URL
- Note if feature is behind a flag

## 5. Testing Checklist

Suggest testing on staging:
- [ ] Login/logout flow
- [ ] Core feature functionality
- [ ] Mobile responsiveness
- [ ] Any new features
- [ ] Error handling

## 6. Next Steps

- Share staging URL with team
- Request testing from stakeholders
- Monitor for any issues
- Prepare for production deployment

Starting staging deployment...