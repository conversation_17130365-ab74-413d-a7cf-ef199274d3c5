# Smart Git Commit

I'll help you create a well-formatted commit by analyzing your changes and suggesting an appropriate message.

## 1. Analyze Current Changes

Let me check:
- What files have been modified
- What type of changes were made
- Whether this relates to a Linear issue

## 2. Understand the Changes

I'll examine the diffs to understand:
- The primary purpose of these changes
- Whether it's a feature, fix, or other type
- The scope of the impact

## 3. Suggest Commit Structure

Based on the analysis, I'll suggest:

### Commit Type
- `feat:` New feature or functionality
- `fix:` Bug fix or correction
- `chore:` Routine task or maintenance
- `docs:` Documentation changes
- `style:` Formatting, missing semicolons, etc.
- `refactor:` Code restructuring without changing behavior
- `test:` Adding or modifying tests
- `perf:` Performance improvements

### Scope (optional)
The area affected, like: `(auth)`, `(api)`, `(ui)`

### Subject
A concise description of what changed

### Body (if needed)
- Why this change was necessary
- What problem it solves
- Any breaking changes

### Footer (if applicable)
- Linear issue reference: `Refs: FAS-123`
- Breaking changes: `BREAKING CHANGE: ...`

## 4. Interactive Commit Creation

I'll:
1. Show you the suggested message format
2. Ask if you want to modify it
3. Stage the appropriate files
4. Create the commit
5. Verify it was created successfully

## 5. Follow-up Actions

After committing, I'll suggest:
- Whether to push to remote
- If you should create a PR
- Any Linear issues to update

Let me analyze your current changes...