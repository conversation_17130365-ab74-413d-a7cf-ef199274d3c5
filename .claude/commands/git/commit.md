# Create Git Commit: $ARGUMENTS

I'll help you create a well-structured commit with message: "$ARGUMENTS"

## 1. Check Current State

First, let me check what needs to be committed:
- Current branch name
- Modified files
- Staged vs unstaged changes
- Any untracked files

## 2. Analyze Changes

I'll review the changes to:
- Understand what was modified
- Group related changes
- Identify the type of change (feat, fix, chore, docs, etc.)

## 3. Stage Appropriate Files

Based on "$ARGUMENTS", I'll:
- Stage relevant files for this commit
- Leave unrelated changes unstaged
- Warn about any large files or sensitive data

## 4. Generate Commit Message

### Commit Type
Based on the changes and "$ARGUMENTS", I'll determine:
- `feat:` New feature
- `fix:` Bug fix
- `chore:` Maintenance task
- `docs:` Documentation only
- `style:` Code style changes
- `refactor:` Code refactoring
- `test:` Test changes
- `perf:` Performance improvements

### Message Format
```
type: subject

Optional body explaining why this change was made
```

## 5. Create Commit

I'll create the commit with:
- Proper type prefix
- Clear, concise message from "$ARGUMENTS"
- Reference to Linear issue (if branch indicates one)
- Any additional context needed

## 6. Post-Commit

After committing:
- Show the commit hash
- Display what was committed
- Suggest next steps (push, create PR, etc.)
- Update any related Linear issues if applicable

Let me analyze your changes...