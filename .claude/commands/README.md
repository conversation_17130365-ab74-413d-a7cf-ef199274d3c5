# FashionLab Custom Commands

These commands enhance your development workflow using <PERSON>'s native features. No external scripts required!

## Available Commands

### Daily Workflows
- `/project:daily:start` - Start your development day with comprehensive overview
- `/project:daily:status` - Check project status anytime

### Feature Development
- `/project:features:new [issue-id or description]` - Start working on a new feature
- `/project:features:complete` - Complete current feature and create PR
- `/project:features:enable-production [feature-name]` - Enable feature flag in production
- `/project:features:review [pr-number]` - Review a pull request

### Deployment
- `/project:deployment:staging [message]` - Deploy to staging environment
- `/project:deployment:production [message]` - Deploy to production
- `/project:deployment:hotfix` - Create urgent production fix
- `/project:deployment:rollback [environment]` - Emergency rollback

### Database Operations
- `/project:database:migration [description]` - Create new migration
- `/project:database:migrate-staging` - Deploy migrations to staging (via MCP)
- `/project:database:migrate-production` - Deploy migrations to production (via MCP)
- `/project:database:sync [from] [to]` - Sync data between environments
- `/project:database:debug [issue]` - Debug database issues

### Debugging Tools
- `/project:debug:error [description]` - Analyze and fix errors
- `/project:debug:performance [area]` - Performance analysis

### Git Workflows
- `/project:git:commit [message]` - Create commit with proper formatting
- `/project:git:smart-commit` - Analyze changes and suggest commit message

## How Commands Work

All commands:
- Use `$ARGUMENTS` for dynamic parameters
- Leverage MCP servers (Linear, Supabase, IDE)
- Integrate with Claude's todo system
- Provide step-by-step guidance

## Examples

```bash
# Start working on a Linear issue
/project:features:new FAS-123

# Create a new feature without issue
/project:features:new implement user preferences

# Deploy to staging with a message
/project:deployment:staging Fixed asset upload bug

# Create a database migration
/project:database:migration add user preferences table
```

## Creating Personal Commands

You can create personal commands in `~/.claude/commands/`:

```bash
mkdir -p ~/.claude/commands
echo "Your custom prompt here" > ~/.claude/commands/my-command.md
```

Then use it with `/user:my-command`.