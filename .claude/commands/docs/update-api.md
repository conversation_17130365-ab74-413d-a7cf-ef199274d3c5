# Update API Documentation

Update API documentation when endpoints change or new ones are added.

## Parameters
- `endpoint`: The API endpoint (e.g., "/api/assets")
- `method`: HTTP method (GET, POST, PUT, DELETE)
- `description`: What the endpoint does

## Tasks

1. **Analyze Endpoint**
   - Locate the endpoint implementation
   - Check authentication requirements
   - Identify request/response types
   - Note any RLS policies

2. **Update Main API Docs**
   
   Edit `docs/04-api/README.md`:
   ```markdown
   ### ${endpoint}
   
   ```http
   ${method} ${endpoint}
   ```
   
   ${description}
   
   **Authentication**: Required
   **Permissions**: ${permissions}
   ```

3. **Document Request Format**
   ```markdown
   **Request Body**:
   ```json
   {
     "field1": "string",
     "field2": 123,
     "field3": {
       "nested": "value"
     }
   }
   ```
   
   **Headers**:
   - `Authorization: Bearer TOKEN`
   - `Content-Type: application/json`
   ```

4. **Document Response Format**
   ```markdown
   **Success Response** (200):
   ```json
   {
     "data": {
       "id": "uuid",
       "created_at": "2024-01-15T10:00:00Z"
     }
   }
   ```
   
   **Error Responses**:
   - `400 Bad Request`: Invalid input
   - `401 Unauthorized`: Missing/invalid token
   - `403 Forbidden`: Insufficient permissions
   - `404 Not Found`: Resource not found
   ```

5. **Add Usage Examples**
   
   ### JavaScript/TypeScript
   ```typescript
   const response = await fetch('${endpoint}', {
     method: '${method}',
     headers: {
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify(data)
   });
   ```
   
   ### Using Supabase Client
   ```typescript
   const { data, error } = await supabase
     .from('table_name')
     .method()
     .match(filters);
   ```

6. **Update Endpoint Index**
   
   Add to `docs/04-api/endpoints/index.md`:
   ```markdown
   | Endpoint | Method | Description | Auth |
   |----------|--------|-------------|------|
   | ${endpoint} | ${method} | ${description} | ✓ |
   ```

7. **Create Detailed Endpoint Doc**
   
   Create `docs/04-api/endpoints/${endpoint_name}.md`:
   ```markdown
   # ${Endpoint Name}
   
   ## Overview
   ${detailed description}
   
   ## Use Cases
   - ${use case 1}
   - ${use case 2}
   
   ## Implementation Details
   - Database tables involved
   - RLS policies applied
   - Business logic
   
   ## Performance Considerations
   - Caching strategy
   - Rate limits
   - Optimization tips
   ```

8. **Update Postman Collection**
   ```json
   {
     "name": "${endpoint}",
     "request": {
       "method": "${method}",
       "url": "{{base_url}}${endpoint}",
       "header": [
         {
           "key": "Authorization",
           "value": "Bearer {{token}}"
         }
       ],
       "body": {
         "mode": "raw",
         "raw": "{}"
       }
     }
   }
   ```

9. **Add to API Tests**
   ```typescript
   describe('${endpoint}', () => {
     it('should ${expected behavior}', async () => {
       const response = await request(app)
         .${method.toLowerCase()}('${endpoint}')
         .set('Authorization', `Bearer ${token}`)
         .send(testData);
         
       expect(response.status).toBe(200);
       expect(response.body).toMatchObject(expected);
     });
   });
   ```

10. **Cross-Reference**
    - Link from feature docs to API docs
    - Update SDK documentation if applicable
    - Add to changelog if breaking change