# Deploy to Staging

Deploy current branch to staging environment with all checks.

## Pre-deployment Checklist

1. **Run Tests**
   ```bash
   npm run lint
   npm run typecheck
   npm run test
   ```

2. **Check for Secrets**
   ```bash
   # Scan for exposed secrets
   grep -r "VITE_" --include="*.ts" --include="*.tsx" | grep -v "import.meta.env"
   ```

3. **Build Test**
   ```bash
   npm run build
   ```

## Deployment Steps

1. **Create/Update PR**
   ```bash
   # If no PR exists
   gh pr create --title "Deploy: ${description}" --body "Deployment to staging"
   
   # If PR exists
   git push origin HEAD
   ```

2. **Check CI Status**
   - Wait for all checks to pass
   - Review Vercel preview deployment

3. **Database Migrations (if any)**
   ```bash
   # Check for new migrations
   ls supabase/migrations/
   
   # Apply to staging
   mcp__supabase__apply_migration \
     --project_id qnfmiotatmkoumlymynq \
     --name "migration_name" \
     --query "SQL_QUERY"
   ```

4. **Update Environment Variables (if needed)**
   - Go to Vercel Dashboard
   - Update staging environment variables

5. **Merge PR**
   ```bash
   gh pr merge --merge
   ```

## Post-deployment Verification

1. **Check Application**
   - Visit https://staging.fashionlab.ai
   - Test login
   - Verify new features

2. **Monitor Logs**
   ```bash
   # Check function logs
   mcp__supabase__get_logs --project_id qnfmiotatmkoumlymynq --service edge-function
   
   # Check Vercel logs
   vercel logs --scope=your-team
   ```

3. **Run Smoke Tests**
   - [ ] Application loads
   - [ ] Authentication works
   - [ ] Core features functional
   - [ ] No console errors

4. **Update Linear**
   ```bash
   mcp__linear__updateIssue --id ISSUE_ID --stateId "in-review"
   mcp__linear__createComment --issueId ISSUE_ID --body "Deployed to staging: https://staging.fashionlab.ai"
   ```

## Rollback Procedure

If issues are found:

1. **Quick Rollback**
   - Go to Vercel Dashboard
   - Find previous deployment
   - Click "Promote to Production"

2. **Git Rollback**
   ```bash
   git revert HEAD
   git push origin main
   ```

3. **Database Rollback**
   ```bash
   # Only if migrations were applied
   mcp__supabase__execute_sql \
     --project_id qnfmiotatmkoumlymynq \
     --query "DROP TABLE IF EXISTS new_table;"
   ```