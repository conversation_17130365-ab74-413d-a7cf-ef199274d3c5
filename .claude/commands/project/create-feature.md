# Create Feature

Create a new feature for FashionLab with proper setup and documentation.

## Parameters
- `feature_name`: Name of the feature (e.g., "bulk-export")
- `description`: Brief description of what the feature does

## Tasks

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/${feature_name}
   ```

2. **Create Linear Issue**
   ```bash
   mcp__linear__createIssue \
     --title "Feature: ${feature_name}" \
     --description "${description}" \
     --teamId TEAM_ID \
     --priority 2
   ```

3. **Setup Feature Structure**
   Create the following structure:
   ```
   src/
   ├── components/${feature_name}/
   │   ├── index.tsx
   │   ├── README.md
   │   └── types.ts
   ├── hooks/use${FeatureName}.ts
   └── pages/${feature_name}/
       └── index.tsx
   ```

4. **Create Base Component**
   ```typescript
   // src/components/${feature_name}/index.tsx
   import React from 'react';
   
   export function ${FeatureName}() {
     return (
       <div>
         <h1>${Feature Name}</h1>
         {/* TODO: Implement feature */}
       </div>
     );
   }
   ```

5. **Create Hook**
   ```typescript
   // src/hooks/use${FeatureName}.ts
   import { useState, useEffect } from 'react';
   
   export function use${FeatureName}() {
     // TODO: Implement hook logic
     return {};
   }
   ```

6. **Add Route**
   Update routing configuration to include new feature page.

7. **Create Tests**
   ```typescript
   // src/components/${feature_name}/__tests__/index.test.tsx
   import { render } from '@testing-library/react';
   import { ${FeatureName} } from '../index';
   
   describe('${FeatureName}', () => {
     it('renders without crashing', () => {
       render(<${FeatureName} />);
     });
   });
   ```

8. **Update Documentation**
   - Add feature to `docs/02-features/${feature_name}/README.md`
   - Update main README if needed

9. **Create PR Template**
   ```markdown
   ## Feature: ${feature_name}
   
   ### Description
   ${description}
   
   ### Changes
   - [ ] Created component structure
   - [ ] Implemented core functionality
   - [ ] Added tests
   - [ ] Updated documentation
   
   ### Testing
   - [ ] Manual testing completed
   - [ ] Unit tests passing
   - [ ] No console errors
   
   ### Screenshots
   [Add screenshots if applicable]
   
   Closes #LINEAR-ISSUE-ID
   ```