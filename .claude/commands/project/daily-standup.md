# Daily Standup

Generate a daily standup report for the FashionLab project.

## Tasks

1. **Check Recent Commits**
   - Run `git log --oneline -10` to see recent changes
   - Summarize key commits from the last 24 hours

2. **Review Active Issues**
   - Use `mcp__linear__getIssues --limit 10` to check Linear
   - List issues currently in progress

3. **Check CI/CD Status**
   - Review any failed builds or deployments
   - Check staging environment health

4. **Database Status**
   - Run `mcp__supabase__execute_sql --project_id qnfmiotatmkoumlymynq --query "SELECT COUNT(*) FROM assets WHERE created_at > NOW() - INTERVAL '24 hours'"`
   - Report on recent activity

5. **Generate Report**
   Format as:
   ```markdown
   ## Daily Standup - [Date]
   
   ### Yesterday's Progress
   - [List completed items]
   
   ### Today's Focus
   - [List planned work]
   
   ### Blockers
   - [Any issues or dependencies]
   
   ### Metrics
   - New assets: X
   - Active users: Y
   - Build status: ✅/❌
   ```

6. **Update Linear**
   - Move completed issues to "Done"
   - Update issue comments with progress