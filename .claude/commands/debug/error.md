# Debug Error: $ARGUMENTS

I'll help you debug and fix: $ARGUMENTS

## 1. Error Analysis

### Parse Error Information

From "$ARGUMENTS", I'll identify:
- Error type and message
- Stack trace if available
- Affected component or service
- Environment where it occurs

### Search for Error Pattern

- Search codebase for error message
- Check recent changes that might cause this
- Look for similar past issues

## 2. Gather Context

### Recent Changes

- Check recent commits
- Review recent deployments
- Look for related PRs

### Environment Specific

- Is this production only?
- Staging specific?
- Local development?

### Related Systems

- Check Supabase logs if database related
- Review Linear for similar reported issues
- Check browser console for frontend errors

## 3. Investigation Steps

### For Frontend Errors

- Component state issues
- API call failures
- Render errors
- Event handler problems

### For Backend Errors

- Database query issues
- RLS policy violations
- API endpoint errors
- Authentication problems

### For Build Errors

- Dependency conflicts
- TypeScript errors
- Import issues
- Configuration problems

## 4. Common Solutions

Based on error type, check:
- Missing environment variables
- Database migration needed
- Permission/RLS issues
- Cache clearing needed
- Dependency updates required

## 5. Create Fix

### Implement Solution

- Make minimal change to fix
- Add error handling if missing
- Include helpful error messages
- Consider edge cases

### Add Tests

- Write test to reproduce error
- Verify fix resolves issue
- Add regression test

## 6. Verify Fix

- Test locally
- Deploy to staging if needed
- Verify error is resolved
- Check for side effects

## 7. Document Resolution

- Add comment to code if non-obvious
- Update Linear issue if exists
- Document in team knowledge base
- Consider adding to troubleshooting guide

Let me start investigating "$ARGUMENTS"...