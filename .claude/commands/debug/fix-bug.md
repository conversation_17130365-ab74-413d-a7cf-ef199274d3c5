# Fix Bug

Debug and fix issues in the FashionLab application.

## Parameters
- `issue_description`: Description of the bug
- `error_message`: Any error messages (optional)

## Debugging Steps

1. **Gather Information**
   ```bash
   # Check recent changes
   git log --oneline -20
   
   # Check for TypeScript errors
   npm run typecheck
   
   # Check for lint errors
   npm run lint
   ```

2. **Reproduce the Issue**
   - Start local development server
   - Navigate to the affected area
   - Open browser DevTools (F12)
   - Check Console for errors
   - Check Network tab for failed requests

3. **Check Common Issues**

   ### Authentication Errors
   ```typescript
   // Check auth state
   const { data: { session } } = await supabase.auth.getSession();
   console.log('Session:', session);
   ```

   ### API Errors
   ```bash
   # Check Fashion Lab API format
   # Should be: Authorization: jwt TOKEN
   # NOT: Authorization: Bearer TOKEN
   ```

   ### Database Errors
   ```sql
   -- Check RLS policies
   SELECT tablename, policyname, permissive, roles, cmd 
   FROM pg_policies 
   WHERE tablename = 'affected_table';
   ```

4. **Enable Debug Mode**
   ```typescript
   // Add to affected component
   useEffect(() => {
     console.log('Component mounted with props:', props);
     console.log('Current state:', state);
   }, []);
   ```

5. **Check Logs**
   ```bash
   # Local Supabase logs
   supabase functions logs
   
   # Production logs
   mcp__supabase__get_logs --project_id PROJECT_ID --service api
   ```

6. **Common Fixes**

   ### Clear Cache
   ```bash
   rm -rf node_modules/.cache
   rm -rf .next
   npm install
   ```

   ### Reset Database
   ```bash
   supabase db reset
   ```

   ### Update Types
   ```bash
   npm run types:generate
   ```

7. **Test Fix**
   ```bash
   # Run specific test
   npm test -- --testNamePattern="test name"
   
   # Test in browser
   npm run dev
   ```

8. **Create Fix Commit**
   ```bash
   git add .
   git commit -m "fix: ${issue_description}
   
   - Root cause: ${cause}
   - Solution: ${solution}
   
   Fixes #ISSUE_NUMBER"
   ```

9. **Update Linear**
   ```bash
   mcp__linear__updateIssue --id ISSUE_ID --stateId "done"
   mcp__linear__createComment --issueId ISSUE_ID --body "Fixed: ${solution}"
   ```

## Debug Checklist

- [ ] Error reproduced locally
- [ ] Root cause identified
- [ ] Fix implemented
- [ ] Tests added/updated
- [ ] No new TypeScript errors
- [ ] No new lint warnings
- [ ] Fix verified in browser
- [ ] Code reviewed