# Start New Feature: $ARGUMENTS

I'll help you start working on $ARGUMENTS.

## 1. Determine Feature Type

First, I'll check if "$ARGUMENTS" is a Linear issue ID (format: FAS-XXX):
- If yes → Fetch issue details with mcp__linear__get_issue
- If no → Create a new Linear issue with title "$ARGUMENTS"

## 2. Linear Issue Setup

For existing issue:
- Show issue title, description, and acceptance criteria
- Check comments for additional context
- Update status to "In Progress"

For new issue:
- Create issue with mcp__linear__create_issue
- Set title: "$ARGUMENTS"
- Assign to current user
- Set appropriate labels

## 3. Git Branch Management

Create and checkout feature branch:
- Branch name: `feature/[issue-key]-[slugified-title]`
- Ensure we're up to date with main
- Push branch to remote with tracking

## 4. Development Planning

Create comprehensive todo list:
- [ ] Understand requirements and acceptance criteria
- [ ] Research existing code and patterns
- [ ] Determine if feature flag is needed for staging-only deployment
- [ ] Write tests for new functionality
- [ ] Implement the feature
- [ ] Add feature flag if needed (staging: true, production: false)
- [ ] Update documentation if needed
- [ ] Test on local environment
- [ ] Deploy to staging for testing
- [ ] Update linear issue status to "In Review"
- [ ] Create pull request
- [ ] Address review feedback

## 5. Initial Setup

- Show relevant files for this feature
- Display any related documentation
- Check for similar implementations
- Set up any necessary test data

## 6. Next Steps

- First todo to tackle
- Helpful commands for this feature
- Related documentation links

Starting work on "$ARGUMENTS"...