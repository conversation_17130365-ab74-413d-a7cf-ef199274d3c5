# Enable Feature in Production: $ARGUMENTS

I'll help you enable the "$ARGUMENTS" feature flag in production.

## 1. Validate Feature Name

First, I'll check if "$ARGUMENTS" is a valid feature flag:
- Read src/utils/featureFlags.ts
- Verify feature exists in FEATURES object
- Show current staging/production status

## 2. Pre-Production Checklist

Before enabling in production:
- Confirm feature has been tested on staging
- Check Linear issues for this feature
- Verify all related bugs are resolved
- Ensure documentation is updated

## 3. Update Feature Flag

Make the change:
- Edit src/utils/featureFlags.ts
- Change `production: false` to `production: true`
- Keep `staging: true` unchanged

## 4. Commit and Deploy

Create deployment commit:
- Commit message: "feat: Enable $ARGUMENTS in production"
- Include Linear issue reference if applicable
- Push to main branch

## 5. Create Production PR

Follow standard production deployment:
- Create PR from main to production
- Note in PR description that feature is being enabled
- Include testing summary from staging

## 6. Post-Deployment

After deployment:
- Verify feature is visible in production
- Monitor for any issues
- Update Linear issues to "Done"
- Notify stakeholders

## 7. Rollback Plan

If issues arise:
- Quickly set `production: false`
- Deploy hotfix to production
- Investigate issues on staging

Let me start by checking the current feature flag status...