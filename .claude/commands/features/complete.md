# Complete Current Feature

I'll help you complete the current feature and create a pull request.

## 1. Identify Current Feature

- Check current git branch name
- Extract Linear issue ID from branch name
- Fetch issue details with mcp__linear__get_issue

## 2. Review Progress

Check completion status:
- Review todo list with TodoRead
- Identify any incomplete tasks
- Check for uncommitted changes
- Run tests to ensure everything passes

## 3. Final Checks

Before creating PR:
- Run linting: `npm run lint`
- Run type checking: `npm run typecheck`
- Run tests: `npm test`
- Check for console.logs or debug code
- Ensure documentation is updated
- Review feature flags (if staging-only feature)
- Verify feature flag is properly configured

## 4. Commit Final Changes

- Stage all relevant changes
- Create meaningful commit message
- Include Linear issue reference

## 5. Create Pull Request

Using gh CLI:
- Create PR to main branch
- Set title: "[Issue-ID] Issue Title"
- Generate comprehensive PR description:
  - Summary of changes
  - Testing performed
  - Screenshots if UI changes
  - Linear issue link
  - Feature flag status (if applicable)
  - Note if feature is staging-only
- Add appropriate reviewers

## 6. Update Linear

- Update issue status to "In Review"
- Add comment with PR link
- Move any incomplete todos to follow-up issues

## 7. Deploy to Staging

If appropriate:
- Deploy feature branch to staging
- Add staging URL to PR description
- Update Linear with staging link

## 8. Next Steps

- Monitor PR for review comments
- Be ready to address feedback
- Plan next feature to work on

Let me help you complete this feature...