# Clean Up After Feature

I'll help you clean up and finalize everything after completing a feature or bug fix.

## 1. Check Current Status
First, let me check what needs to be cleaned up:
- Current branch
- Uncommitted changes
- Test files
- Temporary files
- Feature documentation

## 2. Run Tests and Checks
Run all necessary checks to ensure code quality:
- Type checking: `npm run type-check`
- Linting: `npm run lint`
- Unit tests: `npm test`
- E2E tests (if applicable): `npm run test:e2e`

## 3. Clean Up Test Files
I'll check for and handle:
- Temporary test files created during development
- `.test.tsx` or `.spec.ts` files that were for experimentation
- Test data files that are no longer needed
- Screenshot artifacts from failed tests

## 4. Update Documentation
Ensure all documentation is current:
- Remove outdated documentation
- Update any changed APIs or workflows
- Ensure test plans are finalized
- Check that CLAUDE.md has any new learnings

## 5. Clean Up Git History (if needed)
If there are many small commits:
- Offer to squash related commits
- Ensure commit messages follow conventions
- Remove any experimental branches

## 6. Final Checklist
Before completing:
- [ ] All tests pass
- [ ] No console.log() statements left
- [ ] No TODO comments that should be addressed
- [ ] Documentation is updated
- [ ] Linear issue is in correct status
- [ ] PR description is comprehensive

## 7. <PERSON>reate Summary
I'll create a summary of:
- What was implemented/fixed
- What was cleaned up
- Any follow-up tasks identified

Let me start the cleanup process...