# Enhance Linear Issue: $ARGUMENTS

I'll guide you through enhancing Linear issue $ARGUMENTS using our step-by-step workflow. This process is now split into separate commands to give you full control at each stage.

## Overview

The enhancement process consists of three stages:
1. **Analyze** - Research and understand the issue
2. **Update Description** - Apply comprehensive documentation
3. **Generate Questions** - Create clarifying questions

## Recommended Workflow

### Step 1: Analyze the Issue
```
/project:linear:analyze-issue $ARGUMENTS
```

This command will:
- Fetch the issue details from Linear
- Research the codebase for context
- Generate an enhanced description proposal
- Identify areas needing clarification

**What you'll see:**
- Current issue state
- Codebase analysis findings  
- Proposed enhanced description
- Areas that need clarifying questions

### Step 2: Update the Description
```
/project:linear:enhance-description $ARGUMENTS
```

This command will:
- Show the current vs proposed description
- Ask for your confirmation
- Update the issue if approved
- Provide a link to the updated issue

**Control point:** You can review and modify before applying changes.

### Step 3: Generate Clarifying Questions
```
/project:linear:generate-questions $ARGUMENTS
```

This command will:
- Generate questions based on gaps in requirements
- Group questions by category
- Show questions for your review
- Post as a comment if approved
- Tag the issue creator

**Control point:** You can edit or remove questions before posting.

## Benefits of the Split Workflow

1. **Full Control** - Review and approve each step
2. **Flexibility** - Skip steps if not needed
3. **Transparency** - See exactly what will be changed
4. **Iterative** - Modify proposals before applying

## Quick Start

To enhance issue $ARGUMENTS, I'll run the analyze command first:

```
/project:linear:analyze-issue $ARGUMENTS
```

After that, you can decide whether to:
- Update the description
- Generate questions
- Both or neither

This gives you complete control over the enhancement process.