# Linear Commands

Commands for working with Linear issues.

## Single Issue Workflow

Use the existing feature command:
```bash
/project:features:new FAS-101
```

## Multiple Issues in Parallel

### Simple Setup (work-on-multiple-issues)
```bash
/project:linear:work-on-multiple-issues FAS-101 FAS-102 FAS-103
```
- Creates git worktrees
- Shows manual steps to follow
- You control when/how to start <PERSON> in each

### Full Automation (parallel-development)
```bash
/project:linear:parallel-development FAS-101 FAS-102 FAS-103
```
- Creates git worktrees
- Generates automation scripts
- Creates work plans in each directory
- Can auto-open Claude instances
- Includes status monitoring scripts

## Other Linear Commands

- `analyze-issue` - Analyze a Linear issue in detail
- `enhance-description` - Improve a Linear issue description
- `enhance-issue` - Enhance issue with acceptance criteria
- `generate-questions` - Generate questions about an issue