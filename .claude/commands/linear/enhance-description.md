# Update Linear Issue Description: $ARGUMENTS

I'll update Linear issue $ARGUMENTS with the enhanced description after showing you the changes for approval.

## Prerequisites

You should have already run `/project:linear:analyze-issue $ARGUMENTS` to generate the enhanced description. If not, I'll need to analyze the issue first.

## 1. Fetch Current Issue

I'll fetch issue $ARGUMENTS to get:
- Current description
- Issue metadata (title, status, etc.)
- Recent updates or changes

## 2. Present Enhanced Description

I'll show you:

### Current Description
The existing issue description as it appears in Linear.

### Proposed Enhanced Description
The comprehensive description generated from the codebase analysis, including:
- Problem statement
- Business value and impact
- Technical implementation details
- Success criteria
- Testing requirements

### Key Improvements
I'll highlight what's being added or clarified.

## 3. Request Confirmation

Before making any changes, I'll ask:
- "Do you want to update issue $ARGUMENTS with this enhanced description?"
- You can review and request modifications
- Only proceeds with your explicit approval

## 4. Update the Issue

If approved, I'll:
- Update the issue description using mcp__linear__update_issue
- Preserve any existing formatting or special sections
- Maintain references to attachments or links

## 5. Confirm Success

After updating:
- ✅ Show confirmation of successful update
- 🔗 Provide direct link to the updated issue
- 📝 Suggest next steps:
  - Run `/project:linear:generate-questions` for clarifying questions
  - Update issue status if needed
  - Notify stakeholders of the enhancement

## 6. Handle Errors

If something goes wrong:
- Show the specific error
- Suggest remediation steps
- Offer to retry with modifications

Let me proceed with fetching and presenting the enhanced description for issue $ARGUMENTS...