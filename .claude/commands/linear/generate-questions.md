# Generate Clarifying Questions: $ARGUMENTS

I'll generate and post clarifying questions for Linear issue $ARGUMENTS to help gather any missing requirements or specifications.

## Prerequisites

You should have already run `/project:linear:analyze-issue $ARGUMENTS` to understand the issue context. If not, I'll analyze it first.

## 1. Analyze Issue Context

I'll review issue $ARGUMENTS to understand:
- What's already specified
- What's ambiguous or missing
- Related technical areas
- Potential edge cases

## 2. Generate Clarifying Questions

Based on the analysis, I'll create questions organized by category:

### Implementation Details
- Technical specifications needed
- Architecture decisions required
- API or database considerations

### Edge Cases & Error Handling
- Unusual scenarios to handle
- Error states and recovery
- Boundary conditions

### User Experience
- UI/UX clarifications
- User flow questions
- Accessibility requirements

### Integration & Dependencies
- Other systems affected
- Third-party service interactions
- Migration from existing features

### Performance & Scale
- Expected load and usage
- Performance requirements
- Resource constraints

### Testing & Validation
- Success criteria clarification
- Test scenario requirements
- Acceptance criteria details

## 3. Format Questions

I'll format the questions to be:
- Clear and specific
- Grouped logically
- Easy to answer
- Numbered for reference

## 4. Request Confirmation

Before posting, I'll:
- Show all proposed questions
- Highlight any critical questions
- Ask: "Do you want to post these clarifying questions to issue $ARGUMENTS?"
- Allow you to modify or remove questions

## 5. Post to Linear

If approved, I'll:
- Create a well-formatted comment using mcp__linear__create_comment
- Tag the issue creator for visibility
- Use clear formatting with sections
- Add context for why each question matters

## 6. Follow-up Actions

After posting:
- ✅ Confirm questions were posted successfully
- 👤 Show who was tagged (creator: @username)
- 🔗 Provide direct link to the comment
- 📊 Suggest monitoring for responses
- 🔔 Recommend updating issue description once answers are received

## 7. Handle Edge Cases

- If creator can't be determined, tag assignee instead
- If no questions needed, explain why the issue is sufficiently clear
- If questions already exist, show them and ask if more are needed

Let me analyze issue $ARGUMENTS and generate appropriate clarifying questions...