I want to work on these Linear issues in parallel: $ARGUMENTS.

Please create a complete automated setup by:

1. **Fetch Linear Issue Details**
   - Use the Linear MCP to get full details for each issue
   - Extract issue titles, descriptions, and types (bug/feature)
   - Identify which team each issue belongs to

2. **Create Automation Script**
   - Generate a bash script called `parallel-work-setup.sh` that:
     - Creates worktrees for each issue
     - Names branches appropriately (feature/FAS-XXX-slug or fix/FAS-XXX-slug)
     - Runs npm install in each worktree
     - Creates a launcher script for each worktree
     - Opens Claude Code in separate terminals (detect platform automatically)

3. **Generate Work Plans**
   - For each issue, create a detailed work plan based on the Linear description
   - Save each plan to a WORKPLAN.md file in the respective worktree
   - Include acceptance criteria and testing requirements

4. **Create Master TODO List**
   - Set up a todo list tracking all issues
   - Include subtasks for each issue based on the work plans
   - Mark the first subtask of each issue as "in_progress" to start

5. **Provide Status Dashboard**
   - Create a script called `parallel-work-status.sh` to check the status of all worktrees
   - Show current branch, uncommitted changes, and Linear issue status

The setup script should handle platform-specific terminal launching:
- macOS: Use Terminal.app or iTerm2 if available
- Linux: Use gnome-terminal or the default terminal
- Windows: Use Windows Terminal

Also create a cleanup script `parallel-work-cleanup.sh` to remove worktrees after PRs are merged.