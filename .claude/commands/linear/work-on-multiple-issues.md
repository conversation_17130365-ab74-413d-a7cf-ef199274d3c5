Set up git worktrees to work on these Linear issues in parallel: $ARGUMENTS

This is the SIMPLE version - just sets up the worktrees and gives you instructions.

1. Fetch brief details for each issue from Linear (just title and type)
2. Create a git worktree for each issue with branch names:
   - feature/[ISSUE-ID]-[slug] for features
   - fix/[ISSUE-ID]-[slug] for bugs
3. Show me simple commands to:
   - cd into each worktree
   - run npm install
   - start claude in each

Keep it simple - just the setup, no automation scripts. If I want full automation, I'll use /project:linear:parallel-development instead.