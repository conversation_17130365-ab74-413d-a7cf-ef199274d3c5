# Analyze Linear Issue: $ARGUMENTS

I'll analyze Linear issue $ARGUMENTS by fetching its details and researching the codebase to understand the implementation scope.

## 1. Fetch Issue Details

First, I'll fetch issue $ARGUMENTS from Linear using mcp__linear__get_issue to understand:
- Title and current description
- Creator and assignee information
- Current status and priority
- Any existing comments or attachments
- Related projects and labels

## 2. Research Codebase

Based on the issue context, I'll research the codebase to understand:

### Affected Areas
- **Components**: Search for UI components mentioned or affected
- **Pages**: Identify page-level components that need updates
- **APIs**: Find relevant API endpoints or database queries
- **Utilities**: Locate helper functions and shared utilities

### Implementation Context
- **Current State**: How the feature/area currently works
- **Similar Patterns**: Existing patterns to follow for consistency
- **Dependencies**: Systems and components that depend on this area
- **Database Schema**: Relevant tables and relationships

### Technical Considerations
- **Architecture Impact**: How changes affect system design
- **Performance**: Potential performance implications
- **Security**: Security considerations or requirements
- **Testing**: Existing test coverage and patterns

I'll use various search strategies:
- `Grep` for text patterns and string literals
- `Glob` for finding files by type or name
- `Read` for examining specific implementations
- `Agent` for complex multi-file analysis
- `Supabase MCP` for database schema queries

## 3. Generate Enhanced Description

Based on my analysis, I'll create a comprehensive description including:

### Structure
- **Problem Statement**: Clear articulation with current vs desired state
- **Why This Matters**: Business value, impact, and priority justification
- **Technical Scope**: Affected files, implementation approach, challenges
- **Success Criteria**: Acceptance criteria and definition of done
- **Expected Outcome**: User-facing changes and measurable results

### Review Before Proceeding
I'll present the enhanced description for your review. You can then use `/project:linear:enhance-description` to update the issue if you approve.

## 4. Identify Question Areas

I'll also identify areas that need clarification:
- Missing requirements or edge cases
- Ambiguous specifications
- Integration concerns
- Testing requirements

These questions can be posted using `/project:linear:generate-questions` after you've reviewed them.

Let me start by fetching issue $ARGUMENTS...