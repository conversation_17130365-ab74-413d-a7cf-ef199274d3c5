# Project Status Overview

I'll provide a comprehensive status overview of the FashionLab project.

## 1. Current Work Context

- Show current git branch and any uncommitted changes
- Display active Linear issue (if on a feature branch)
- Show current todos and their status

## 2. Environment Status

Check all environments:

- **Local**: Development server status
- **Staging** (qnfmiotatmkoumlymynq): Latest deployment, health check
- **Production** (cpelxqvcjnbpnphttzsn): Version, last deployment, metrics

## 3. Recent Activity

- Recent commits (last 24 hours)
- Recent Linear activity
- Recent deployments

## 4. Pending Work

- Open Linear issues assigned to you
- Incomplete todos
- PRs awaiting review

## 5. Potential Issues

I'll check for:

- Uncommitted changes older than 1 day
- Stale branches
- Failed deployments
- High-priority issues not in progress

## 6. Quick Actions

Based on the status, I'll suggest:

- Next task to work on
- PRs that need attention
- Deployments that might be needed

Let me gather this information...
