# Start Your Development Day

I'll help you start your development day with a comprehensive overview and setup.

## 1. Environment Check

First, let me check your development environment:

- Current branch and git status
- Any uncommitted changes
- Environment variables status
- Local vs remote branch status

## 2. Pull Latest Changes

I'll pull the latest changes from the main branch and show you what's new:

- Recent commits
- Files changed
- Any new dependencies or migrations

## 3. Linear Issues Overview

Let me fetch your assigned issues from Linear:

- Use Linear MCP to get active issues
- Show status, priority, and due dates
- Highlight any blockers or urgent items

## 4. Todo List Review

I'll check your current todos:

- Use TodoRead to show pending tasks
- Identify incomplete tasks from yesterday
- Suggest task prioritization

## 5. Feature Flags Status

Review current feature flags:

- Show features enabled in staging but not production
- Identify features ready for production release
- Check if any features need testing

## 6. Staging Environment Status

Quick check of staging environment:

- Use mcp__supabase__get_project with ID "qnfmiotatmkoumlymynq"
- Check for any recent deployments
- Review any staging-specific issues
- List features currently in staging-only mode

## 7. Today's Priorities

Based on the above, I'll suggest your priorities for today:

- Critical bugs or blockers
- High-priority Linear issues
- Incomplete todos
- Features near completion
- Feature flags ready for production

## 8. Quick Commands

Here are today's most useful commands:

- `/project:features:new [issue-id]` - Start a new feature
- `/project:features:enable-production [feature]` - Enable feature in production
- `/project:deployment:staging` - Deploy to staging
- `/project:deployment:hotfix` - Create urgent production fix
- `/project:daily:status` - Check status anytime

Let me start gathering this information for you...
